# 订阅系统需求文档

## 介绍

LavaChat需要实现一个平衡用户体验和商业收入的订阅系统。基于应用的独特功能（多模型交互、自定义Actions、分享功能等），我们需要设计一个既能吸引用户又能产生稳定收入的订阅模式。

## 需求

### 需求 1: 免费用户基础体验

**用户故事:** 作为一个新用户，我希望能够免费体验应用的核心功能，这样我可以了解应用价值并决定是否订阅。

#### 验收标准

1. WHEN 用户首次安装应用 THEN 系统 SHALL 提供免费账户，包含完整的基础功能访问权限
2. WHEN 免费用户使用应用 THEN 系统 SHALL 允许访问以下功能：
   - 无限ChatSession创建和管理
   - 使用用户自己的API Key连接任意LLM
   - 完整的消息交互（发送、接收、编辑、树状结构）
   - 多AI回复功能（每个消息最多同时请求3个AI实例）
   - 导入/导出ChatSession文件（.lavachat格式）
   - 无限自定义LLMInstance和LLMInstanceGroup
   - 基础的自定义Action（最多10个，每天最多使用50次）
   - @mention功能（文件引用和实例引用）
3. WHEN 免费用户尝试访问高级功能 THEN 系统 SHALL 显示订阅提示界面
4. WHEN 免费用户使用应用超过30天 THEN 系统 SHALL 适时显示支持开发者的温和提示

### 需求 2: 订阅计划层级设计

**用户故事:** 作为产品运营者，我希望提供简洁的订阅计划，这样可以满足不同用户的需求并最大化收入。

#### 验收标准

1. WHEN 系统初始化订阅计划 THEN 系统 SHALL 提供以下两个层级：
   - Pro Plan (专业版): 月费 $2.99/¥19/月 或 年费 $29.99/¥199/年
   - Ultimate Plan (旗舰版): 月费 $5.99/¥39/月 或 年费 $59.99/¥399/年
2. WHEN 用户查看订阅计划 THEN 系统 SHALL 清晰展示每个计划的功能差异和价值主张
3. WHEN 用户选择年费计划 THEN 系统 SHALL 提供相当于2个月免费的折扣优惠
4. WHEN 新用户注册 THEN 系统 SHALL 提供7天免费试用任意付费计划

### 需求 3: 功能权限分层

**用户故事:** 作为付费用户，我希望根据我的订阅级别获得相应的功能权限，这样我可以根据需求选择合适的计划。

#### 验收标准

1. WHEN 用户为Pro订阅者 THEN 系统 SHALL 提供免费功能加上：
   - 无限多AI回复（免费用户限制每消息3个AI实例）
   - 无限自定义Action使用次数（免费用户每天限制50次）
   - 最多50个自定义Action（免费用户限制10个）
   - iCloud链接分享功能（ChatSession、LLMInstance、Action等）
   - 二维码分享功能
   - 高级ChatSession设置和模板
   - FileSession功能（文件交互和编辑）
   - 高级统计和使用分析
   - 主题和界面定制选项
2. WHEN 用户为Ultimate订阅者 THEN 系统 SHALL 提供Pro权限加上：
   - 无限自定义Action数量（Pro用户限制50个）
   - 批量操作功能（批量导入/导出、批量设置）
   - 高级自动化Action和工作流
   - 优先功能更新和Beta测试权限
   - 专属客户支持渠道
   - 高级数据备份和恢复选项
   - 企业级功能（团队分享、管理面板等）

### 需求 4: Apple生态订阅管理

**用户故事:** 作为用户，我希望通过熟悉的Apple订阅系统管理我的付费计划，享受无缝的支付和管理体验。

#### 验收标准

1. WHEN 用户选择订阅计划 THEN 系统 SHALL 使用Apple StoreKit 2进行购买流程
2. WHEN 系统验证订阅状态 THEN 系统 SHALL 使用Apple的服务器端验证确保订阅有效性
3. WHEN 用户管理订阅 THEN 系统 SHALL 引导用户到Apple的订阅管理界面
4. WHEN 订阅状态发生变化 THEN 系统 SHALL 通过StoreKit监听并实时更新本地状态
5. WHEN 用户在多设备间切换 THEN 系统 SHALL 通过CloudKit同步订阅状态和权限

### 需求 5: 分享功能商业化

**用户故事:** 作为产品设计者，我希望将分享功能作为付费功能的一部分，这样可以鼓励用户升级订阅。

#### 验收标准

1. WHEN 免费用户尝试使用iCloud分享 THEN 系统 SHALL 显示订阅升级提示
2. WHEN 免费用户尝试生成二维码分享 THEN 系统 SHALL 显示订阅升级提示
3. WHEN Pro或Ultimate用户分享内容 THEN 系统 SHALL 在分享内容中包含"Created with LavaChat"水印
4. WHEN 用户通过分享链接安装应用 THEN 系统 SHALL 为分享者提供推荐奖励（延长订阅或积分）
5. WHEN 分享的内容被访问 THEN 系统 SHALL 记录分享统计数据供用户查看

### 需求 6: 订阅状态管理

**用户故事:** 作为系统管理员，我需要准确跟踪和管理用户的订阅状态，确保服务的正确提供和计费。

#### 验收标准

1. WHEN 用户完成Apple IAP购买 THEN 系统 SHALL 验证收据并更新SubscriptionStatus
2. WHEN 订阅即将到期 THEN 系统 SHALL 提前7天和1天发送续费提醒
3. WHEN 订阅过期 THEN 系统 SHALL 提供3天宽限期，期间功能降级但不完全禁用
4. WHEN 用户取消订阅 THEN 系统 SHALL 在当前周期结束前保持服务，并提供挽留优惠
5. WHEN 系统检测到订阅状态变化 THEN 系统 SHALL 实时更新用户权限和UI显示

### 需求 7: 用户引导和转化

**用户故事:** 作为产品经理，我希望通过智能的用户引导提高免费用户到付费用户的转化率。

#### 验收标准

1. WHEN 免费用户达到Action数量限制 THEN 系统 SHALL 展示升级的价值主张和Pro功能预览
2. WHEN 用户使用高频功能 THEN 系统 SHALL 适时推荐相关的付费功能（如FileSession、高级分享）
3. WHEN 用户尝试分享功能 THEN 系统 SHALL 展示分享功能的完整体验预览和升级引导
4. WHEN 用户在试用期内 THEN 系统 SHALL 通过应用内通知发送个性化的功能介绍
5. WHEN 用户试用即将结束 THEN 系统 SHALL 提供限时优惠促进转化

### 需求 8: 独有功能商业化策略

**用户故事:** 作为产品设计者，我希望将应用的独有功能（多AI回复、自定义Action）作为核心付费价值点，让用户体验到独特价值后产生升级动力。

#### 验收标准

1. WHEN 免费用户尝试同时向超过3个AI实例发送消息 THEN 系统 SHALL 显示升级提示，展示Pro用户的无限多AI回复体验
2. WHEN 免费用户的Action使用次数接近每日限制 THEN 系统 SHALL 显示剩余次数提醒和升级建议
3. WHEN 免费用户达到10个Action限制 THEN 系统 SHALL 展示Pro用户可创建50个Action的优势
4. WHEN 用户体验多AI回复功能 THEN 系统 SHALL 在UI中突出显示这是LavaChat的独有功能
5. WHEN 用户使用Action功能 THEN 系统 SHALL 适时展示高级Action和自动化工作流的预览

### 需求 9: 社区排行榜功能

**用户故事:** 作为用户，我希望能够发现和下载社区中热门的Action和ChatSessionSetting，这样我可以快速获得高质量的配置而不需要从零开始创建。

#### 验收标准

1. WHEN 免费用户访问排行榜 THEN 系统 SHALL 允许浏览但限制每天最多下载3个Action或Setting
2. WHEN Pro用户访问排行榜 THEN 系统 SHALL 允许每天下载最多20个Action或Setting
3. WHEN Ultimate用户访问排行榜 THEN 系统 SHALL 允许无限下载Action和Setting
4. WHEN 用户上传Action或Setting到排行榜 THEN 系统 SHALL 通过CloudKit Public Database实现分享
5. WHEN Action或Setting获得高下载量 THEN 系统 SHALL 为创作者提供认证标识和推荐位置
6. WHEN 免费用户达到下载限制 THEN 系统 SHALL 显示升级提示，强调Pro用户的更高下载额度
7. WHEN 用户下载热门内容 THEN 系统 SHALL 记录下载统计并更新排行榜排序

### 需求 10: 收入优化策略

**用户故事:** 作为商业决策者，我希望通过数据驱动的方式优化订阅收入和用户留存。

#### 验收标准

1. WHEN 系统收集用户行为数据 THEN 系统 SHALL 分析功能使用模式和转化路径
2. WHEN 检测到用户流失风险 THEN 系统 SHALL 提供个性化的挽留优惠
3. WHEN 用户长期使用某个计划 THEN 系统 SHALL 适时推荐升级到更高级计划
4. WHEN 节假日或特殊时期 THEN 系统 SHALL 支持灵活的促销活动配置
5. WHEN 用户推荐新用户 THEN 系统 SHALL 提供双向奖励机制（推荐者和被推荐者都获得优惠）