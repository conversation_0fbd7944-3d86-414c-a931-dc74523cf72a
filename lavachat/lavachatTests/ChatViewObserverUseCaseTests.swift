import XCTest
import Combine
@testable import lavachat

final class ChatViewObserverUseCaseTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var useCase: ObserveChatsChangesUseCase!
    var chatRepository: ChatRepository!
    var testUserId: UUID!
    var mockData: MockChatPreviewData!
    var cancellables: Set<AnyCancellable>!
    
    // Test session and setting IDs
    var testChatSessionId: UUID!
    var testSettingId: UUID!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        let container = DIContainer(context: PersistenceController.shared.container.viewContext)
        chatRepository = container.getSharedChatRepository()
        // MockChatRepository is working, but it's not a good idea to use it in production
        // Use real CoreData for testing instead of MockChatRepository
        // chatRepository = MockChatRepository()
        useCase = ObserveChatsChangesUseCase(chatRepository: chatRepository)
        testUserId = MockChatPreviewData.systemDefaultUserId
        mockData = MockChatPreviewData()
        cancellables = Set<AnyCancellable>()
        
        // Initialize test IDs
        testChatSessionId = UUID()
        testSettingId = UUID()
        
        // Setup test data
        let expectation = XCTestExpectation(description: "Setup test data")
        Task {
            await setupTestData()
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10.0)
    }
    
    override func tearDown() {
        cancellables.removeAll()
        useCase = nil
        chatRepository = nil
        testChatSessionId = nil
        testSettingId = nil
        super.tearDown()
    }
    
    // MARK: - Setup Test Data
    
    private func setupTestData() async {
        do {
            // Create test chat session setting
            let testSetting = ChatSessionSetting(
                id: testSettingId,
                name: "Test Setting",
                isSystemDefault: false,
                createdAt: Date(),
                lastModifiedAt: Date(),
                llmParameterOverrides: [:],
                defaultContextServerIds: [],
                shouldExpandThinking: true,
                uiThemeSettings: UIThemeSettings(
                    themeIdentifier: "dark",
                    customColors: nil,
                    backgroundImage: nil,
                    fontSize: .large
                ),
                messageActionSettings: MessageActionSettings(
                    actionPanelActions: [SystemMessageActions.camera.id, SystemMessageActions.photo.id, SystemMessageActions.file.id],
                    userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.like.id],
                    assistantMessageCardActions: [SystemMessageActions.copy.id, SystemMessageActions.select.id],
                    assistantMessageMenuActions: [SystemMessageActions.copy.id, SystemMessageActions.select.id]
                ),
                savedPromptSegments: [],
                auxiliaryLLMInstanceId: nil,
                shouldAutoGenerateTitle: false,
                contextMessageCount: Int64.max,
                metadata: [:]
            )
            
            // Create test chat session
            let testSession = ChatSession(
                id: testChatSessionId,
                title: "Test Session for Observer",
                createdAt: Date(),
                lastModifiedAt: Date(),
                activeMessageId: nil,
                activeLLMInstanceIds: [MockChatPreviewData.claudeInstanceId],
                usedLLMInstanceIds: [MockChatPreviewData.claudeInstanceId],
                activeContextServerIds: [],
                settingsId: testSettingId,
                userId: testUserId,
                instanceSettings: [:]
            )
            
            // Add to repository
            try await chatRepository.createSetting(testSetting)
            try await chatRepository.createChatSession(testSession)
            
            print("✅ ChatViewObserverUseCaseTests: Test data setup completed")
            print("   - Session ID: \(testChatSessionId!)")
            print("   - Setting ID: \(testSettingId!)")
            
        } catch {
            print("❌ ChatViewObserverUseCaseTests: Failed to setup test data: \(error)")
            XCTFail("Failed to setup test data: \(error)")
        }
    }
    
    // MARK: - Basic Observer Tests
    
    func testObserveSpecificSessionExists() async throws {
        // Given - test session exists
        let expectation = XCTestExpectation(description: "Observe specific session")
        expectation.expectedFulfillmentCount = 1
        
        var receivedChange: ChatViewRelevantChange?
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                receivedChange = change
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Trigger a change - update session title
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let updatedSession = try await chatRepository.getChatSession(byId: testChatSessionId)!
        let modifiedSession = ChatSession(
            id: updatedSession.id,
            title: "Updated Test Session Title",
            createdAt: updatedSession.createdAt,
            lastModifiedAt: Date(),
            activeMessageId: updatedSession.activeMessageId,
            activeLLMInstanceIds: updatedSession.activeLLMInstanceIds,
            usedLLMInstanceIds: updatedSession.usedLLMInstanceIds,
            activeContextServerIds: updatedSession.activeContextServerIds,
            settingsId: updatedSession.settingsId,
            userId: updatedSession.userId,
            instanceSettings: updatedSession.instanceSettings
        )
        
        try await chatRepository.updateChatSession(modifiedSession)
        
        // Then - should receive session title change
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedChange, "Should receive a change notification")
        if case .sessionTitleChanged(let newTitle) = receivedChange {
            XCTAssertEqual(newTitle, "Updated Test Session Title", "Should receive updated title")
        } else {
            XCTFail("Should receive sessionTitleChanged event, got: \(String(describing: receivedChange))")
        }
        
        print("✅ Session title change test passed")
    }
    
    func testObserveSessionSettingsChange() async throws {
        // Given - test session with setting exists
        let expectation = XCTestExpectation(description: "Observe setting change")
        expectation.expectedFulfillmentCount = 1
        
        var receivedChange: ChatViewRelevantChange?
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                receivedChange = change
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Trigger a change - update session's settingsId
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let newSettingId = UUID()
        let newSetting = ChatSessionSetting(
            id: newSettingId,
            name: "New Test Setting",
            isSystemDefault: false,
            createdAt: Date(),
            lastModifiedAt: Date(),
            llmParameterOverrides: nil,
            defaultContextServerIds: nil,
            shouldExpandThinking: true,
            uiThemeSettings: UIThemeSettings(
                themeIdentifier: "light",
                customColors: nil,
                backgroundImage: nil,
                fontSize: .small
            ),
            messageActionSettings: MessageActionSettings(
                actionPanelActions: [SystemMessageActions.camera.id, SystemMessageActions.photo.id, SystemMessageActions.file.id],
                userMessageActions: [SystemMessageActions.copy.id],
                assistantMessageCardActions: [SystemMessageActions.copy.id],
                assistantMessageMenuActions: [SystemMessageActions.copy.id]
            ),
            savedPromptSegments: [],
            auxiliaryLLMInstanceId: nil,
            shouldAutoGenerateTitle: false,
            contextMessageCount: Int64.max,
            metadata: nil
        )
        
        try await chatRepository.createSetting(newSetting)
        
        let updatedSession = try await chatRepository.getChatSession(byId: testChatSessionId)!
        let modifiedSession = ChatSession(
            id: updatedSession.id,
            title: updatedSession.title,
            createdAt: updatedSession.createdAt,
            lastModifiedAt: Date(),
            activeMessageId: updatedSession.activeMessageId,
            activeLLMInstanceIds: updatedSession.activeLLMInstanceIds,
            usedLLMInstanceIds: updatedSession.usedLLMInstanceIds,
            activeContextServerIds: updatedSession.activeContextServerIds,
            settingsId: newSettingId, // Changed settingsId
            userId: updatedSession.userId,
            instanceSettings: updatedSession.instanceSettings
        )
        
        try await chatRepository.updateChatSession(modifiedSession)
        
        // Then - should receive session settings change
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedChange, "Should receive a change notification")
        if case .sessionSettingsChanged(let newSettingsId) = receivedChange {
            XCTAssertEqual(newSettingsId, newSettingId, "Should receive updated settings ID")
        } else {
            XCTFail("Should receive sessionSettingsChanged event, got: \(String(describing: receivedChange))")
        }
        
        print("✅ Session settings change test passed")
    }
    
    func testObserveUIThemeSettingsChange() async throws {
        // Given - test session and setting exist
        let expectation = XCTestExpectation(description: "Observe UI theme change")
        expectation.expectedFulfillmentCount = 1
        
        var receivedChange: ChatViewRelevantChange?
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                receivedChange = change
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Trigger a change - update UI theme settings
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let originalSetting = try await chatRepository.getSetting(byId: testSettingId)!
        let newUIThemeSettings = UIThemeSettings(
            themeIdentifier: "custom",
            customColors: nil,
            backgroundImage: nil,
            fontSize: .large
        )
        
        let modifiedSetting = ChatSessionSetting(
            id: originalSetting.id,
            name: originalSetting.name,
            isSystemDefault: originalSetting.isSystemDefault,
            createdAt: originalSetting.createdAt,
            lastModifiedAt: Date(),
            llmParameterOverrides: originalSetting.llmParameterOverrides,
            defaultContextServerIds: originalSetting.defaultContextServerIds,
            shouldExpandThinking: originalSetting.shouldExpandThinking,
            uiThemeSettings: newUIThemeSettings, // Changed UI theme
            messageActionSettings: originalSetting.messageActionSettings,
            savedPromptSegments: originalSetting.savedPromptSegments,
            metadata: originalSetting.metadata
        )
        
        try await chatRepository.updateSetting(modifiedSetting)
        
        // Then - should receive UI theme change
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedChange, "Should receive a change notification")
        if case .settingUIThemeChanged(let newThemeSettings) = receivedChange {
            XCTAssertEqual(newThemeSettings?.themeIdentifier, "custom", "Should receive updated theme identifier")
            XCTAssertEqual(newThemeSettings?.fontSize, .large, "Should receive updated font size")
        } else {
            XCTFail("Should receive settingUIThemeChanged event, got: \(String(describing: receivedChange))")
        }
        
        print("✅ UI theme settings change test passed")
    }
    
    func testObserveMessageActionSettingsChange() async throws {
        // Given - test session and setting exist
        let expectation = XCTestExpectation(description: "Observe message action change")
        expectation.expectedFulfillmentCount = 1
        
        var receivedChange: ChatViewRelevantChange?
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                receivedChange = change
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Trigger a change - update message action settings
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let originalSetting = try await chatRepository.getSetting(byId: testSettingId)!
        let newMessageActionSettings = MessageActionSettings(
            actionPanelActions: [SystemMessageActions.camera.id, SystemMessageActions.photo.id, SystemMessageActions.file.id],
            userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id, SystemMessageActions.edit.id],
            assistantMessageCardActions: [SystemMessageActions.copy.id, SystemMessageActions.select.id, SystemMessageActions.edit.id, SystemMessageActions.regenerate.id],
            assistantMessageMenuActions: [SystemMessageActions.copy.id, SystemMessageActions.select.id, SystemMessageActions.edit.id, SystemMessageActions.regenerate.id]
        )
        
        let modifiedSetting = ChatSessionSetting(
            id: originalSetting.id,
            name: originalSetting.name,
            isSystemDefault: originalSetting.isSystemDefault,
            createdAt: originalSetting.createdAt,
            lastModifiedAt: Date(),
            llmParameterOverrides: originalSetting.llmParameterOverrides,
            defaultContextServerIds: originalSetting.defaultContextServerIds,
            shouldExpandThinking: originalSetting.shouldExpandThinking,
            uiThemeSettings: originalSetting.uiThemeSettings,
            messageActionSettings: newMessageActionSettings, // Changed message actions
            savedPromptSegments: originalSetting.savedPromptSegments,
            metadata: originalSetting.metadata
        )
        
        try await chatRepository.updateSetting(modifiedSetting)
        
        // Then - should receive message action change
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedChange, "Should receive a change notification")
        if case .settingMessageActionsChanged(let newActionSettings) = receivedChange {
            XCTAssertTrue(newActionSettings?.userMessageActions?.contains(SystemMessageActions.regenerate.id) ?? false, "Should include regenerate action")
            XCTAssertTrue(newActionSettings?.assistantMessageCardActions?.contains(SystemMessageActions.regenerate.id) ?? false, "Should include regenerate action for assistant")
        } else {
            XCTFail("Should receive settingMessageActionsChanged event, got: \(String(describing: receivedChange))")
        }
        
        print("✅ Message action settings change test passed")
    }
    
    func testObservePromptSegmentsChange() async throws {
        // Given - test session and setting exist
        let expectation = XCTestExpectation(description: "Observe prompt segments change")
        expectation.expectedFulfillmentCount = 1
        
        var receivedChange: ChatViewRelevantChange?
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                receivedChange = change
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Trigger a change - update prompt segments
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let originalSetting = try await chatRepository.getSetting(byId: testSettingId)!
        let newPromptSegments = [
            SavedPromptSegment(
                id: UUID(),
                content: "Test prompt segment",
                usageCount: 1,
                createdAt: Date(),
                lastUsedAt: Date()
            )
        ]
        
        let modifiedSetting = ChatSessionSetting(
            id: originalSetting.id,
            name: originalSetting.name,
            isSystemDefault: originalSetting.isSystemDefault,
            createdAt: originalSetting.createdAt,
            lastModifiedAt: Date(),
            llmParameterOverrides: originalSetting.llmParameterOverrides,
            defaultContextServerIds: originalSetting.defaultContextServerIds,
            shouldExpandThinking: originalSetting.shouldExpandThinking,
            uiThemeSettings: originalSetting.uiThemeSettings,
            messageActionSettings: originalSetting.messageActionSettings,
            savedPromptSegments: newPromptSegments, // Changed prompt segments
            metadata: originalSetting.metadata
        )
        
        try await chatRepository.updateSetting(modifiedSetting)
        
        // Then - should receive prompt segments change
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedChange, "Should receive a change notification")
        if case .settingPromptSegmentsChanged(let newSegments) = receivedChange {
            XCTAssertEqual(newSegments?.count, 1, "Should have one prompt segment")
            XCTAssertEqual(newSegments?.first?.content, "Test prompt segment", "Should receive updated prompt segment content")
        } else {
            XCTFail("Should receive settingPromptSegmentsChanged event, got: \(String(describing: receivedChange))")
        }
        
        print("✅ Prompt segments change test passed")
    }
    
    func testObserveSessionDeletion() async throws {
        // Given - test session exists
        let expectation = XCTestExpectation(description: "Observe session deletion")
        expectation.expectedFulfillmentCount = 1
        
        var receivedChange: ChatViewRelevantChange?
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                receivedChange = change
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Trigger a change - delete the session
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        try await chatRepository.deleteChatSession(byId: testChatSessionId)
        
        // Then - should receive session deletion
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedChange, "Should receive a change notification")
        if case .sessionDeleted = receivedChange {
            print("✅ Correctly received sessionDeleted event")
        } else {
            XCTFail("Should receive sessionDeleted event, got: \(String(describing: receivedChange))")
        }
        
        print("✅ Session deletion test passed")
    }
    
    // MARK: - Filtering Tests
    
    func testObserverFiltersIrrelevantChanges() async throws {
        // Given - test session exists
        let expectation = XCTestExpectation(description: "No irrelevant changes")
        expectation.isInverted = true // Should NOT fulfill
        
        // When - start observing the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                expectation.fulfill() // Should not be called for irrelevant changes
            }
            .store(in: &cancellables)
        
        // Trigger irrelevant changes
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let updatedSession = try await chatRepository.getChatSession(byId: testChatSessionId)!
        let modifiedSession = ChatSession(
            id: updatedSession.id,
            title: updatedSession.title, // No change
            createdAt: updatedSession.createdAt,
            lastModifiedAt: Date(), // This change should be filtered out
            activeMessageId: updatedSession.activeMessageId,
            activeLLMInstanceIds: [MockChatPreviewData.gpt4InstanceId], // This change should be filtered out
            usedLLMInstanceIds: [MockChatPreviewData.claudeInstanceId, MockChatPreviewData.gpt4InstanceId], // This change should be filtered out
            activeContextServerIds: updatedSession.activeContextServerIds,
            settingsId: updatedSession.settingsId, // No change
            userId: updatedSession.userId,
            instanceSettings: updatedSession.instanceSettings
        )
        
        try await chatRepository.updateChatSession(modifiedSession)
        
        // Then - should NOT receive any change notifications for irrelevant fields
        await fulfillment(of: [expectation], timeout: 2.0)
        
        print("✅ Irrelevant changes filtering test passed")
    }
    
    func testObserverIgnoresOtherSessions() async throws {
        // Given - create another session
        let otherSessionId = UUID()
        let otherSession = ChatSession(
            id: otherSessionId,
            title: "Other Session",
            createdAt: Date(),
            lastModifiedAt: Date(),
            activeMessageId: nil,
            activeLLMInstanceIds: [MockChatPreviewData.claudeInstanceId],
            usedLLMInstanceIds: [MockChatPreviewData.claudeInstanceId],
            activeContextServerIds: [],
            settingsId: testSettingId,
            userId: testUserId,
            instanceSettings: [:]
        )
        
        try await chatRepository.createChatSession(otherSession)
        
        let expectation = XCTestExpectation(description: "No changes from other sessions")
        expectation.isInverted = true // Should NOT fulfill
        
        // When - start observing only the specific session
        useCase.execute(forSpecificSessionId: testChatSessionId)
            .sink { change in
                expectation.fulfill() // Should not be called for other sessions
            }
            .store(in: &cancellables)
        
        // Trigger changes in the other session
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let modifiedOtherSession = ChatSession(
            id: otherSession.id,
            title: "Updated Other Session Title", // This should be ignored
            createdAt: otherSession.createdAt,
            lastModifiedAt: Date(),
            activeMessageId: otherSession.activeMessageId,
            activeLLMInstanceIds: otherSession.activeLLMInstanceIds,
            usedLLMInstanceIds: otherSession.usedLLMInstanceIds,
            activeContextServerIds: otherSession.activeContextServerIds,
            settingsId: otherSession.settingsId,
            userId: otherSession.userId,
            instanceSettings: otherSession.instanceSettings
        )
        
        try await chatRepository.updateChatSession(modifiedOtherSession)
        
        // Then - should NOT receive any change notifications for other sessions
        await fulfillment(of: [expectation], timeout: 2.0)
        
        print("✅ Other sessions filtering test passed")
    }
    
    func testObserveNonExistentSession() async {
        // Given - non-existent session ID
        let nonExistentSessionId = UUID()
        let expectation = XCTestExpectation(description: "No changes for non-existent session")
        expectation.isInverted = true // Should NOT fulfill
        
        // When - start observing non-existent session
        useCase.execute(forSpecificSessionId: nonExistentSessionId)
            .sink { change in
                expectation.fulfill() // Should not receive changes for non-existent session
            }
            .store(in: &cancellables)
        
        // Wait a bit to see if any changes are received
        await fulfillment(of: [expectation], timeout: 1.0)
        
        print("✅ Non-existent session test passed")
    }
} 
