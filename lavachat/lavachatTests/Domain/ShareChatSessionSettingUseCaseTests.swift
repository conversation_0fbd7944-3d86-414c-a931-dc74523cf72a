import XCTest
@testable import lavachat

final class ShareChatSessionSettingUseCaseTests: XCTestCase {
    
    var mockShareService: MockShareService!
    var shareChatSessionSettingUseCase: ShareChatSessionSettingUseCase!
    var testSetting: ChatSessionSetting!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        mockShareService = MockShareService()
        shareChatSessionSettingUseCase = ShareChatSessionSettingUseCase(shareService: mockShareService)
        
        // Create a test chat session setting
        testSetting = ChatSessionSetting(
            id: UUID(),
            name: "Test Setting",
            isSystemDefault: false,
            createdAt: Date(),
            lastModifiedAt: Date(),
            llmParameterOverrides: [:],
            defaultContextServerIds: [],
            shouldExpandThinking: true,
            uiThemeSettings: nil,
            messageActionSettings: nil,
            savedPromptSegments: [],
            auxiliaryLLMInstanceId: nil,
            shouldAutoGenerateTitle: false,
            contextMessageCount: 100,
            metadata: [:]
        )
    }
    
    override func tearDownWithError() throws {
        mockShareService = nil
        shareChatSessionSettingUseCase = nil
        testSetting = nil
        try super.tearDownWithError()
    }
    
    // MARK: - execute() Tests
    
    func testExecuteWithFileFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .file)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareChatSessionSettingUseCase.execute(testSetting, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .file)
            XCTAssertEqual(info.contentType, .chatSessionSetting)
            XCTAssertNotNil(info.fileURL)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteWithICloudFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .icloud)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareChatSessionSettingUseCase.execute(testSetting, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .icloud)
            XCTAssertEqual(info.contentType, .chatSessionSetting)
            XCTAssertNotNil(info.icloudURL)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteFailure() async throws {
        // Given
        let configuration = ShareConfiguration(format: .file)
        mockShareService.shouldSucceed = false
        
        // When
        let result = await shareChatSessionSettingUseCase.execute(testSetting, configuration: configuration)
        
        // Then
        switch result {
        case .success:
            XCTFail("Expected failure but got success")
        case .failure(let error):
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - shareAsFile() Tests
    
    func testShareAsFileSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let fileName = "test_setting.lavachat"
        
        // When
        let fileURL = try await shareChatSessionSettingUseCase.shareAsFile(testSetting, fileName: fileName)
        
        // Then
        XCTAssertEqual(fileURL, mockShareService.mockFileURL)
    }
    
    func testShareAsFileFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        
        // When & Then
        do {
            _ = try await shareChatSessionSettingUseCase.shareAsFile(testSetting, fileName: nil)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - shareViaICloud() Tests
    
    func testShareViaICloudSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let permissions = ICloudSharePermissions()
        
        // When
        let icloudURL = try await shareChatSessionSettingUseCase.shareViaICloud(testSetting, permissions: permissions)
        
        // Then
        XCTAssertEqual(icloudURL, mockShareService.mockICloudURL)
    }
    
    func testShareViaICloudFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        let permissions = ICloudSharePermissions()
        
        // When & Then
        do {
            _ = try await shareChatSessionSettingUseCase.shareViaICloud(testSetting, permissions: permissions)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - generateQRCode() Tests
    
    func testGenerateQRCodeSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let size = CGSize(width: 200, height: 200)
        
        // When
        let qrCodeData = try await shareChatSessionSettingUseCase.generateQRCode(for: testSetting, size: size)
        
        // Then
        XCTAssertEqual(qrCodeData, mockShareService.mockQRCodeData)
    }
    
    func testGenerateQRCodeFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        let size = CGSize(width: 200, height: 200)
        
        // When & Then
        do {
            _ = try await shareChatSessionSettingUseCase.generateQRCode(for: testSetting, size: size)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
}
