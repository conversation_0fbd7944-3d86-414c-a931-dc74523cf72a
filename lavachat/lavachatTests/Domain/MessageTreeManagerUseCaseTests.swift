import XCTest
@testable import lavachat

final class MessageTreeManagerUseCaseTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var useCase: MessageTreeManagerUseCase!
    var chatRepository: ChatRepository!
    var testSessionId: UUID!
    var testUserId: UUID!
    var userFollowupMessageId: UUID!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        let container = DIContainer(context: PersistenceController.shared.container.viewContext)
        chatRepository = container.getSharedChatRepository()
        useCase = MessageTreeManagerUseCase(chatRepository: chatRepository)
        testSessionId = UUID()
        testUserId = UUID()
        userFollowupMessageId = UUID()
        
        // Setup test data using BuiltinChatInitializer pattern - wait for completion
        let expectation = XCTestExpectation(description: "Setup test data")
        Task {
            await setupTestData()
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10.0)
    }
    
    override func tearDown() {
        useCase.clearAllCaches()
        useCase = nil
        chatRepository = nil
        testSessionId = nil
        testUserId = nil
        userFollowupMessageId = nil
        super.tearDown()
    }
    
    // MARK: - Setup Test Data
    
    private func setupTestData() async {
        
        // Create test messages similar to BuiltinChatInitializer
        let rootMessage = Message(
            id: UUID(),
            sessionId: testSessionId,
            parentId: nil,
            role: .user,
            content: [ContentBlock.text("Hello, can you help me with testing?")],
            depth: 0,
            userId: testUserId
        )
        
        let assistantMessage = Message(
            id: UUID(),
            sessionId: testSessionId,
            parentId: rootMessage.id,
            role: .assistant,
            content: [ContentBlock.text("Of course! I'd be happy to help you with testing.")],
            depth: 1,
            llmInstanceId: UUID()
        )
        
        let userFollowup = Message(
            id: userFollowupMessageId,
            sessionId: testSessionId,
            parentId: assistantMessage.id,
            role: .user,
            content: [ContentBlock.text("Great! Can you explain unit testing?")],
            depth: 2,
            userId: testUserId
        )

        // Create test chat session
        let chatSession = ChatSession(
            id: testSessionId,
            title: "Test Session",
            activeMessageId: userFollowupMessageId,
            activeLLMInstanceIds: [UUID()],
            usedLLMInstanceIds: [UUID()],
            userId: testUserId
        )
        
        // Add data to repository and wait for completion
        do {
            try await chatRepository.createChatSession(chatSession)
            try await chatRepository.createMessage(rootMessage)
            try await chatRepository.createMessage(assistantMessage)
            try await chatRepository.createMessage(userFollowup)
            print("setupTestData Done - All data created successfully")
        } catch {
            print("setupTestData Failed: \(error)")
            XCTFail("Failed to setup test data: \(error)")
        }
    }
    
    // MARK: - Initialization Tests
    
    func testInitializeTreeFromRepository() async throws {
        // When
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        
        // Then
        let messageLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        XCTAssertFalse(messageLayers.isEmpty, "Message layers should not be empty after initialization")
    }
    
    // MARK: - Message Layer Tests
    
    func testGetMessageLayersForDisplay() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        
        // When
        let messageLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        
        // Then
        XCTAssertFalse(messageLayers.isEmpty, "Should have message layers")
        
        // Verify each layer has a non-optional activeMessageId
        for layer in messageLayers {
            XCTAssertNotNil(layer.activeMessageId, "Each layer should have a valid activeMessageId")
            XCTAssertTrue(layer.siblingMessages.contains { $0.id == layer.activeMessageId }, 
                         "Active message ID should exist in sibling messages")
        }
    }
    
    // MARK: - Switch Active Message Tests
    
    func testSwitchActiveMessage() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        
        if let firstLayer = initialLayers.first,
              firstLayer.siblingMessages.count == 1 {
            // Add another message to create siblings for testing
            let newMessage = Message(
                sessionId: testSessionId,
                parentId: nil,
                role: .user,
                content: [ContentBlock.text("Another test message")],
                depth: 0,
                userId: testUserId
            )
            try await chatRepository.createMessage(newMessage)
            await useCase.addMessageAndUpdateTree(sessionId: testSessionId, message: newMessage)
        }
        
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        guard let testLayer = updatedLayers.first,
              testLayer.siblingMessages.count > 1 else {
            XCTFail("Need at least 2 sibling messages to test switching")
            return
        }
        
        let currentActiveId = testLayer.activeMessageId
        let newActiveId = testLayer.siblingMessages.first { $0.id != currentActiveId }?.id
        
        guard let newActiveId = newActiveId else {
            XCTFail("Could not find alternative message ID")
            return
        }
        
        // When
        let resultLayers = try await useCase.switchActiveMessage(
            sessionId: testSessionId,
            fromMessageId: currentActiveId,
            newActiveMessageId: newActiveId
        )
        
        // Then
        XCTAssertFalse(resultLayers.isEmpty, "Should return updated message layers")
        
        // Verify the active message was switched
        let finalLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        let finalFirstLayer = finalLayers.first
        XCTAssertEqual(finalFirstLayer?.activeMessageId, newActiveId, "Active message should be switched")
    }
    
    func testSwitchActiveMessageWithInvalidSessionId() async {
        // Given
        let invalidSessionId = UUID()
        let messageId1 = UUID()
        let messageId2 = UUID()
        
        // When & Then
        do {
            _ = try await useCase.switchActiveMessage(
                sessionId: invalidSessionId,
                fromMessageId: messageId1,
                newActiveMessageId: messageId2
            )
            XCTFail("Should throw error for invalid session ID")
        } catch let error as ChatError {
            if case .sessionNotFound = error {
                // Expected error case
            } else {
                XCTFail("Should throw sessionNotFound error, got \(error)")
            }
        } catch {
            XCTFail("Should throw ChatError, got \(error)")
        }
    }
    
    func testSwitchActiveMessageWithInvalidMessageId() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        let invalidMessageId = UUID()
        let validMessageId = UUID()
        
        // When & Then
        do {
            _ = try await useCase.switchActiveMessage(
                sessionId: testSessionId,
                fromMessageId: invalidMessageId,
                newActiveMessageId: validMessageId
            )
            XCTFail("Should throw error for invalid message ID")
        } catch let error as ChatError {
            if case .invalidMessageNavigation = error {
                // Expected error case
            } else {
                XCTFail("Should throw invalidMessageNavigation error, got \(error)")
            }
        } catch {
            XCTFail("Should throw ChatError, got \(error)")
        }
    }
    
    // MARK: - Message History Tests
    
    func testGetActivePathMessage() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        
        // When
        let history = useCase.getActivePathMessage(for: testSessionId)
        
        // Then
        XCTAssertFalse(history.isEmpty, "History should not be empty")
        
        // Verify messages are ordered by depth
        for i in 1..<history.count {
            XCTAssertLessThanOrEqual(history[i-1].depth, history[i].depth, 
                                   "Messages should be ordered by depth")
        }
    }
    
    func testGetActivePathId() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        
        // When
        let activePath = useCase.getActivePathId(for: testSessionId)
        
        // Then
        XCTAssertFalse(activePath.isEmpty, "Active path should not be empty")
        
        // Verify path corresponds to actual messages
        let history = useCase.getActivePathMessage(for: testSessionId)
        XCTAssertEqual(activePath.count, history.count, 
                      "Active path count should match history count")
        
        for (index, messageId) in activePath.enumerated() {
            XCTAssertEqual(messageId, history[index].id, 
                          "Path message ID should match history message ID at index \(index)")
        }
    }
    
    // MARK: - Cache Management Tests
    
    func testAddMessageAndUpdateTree() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        let initialCount = initialLayers.flatMap { $0.siblingMessages }.count

        let newMessage = Message(
            id: UUID(),
            sessionId: testSessionId,
            parentId: userFollowupMessageId,
            role: .assistant,
            content: [ContentBlock.text("Unit testing is a type of software testing where individual units or components of a software are tested. The purpose of unit testing is to validate that each unit of the software performs as expected.")],
            depth: 3,
            llmInstanceId: UUID()
        )
        
        // When
        await useCase.addMessageAndUpdateTree(sessionId: testSessionId, message: newMessage)
        
        // Then
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        let updatedCount = updatedLayers.flatMap { $0.siblingMessages }.count
        XCTAssertEqual(updatedCount, initialCount + 1, "Should have one more message after adding")
    }
    
    func testClearCache() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: testSessionId)
        let layers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        XCTAssertFalse(layers.isEmpty, "Should have layers before clearing cache")
        
        // When
        useCase.clearCache(for: testSessionId)
        
        // Then
        let clearedLayers = await useCase.getMessageLayersForDisplay(sessionId: testSessionId)
        XCTAssertTrue(clearedLayers.isEmpty, "Should have no layers after clearing cache")
    }
    
    // MARK: - Edge Cases Tests
    
    func testGetMessageLayersForNonExistentSession() async {
        // Given
        let nonExistentSessionId = UUID()
        
        // When & Then
        do {
            try await useCase.initializeTreeFromRepository(sessionId: nonExistentSessionId)
            XCTFail("Should throw error for invalid session ID")
        } catch {
            // Expected to throw error
            print("✅ Correctly threw error for invalid session ID: \(error)")
        }
    }
    
    func testGetActivePathMessageForNonExistentSession() {
        // Given
        let nonExistentSessionId = UUID()
        
        // When
        let history = useCase.getActivePathMessage(for: nonExistentSessionId)
        
        // Then
        XCTAssertTrue(history.isEmpty, "Should return empty array for non-existent session")
    }
}
