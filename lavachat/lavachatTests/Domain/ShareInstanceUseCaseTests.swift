import XCTest
@testable import lavachat

final class ShareInstanceUseCaseTests: XCTestCase {
    
    var mockShareService: MockShareService!
    var shareInstanceUseCase: ShareInstanceUseCase!
    var testInstance: LLMInstance!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        mockShareService = MockShareService()
        shareInstanceUseCase = ShareInstanceUseCase(shareService: mockShareService)
        
        // Create a test LLM instance
        testInstance = LLMInstance(
            id: UUID(),
            modelId: UUID(),
            name: "Test Instance",
            customLogoData: nil,
            systemPrompt: "Test system prompt",
            defaultParameters: [:],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
        )
    }
    
    override func tearDownWithError() throws {
        mockShareService = nil
        shareInstanceUseCase = nil
        testInstance = nil
        try super.tearDownWithError()
    }
    
    // MARK: - execute() Tests
    
    func testExecuteWithFileFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .file)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareInstanceUseCase.execute(testInstance, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .file)
            XCTAssertEqual(info.contentType, .llmInstance)
            XCTAssertNotNil(info.fileURL)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteWithICloudFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .icloud)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareInstanceUseCase.execute(testInstance, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .icloud)
            XCTAssertEqual(info.contentType, .llmInstance)
            XCTAssertNotNil(info.icloudURL)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteWithQRCodeFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .qrCode)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareInstanceUseCase.execute(testInstance, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .qrCode)
            XCTAssertEqual(info.contentType, .llmInstance)
            XCTAssertNotNil(info.icloudURL)
            XCTAssertNotNil(info.qrCodeImage)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteFailure() async throws {
        // Given
        let configuration = ShareConfiguration(format: .file)
        mockShareService.shouldSucceed = false
        
        // When
        let result = await shareInstanceUseCase.execute(testInstance, configuration: configuration)
        
        // Then
        switch result {
        case .success:
            XCTFail("Expected failure but got success")
        case .failure(let error):
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - shareAsFile() Tests
    
    func testShareAsFileSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let fileName = "test_instance.lavachat"
        
        // When
        let fileURL = try await shareInstanceUseCase.shareAsFile(testInstance, fileName: fileName)
        
        // Then
        XCTAssertEqual(fileURL, mockShareService.mockFileURL)
    }
    
    func testShareAsFileFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        
        // When & Then
        do {
            _ = try await shareInstanceUseCase.shareAsFile(testInstance, fileName: nil)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - shareViaICloud() Tests
    
    func testShareViaICloudSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let permissions = ICloudSharePermissions()
        
        // When
        let icloudURL = try await shareInstanceUseCase.shareViaICloud(testInstance, permissions: permissions)
        
        // Then
        XCTAssertEqual(icloudURL, mockShareService.mockICloudURL)
    }
    
    func testShareViaICloudFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        let permissions = ICloudSharePermissions()
        
        // When & Then
        do {
            _ = try await shareInstanceUseCase.shareViaICloud(testInstance, permissions: permissions)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - generateQRCode() Tests
    
    func testGenerateQRCodeSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let size = CGSize(width: 200, height: 200)
        
        // When
        let qrCodeData = try await shareInstanceUseCase.generateQRCode(for: testInstance, size: size)
        
        // Then
        XCTAssertEqual(qrCodeData, mockShareService.mockQRCodeData)
    }
    
    func testGenerateQRCodeFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        let size = CGSize(width: 200, height: 200)
        
        // When & Then
        do {
            _ = try await shareInstanceUseCase.generateQRCode(for: testInstance, size: size)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
}
