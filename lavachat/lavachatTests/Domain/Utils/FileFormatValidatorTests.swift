import XCTest
@testable import lavachat

final class FileFormatValidatorTests: XCTestCase {
    
    // MARK: - Image Format Validation Tests
    
    func testValidImageFormats() {
        // Google API
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/png", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/jpeg", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/webp", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/heic", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/heif", for: .google))
        
        // OpenAI Compatible API
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/png", for: .openaiCompatible))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/jpeg", for: .openaiCompatible))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/webp", for: .openaiCompatible))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/gif", for: .openaiCompatible))
        
        // Anthropic API
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/jpeg", for: .anthropic))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/png", for: .anthropic))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/gif", for: .anthropic))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/webp", for: .anthropic))
    }
    
    func testInvalidImageFormats() {
        // Test unsupported formats
        XCTAssertFalse(FileFormatValidator.isValidImageFormat("image/bmp", for: .google))
        XCTAssertFalse(FileFormatValidator.isValidImageFormat("image/tiff", for: .openaiCompatible))
        XCTAssertFalse(FileFormatValidator.isValidImageFormat("image/svg+xml", for: .anthropic))
        
        // Test HEIC/HEIF not supported by OpenAI and Anthropic
        XCTAssertFalse(FileFormatValidator.isValidImageFormat("image/heic", for: .openaiCompatible))
        XCTAssertFalse(FileFormatValidator.isValidImageFormat("image/heif", for: .anthropic))
        
        // Test GIF not supported by Google
        XCTAssertFalse(FileFormatValidator.isValidImageFormat("image/gif", for: .google))
    }
    
    func testCaseInsensitiveImageFormats() {
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("IMAGE/JPEG", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("Image/Png", for: .openaiCompatible))
        XCTAssertTrue(FileFormatValidator.isValidImageFormat("image/WEBP", for: .anthropic))
    }
    
    // MARK: - Document Format Validation Tests
    
    func testValidDocumentFormats() {
        // Google API (most comprehensive)
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("application/pdf", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("application/msword", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("application/vnd.openxmlformats-officedocument.wordprocessingml.document", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("text/plain", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("text/markdown", for: .google))
        
        // OpenAI Compatible API (PDF only)
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("application/pdf", for: .openaiCompatible))
        XCTAssertFalse(FileFormatValidator.isValidDocumentFormat("application/msword", for: .openaiCompatible))
        
        // Anthropic API (PDF only)
        XCTAssertTrue(FileFormatValidator.isValidDocumentFormat("application/pdf", for: .anthropic))
        XCTAssertFalse(FileFormatValidator.isValidDocumentFormat("text/plain", for: .anthropic))
    }
    
    func testInvalidDocumentFormats() {
        XCTAssertFalse(FileFormatValidator.isValidDocumentFormat("application/zip", for: .google))
        XCTAssertFalse(FileFormatValidator.isValidDocumentFormat("video/mp4", for: .openaiCompatible))
        XCTAssertFalse(FileFormatValidator.isValidDocumentFormat("audio/mpeg", for: .anthropic))
    }
    
    // MARK: - File Type Detection Tests
    
    func testFileTypeDetection() {
        XCTAssertEqual(FileFormatValidator.fileType(from: "image/jpeg"), .image)
        XCTAssertEqual(FileFormatValidator.fileType(from: "image/png"), .image)
        XCTAssertEqual(FileFormatValidator.fileType(from: "application/pdf"), .document)
        XCTAssertEqual(FileFormatValidator.fileType(from: "text/plain"), .document)
        XCTAssertEqual(FileFormatValidator.fileType(from: "audio/mpeg"), .other)
        XCTAssertEqual(FileFormatValidator.fileType(from: "video/mp4"), .other)
    }
    
    func testDocumentMimeTypeDetection() {
        XCTAssertTrue(FileFormatValidator.isDocumentMimeType("application/pdf"))
        XCTAssertTrue(FileFormatValidator.isDocumentMimeType("text/plain"))
        XCTAssertTrue(FileFormatValidator.isDocumentMimeType("application/msword"))
        XCTAssertFalse(FileFormatValidator.isDocumentMimeType("image/jpeg"))
        XCTAssertFalse(FileFormatValidator.isDocumentMimeType("audio/mpeg"))
    }
    
    // MARK: - MIME Type Validation Tests
    
    func testValidMimeTypes() {
        XCTAssertTrue(FileFormatValidator.isValidMimeType("image/jpeg"))
        XCTAssertTrue(FileFormatValidator.isValidMimeType("application/pdf"))
        XCTAssertTrue(FileFormatValidator.isValidMimeType("text/plain"))
        XCTAssertTrue(FileFormatValidator.isValidMimeType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
    }
    
    func testInvalidMimeTypes() {
        XCTAssertFalse(FileFormatValidator.isValidMimeType(""))
        XCTAssertFalse(FileFormatValidator.isValidMimeType("invalid"))
        XCTAssertFalse(FileFormatValidator.isValidMimeType("image"))
        XCTAssertFalse(FileFormatValidator.isValidMimeType("image/"))
        XCTAssertFalse(FileFormatValidator.isValidMimeType("/jpeg"))
        XCTAssertFalse(FileFormatValidator.isValidMimeType("image jpeg"))
    }
    
    // MARK: - File Extension Tests
    
    func testFileExtensionMapping() {
        XCTAssertEqual(FileFormatValidator.fileExtension(for: "image/jpeg"), "jpg")
        XCTAssertEqual(FileFormatValidator.fileExtension(for: "image/png"), "png")
        XCTAssertEqual(FileFormatValidator.fileExtension(for: "application/pdf"), "pdf")
        XCTAssertEqual(FileFormatValidator.fileExtension(for: "text/plain"), "txt")
        XCTAssertNil(FileFormatValidator.fileExtension(for: "unknown/type"))
    }
    
    func testMimeTypeFromExtension() {
        XCTAssertEqual(FileFormatValidator.mimeType(for: "jpg"), "image/jpeg")
        XCTAssertEqual(FileFormatValidator.mimeType(for: "jpeg"), "image/jpeg")
        XCTAssertEqual(FileFormatValidator.mimeType(for: "png"), "image/png")
        XCTAssertEqual(FileFormatValidator.mimeType(for: "pdf"), "application/pdf")
        XCTAssertEqual(FileFormatValidator.mimeType(for: "txt"), "text/plain")
        XCTAssertNil(FileFormatValidator.mimeType(for: "unknown"))
    }
    
    func testCaseInsensitiveExtensions() {
        XCTAssertEqual(FileFormatValidator.mimeType(for: "JPG"), "image/jpeg")
        XCTAssertEqual(FileFormatValidator.mimeType(for: "PNG"), "image/png")
        XCTAssertEqual(FileFormatValidator.mimeType(for: "PDF"), "application/pdf")
    }
    
    // MARK: - Validation with Error Results Tests
    
    func testValidateFileFormatSuccess() {
        XCTAssertNil(FileFormatValidator.validateFileFormat("image/jpeg", for: .google))
        XCTAssertNil(FileFormatValidator.validateFileFormat("application/pdf", for: .openaiCompatible))
        XCTAssertNil(FileFormatValidator.validateFileFormat("image/png", for: .anthropic))
    }
    
    func testValidateFileFormatInvalidMimeType() {
        let error = FileFormatValidator.validateFileFormat("invalid-mime", for: .google)
        XCTAssertNotNil(error)
        if case .invalidMimeType(let mimeType) = error {
            XCTAssertEqual(mimeType, "invalid-mime")
        } else {
            XCTFail("Expected invalidMimeType error")
        }
    }
    
    func testValidateFileFormatUnsupportedFormat() {
        let error = FileFormatValidator.validateFileFormat("image/bmp", for: .google)
        XCTAssertNotNil(error)
        if case .unsupportedFormat(let mimeType, let apiStyle) = error {
            XCTAssertEqual(mimeType, "image/bmp")
            XCTAssertEqual(apiStyle, .google)
        } else {
            XCTFail("Expected unsupportedFormat error")
        }
    }
    
    // MARK: - Combined Format Validation Tests
    
    func testIsValidFileFormat() {
        // Should return true for both images and documents
        XCTAssertTrue(FileFormatValidator.isValidFileFormat("image/jpeg", for: .google))
        XCTAssertTrue(FileFormatValidator.isValidFileFormat("application/pdf", for: .google))
        XCTAssertFalse(FileFormatValidator.isValidFileFormat("audio/mpeg", for: .google))
        
        // Test API-specific limitations
        XCTAssertFalse(FileFormatValidator.isValidFileFormat("application/msword", for: .openaiCompatible))
        XCTAssertTrue(FileFormatValidator.isValidFileFormat("application/pdf", for: .openaiCompatible))
    }
}
