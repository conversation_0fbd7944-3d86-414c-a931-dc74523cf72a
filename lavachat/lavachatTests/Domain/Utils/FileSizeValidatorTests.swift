import XCTest
@testable import lavachat

final class FileSizeValidatorTests: XCTestCase {
    
    // MARK: - Size Limit Tests
    
    func testIsWithinSizeLimit() {
        // Google API (20MB limit)
        let googleLimit = 20 * 1024 * 1024
        XCTAssertTrue(FileSizeValidator.isWithinSizeLimit(Int64(googleLimit), for: .google))
        XCTAssertTrue(FileSizeValidator.isWithinSizeLimit(Int64(googleLimit - 1), for: .google))
        XCTAssertFalse(FileSizeValidator.isWithinSizeLimit(Int64(googleLimit + 1), for: .google))
        
        // OpenAI Compatible API (20MB limit)
        let openaiLimit = 20 * 1024 * 1024
        XCTAssertTrue(FileSizeValidator.isWithinSizeLimit(Int64(openaiLimit), for: .openaiCompatible))
        XCTAssertFalse(FileSizeValidator.isWithinSizeLimit(Int64(openaiLimit + 1), for: .openaiCompatible))
        
        // Anthropic API (5MB limit)
        let anthropicLimit = 5 * 1024 * 1024
        XCTAssertTrue(FileSizeValidator.isWithinSizeLimit(Int64(anthropicLimit), for: .anthropic))
        XCTAssertFalse(FileSizeValidator.isWithinSizeLimit(Int64(anthropicLimit + 1), for: .anthropic))
    }
    
    func testMeetsMinimumSize() {
        XCTAssertTrue(FileSizeValidator.meetsMinimumSize(1024)) // 1KB
        XCTAssertTrue(FileSizeValidator.meetsMinimumSize(2048)) // 2KB
        XCTAssertFalse(FileSizeValidator.meetsMinimumSize(1023)) // Less than 1KB
        XCTAssertFalse(FileSizeValidator.meetsMinimumSize(0))
    }
    
    // MARK: - Compression Tests
    
    func testNeedsCompression() {
        // Google API
        let googleLimit = 20 * 1024 * 1024
        XCTAssertFalse(FileSizeValidator.needsCompression(Int64(googleLimit), for: .google))
        XCTAssertTrue(FileSizeValidator.needsCompression(Int64(googleLimit + 1), for: .google))
        
        // Anthropic API (smaller limit)
        let anthropicLimit = 5 * 1024 * 1024
        XCTAssertFalse(FileSizeValidator.needsCompression(Int64(anthropicLimit), for: .anthropic))
        XCTAssertTrue(FileSizeValidator.needsCompression(Int64(anthropicLimit + 1), for: .anthropic))
    }
    
    func testShouldCompress() {
        // Test compression recommendation threshold
        let compressionThreshold = 1024 * 1024 // 1MB
        
        // Google API (threshold is min of 1MB or 10MB)
        XCTAssertFalse(FileSizeValidator.shouldCompress(Int64(compressionThreshold - 1), for: .google))
        XCTAssertTrue(FileSizeValidator.shouldCompress(Int64(compressionThreshold + 1), for: .google))
        
        // Anthropic API (threshold is min of 1MB or 2.5MB)
        let anthropicThreshold = min(compressionThreshold, 5 * 1024 * 1024 / 2)
        XCTAssertFalse(FileSizeValidator.shouldCompress(Int64(anthropicThreshold - 1), for: .anthropic))
        XCTAssertTrue(FileSizeValidator.shouldCompress(Int64(anthropicThreshold + 1), for: .anthropic))
    }
    
    // MARK: - Size Calculation Tests
    
    func testTargetCompressionSize() {
        // Should be 80% of max limit
        let googleTarget = FileSizeValidator.targetCompressionSize(for: .google)
        let expectedGoogleTarget = Int64(Double(20 * 1024 * 1024) * 0.8)
        XCTAssertEqual(googleTarget, expectedGoogleTarget)
        
        let anthropicTarget = FileSizeValidator.targetCompressionSize(for: .anthropic)
        let expectedAnthropicTarget = Int64(Double(5 * 1024 * 1024) * 0.8)
        XCTAssertEqual(anthropicTarget, expectedAnthropicTarget)
    }
    
    func testRequiredCompressionRatio() {
        let originalSize: Int64 = 10 * 1024 * 1024 // 10MB
        let targetSize = FileSizeValidator.targetCompressionSize(for: .anthropic) // 4MB (80% of 5MB)
        
        let ratio = FileSizeValidator.requiredCompressionRatio(originalSize: originalSize, for: .anthropic)
        let expectedRatio = Double(targetSize) / Double(originalSize)
        
        XCTAssertEqual(ratio, expectedRatio, accuracy: 0.001)
    }
    
    // MARK: - Human Readable Size Tests
    
    func testHumanReadableSize() {
        // Test actual output from ByteCountFormatter
        let result1000 = FileSizeValidator.humanReadableSize(1000)
        let result1MB = FileSizeValidator.humanReadableSize(1000 * 1000)
        let result1GB = FileSizeValidator.humanReadableSize(1000 * 1000 * 1000)

        // Print actual results to debug
        print("1000 bytes: '\(result1000)'")
        print("1MB: '\(result1MB)'")
        print("1GB: '\(result1GB)'")

        XCTAssertTrue(result1000.contains("KB") || result1000.contains("bytes"))
        XCTAssertTrue(result1MB.contains("MB"))
        XCTAssertTrue(result1GB.contains("GB"))

        // Test that we get reasonable results (not exact strings since formatter behavior may vary)
        // ByteCountFormatter may format 1000 bytes as "1 KB" or "1,000 bytes" depending on system settings
        XCTAssertFalse(result1000.isEmpty, "Should return non-empty string for 1000 bytes")
        XCTAssertFalse(result1MB.isEmpty, "Should return non-empty string for 1MB")
        XCTAssertFalse(result1GB.isEmpty, "Should return non-empty string for 1GB")

        // Test that larger values produce different results
        XCTAssertNotEqual(result1000, result1MB, "1000 bytes and 1MB should format differently")
        XCTAssertNotEqual(result1MB, result1GB, "1MB and 1GB should format differently")
    }
    
    func testMegabytes() {
        XCTAssertEqual(FileSizeValidator.megabytes(1024 * 1024), "1.0 MB")
        XCTAssertEqual(FileSizeValidator.megabytes(1536 * 1024), "1.5 MB")
        XCTAssertEqual(FileSizeValidator.megabytes(2 * 1024 * 1024), "2.0 MB")
    }
    
    // MARK: - Validation with Error Results Tests
    
    func testValidateFileSizeSuccess() {
        let validSize: Int64 = 5 * 1024 * 1024 // 5MB
        XCTAssertNil(FileSizeValidator.validateFileSize(validSize, for: .google))
        XCTAssertNil(FileSizeValidator.validateFileSize(validSize, for: .anthropic))
    }
    
    func testValidateFileSizeTooSmall() {
        let tooSmallSize: Int64 = 512 // 512 bytes
        let error = FileSizeValidator.validateFileSize(tooSmallSize, for: .google)
        
        XCTAssertNotNil(error)
        if case .fileTooSmall(let actualSize, let minSize) = error {
            XCTAssertEqual(actualSize, tooSmallSize)
            XCTAssertEqual(minSize, FileSizeValidator.minimumFileSizeBytes)
        } else {
            XCTFail("Expected fileTooSmall error")
        }
    }
    
    func testValidateFileSizeTooLarge() {
        let tooLargeSize: Int64 = 25 * 1024 * 1024 // 25MB
        let error = FileSizeValidator.validateFileSize(tooLargeSize, for: .google)
        
        XCTAssertNotNil(error)
        if case .fileTooLarge(let actualSize, let maxSize) = error {
            XCTAssertEqual(actualSize, tooLargeSize)
            XCTAssertEqual(maxSize, APIStyle.google.maxFileSizeBytes)
        } else {
            XCTFail("Expected fileTooLarge error")
        }
    }
    
    // MARK: - Validation with Recommendations Tests
    
    func testValidateWithRecommendationsValid() {
        let validSize: Int64 = 2 * 1024 * 1024 // 2MB
        let result = FileSizeValidator.validateSizeWithRecommendations(validSize, for: .google)
        
        XCTAssertTrue(result.isValid)
        XCTAssertNil(result.error)
        XCTAssertFalse(result.needsCompression)
        XCTAssertTrue(result.shouldCompress) // 2MB > 1MB threshold
        XCTAssertNotNil(result.targetSize)
    }
    
    func testValidateWithRecommendationsInvalid() {
        let invalidSize: Int64 = 25 * 1024 * 1024 // 25MB (too large for Google)
        let result = FileSizeValidator.validateSizeWithRecommendations(invalidSize, for: .google)
        
        XCTAssertFalse(result.isValid)
        XCTAssertNotNil(result.error)
        XCTAssertTrue(result.needsCompression)
        XCTAssertFalse(result.shouldCompress)
        XCTAssertNil(result.targetSize)
    }
    
    func testValidateWithRecommendationsSmallFile() {
        let smallSize: Int64 = 512 * 1024 // 512KB
        let result = FileSizeValidator.validateSizeWithRecommendations(smallSize, for: .google)
        
        XCTAssertTrue(result.isValid)
        XCTAssertNil(result.error)
        XCTAssertFalse(result.needsCompression)
        XCTAssertFalse(result.shouldCompress) // Below 1MB threshold
        XCTAssertNil(result.targetSize)
    }
    
    // MARK: - SizeValidationResult Tests
    
    func testSizeValidationResultRecommendation() {
        // Test with error
        let errorResult = SizeValidationResult(
            isValid: false,
            error: .fileTooLarge(actualSize: 25 * 1024 * 1024, maxSize: 20 * 1024 * 1024),
            needsCompression: true,
            shouldCompress: false,
            targetSize: nil
        )
        XCTAssertNotNil(errorResult.recommendation)
        XCTAssertTrue(errorResult.recommendation!.contains("smaller file"))
        
        // Test with compression recommendation
        let compressionResult = SizeValidationResult(
            isValid: true,
            error: nil,
            needsCompression: false,
            shouldCompress: true,
            targetSize: 4 * 1024 * 1024
        )
        XCTAssertNotNil(compressionResult.recommendation)
        XCTAssertTrue(compressionResult.recommendation!.contains("compressing"))
        
        // Test without recommendations
        let validResult = SizeValidationResult(
            isValid: true,
            error: nil,
            needsCompression: false,
            shouldCompress: false,
            targetSize: nil
        )
        XCTAssertNil(validResult.recommendation)
    }
    
    // MARK: - Edge Cases Tests
    
    func testZeroSize() {
        XCTAssertFalse(FileSizeValidator.meetsMinimumSize(0))
        let error = FileSizeValidator.validateFileSize(0, for: .google)
        XCTAssertNotNil(error)
    }
    
    func testExactLimits() {
        // Test exact limit boundaries
        let googleLimit = APIStyle.google.maxFileSizeBytes
        XCTAssertTrue(FileSizeValidator.isWithinSizeLimit(googleLimit, for: .google))
        XCTAssertFalse(FileSizeValidator.isWithinSizeLimit(googleLimit + 1, for: .google))
        
        let minSize = FileSizeValidator.minimumFileSizeBytes
        XCTAssertTrue(FileSizeValidator.meetsMinimumSize(minSize))
        XCTAssertFalse(FileSizeValidator.meetsMinimumSize(minSize - 1))
    }
}
