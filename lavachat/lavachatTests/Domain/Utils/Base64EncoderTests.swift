import XCTest
@testable import lavachat

final class Base64EncoderTests: XCTestCase {
    
    // MARK: - Test Data
    
    private let testString = "Hello, <PERSON>!"
    private let testData = "Hello, <PERSON>!".data(using: .utf8)!
    private let expectedBase64 = "SGVsbG8sIFdvcmxkIQ=="
    
    // MARK: - Encoding Tests
    
    func testEncodeData() throws {
        let result = try Base64Encoder.encode(testData)
        XCTAssertEqual(result, expectedBase64)
    }
    
    func testEncodeEmptyData() {
        let emptyData = Data()
        XCTAssertThrowsError(try Base64Encoder.encode(emptyData)) { error in
            if case FileProcessingError.encodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Data is empty")
            } else {
                XCTFail("Expected encodingFailed error")
            }
        }
    }
    
    func testEncodeAsDataURL() throws {
        let mimeType = "text/plain"
        let result = try Base64Encoder.encodeAsDataURL(testData, mimeType: mimeType)
        let expected = "data:text/plain;base64,\(expectedBase64)"
        XCTAssertEqual(result, expected)
    }
    
    func testEncodeAsDataURLWithImageMimeType() throws {
        let imageData = Data([0xFF, 0xD8, 0xFF, 0xE0]) // JPEG header
        let mimeType = "image/jpeg"
        let result = try Base64Encoder.encodeAsDataURL(imageData, mimeType: mimeType)
        
        XCTAssertTrue(result.hasPrefix("data:image/jpeg;base64,"))
        let base64Part = String(result.dropFirst("data:image/jpeg;base64,".count))
        XCTAssertEqual(try Base64Encoder.decode(base64Part), imageData)
    }
    
    func testEncodeChunked() {
        let largeData = Data(repeating: 0x41, count: 3000) // 3KB of 'A's
        let chunkSize = 1000

        do {
            let result = try Base64Encoder.encodeChunked(largeData, chunkSize: chunkSize)
            let decodedData = try Base64Encoder.decode(result)

            XCTAssertEqual(decodedData.count, largeData.count, "Decoded data size should match original")
            XCTAssertEqual(decodedData, largeData, "Decoded data should match original data")
        } catch {
            XCTFail("Test failed with error: \(error)")
        }
    }
    
    func testEncodeChunkedEmptyData() {
        let emptyData = Data()
        XCTAssertThrowsError(try Base64Encoder.encodeChunked(emptyData)) { error in
            if case FileProcessingError.encodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Data is empty")
            } else {
                XCTFail("Expected encodingFailed error")
            }
        }
    }
    
    // MARK: - Decoding Tests
    
    func testDecodeBase64() throws {
        let result = try Base64Encoder.decode(expectedBase64)
        XCTAssertEqual(result, testData)
    }
    
    func testDecodeEmptyString() {
        XCTAssertThrowsError(try Base64Encoder.decode("")) { error in
            if case FileProcessingError.decodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Base64 string is empty")
            } else {
                XCTFail("Expected decodingFailed error")
            }
        }
    }
    
    func testDecodeInvalidBase64() {
        let invalidBase64 = "This is not base64!"
        XCTAssertThrowsError(try Base64Encoder.decode(invalidBase64)) { error in
            if case FileProcessingError.decodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Invalid base64 format")
            } else {
                XCTFail("Expected decodingFailed error")
            }
        }
    }
    
    func testDecodeBase64WithWhitespace() throws {
        let base64WithWhitespace = "SGVs\nbG8s\r\nIFdv\tcmxkIQ=="
        let result = try Base64Encoder.decode(base64WithWhitespace)
        XCTAssertEqual(result, testData)
    }
    
    func testDecodeDataURL() throws {
        let dataURL = "data:text/plain;base64,\(expectedBase64)"
        let result = try Base64Encoder.decodeDataURL(dataURL)
        
        XCTAssertEqual(result.data, testData)
        XCTAssertEqual(result.mimeType, "text/plain")
    }
    
    func testDecodeDataURLInvalidFormat() {
        let invalidDataURL = "not-a-data-url"
        XCTAssertThrowsError(try Base64Encoder.decodeDataURL(invalidDataURL)) { error in
            if case FileProcessingError.decodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Not a valid data URL")
            } else {
                XCTFail("Expected decodingFailed error")
            }
        }
    }
    
    func testDecodeDataURLMissingComma() {
        let invalidDataURL = "data:text/plain;base64"
        XCTAssertThrowsError(try Base64Encoder.decodeDataURL(invalidDataURL)) { error in
            if case FileProcessingError.decodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Invalid data URL format")
            } else {
                XCTFail("Expected decodingFailed error")
            }
        }
    }
    
    func testDecodeDataURLInvalidHeader() {
        let invalidDataURL = "data:text/plain,\(expectedBase64)" // Missing ;base64
        XCTAssertThrowsError(try Base64Encoder.decodeDataURL(invalidDataURL)) { error in
            if case FileProcessingError.decodingFailed(let reason) = error {
                XCTAssertEqual(reason, "Invalid data URL header")
            } else {
                XCTFail("Expected decodingFailed error")
            }
        }
    }
    
    func testDecodeDataURLInvalidMimeType() {
        let invalidDataURL = "data:invalid-mime-type;base64,\(expectedBase64)"
        XCTAssertThrowsError(try Base64Encoder.decodeDataURL(invalidDataURL)) { error in
            if case FileProcessingError.invalidMimeType(let mimeType) = error {
                XCTAssertEqual(mimeType, "invalid-mime-type")
            } else {
                XCTFail("Expected invalidMimeType error")
            }
        }
    }
    
    // MARK: - Validation Tests
    
    func testIsValidBase64() {
        XCTAssertTrue(Base64Encoder.isValidBase64(expectedBase64))
        XCTAssertTrue(Base64Encoder.isValidBase64("SGVsbG8=")) // With padding
        XCTAssertFalse(Base64Encoder.isValidBase64(""))
        XCTAssertFalse(Base64Encoder.isValidBase64("This is not base64!"))
        XCTAssertFalse(Base64Encoder.isValidBase64("SGVsbG8@")) // Invalid character
    }
    
    func testIsValidDataURL() {
        let validDataURL = "data:text/plain;base64,\(expectedBase64)"
        XCTAssertTrue(Base64Encoder.isValidDataURL(validDataURL))
        
        let invalidDataURL = "not-a-data-url"
        XCTAssertFalse(Base64Encoder.isValidDataURL(invalidDataURL))
        
        let invalidMimeDataURL = "data:invalid-mime;base64,\(expectedBase64)"
        XCTAssertFalse(Base64Encoder.isValidDataURL(invalidMimeDataURL))
    }
    
    // MARK: - Size Estimation Tests
    
    func testEstimateBase64Size() {
        let dataSize: Int64 = 1000
        let estimatedSize = Base64Encoder.estimateBase64Size(for: dataSize)
        
        // Base64 increases size by ~33% (4/3 ratio) plus padding
        let expectedSize = Int64(ceil(Double(dataSize) * 4.0 / 3.0)) + 4
        XCTAssertEqual(estimatedSize, expectedSize)
    }
    
    func testEstimateOriginalSize() {
        let base64Length = 1000
        let estimatedSize = Base64Encoder.estimateOriginalSize(from: base64Length)
        
        // Reverse calculation
        let expectedSize = Int64(Double(base64Length) * 3.0 / 4.0)
        XCTAssertEqual(estimatedSize, expectedSize)
    }
    
    func testSizeEstimationRoundTrip() {
        let originalSize: Int64 = 1500
        let estimatedBase64Size = Base64Encoder.estimateBase64Size(for: originalSize)
        let estimatedOriginalSize = Base64Encoder.estimateOriginalSize(from: Int(estimatedBase64Size))
        
        // Should be approximately equal (within padding tolerance)
        XCTAssertLessThanOrEqual(abs(estimatedOriginalSize - originalSize), 4)
    }
    
    // MARK: - Utility Methods Tests
    
    func testCreateImageInfo() throws {
        let imageData = Data([0xFF, 0xD8, 0xFF, 0xE0]) // JPEG header
        let mimeType = "image/jpeg"
        let caption = "Test image"
        
        let imageInfo = try Base64Encoder.createImageInfo(
            from: imageData,
            mimeType: mimeType,
            caption: caption
        )
        
        XCTAssertEqual(imageInfo.data, imageData)
        XCTAssertEqual(imageInfo.mimeType, mimeType)
        XCTAssertEqual(imageInfo.caption, caption)
        XCTAssertEqual(imageInfo.fileSizeBytes, Int64(imageData.count))
        XCTAssertNotNil(imageInfo.base64Data)
        
        // Verify base64 data can be decoded back to original
        let decodedData = try Base64Encoder.decode(imageInfo.base64Data!)
        XCTAssertEqual(decodedData, imageData)
    }
    
    func testCreateFileInfo() throws {
        let fileData = Data("PDF content".utf8)
        let fileName = "document.pdf"
        let mimeType = "application/pdf"
        
        let fileInfo = try Base64Encoder.createFileInfo(
            from: fileData,
            fileName: fileName,
            mimeType: mimeType
        )
        
        XCTAssertEqual(fileInfo.fileName, fileName)
        XCTAssertEqual(fileInfo.mimeType, mimeType)
        XCTAssertEqual(fileInfo.fileSizeBytes, Int64(fileData.count))
        XCTAssertEqual(fileInfo.fileType, .document)
        
        // Verify base64 data can be decoded back to original
        let decodedData = try Base64Encoder.decode(fileInfo.base64Data)
        XCTAssertEqual(decodedData, fileData)
    }
    
    // MARK: - Performance Tests
    
    func testEncodingPerformance() {
        let largeData = Data(repeating: 0x41, count: 1024 * 1024) // 1MB
        
        measure {
            _ = try? Base64Encoder.encode(largeData)
        }
    }
    
    func testDecodingPerformance() throws {
        let largeData = Data(repeating: 0x41, count: 1024 * 1024) // 1MB
        let base64String = try Base64Encoder.encode(largeData)
        
        measure {
            _ = try? Base64Encoder.decode(base64String)
        }
    }
}
