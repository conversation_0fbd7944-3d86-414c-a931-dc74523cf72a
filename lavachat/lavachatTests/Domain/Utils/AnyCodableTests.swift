import XCTest
@testable import lavachat

final class AnyCodableTests: XCTestCase {
    
    func testAnyCodableFromStringParsing() throws {
        // Test various string parsing scenarios
        let testCases: [(String, Any)] = [
            ("true", true),
            ("false", false),
            ("123", Int64(123)),
            ("45.67", 45.67),
            ("hello", "hello"),
            ("", ""),
            ("null", ()),
            ("3.14159", 3.14159)
        ]
        
        for (input, expected) in testCases {
            let result = AnyCodable.fromString(input)
            print("result: \(result)")
            switch expected {
            case let bool as Bool:
                XCTAssertEqual(result.boolValue, bool, "Failed for input: \(input)")
            case let int as Int64:
                XCTAssertEqual(result.value as? Int64, int, "Failed for input: \(input)")
            case let double as Double:
                XCTAssertEqual(result.doubleValue ?? 0, double, accuracy: 0.001, "Failed for input: \(input)")
            case let string as String:
                XCTAssertEqual(result.stringValue, string, "Failed for input: \(input)")
            case is Void:
                XCTAssertTrue(result.isNil, "Failed for input: \(input)")
            default:
                XCTFail("Unexpected type for input: \(input)")
            }
        }
    }
    
    func testAnyCodableFromStringDictionary() throws {
        // Given
        let stringDict: [String: String] = [
            "temperature": "0.7",
            "max_tokens": "2048",
            "reasoning_effort": "high",
            "enable_thinking": "true",
            "stop_sequences": "null"
        ]
        
        // When
        let anyCodableDict = AnyCodable.fromStringDictionary(stringDict)

        print("anyCodableDict: \(anyCodableDict)")
        
        // Then
        XCTAssertEqual(anyCodableDict["temperature"]?.doubleValue, 0.7)
        XCTAssertEqual(anyCodableDict["max_tokens"]?.value as? Int64, 2048)
        XCTAssertEqual(anyCodableDict["reasoning_effort"]?.stringValue, "high")
        XCTAssertEqual(anyCodableDict["enable_thinking"]?.boolValue, true)
        XCTAssertTrue(anyCodableDict["stop_sequences"]?.isNil == true)
    }
    
    func testAnyCodableJSONObjectParsing() throws {
        // Test JSON object parsing
        let testCases: [(String, [String: Any])] = [
            ("{\"type\": \"json_object\"}", ["type": "json_object"]),
            ("{\"key\": \"value\",}", ["key": "value"]),
            ("{\"temperature\": 0.8, \"max_tokens\": 1024}", ["temperature": 0.8, "max_tokens": 1024]),
            ("{\"nested\": {\"key\": \"value\"}}", ["nested": ["key": "value"]]),
            ("{\"array_field\": [1, 2, 3]}", ["array_field": [1, 2, 3]]),
            ("{\"mixed\": {\"string\": \"test\", \"number\": 42, \"bool\": true}}", 
             ["mixed": ["string": "test", "number": 42, "bool": true]])
        ]
        
        for (input, expected) in testCases {
            let result = AnyCodable.fromString(input)
            let resultDict = result.dictionaryValue
            
            XCTAssertNotNil(resultDict, "Failed to parse JSON object: \(input)")
            
            // Compare keys
            let expectedDict = expected
            XCTAssertEqual(resultDict?.keys.sorted(), expectedDict.keys.sorted(), 
                          "Keys don't match for input: \(input)")
            
            print("JSON Object Test - Input: \(input)")
            print("  Result: \(resultDict ?? [:])")
            print("  Expected: \(expectedDict)")
        }
    }
    
    func testAnyCodableJSONArrayParsing() throws {
        // Test JSON array parsing
        let testCases: [(String, [Any])] = [
            ("[\"item1\", \"item2\"]", ["item1", "item2"]),
            ("[1, 2, 3]", [1, 2, 3]),
            ("[true, false, null]", [true, false, NSNull()]),
            ("[{\"key\": \"value\"}, {\"key2\": \"value2\"}]",
             [["key": "value"], ["key2": "value2"]]),
            ("[]", [])
        ]
        
        for (input, expected) in testCases {
            let result = AnyCodable.fromString(input)
            let resultArray = result.arrayValue
            
            XCTAssertNotNil(resultArray, "Failed to parse JSON array: \(input)")
            XCTAssertEqual(resultArray?.count, expected.count, 
                          "Array count mismatch for input: \(input)")
            
            print("JSON Array Test - Input: \(input)")
            print("  Result: \(resultArray ?? [])")
            print("  Expected: \(expected)")
        }
    }
    
    func testAnyCodablePlainStringPreservation() throws {
        // Test that plain strings remain unchanged
        let plainStrings = [
            "aaabbb",
            "simple text",
            "text with spaces",
            "text-with-dashes",
            "text_with_underscores",
            "MixedCaseText",
            "123abc",
            "abc123",
            "{not json}",
            "[not an array",
            "json without quotes{key:value}",
            ""
        ]
        
        for input in plainStrings {
            let result = AnyCodable.fromString(input)
            XCTAssertEqual(result.stringValue, input, 
                          "Plain string should be preserved: \(input)")
            print("Plain String Test - '\(input)' -> '\(result.stringValue ?? "nil")'")
        }
    }
    
    func testAnyCodableBoundaryAndEdgeCases() throws {
        // Test boundary cases and strings that look like JSON but aren't
        let edgeCases: [(String, String)] = [
            ("{}", "{}"), // Empty object should be parsed as JSON
            ("{\"}", "{\""), // Invalid JSON should remain as string
            ("{key: value}", "{key: value}"), // JSON without quotes should remain as string
            ("[item1, item2]", "[item1, item2]"), // Array without quotes should remain as string
            ("{\"key\": }", "{\"key\": }"), // Invalid JSON should remain as string
            ("{ \"type\": \"json_object\" }", "json_object"), // Valid JSON with spaces should be parsed
            ("{\"escaped\": \"quote\\\"test\"}", "quote\"test") // Test escaped quotes
        ]
        
        for (input, expectedStringOrJsonValue) in edgeCases {
            let result = AnyCodable.fromString(input)
            
            if input == "{}" {
                // Empty object should be parsed as JSON dictionary
                XCTAssertNotNil(result.dictionaryValue, "Empty object should be parsed as JSON")
                XCTAssertTrue(result.dictionaryValue?.isEmpty == true)
            } else if input == "{ \"type\": \"json_object\" }" {
                // Valid JSON with spaces should be parsed
                XCTAssertNotNil(result.dictionaryValue, "Valid JSON should be parsed")
                XCTAssertEqual(result.dictionaryValue?["type"] as? String, expectedStringOrJsonValue)
            } else if input == "{\"escaped\": \"quote\\\"test\"}" {
                // Test escaped quotes
                XCTAssertNotNil(result.dictionaryValue, "JSON with escaped quotes should be parsed")
                XCTAssertEqual(result.dictionaryValue?["escaped"] as? String, expectedStringOrJsonValue)
            } else {
                // Invalid JSON should remain as string
                XCTAssertEqual(result.stringValue, input, 
                              "Invalid JSON should remain as string: \(input)")
            }
        }
    }
    
    func testAnyCodableJSONParsingFallback() throws {
        // Test that malformed JSON falls back to string
        let malformedJSON = [
            "{\"key\": value}", // Missing quotes around value
            "{key: \"value\"}", // Missing quotes around key
            "[\"item1\", \"item2\"", // Missing closing bracket
            "{\"key\": \"value\" \"key2\": \"value2\"}", // Missing comma
            "null", // Should be parsed as null, not string
            "true", // Should be parsed as boolean, not string
            "false" // Should be parsed as boolean, not string
        ]
        
        for input in malformedJSON {
            let result = AnyCodable.fromString(input)
            
            // Special cases that should be parsed as non-string types
            if input == "null" {
                XCTAssertTrue(result.isNil, "null should be parsed as nil")
            } else if input == "true" {
                XCTAssertEqual(result.boolValue, true, "true should be parsed as boolean")
            } else if input == "false" {
                XCTAssertEqual(result.boolValue, false, "false should be parsed as boolean")
            } else {
                // Malformed JSON should fallback to string
                XCTAssertEqual(result.stringValue, input, 
                              "Malformed JSON should fallback to string: \(input)")
            }
            
            print("Fallback Test - Input: '\(input)' -> Type: \(type(of: result.value))")
        }
    }
    
    func testAnyCodableJSONInRequestBuilding() throws {
        // Test integration with OpenAI request building
        let stringDict: [String: String] = [
            "temperature": "0.7",
            "max_tokens": "2048", 
            "response_format": "{\"type\": \"json_object\"}",
            "stop_sequences": "[\"\\n\", \"END\"]",
            "thinking_enabled": "true",
            "custom_param": "simple_string"
        ]
        
        let anyCodableDict = AnyCodable.fromStringDictionary(stringDict)
        
        // Verify correct parsing
        XCTAssertEqual(anyCodableDict["temperature"]?.doubleValue, 0.7)
        XCTAssertEqual(anyCodableDict["max_tokens"]?.value as? Int64, 2048)
        XCTAssertEqual(anyCodableDict["thinking_enabled"]?.boolValue, true)
        XCTAssertEqual(anyCodableDict["custom_param"]?.stringValue, "simple_string")
        
        // Verify JSON object parsing
        let responseFormat = anyCodableDict["response_format"]?.dictionaryValue
        XCTAssertNotNil(responseFormat)
        XCTAssertEqual(responseFormat?["type"] as? String, "json_object")
        
        // Verify JSON array parsing
        let stopSequences = anyCodableDict["stop_sequences"]?.arrayValue as? [String]
        XCTAssertNotNil(stopSequences)
        XCTAssertEqual(stopSequences?.count, 2)
        XCTAssertEqual(stopSequences?[0], "\n")
        XCTAssertEqual(stopSequences?[1], "END")
        
        print("Integration Test Results:")
        for (key, value) in anyCodableDict {
            print("  \(key): \(value.value) (type: \(type(of: value.value)))")
        }
    }
    
    func testAnyCodableNestedJSONParsing() throws {
        // Test nested JSON structures
        let nestedObject = "{\"user\": {\"name\": \"John\", \"age\": 30}}"
        let nestedResult = AnyCodable.fromString(nestedObject)
        XCTAssertNotNil(nestedResult.dictionaryValue)
        
        let userObject = nestedResult.dictionaryValue?["user"] as? [String: Any]
        XCTAssertNotNil(userObject)
        XCTAssertEqual(userObject?["name"] as? String, "John")
        XCTAssertEqual(userObject?["age"] as? Int, 30)
        
        // Test nested arrays
        let nestedArray = "[[1, 2], [3, 4]]"
        let arrayResult = AnyCodable.fromString(nestedArray)
        XCTAssertNotNil(arrayResult.arrayValue)
        
        let outerArray = arrayResult.arrayValue
        XCTAssertEqual(outerArray?.count, 2)
        let firstInnerArray = outerArray?[0] as? [Any]
        XCTAssertEqual(firstInnerArray?.count, 2)
        XCTAssertEqual(firstInnerArray?[0] as? Int, 1)
        XCTAssertEqual(firstInnerArray?[1] as? Int, 2)
    }
    
    func testAnyCodableIntegrationWithDefaultParameters() throws {
        // Test integration with the full parameter processing pipeline
        let complexDefaults: [String: String] = [
            "response_format": "{ \"type\": \"json_object\" }",
            "tools": "[{\"type\": \"function\", \"function\": {\"name\": \"search\"}}]",
            "temperature": "0.7",
            "reasoning_effort": "medium"
        ]
        
        let result = AnyCodable.fromStringDictionary(complexDefaults)
        
        // Verify JSON object parsing
        let responseFormat = result["response_format"]?.dictionaryValue
        XCTAssertNotNil(responseFormat)
        XCTAssertEqual(responseFormat?["type"] as? String, "json_object")
        
        // Verify JSON array parsing
        let tools = result["tools"]?.arrayValue
        XCTAssertNotNil(tools)
        XCTAssertEqual(tools?.count, 1)
        
        // Verify scalar parsing
        XCTAssertEqual(result["temperature"]?.doubleValue, 0.7)
        XCTAssertEqual(result["reasoning_effort"]?.stringValue, "medium")
    }
    
    func testIntValueConversionFromVariousTypes() {
        // Test conversion from Int64 (like the case in the GoogleClient issue)
        let int64Value = AnyCodable(Int64(12288))
        XCTAssertEqual(int64Value.intValue, 12288, "Should convert Int64 to Int")
        
        // Test conversion from Double
        let doubleValue = AnyCodable(12288.0)
        XCTAssertEqual(doubleValue.intValue, 12288, "Should convert whole number Double to Int")
        
        // Test conversion from Float
        let floatValue = AnyCodable(Float(12288.0))
        XCTAssertEqual(floatValue.intValue, 12288, "Should convert whole number Float to Int")
        
        // Test conversion from String
        let stringValue = AnyCodable("12288")
        XCTAssertEqual(stringValue.intValue, 12288, "Should convert numeric String to Int")
        
        // Test various integer types
        let int8Value = AnyCodable(Int8(100))
        XCTAssertEqual(int8Value.intValue, 100, "Should convert Int8 to Int")
        
        let int16Value = AnyCodable(Int16(1000))
        XCTAssertEqual(int16Value.intValue, 1000, "Should convert Int16 to Int")
        
        let int32Value = AnyCodable(Int32(10000))
        XCTAssertEqual(int32Value.intValue, 10000, "Should convert Int32 to Int")
        
        // Test edge cases
        let fractionalDouble = AnyCodable(12288.5)
        XCTAssertNil(fractionalDouble.intValue, "Should return nil for fractional Double")
        
        let invalidString = AnyCodable("not_a_number")
        XCTAssertNil(invalidString.intValue, "Should return nil for non-numeric String")
    }
    
} 
