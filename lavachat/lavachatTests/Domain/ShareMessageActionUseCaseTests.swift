import XCTest
@testable import lavachat

final class ShareMessageActionUseCaseTests: XCTestCase {
    
    var mockShareService: MockShareService!
    var shareMessageActionUseCase: ShareMessageActionUseCase!
    var testAction: MessageAction!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        mockShareService = MockShareService()
        shareMessageActionUseCase = ShareMessageActionUseCase(shareService: mockShareService)
        
        // Create a test message action
        testAction = MessageAction(
            id: UUID(),
            name: "Test Action",
            icon: "star",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt 1", "Test prompt 2"],
            targetLLMInstanceId: UUID(),
            metadata: ["key": "value"]
        )
    }
    
    override func tearDownWithError() throws {
        mockShareService = nil
        shareMessageActionUseCase = nil
        testAction = nil
        try super.tearDownWithError()
    }
    
    // MARK: - execute() Tests
    
    func testExecuteWithFileFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .file)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareMessageActionUseCase.execute(testAction, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .file)
            XCTAssertEqual(info.contentType, .messageAction)
            XCTAssertNotNil(info.fileURL)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteWithICloudFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .icloud)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareMessageActionUseCase.execute(testAction, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .icloud)
            XCTAssertEqual(info.contentType, .messageAction)
            XCTAssertNotNil(info.icloudURL)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteWithQRCodeFormat() async throws {
        // Given
        let configuration = ShareConfiguration(format: .qrCode)
        mockShareService.shouldSucceed = true
        
        // When
        let result = await shareMessageActionUseCase.execute(testAction, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertEqual(info.format, .qrCode)
            XCTAssertEqual(info.contentType, .messageAction)
            XCTAssertNotNil(info.icloudURL)
            XCTAssertNotNil(info.qrCodeImage)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testExecuteFailure() async throws {
        // Given
        let configuration = ShareConfiguration(format: .file)
        mockShareService.shouldSucceed = false
        
        // When
        let result = await shareMessageActionUseCase.execute(testAction, configuration: configuration)
        
        // Then
        switch result {
        case .success:
            XCTFail("Expected failure but got success")
        case .failure(let error):
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - shareAsFile() Tests
    
    func testShareAsFileSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let fileName = "test_action.lavachat"
        
        // When
        let fileURL = try await shareMessageActionUseCase.shareAsFile(testAction, fileName: fileName)
        
        // Then
        XCTAssertEqual(fileURL, mockShareService.mockFileURL)
    }
    
    func testShareAsFileFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        
        // When & Then
        do {
            _ = try await shareMessageActionUseCase.shareAsFile(testAction, fileName: nil)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - shareViaICloud() Tests
    
    func testShareViaICloudSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let permissions = ICloudSharePermissions()
        
        // When
        let icloudURL = try await shareMessageActionUseCase.shareViaICloud(testAction, permissions: permissions)
        
        // Then
        XCTAssertEqual(icloudURL, mockShareService.mockICloudURL)
    }
    
    func testShareViaICloudFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        let permissions = ICloudSharePermissions()
        
        // When & Then
        do {
            _ = try await shareMessageActionUseCase.shareViaICloud(testAction, permissions: permissions)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - generateQRCode() Tests
    
    func testGenerateQRCodeSuccess() async throws {
        // Given
        mockShareService.shouldSucceed = true
        let size = CGSize(width: 200, height: 200)
        
        // When
        let qrCodeData = try await shareMessageActionUseCase.generateQRCode(for: testAction, size: size)
        
        // Then
        XCTAssertEqual(qrCodeData, mockShareService.mockQRCodeData)
    }
    
    func testGenerateQRCodeFailure() async throws {
        // Given
        mockShareService.shouldSucceed = false
        let size = CGSize(width: 200, height: 200)
        
        // When & Then
        do {
            _ = try await shareMessageActionUseCase.generateQRCode(for: testAction, size: size)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
}
