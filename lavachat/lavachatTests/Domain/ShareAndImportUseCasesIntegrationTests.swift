import XCTest
@testable import lavachat

final class ShareAndImportUseCasesIntegrationTests: XCTestCase {
    
    var mockShareService: MockShareService!
    var mockImportService: MockImportService!
    
    // Share Use Cases
    var shareInstanceUseCase: ShareInstanceUseCase!
    var shareChatSessionSettingUseCase: ShareChatSessionSettingUseCase!
    var shareMessageActionUseCase: ShareMessageActionUseCase!
    var shareChatSessionUseCase: ShareChatSessionUseCase!
    
    // Import Use Cases
    var importInstanceUseCase: ImportInstanceUseCase!
    var importChatSessionSettingUseCase: ImportChatSessionSettingUseCase!
    var importMessageActionUseCase: ImportMessageActionUseCase!
    var importChatSessionUseCase: ImportChatSessionUseCase!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Setup mock services
        mockShareService = MockShareService()
        mockImportService = MockImportService()
        
        // Setup share use cases
        shareInstanceUseCase = ShareInstanceUseCase(shareService: mockShareService)
        shareChatSessionSettingUseCase = ShareChatSessionSettingUseCase(shareService: mockShareService)
        shareMessageActionUseCase = ShareMessageActionUseCase(shareService: mockShareService)
        shareChatSessionUseCase = ShareChatSessionUseCase(shareService: mockShareService)
        
        // Setup import use cases
        importInstanceUseCase = ImportInstanceUseCase(importService: mockImportService)
        importChatSessionSettingUseCase = ImportChatSessionSettingUseCase(importService: mockImportService)
        importMessageActionUseCase = ImportMessageActionUseCase(importService: mockImportService)
        importChatSessionUseCase = ImportChatSessionUseCase(importService: mockImportService)
    }
    
    override func tearDownWithError() throws {
        mockShareService = nil
        mockImportService = nil
        shareInstanceUseCase = nil
        shareChatSessionSettingUseCase = nil
        shareMessageActionUseCase = nil
        shareChatSessionUseCase = nil
        importInstanceUseCase = nil
        importChatSessionSettingUseCase = nil
        importMessageActionUseCase = nil
        importChatSessionUseCase = nil
        try super.tearDownWithError()
    }
    
    // MARK: - Integration Tests
    
    func testAllShareUseCasesWithDifferentFormats() async throws {
        // Given
        let testInstance = createTestInstance()
        let testSetting = createTestSetting()
        let testAction = createTestAction()
        let testSession = createTestSession()
        
        mockShareService.shouldSucceed = true
        
        let formats: [ShareFormat] = [.file, .icloud, .qrCode]
        
        for format in formats {
            let configuration = ShareConfiguration(format: format)
            
            // Test Instance sharing
            let instanceResult = await shareInstanceUseCase.execute(testInstance, configuration: configuration)
            XCTAssertTrue(instanceResult.isSuccess, "Instance sharing failed for format \(format)")
            
            // Test Setting sharing
            let settingResult = await shareChatSessionSettingUseCase.execute(testSetting, configuration: configuration)
            XCTAssertTrue(settingResult.isSuccess, "Setting sharing failed for format \(format)")
            
            // Test Action sharing
            let actionResult = await shareMessageActionUseCase.execute(testAction, configuration: configuration)
            XCTAssertTrue(actionResult.isSuccess, "Action sharing failed for format \(format)")
            
            // Test Session sharing
            let sessionResult = await shareChatSessionUseCase.execute(testSession, configuration: configuration)
            XCTAssertTrue(sessionResult.isSuccess, "Session sharing failed for format \(format)")
        }
    }
    
    func testAllImportUseCasesWithDifferentSources() async throws {
        // Given
        let fileURL = URL(fileURLWithPath: "/tmp/test.lavachat")
        let icloudURL = URL(string: "https://icloud.com/share/test")!
        let qrCodeData = "https://icloud.com/share/qr-test"
        let configuration = ImportConfiguration()
        
        mockImportService.shouldSucceed = true
        
        // Test file import for all use cases
        let instanceFileResult = await importInstanceUseCase.importFromFile(fileURL, configuration: configuration)
        XCTAssertTrue(instanceFileResult.isSuccess, "Instance file import failed")
        
        let settingFileResult = await importChatSessionSettingUseCase.importFromFile(fileURL, configuration: configuration)
        XCTAssertTrue(settingFileResult.isSuccess, "Setting file import failed")
        
        let actionFileResult = await importMessageActionUseCase.importFromFile(fileURL, configuration: configuration)
        XCTAssertTrue(actionFileResult.isSuccess, "Action file import failed")
        
        let sessionFileResult = await importChatSessionUseCase.importFromFile(fileURL, configuration: configuration)
        XCTAssertTrue(sessionFileResult.isSuccess, "Session file import failed")
        
        // Test iCloud import for all use cases
        let instanceICloudResult = await importInstanceUseCase.importFromICloudShare(icloudURL, configuration: configuration)
        XCTAssertTrue(instanceICloudResult.isSuccess, "Instance iCloud import failed")
        
        let settingICloudResult = await importChatSessionSettingUseCase.importFromICloudShare(icloudURL, configuration: configuration)
        XCTAssertTrue(settingICloudResult.isSuccess, "Setting iCloud import failed")
        
        let actionICloudResult = await importMessageActionUseCase.importFromICloudShare(icloudURL, configuration: configuration)
        XCTAssertTrue(actionICloudResult.isSuccess, "Action iCloud import failed")
        
        let sessionICloudResult = await importChatSessionUseCase.importFromICloudShare(icloudURL, configuration: configuration)
        XCTAssertTrue(sessionICloudResult.isSuccess, "Session iCloud import failed")
        
        // Test QR code import for all use cases
        let instanceQRResult = await importInstanceUseCase.importFromQRCode(qrCodeData, configuration: configuration)
        XCTAssertTrue(instanceQRResult.isSuccess, "Instance QR import failed")
        
        let settingQRResult = await importChatSessionSettingUseCase.importFromQRCode(qrCodeData, configuration: configuration)
        XCTAssertTrue(settingQRResult.isSuccess, "Setting QR import failed")
        
        let actionQRResult = await importMessageActionUseCase.importFromQRCode(qrCodeData, configuration: configuration)
        XCTAssertTrue(actionQRResult.isSuccess, "Action QR import failed")
        
        let sessionQRResult = await importChatSessionUseCase.importFromQRCode(qrCodeData, configuration: configuration)
        XCTAssertTrue(sessionQRResult.isSuccess, "Session QR import failed")
    }
    
    func testErrorHandlingAcrossAllUseCases() async throws {
        // Given
        let testInstance = createTestInstance()
        let testSetting = createTestSetting()
        let testAction = createTestAction()
        let testSession = createTestSession()
        
        let fileURL = URL(fileURLWithPath: "/tmp/test.lavachat")
        let configuration = ShareConfiguration(format: .file)
        let importConfiguration = ImportConfiguration()
        
        // Configure services to fail
        mockShareService.shouldSucceed = false
        mockImportService.shouldSucceed = false
        
        // Test that all share use cases handle errors properly
        let shareResults = await withTaskGroup(of: Bool.self) { group in
            group.addTask { await self.shareInstanceUseCase.execute(testInstance, configuration: configuration).isFailure }
            group.addTask { await self.shareChatSessionSettingUseCase.execute(testSetting, configuration: configuration).isFailure }
            group.addTask { await self.shareMessageActionUseCase.execute(testAction, configuration: configuration).isFailure }
            group.addTask { await self.shareChatSessionUseCase.execute(testSession, configuration: configuration).isFailure }
            
            var results: [Bool] = []
            for await result in group {
                results.append(result)
            }
            return results
        }
        
        XCTAssertTrue(shareResults.allSatisfy { $0 }, "All share use cases should fail when service is configured to fail")
        
        // Test that all import use cases handle errors properly
        let importResults = await withTaskGroup(of: Bool.self) { group in
            group.addTask { await self.importInstanceUseCase.importFromFile(fileURL, configuration: importConfiguration).isFailure }
            group.addTask { await self.importChatSessionSettingUseCase.importFromFile(fileURL, configuration: importConfiguration).isFailure }
            group.addTask { await self.importMessageActionUseCase.importFromFile(fileURL, configuration: importConfiguration).isFailure }
            group.addTask { await self.importChatSessionUseCase.importFromFile(fileURL, configuration: importConfiguration).isFailure }
            
            var results: [Bool] = []
            for await result in group {
                results.append(result)
            }
            return results
        }
        
        XCTAssertTrue(importResults.allSatisfy { $0 }, "All import use cases should fail when service is configured to fail")
    }
    
    // MARK: - Helper Methods
    
    private func createTestInstance() -> LLMInstance {
        return LLMInstance(
            modelId: UUID(),
            name: "Test Instance",
            systemPrompt: "You are a helpful assistant",
            isUserModified: false
        )
    }
    
    private func createTestSetting() -> ChatSessionSetting {
        return ChatSessionSetting(
            id: UUID(),
            name: "Test Setting",
            isSystemDefault: false,
            createdAt: Date(),
            lastModifiedAt: Date()
        )
    }
    
    private func createTestAction() -> MessageAction {
        return MessageAction(
            name: "Test Action",
            icon: "star",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt"],
            targetLLMInstanceId: UUID()
        )
    }
    
    private func createTestSession() -> ChatSession {
        return ChatSession(
            title: "Test Session",
            activeLLMInstanceIds: [UUID()],
            usedLLMInstanceIds: [UUID()],
            settingsId: UUID(),
            userId: UUID()
        )
    }
}

// MARK: - Result Extensions for Testing

extension ShareResult {
    var isSuccess: Bool {
        switch self {
        case .success: return true
        case .failure: return false
        }
    }
    
    var isFailure: Bool {
        return !isSuccess
    }
}

extension ImportResult {
    var isSuccess: Bool {
        switch self {
        case .success: return true
        case .failure: return false
        }
    }
    
    var isFailure: Bool {
        return !isSuccess
    }
}
