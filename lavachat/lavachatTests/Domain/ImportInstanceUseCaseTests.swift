import XCTest
@testable import lavachat

final class ImportInstanceUseCaseTests: XCTestCase {
    
    var mockImportService: MockImportService!
    var importInstanceUseCase: ImportInstanceUseCase!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        mockImportService = MockImportService()
        importInstanceUseCase = ImportInstanceUseCase(importService: mockImportService)
    }
    
    override func tearDownWithError() throws {
        mockImportService = nil
        importInstanceUseCase = nil
        try super.tearDownWithError()
    }
    
    // MARK: - importFromFile() Tests
    
    func testImportFromFileSuccess() async throws {
        // Given
        let fileURL = URL(fileURLWithPath: "/tmp/test_instance.lavachat")
        let configuration = ImportConfiguration()
        mockImportService.shouldSucceed = true
        
        // When
        let result = await importInstanceUseCase.importFromFile(fileURL, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertFalse(info.importedItemIds.isEmpty)
            XCTAssertEqual(info.contentType, .llmInstance)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testImportFromFileFailure() async throws {
        // Given
        let fileURL = URL(fileURLWithPath: "/tmp/test_instance.lavachat")
        let configuration = ImportConfiguration()
        mockImportService.shouldSucceed = false
        
        // When
        let result = await importInstanceUseCase.importFromFile(fileURL, configuration: configuration)
        
        // Then
        switch result {
        case .success:
            XCTFail("Expected failure but got success")
        case .failure(let error):
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - importFromICloudShare() Tests
    
    func testImportFromICloudShareSuccess() async throws {
        // Given
        let shareURL = URL(string: "https://icloud.com/share/test")!
        let configuration = ImportConfiguration()
        mockImportService.shouldSucceed = true
        
        // When
        let result = await importInstanceUseCase.importFromICloudShare(shareURL, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertFalse(info.importedItemIds.isEmpty)
            XCTAssertEqual(info.contentType, .llmInstance)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testImportFromICloudShareFailure() async throws {
        // Given
        let shareURL = URL(string: "https://icloud.com/share/test")!
        let configuration = ImportConfiguration()
        mockImportService.shouldSucceed = false
        
        // When
        let result = await importInstanceUseCase.importFromICloudShare(shareURL, configuration: configuration)
        
        // Then
        switch result {
        case .success:
            XCTFail("Expected failure but got success")
        case .failure(let error):
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - importFromQRCode() Tests
    
    func testImportFromQRCodeSuccess() async throws {
        // Given
        let qrCodeData = "https://icloud.com/share/qr-test"
        let configuration = ImportConfiguration()
        mockImportService.shouldSucceed = true
        
        // When
        let result = await importInstanceUseCase.importFromQRCode(qrCodeData, configuration: configuration)
        
        // Then
        switch result {
        case .success(let info):
            XCTAssertFalse(info.importedItemIds.isEmpty)
            XCTAssertEqual(info.contentType, .llmInstance)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
    
    func testImportFromQRCodeFailure() async throws {
        // Given
        let qrCodeData = "https://icloud.com/share/qr-test"
        let configuration = ImportConfiguration()
        mockImportService.shouldSucceed = false
        
        // When
        let result = await importInstanceUseCase.importFromQRCode(qrCodeData, configuration: configuration)
        
        // Then
        switch result {
        case .success:
            XCTFail("Expected failure but got success")
        case .failure(let error):
            XCTAssertNotNil(error)
        }
    }
    
    // MARK: - Configuration Tests
    
    func testImportWithDifferentConfigurations() async throws {
        // Given
        let fileURL = URL(fileURLWithPath: "/tmp/test_instance.lavachat")
        mockImportService.shouldSucceed = true
        
        // Test with createMissingDependencies = false
        let config1 = ImportConfiguration(createMissingDependencies: false, sanitizeImportedData: true)
        let result1 = await importInstanceUseCase.importFromFile(fileURL, configuration: config1)
        
        switch result1 {
        case .success(let info):
            XCTAssertFalse(info.importedItemIds.isEmpty)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
        
        // Test with sanitizeImportedData = false
        let config2 = ImportConfiguration(createMissingDependencies: true, sanitizeImportedData: false)
        let result2 = await importInstanceUseCase.importFromFile(fileURL, configuration: config2)
        
        switch result2 {
        case .success(let info):
            XCTAssertFalse(info.importedItemIds.isEmpty)
        case .failure(let error):
            XCTFail("Expected success but got failure: \(error)")
        }
    }
}
