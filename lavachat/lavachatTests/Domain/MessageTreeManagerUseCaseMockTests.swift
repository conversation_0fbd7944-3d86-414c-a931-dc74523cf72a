import XCTest
@testable import lavachat

final class MessageTreeManagerUseCaseMockTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var useCase: MessageTreeManagerUseCase!
    var chatRepository: ChatRepository!
    var testSessionId: UUID!
    var testUserId: UUID!
    var mockData: MockChatPreviewData!
    
    // Complex test data properties
    var complexTestSessionId: UUID!
    var rootUserMessageId: UUID!
    var firstAssistantClaudeId: UUID!
    var firstAssistantGPT4Id: UUID!
    var firstAssistantCustomId: UUID!
    var editedUserMessageId: UUID!
    var secondAssistantClaudeId: UUID!
    var secondAssistantGPT4Id: UUID!
    var secondAssistantCustomId: UUID!
    var thirdUserMessageId: UUID!
    var thirdAssistantClaudeId: UUID!
    var thirdAssistantGPT4Id: UUID!
    var thirdAssistantCustomId: UUID!
    var regenAssistantClaudeId: UUID!
    
    // LLM Instance IDs for testing
    let claudeInstanceId = MockChatPreviewData.claudeInstanceId
    let gpt4InstanceId = MockChatPreviewData.gpt4InstanceId
    let customInstanceId = MockChatPreviewData.customInstanceId
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        let container = DIContainer(context: PersistenceController.shared.container.viewContext)
        chatRepository = container.getSharedChatRepository()
        useCase = MessageTreeManagerUseCase(chatRepository: chatRepository)
        testSessionId = UUID()
        testUserId = MockChatPreviewData.systemDefaultUserId
        mockData = MockChatPreviewData()
        
        // Initialize complex test data IDs
        setupComplexTestDataIds()

        // Setup test data using BuiltinChatInitializer pattern - wait for completion
        let expectation = XCTestExpectation(description: "Setup test data")
        Task {
            await setupTestData()
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10.0)
    }
    
    override func tearDown() {
        useCase.clearAllCaches()
        useCase = nil
        chatRepository = nil
        testSessionId = nil
        testUserId = nil
        complexTestSessionId = nil
        super.tearDown()
    }
    
    // MARK: - Setup Test Data
    
    private func setupComplexTestDataIds() {
        complexTestSessionId = UUID()
        rootUserMessageId = UUID()
        firstAssistantClaudeId = UUID()
        firstAssistantGPT4Id = UUID()
        firstAssistantCustomId = UUID()
        editedUserMessageId = UUID()
        secondAssistantClaudeId = UUID()
        secondAssistantGPT4Id = UUID()
        secondAssistantCustomId = UUID()
        thirdUserMessageId = UUID()
        thirdAssistantClaudeId = UUID()
        thirdAssistantGPT4Id = UUID()
        thirdAssistantCustomId = UUID()
        regenAssistantClaudeId = UUID()
    }
    
    private func setupTestData() async {
        // Add complex test data to repository based on MockChatPreviewData.exampleSession2
        do {
            // Create complex test chat session (based on exampleSession2)
            let complexChatSession = ChatSession(
                id: complexTestSessionId,
                title: "Complex Multi Instance Test Session",
                createdAt: Date().addingTimeInterval(-3 * 24 * 3600 - 2 * 3600),
                lastModifiedAt: Date().addingTimeInterval(-2 * 3600),
                activeMessageId: regenAssistantClaudeId, // Set to the regenerated Claude message
                activeLLMInstanceIds: [claudeInstanceId, gpt4InstanceId, customInstanceId],
                usedLLMInstanceIds: [claudeInstanceId, gpt4InstanceId, customInstanceId],
                activeContextServerIds: [],
                settingsId: MockChatPreviewData.systemDefaultChatSessionSetting.id,
                userId: testUserId,
                instanceSettings: [
                    claudeInstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false),
                    gpt4InstanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false),
                    customInstanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false)
                ]
            )
            
            // Create complex test messages (based on messagesForExampleSession2)
            let complexMessages = createComplexTestMessages()
            
            // Add session to repository
            try await chatRepository.createChatSession(complexChatSession)
            
            // Add all messages to repository
            for message in complexMessages {
                try await chatRepository.createMessage(message)
            }
            
            print("setupTestData Done - Complex test data created successfully")
            print("- Session ID: \(complexTestSessionId!)")
            print("- Message count: \(complexMessages.count)")
            print("- Active message ID: \(regenAssistantClaudeId!)")
            
        } catch {
            print("setupTestData Failed: \(error)")
            XCTFail("Failed to setup test data: \(error)")
        }
    }
    
    private func createComplexTestMessages() -> [Message] {
        var messages: [Message] = []
        
        // First round user message (root - depth 0)
        let message1 = Message(
            id: rootUserMessageId,
            sessionId: complexTestSessionId,
            parentId: nil,
            timestamp: Date().addingTimeInterval(-1000),
            role: .user,
            content: [.text("What is LavaChat?")],
            depth: 0,
            userId: testUserId,
            status: .received
        )
        messages.append(message1)
        
        // First round assistant replies (three parallel - depth 1)
        let message2_claude = Message(
            id: firstAssistantClaudeId,
            sessionId: complexTestSessionId,
            parentId: message1.id,
            timestamp: Date().addingTimeInterval(-999),
            role: .assistant,
            content: [.text("LavaChat is an AI hub designed to centralize and manage interactions with multiple large language models (LLMs).")],
            depth: 1,
            llmInstanceId: claudeInstanceId,
            promptTokens: 32,
            completionTokens: 128,
            status: .received,
            userFeedback: .liked
        )
        messages.append(message2_claude)
        
        let message2_gpt4 = Message(
            id: firstAssistantGPT4Id,
            sessionId: complexTestSessionId,
            parentId: message1.id,
            timestamp: Date().addingTimeInterval(-998),
            role: .assistant,
            content: [.text("LavaChat is an iOS application that serves as a centralized hub for interacting with multiple AI language models.")],
            depth: 1,
            llmInstanceId: gpt4InstanceId,
            promptTokens: 32,
            completionTokens: 148,
            status: .received,
            userFeedback: .disliked
        )
        messages.append(message2_gpt4)
        
        let message2_custom = Message(
            id: firstAssistantCustomId,
            sessionId: complexTestSessionId,
            parentId: message1.id,
            timestamp: Date().addingTimeInterval(-997),
            role: .assistant,
            content: [.text("LavaChat is a multi-model AI communication platform initially launched for iOS with cross-platform expansion plans.")],
            depth: 1,
            llmInstanceId: customInstanceId,
            promptTokens: 32,
            completionTokens: 168,
            status: .received
        )
        messages.append(message2_custom)
        
        // Second round user message (edited version - depth 0)
        let message3 = Message(
            id: editedUserMessageId,
            sessionId: complexTestSessionId,
            parentId: nil,
            timestamp: Date().addingTimeInterval(-940),
            role: .user,
            content: [.text("What is LavaChat and what are its features?")],
            depth: 0,
            userId: testUserId,
            status: .received,
            metadata: [Message.MetadataKeys.isEditOf: rootUserMessageId.uuidString]
        )
        messages.append(message3)
        
        // Second round assistant replies (three parallel - depth 1)
        let message4_claude = Message(
            id: secondAssistantClaudeId,
            sessionId: complexTestSessionId,
            parentId: message3.id,
            timestamp: Date().addingTimeInterval(-939),
            role: .assistant,
            content: [.text("LavaChat is an AI hub platform that centralizes interactions with multiple large language models (LLMs).\n\nKey features include:\n1. Unified Model Management\n2. Tree-Based Conversations\n3. Multi-Model Responses")],
            depth: 1,
            llmInstanceId: claudeInstanceId,
            promptTokens: 48,
            completionTokens: 192,
            status: .received,
            userFeedback: .liked,
            isReplied: true
        )
        messages.append(message4_claude)
        
        let message4_gpt4 = Message(
            id: secondAssistantGPT4Id,
            sessionId: complexTestSessionId,
            parentId: message3.id,
            timestamp: Date().addingTimeInterval(-938),
            role: .assistant,
            content: [.text("LavaChat is an AI communication hub that brings together multiple language models in one application.\n\nIts key features include:\n1. Multi-Model Integration\n2. Advanced Conversation Structure\n3. File Interaction")],
            depth: 1,
            llmInstanceId: gpt4InstanceId,
            promptTokens: 48,
            completionTokens: 212,
            status: .received,
            userFeedback: .liked
        )
        messages.append(message4_gpt4)
        
        let message4_custom = Message(
            id: secondAssistantCustomId,
            sessionId: complexTestSessionId,
            parentId: message3.id,
            timestamp: Date().addingTimeInterval(-937),
            role: .assistant,
            content: [.text("LavaChat is an AI integration platform primarily for iOS.\n\nFeatures:\n• Multiple AI model access\n• Tree-structured conversations\n• File editing capabilities")],
            depth: 1,
            llmInstanceId: customInstanceId,
            promptTokens: 48,
            completionTokens: 156,
            status: .received,
            userFeedback: .disliked
        )
        messages.append(message4_custom)
        
        // Third round user message (depth 2)
        let message5 = Message(
            id: thirdUserMessageId,
            sessionId: complexTestSessionId,
            parentId: message4_claude.id, // Parent is the selected assistant message
            timestamp: Date().addingTimeInterval(-880),
            role: .user,
            content: [.text("Explain the multi-assistant feature in detail.")],
            depth: 2,
            userId: testUserId,
            status: .received
        )
        messages.append(message5)
        
        // Third round assistant replies (original - depth 3)
        let message6_claude = Message(
            id: thirdAssistantClaudeId,
            sessionId: complexTestSessionId,
            parentId: message5.id,
            timestamp: Date().addingTimeInterval(-879),
            role: .assistant,
            content: [.text("LavaChat's multi-assistant feature allows simultaneous interaction with several AI models.")],
            depth: 3,
            llmInstanceId: claudeInstanceId,
            promptTokens: 64,
            completionTokens: 256,
            status: .received
        )
        messages.append(message6_claude)
        
        let message6_gpt4 = Message(
            id: thirdAssistantGPT4Id,
            sessionId: complexTestSessionId,
            parentId: message5.id,
            timestamp: Date().addingTimeInterval(-878),
            role: .assistant,
            content: [.text("The multi-assistant feature in LavaChat enables users to interact with multiple AI models simultaneously.")],
            depth: 3,
            llmInstanceId: gpt4InstanceId,
            promptTokens: 64,
            completionTokens: 298,
            status: .received
        )
        messages.append(message6_gpt4)
        
        let message6_custom = Message(
            id: thirdAssistantCustomId,
            sessionId: complexTestSessionId,
            parentId: message5.id,
            timestamp: Date().addingTimeInterval(-877),
            role: .assistant,
            content: [.text("The multi-assistant feature is one of LavaChat's core capabilities that sets it apart from single-model chat applications.")],
            depth: 3,
            llmInstanceId: customInstanceId,
            promptTokens: 64,
            completionTokens: 322,
            status: .received
        )
        messages.append(message6_custom)
        
        // Third round assistant reply (regeneration - depth 3)
        let message7_claude_regen = Message(
            id: regenAssistantClaudeId,
            sessionId: complexTestSessionId,
            parentId: message5.id, // Same parent as the original
            timestamp: Date().addingTimeInterval(-820),
            role: .assistant,
            content: [.text("LavaChat's multi-assistant feature is a cornerstone capability that fundamentally changes how you interact with AI systems.")],
            depth: 3,
            llmInstanceId: claudeInstanceId,
            promptTokens: 64,
            completionTokens: 312,
            status: .received,
            metadata: [Message.MetadataKeys.isRegenerationOf: thirdAssistantClaudeId.uuidString]
        )
        messages.append(message7_claude_regen)
        
        return messages
    }
    
    // MARK: - Test Validation Helpers
    
    private func validateMessageLayer(_ layer: MessageLayer, expectedRole: MessageRole, expectedDepth: Int64, expectedSiblingCount: Int) {
        XCTAssertEqual(layer.role, expectedRole, "Layer role should match expected")
        XCTAssertEqual(layer.depth, expectedDepth, "Layer depth should match expected")
        XCTAssertEqual(layer.siblingMessages.count, expectedSiblingCount, "Layer should have expected number of sibling messages")
        XCTAssertTrue(layer.siblingMessages.contains { $0.id == layer.activeMessageId }, "Active message ID should exist in sibling messages")
    }
    
    private func validateMessageTreeIntegrity(layers: [MessageLayer]) {
        XCTAssertFalse(layers.isEmpty, "Message layers should not be empty")
        
        // Verify depth ordering
        for i in 1..<layers.count {
            XCTAssertGreaterThanOrEqual(layers[i].depth, layers[i-1].depth, "Depths should be in ascending order")
        }
        
        // Verify all layers have valid active message IDs
        for layer in layers {
            XCTAssertTrue(layer.siblingMessages.contains { $0.id == layer.activeMessageId }, 
                         "Active message ID should exist in sibling messages for layer at depth \(layer.depth)")
        }
    }
    
    private func printMessageTree(layers: [MessageLayer]) {
        print("📊 Message Tree Structure:")
        for (index, layer) in layers.enumerated() {
            let indent = String(repeating: "  ", count: Int(layer.depth))
            print("\(indent)Layer \(index): \(layer.role) (depth: \(layer.depth), siblings: \(layer.siblingMessages.count), active: \(layer.activeMessageId))")
            for message in layer.siblingMessages {
                let activeMarker = message.id == layer.activeMessageId ? "★" : "  "
                let contentPreview = getContentPreview(from: message.content.first)
                print("\(indent)  \(activeMarker) \(message.id) - \(contentPreview)")
            }
        }
    }
    
    private func getContentPreview(from contentBlock: ContentBlock?) -> String {
        guard let contentBlock = contentBlock else { return "N/A" }
        switch contentBlock {
        case .text(let text):
            return String(text.prefix(50))
        case .attachedFileContent(let fileName, let content):
            return "File: \(fileName) - \(String(content.prefix(30)))"
        case .thinking(let text):
            return "Thinking: \(String(text.prefix(30)))"
        case .diffBlock(let diff):
            return "Diff: \(String(diff.explanation?.prefix(30) ?? "No explanation"))"
        default:
            return contentBlock.typeDescription
        }
    }
    
    private func getContentText(from contentBlock: ContentBlock?) -> String {
        guard let contentBlock = contentBlock else { return "" }
        switch contentBlock {
        case .text(let text):
            return text
        case .attachedFileContent(_, let content):
            return content
        case .thinking(let text):
            return text
        case .diffBlock(let diff):
            return diff.explanation ?? ""
        default:
            return ""
        }
    }
    
    // MARK: - Initialization and Cache Management Tests
    
    func testInitializeTreeFromRepositoryWithComplexData() async throws {
        // When
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // Then
        let messageLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        XCTAssertFalse(messageLayers.isEmpty, "Message layers should not be empty after initialization")
        
        // Validate tree integrity
        validateMessageTreeIntegrity(layers: messageLayers)
        printMessageTree(layers: messageLayers)
        
        // Verify we have the expected structure
        // Should have: edited user message -> second round assistants -> third user -> regenerated assistant
        XCTAssertTrue(messageLayers.count >= 4, "Should have at least 4 layers in complex tree")
        
        // First layer should be user messages (original + edited)
        let firstLayer = messageLayers[0]
        validateMessageLayer(firstLayer, expectedRole: .user, expectedDepth: 0, expectedSiblingCount: 2)
        XCTAssertEqual(firstLayer.activeMessageId, editedUserMessageId, "Active user message should be the edited one")
        
        // Last layer should be the regenerated assistant message
        let lastLayer = messageLayers.last!
        XCTAssertEqual(lastLayer.activeMessageId, regenAssistantClaudeId, "Active assistant message should be the regenerated one")
    }
    
    func testClearCacheForSpecificSessionId() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        XCTAssertFalse(initialLayers.isEmpty, "Should have layers before clearing cache")
        
        // When
        useCase.clearCache(for: complexTestSessionId)
        
        // Then
        let clearedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        XCTAssertTrue(clearedLayers.isEmpty, "Should have no layers after clearing cache")
    }
    
    func testClearAllCaches() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        XCTAssertFalse(initialLayers.isEmpty, "Should have layers before clearing all caches")
        
        // When
        useCase.clearAllCaches()
        
        // Then
        let clearedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        XCTAssertTrue(clearedLayers.isEmpty, "Should have no layers after clearing all caches")
    }
    
    func testInitializationWithInvalidSessionId() async {
        // Given
        let invalidSessionId = UUID()
        
        // When & Then
        do {
            try await useCase.initializeTreeFromRepository(sessionId: invalidSessionId)
            XCTFail("Should throw error for invalid session ID")
        } catch {
            // Expected to throw error
            print("✅ Correctly threw error for invalid session ID: \(error)")
        }
    }
    
    // MARK: - Incremental Updates Tests
    
    func testAddMessageAndUpdateTree_UserMessage() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let initialCount = initialLayers.flatMap { $0.siblingMessages }.count
        
        let newUserMessage = Message(
            sessionId: complexTestSessionId,
            parentId: regenAssistantClaudeId, // Continue from the regenerated message
            role: .user,
            content: [ContentBlock.text("Can you explain more about the file interaction features?")],
            depth: 4,
            userId: testUserId
        )
        
        // When
        await useCase.addMessageAndUpdateTree(sessionId: complexTestSessionId, message: newUserMessage)
        
        // Then
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let updatedCount = updatedLayers.flatMap { $0.siblingMessages }.count
        XCTAssertEqual(updatedCount, initialCount + 1, "Should have one more message after adding")
        
        // Verify the new message is included in the layers
        let lastLayer = updatedLayers.last!
        XCTAssertEqual(lastLayer.role, .user, "Last layer should be user role")
        XCTAssertEqual(lastLayer.activeMessageId, newUserMessage.id, "New user message should be active")
    }
    
    func testAddMessageAndUpdateTree_AssistantMessage() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let initialCount = initialLayers.flatMap { $0.siblingMessages }.count
        
        // First add a user message to continue from
        let newUserMessage = Message(
            sessionId: complexTestSessionId,
            parentId: regenAssistantClaudeId,
            role: .user,
            content: [ContentBlock.text("Tell me about file features.")],
            depth: 4,
            userId: testUserId
        )
        await useCase.addMessageAndUpdateTree(sessionId: complexTestSessionId, message: newUserMessage)
        
        // Now add an assistant message
        let newAssistantMessage = Message(
            sessionId: complexTestSessionId,
            parentId: newUserMessage.id,
            role: .assistant,
            content: [ContentBlock.text("LavaChat's file interaction features include...")],
            depth: 5,
            llmInstanceId: claudeInstanceId
        )
        
        // When
        await useCase.addMessageAndUpdateTree(sessionId: complexTestSessionId, message: newAssistantMessage)
        
        // Then
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let updatedCount = updatedLayers.flatMap { $0.siblingMessages }.count
        XCTAssertEqual(updatedCount, initialCount + 2, "Should have two more messages after adding user and assistant")
        
        // Verify the assistant message is included and active
        let lastLayer = updatedLayers.last!
        XCTAssertEqual(lastLayer.role, .assistant, "Last layer should be assistant role")
        XCTAssertEqual(lastLayer.activeMessageId, newAssistantMessage.id, "New assistant message should be active")
    }
    
    func testaddMultipleMessagesAndUpdateTree() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let initialCount = initialLayers.flatMap { $0.siblingMessages }.count
        
        // Create multiple assistant messages (simulating multi-instance response)
        let parentId = regenAssistantClaudeId
        let newMessages = [
            Message(
                sessionId: complexTestSessionId,
                parentId: parentId,
                role: .assistant,
                content: [ContentBlock.text("Claude's response about advanced features...")],
                depth: 4,
                llmInstanceId: claudeInstanceId,
                userFeedback: .liked,
                isReplied: true
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: parentId,
                role: .assistant,
                content: [ContentBlock.text("GPT-4's response about advanced features...")],
                depth: 4,
                llmInstanceId: gpt4InstanceId,
                userFeedback: .none
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: parentId,
                role: .assistant,
                content: [ContentBlock.text("Custom model's response about advanced features...")],
                depth: 4,
                llmInstanceId: customInstanceId,
                userFeedback: .disliked
            ),
        ]
        
        // When
        await useCase.addMultipleMessagesAndUpdateTree(sessionId: complexTestSessionId, messages: newMessages)
        
        // Then
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let updatedCount = updatedLayers.flatMap { $0.siblingMessages }.count
        XCTAssertEqual(updatedCount, initialCount + 3, "Should have three more messages after batch adding")
        
        // Verify the highest priority message becomes active (Claude with liked feedback and isReplied)
        let lastLayer = updatedLayers.last!
        XCTAssertEqual(lastLayer.role, .assistant, "Last layer should be assistant role")
        XCTAssertEqual(lastLayer.activeMessageId, newMessages[0].id, "Claude message should be active due to highest priority")
        
        // Verify disliked message is filtered out from display
        let assistantLayer = updatedLayers.last!
        XCTAssertEqual(assistantLayer.siblingMessages.count, 3, "Should only show 3 messages")
    }
    
    func testSwitchActiveMessage() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Find a layer with multiple siblings to test switching
        guard let assistantLayer = initialLayers.first(where: { $0.role == .assistant && $0.siblingMessages.count > 1 }) else {
            XCTFail("Need a layer with multiple assistant messages to test switching")
            return
        }
        
        let currentActiveId = assistantLayer.activeMessageId
        let newActiveId = assistantLayer.siblingMessages.first { $0.id != currentActiveId }?.id
        
        guard let newActiveId = newActiveId else {
            XCTFail("Could not find alternative message ID")
            return
        }
        
        // When
        let resultLayers = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: currentActiveId,
            newActiveMessageId: newActiveId
        )
        
        // Then
        XCTAssertFalse(resultLayers.isEmpty, "Should return updated message layers")
        
        // Verify the active message was switched
        let finalLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        guard let finalAssistantLayer = finalLayers.first(where: { $0.role == .assistant && $0.siblingMessages.count > 1 }) else {
            XCTFail("Could not find assistant layer in final results")
            return
        }
        
        XCTAssertEqual(finalAssistantLayer.activeMessageId, newActiveId, "Active message should be switched")
        print("✅ Successfully switched active message from \(currentActiveId) to \(newActiveId)")
    }
    
    func testUpdateMessageInCache() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let originalLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Find a message to update
        guard let originalMessage = originalLayers.flatMap({ $0.siblingMessages }).first(where: { $0.role == .assistant }) else {
            XCTFail("Could not find assistant message to update")
            return
        }
        
        // Create updated message with new content
        let originalContentText = getContentText(from: originalMessage.content.first)
        let updatedMessage = Message(
            id: originalMessage.id,
            sessionId: originalMessage.sessionId,
            parentId: originalMessage.parentId,
            timestamp: originalMessage.timestamp,
            role: originalMessage.role,
            content: [ContentBlock.text("UPDATED: " + originalContentText)],
            depth: originalMessage.depth,
            llmInstanceId: originalMessage.llmInstanceId,
            userFeedback: .liked // Change feedback
        )
        
        // When
        useCase.updateMessageInCache(sessionId: complexTestSessionId, message: updatedMessage)
        
        // Then
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let updatedMessageInLayers = updatedLayers.flatMap({ $0.siblingMessages }).first { $0.id == originalMessage.id }
        
        XCTAssertNotNil(updatedMessageInLayers, "Updated message should be found in layers")
        let updatedContentText = getContentText(from: updatedMessageInLayers!.content.first)
        XCTAssertTrue(updatedContentText.contains("UPDATED:"), "Message content should be updated")
        XCTAssertEqual(updatedMessageInLayers!.userFeedback, .liked, "Message feedback should be updated")
    }
    
    func testRemoveMessageFromCache() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let originalLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let originalCount = originalLayers.flatMap({ $0.siblingMessages }).count
        
        // Find a message to remove (not the active one to avoid breaking the tree)
        guard let messageToRemove = originalLayers.flatMap({ $0.siblingMessages }).first(where: { message in
            message.role == .assistant && 
            !originalLayers.contains(where: { layer in layer.activeMessageId == message.id })
        }) else {
            XCTFail("Could not find suitable message to remove")
            return
        }
        
        // When
        useCase.removeMessageFromCache(sessionId: complexTestSessionId, messageId: messageToRemove.id)
        
        // Then
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let updatedCount = updatedLayers.flatMap({ $0.siblingMessages }).count
        XCTAssertEqual(updatedCount, originalCount - 1, "Should have one less message after removal")
        
        let removedMessageInLayers = updatedLayers.flatMap({ $0.siblingMessages }).first { $0.id == messageToRemove.id }
        XCTAssertNil(removedMessageInLayers, "Removed message should not be found in layers")
    }
    
    // MARK: - Query Interface Tests
    
    func testGetMessageLayersForDisplay() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // When
        let messageLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Then
        validateMessageTreeIntegrity(layers: messageLayers)
        
        // Verify layer structure matches expected complex data
        XCTAssertTrue(messageLayers.count >= 4, "Should have at least 4 layers in complex structure")
        
        // Verify each layer has correct properties
        for layer in messageLayers {
            XCTAssertFalse(layer.siblingMessages.isEmpty, "Each layer should have at least one message")
            XCTAssertTrue(layer.siblingMessages.contains { $0.id == layer.activeMessageId }, 
                         "Active message should exist in sibling messages")
        }
        
        printMessageTree(layers: messageLayers)
    }
    
    func testGetActivePathMessage() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // When
        let history = useCase.getActivePathMessage(for: complexTestSessionId)
        
        // Then
        XCTAssertFalse(history.isEmpty, "History should not be empty")
        
        // Verify messages are ordered by depth
        for i in 1..<history.count {
            XCTAssertLessThanOrEqual(history[i-1].depth, history[i].depth, 
                                   "Messages should be ordered by depth")
        }
        
        // Verify the last message is the regenerated Claude response
        XCTAssertEqual(history.last?.id, regenAssistantClaudeId, "Last message should be the regenerated Claude response")
        
        // Verify the path includes edited user message (not original)
        let userMessages = history.filter { $0.role == .user }
        XCTAssertTrue(userMessages.contains { $0.id == editedUserMessageId }, "History should include edited user message")
        XCTAssertFalse(userMessages.contains { $0.id == rootUserMessageId }, "History should not include original user message (since edited version is active)")
        
        print("📚 Active message history (\(history.count) messages):")
        for (index, message) in history.enumerated() {
            let contentPreview = getContentPreview(from: message.content.first)
            print("  \(index): \(message.role) (depth: \(message.depth)) - \(contentPreview)")
        }
    }
    
    func testGetActivePathId() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // When
        let activePath = useCase.getActivePathId(for: complexTestSessionId)
        
        // Then
        XCTAssertFalse(activePath.isEmpty, "Active path should not be empty")
        
        // Verify path corresponds to actual messages
        let history = useCase.getActivePathMessage(for: complexTestSessionId)
        XCTAssertEqual(activePath.count, history.count, 
                      "Active path count should match history count")
        
        for (index, messageId) in activePath.enumerated() {
            XCTAssertEqual(messageId, history[index].id, 
                          "Path message ID should match history message ID at index \(index)")
        }
        
        // Verify the path ends with the regenerated Claude message
        XCTAssertEqual(activePath.last, regenAssistantClaudeId, "Active path should end with regenerated Claude message")
        
        print("🛤️ Active path (\(activePath.count) messages): \(activePath.map { $0.uuidString.prefix(8) }.joined(separator: " -> "))")
    }
    
    func testGetMessageLayersForDifferentActiveBranch() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // Switch to a different active message to test different branch
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        guard let assistantLayer = initialLayers.first(where: { $0.role == .assistant && $0.siblingMessages.count > 1 }) else {
            XCTFail("Need a layer with multiple assistant messages")
            return
        }
        
        let currentActiveId = assistantLayer.activeMessageId
        let alternativeActiveId = assistantLayer.siblingMessages.first { $0.id != currentActiveId }?.id
        
        guard let alternativeActiveId = alternativeActiveId else {
            XCTFail("Could not find alternative message")
            return
        }
        
        // When
        _ = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: currentActiveId,
            newActiveMessageId: alternativeActiveId
        )
        
        // Then
        let newLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let newHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        
        // Verify the branch has changed
        XCTAssertNotEqual(newHistory.last?.id, regenAssistantClaudeId, "Should have different active branch")
        
        // Verify tree integrity is still maintained
        validateMessageTreeIntegrity(layers: newLayers)
        
        print("🌿 Switched to alternative branch, new active message: \(newHistory.last?.id ?? UUID())")
    }
    
    func testQueryWithNonExistentSession() async {
        // Given
        let nonExistentSessionId = UUID()
        
        // When
        let layers = await useCase.getMessageLayersForDisplay(sessionId: nonExistentSessionId)
        let history = useCase.getActivePathMessage(for: nonExistentSessionId)
        let activePath = useCase.getActivePathId(for: nonExistentSessionId)
        
        // Then
        XCTAssertTrue(layers.isEmpty, "Should return empty layers for non-existent session")
        XCTAssertTrue(history.isEmpty, "Should return empty history for non-existent session")
        XCTAssertTrue(activePath.isEmpty, "Should return empty active path for non-existent session")
    }
    
    // MARK: - Message Filtering Tests
    
    func testUserFeedbackImpactOnSorting() async throws {
        // Given
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Find an assistant layer with multiple messages
        guard let assistantLayer = initialLayers.first(where: { $0.role == .assistant && $0.siblingMessages.count > 1 }) else {
            XCTFail("Need an assistant layer with multiple messages")
            return
        }
        
        print("🎭 Testing user feedback impact on \(assistantLayer.siblingMessages.count) assistant messages")
        
        // The messages should be sorted with liked feedback first, then neutral
        let likedMessages = assistantLayer.siblingMessages.filter { $0.userFeedback == .liked }
        let neutralMessages = assistantLayer.siblingMessages.filter { $0.userFeedback == .none }
        let dislikedMessages = assistantLayer.siblingMessages.filter { $0.userFeedback == .disliked }
        
        print("  - Liked: \(likedMessages.count)")
        print("  - Neutral: \(neutralMessages.count)")
        print("  - Disliked: \(dislikedMessages.count) (should be 1, handled at MessageRowView level)")
        
        // Verify disliked messages are handled at MessageRowView level
        XCTAssertEqual(dislikedMessages.count, 1, "Disliked messages handled at MessageRowView level")
        
        // If there are both liked and neutral messages, liked should come first
        if !likedMessages.isEmpty && !neutralMessages.isEmpty {
            let firstLikedIndex = assistantLayer.siblingMessages.firstIndex { $0.userFeedback == .liked }
            let firstNeutralIndex = assistantLayer.siblingMessages.firstIndex { $0.userFeedback == .none }
            
            if let likedIdx = firstLikedIndex, let neutralIdx = firstNeutralIndex {
                XCTAssertLessThan(likedIdx, neutralIdx, "Liked messages should appear before neutral messages")
                         }
         }
     }
    
    // MARK: - ChatViewModel Usage Scenario Tests
    
    func testSimulateUserClickingSiblingMessage() async throws {
        // Given - Initialize and get current state
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let initialHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        
        // Find a layer with multiple siblings
        guard let multiSiblingLayer = initialLayers.first(where: { $0.siblingMessages.count > 1 }) else {
            XCTFail("Need a layer with multiple sibling messages")
            return
        }
        
        let currentActiveId = multiSiblingLayer.activeMessageId
        let newActiveId = multiSiblingLayer.siblingMessages.first { $0.id != currentActiveId }?.id
        
        guard let newActiveId = newActiveId else {
            XCTFail("Could not find alternative message")
            return
        }
        
        print("👆 Simulating user clicking sibling message: \(currentActiveId) -> \(newActiveId)")
        
        // When - Simulate ChatViewModel switching active message
        let updatedLayers = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: currentActiveId,
            newActiveMessageId: newActiveId
        )
        
        // Then - Verify the tree was properly updated
        let finalHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        let finalLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Verify active message changed
        XCTAssertNotEqual(initialHistory.last?.id, finalHistory.last?.id, "Active path should have changed")
        
        // Verify tree integrity maintained
        validateMessageTreeIntegrity(layers: finalLayers)
        
        // Verify returned layers match current state
        XCTAssertEqual(updatedLayers.count, finalLayers.count, "Returned layers should match current state")
        
        print("✅ Successfully switched to sibling message, new path length: \(finalHistory.count)")
    }
    
    func testSimulateEditingMessage() async throws {
        // Given - Initialize and find a user message to edit
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Find the first user message layer (should have original + edited message)
        guard let userLayer = initialLayers.first(where: { $0.role == .user }) else {
            XCTFail("Could not find user message layer")
            return
        }
        
        let originalMessageId = userLayer.activeMessageId
        
        // Create an edited version
        let editedMessage = Message(
            sessionId: complexTestSessionId,
            parentId: nil, // Same level as original
            role: .user,
            content: [ContentBlock.text("EDITED: What is LavaChat and how does it work exactly?")],
            depth: 0,
            userId: testUserId,
            metadata: [Message.MetadataKeys.isEditOf: originalMessageId.uuidString]
        )
        
        print("✏️ Simulating message edit: \(originalMessageId) -> \(editedMessage.id)")
        
        // When - Add the edited message
        await useCase.addMessageAndUpdateTree(sessionId: complexTestSessionId, message: editedMessage)
        
        // Then - Verify the edited message becomes active
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let userLayerAfterEdit = updatedLayers.first { $0.role == .user }
        
        XCTAssertNotNil(userLayerAfterEdit, "Should still have user layer after edit")
        XCTAssertEqual(userLayerAfterEdit!.activeMessageId, editedMessage.id, "Edited message should become active")
        XCTAssertEqual(userLayerAfterEdit!.siblingMessages.count, userLayer.siblingMessages.count + 1, "Should have one more sibling message")
        
        // Verify the tree still maintains integrity
        validateMessageTreeIntegrity(layers: updatedLayers)
        
        print("✅ Successfully added edited message, user layer now has \(userLayerAfterEdit!.siblingMessages.count) siblings")
    }
    
    func testSimulateRegeneratingMessage() async throws {
        // Given - Initialize and find an assistant message to regenerate
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Find an assistant message to regenerate
        guard let assistantLayer = initialLayers.first(where: { $0.role == .assistant }) else {
            XCTFail("Could not find assistant message layer")
            return
        }
        
        let originalAssistantMessage = assistantLayer.siblingMessages.first { $0.id == assistantLayer.activeMessageId }!
        
        // Create a regenerated version
        let originalContentText = getContentText(from: originalAssistantMessage.content.first)
        let regeneratedMessage = Message(
            sessionId: complexTestSessionId,
            parentId: originalAssistantMessage.parentId,
            role: .assistant,
            content: [ContentBlock.text("REGENERATED: " + originalContentText)],
            depth: originalAssistantMessage.depth,
            llmInstanceId: originalAssistantMessage.llmInstanceId,
            metadata: [Message.MetadataKeys.isRegenerationOf: originalAssistantMessage.id.uuidString]
        )
        
        print("🔄 Simulating message regeneration: \(originalAssistantMessage.id) -> \(regeneratedMessage.id)")
        
        // When - Add the regenerated message
        await useCase.addMessageAndUpdateTree(sessionId: complexTestSessionId, message: regeneratedMessage)
        
        // Then - Verify the regenerated message is included
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let assistantLayerAfterRegen = updatedLayers.first { 
            $0.role == .assistant && 
            $0.depth == originalAssistantMessage.depth 
        }
        
        XCTAssertNotNil(assistantLayerAfterRegen, "Should still have assistant layer after regeneration")
        XCTAssertEqual(assistantLayerAfterRegen!.siblingMessages.count, assistantLayer.siblingMessages.count + 1, "Should have one more sibling message")
        
        // The regenerated message should become active (latest timestamp)
        XCTAssertEqual(assistantLayerAfterRegen!.activeMessageId, regeneratedMessage.id, "Regenerated message should become active")
        
        print("✅ Successfully added regenerated message, assistant layer now has \(assistantLayerAfterRegen!.siblingMessages.count) siblings")
    }
    
    func testSimulateMultiInstanceResponse() async throws {
        // Given - Initialize and add a user message to get responses to
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        let newUserMessage = Message(
            sessionId: complexTestSessionId,
            parentId: regenAssistantClaudeId,
            role: .user,
            content: [ContentBlock.text("Please compare different approaches to this problem.")],
            depth: 4,
            userId: testUserId
        )
        
        await useCase.addMessageAndUpdateTree(sessionId: complexTestSessionId, message: newUserMessage)
        
        // Create multiple assistant responses (simulating multi-instance response)
        let multiInstanceResponses = [
            Message(
                sessionId: complexTestSessionId,
                parentId: newUserMessage.id,
                role: .assistant,
                content: [ContentBlock.text("Claude's analytical approach to the problem...")],
                depth: 5,
                llmInstanceId: claudeInstanceId,
                userFeedback: .liked,
                isReplied: true
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: newUserMessage.id,
                role: .assistant,
                content: [ContentBlock.text("GPT-4's systematic approach to the problem...")],
                depth: 5,
                llmInstanceId: gpt4InstanceId,
                userFeedback: .none
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: newUserMessage.id,
                role: .assistant,
                content: [ContentBlock.text("Custom model's creative approach to the problem...")],
                depth: 5,
                llmInstanceId: customInstanceId,
                userFeedback: .disliked
            )
        ]
        
        print("🤖 Simulating multi-instance response with \(multiInstanceResponses.count) models")
        
        // When - Add all responses simultaneously
        await useCase.addMultipleMessagesAndUpdateTree(
            sessionId: complexTestSessionId, 
            messages: multiInstanceResponses
        )
        
        // Then - Verify all responses are handled correctly
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let responseLayer = updatedLayers.last!
        
        XCTAssertEqual(responseLayer.role, .assistant, "Last layer should be assistant responses")
        XCTAssertEqual(responseLayer.siblingMessages.count, 3, "Should show 3 responses")
        XCTAssertEqual(responseLayer.activeMessageId, multiInstanceResponses[0].id, "Claude response should be active (highest priority)")
        
        // Verify responses are properly sorted (liked + isReplied first)
        let firstResponse = responseLayer.siblingMessages[0]
        XCTAssertEqual(firstResponse.llmInstanceId, claudeInstanceId, "Claude should be first (highest priority)")
        XCTAssertEqual(firstResponse.userFeedback, .liked, "First response should be liked")
        XCTAssertTrue(firstResponse.isReplied, "First response should be marked as replied")
        
        print("✅ Multi-instance response handled correctly, \(responseLayer.siblingMessages.count) responses displayed")
    }
    
    func testSimulateComplexMessageTreeNavigation() async throws {
        // Given - Initialize the complex tree
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        
        print("🗺️ Starting complex navigation test with \(initialHistory.count) messages in active path")
        
        // Step 1: Switch to first round of assistant responses
        let firstAssistantLayer = (await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId))
            .first { $0.role == .assistant && $0.depth == 1 }!
        
        let originalFirstActiveId = firstAssistantLayer.activeMessageId
        let newFirstActiveId = firstAssistantLayer.siblingMessages.first { $0.id != originalFirstActiveId }?.id
        
        guard let newFirstActiveId = newFirstActiveId else {
            XCTFail("Could not find alternative first assistant message")
            return
        }
        
        _ = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: originalFirstActiveId,
            newActiveMessageId: newFirstActiveId
        )
        
        let historyAfterStep1 = useCase.getActivePathMessage(for: complexTestSessionId)
        print("  After step 1: \(historyAfterStep1.count) messages in path")
        
        // Step 2: Switch back to edited user message branch
        _ = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: rootUserMessageId,
            newActiveMessageId: editedUserMessageId
        )
        
        let historyAfterStep2 = useCase.getActivePathMessage(for: complexTestSessionId)
        print("  After step 2: \(historyAfterStep2.count) messages in path")
        
        // Step 3: Navigate to different assistant response in second round
        let secondAssistantLayer = (await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId))
            .first { $0.role == .assistant && $0.depth == 1 }!
        
        let currentSecondActiveId = secondAssistantLayer.activeMessageId
        let newSecondActiveId = secondAssistantLayer.siblingMessages.first { $0.id != currentSecondActiveId }?.id
        
        if let newSecondActiveId = newSecondActiveId {
            _ = try await useCase.switchActiveMessage(
                sessionId: complexTestSessionId,
                fromMessageId: currentSecondActiveId,
                newActiveMessageId: newSecondActiveId
            )
        }
        
        let finalHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        let finalLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Then - Verify navigation maintained tree integrity
        validateMessageTreeIntegrity(layers: finalLayers)
        XCTAssertFalse(finalHistory.isEmpty, "Should have valid active path after navigation")
        
        print("✅ Complex navigation completed, final path: \(finalHistory.count) messages")
    }
    
    // MARK: - Database Synchronization Tests (New after refactoring)
    
    func testActiveMessageIdPersistenceInDatabase() async throws {
        // Given - Initialize the tree
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let initialLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Find a layer with multiple siblings to test switching
        guard let assistantLayer = initialLayers.first(where: { $0.role == .assistant && $0.siblingMessages.count > 1 }) else {
            XCTFail("Need a layer with multiple assistant messages to test database sync")
            return
        }
        
        let currentActiveId = assistantLayer.activeMessageId
        let newActiveId = assistantLayer.siblingMessages.first { $0.id != currentActiveId }?.id
        
        guard let newActiveId = newActiveId else {
            XCTFail("Could not find alternative message ID")
            return
        }
        
        // When - Switch active message (should trigger database update)
        _ = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: currentActiveId,
            newActiveMessageId: newActiveId
        )
        
        // Wait for async database update
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then - Verify the database has been updated
        let updatedChatSession = try await chatRepository.getChatSession(byId: complexTestSessionId)
        XCTAssertNotNil(updatedChatSession, "Chat session should exist in database")
        XCTAssertEqual(updatedChatSession?.activeMessageId, newActiveId, "Database should have updated activeMessageId")
        
        print("💾 Database sync test: \(currentActiveId) -> \(newActiveId)")
        print("✅ Database activeMessageId correctly updated to: \(updatedChatSession?.activeMessageId ?? UUID())")
    }
    
    func testInitializeFromDatabaseWithCorrectActivePath() async throws {
        // Given - First setup a specific active path in database
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // Switch to a specific message to create a known state
        let dislikedTargetActiveId = firstAssistantGPT4Id // Switch to GPT-4 response in first round
        
        do {
            _ = try await useCase.switchActiveMessage(
                sessionId: complexTestSessionId,
                fromMessageId: secondAssistantClaudeId, // From current active
                newActiveMessageId: dislikedTargetActiveId!
            )
            XCTFail("Should throw error for disliked message")
        } catch {
            print("✅ Error correctly thrown for disliked message: \(error)")
        }

        let notSiblingTargetActiveId = firstAssistantCustomId // Switch to custom response in first round
         do {
            _ = try await useCase.switchActiveMessage(
                sessionId: complexTestSessionId,
                fromMessageId: secondAssistantClaudeId, // From current active
                newActiveMessageId: notSiblingTargetActiveId!
            )
            XCTFail("Should throw error for not sibling message")
        } catch {
            print("✅ Error correctly thrown for not sibling message: \(error)")
        }

        let targetActiveId = secondAssistantGPT4Id // Switch to GPT-4 response in second round
        _ = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: secondAssistantClaudeId, // From current active
            newActiveMessageId: targetActiveId!
        )
        
        // Wait for database update
        try await Task.sleep(nanoseconds: 100_000_000)
        
        // Clear cache to force reload from database
        useCase.clearCache(for: complexTestSessionId)
        
        // When - Re-initialize from database
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // Then - Verify the active path matches what was saved
        let restoredHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        let restoredLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // Verify the active path ends with our target message
        let activePath = useCase.getActivePathId(for: complexTestSessionId)
        XCTAssertTrue(activePath.contains(targetActiveId!), "Active path should contain the target message")
        
        // Verify the first assistant layer shows the correct active message
        let firstAssistantLayer = restoredLayers.first { $0.role == .assistant && $0.depth == 1 }
        XCTAssertEqual(firstAssistantLayer?.activeMessageId, targetActiveId, "First assistant layer should have correct active message")
        
        print("🔄 Database restoration test: Successfully restored path with target message \(targetActiveId!)")
        print("📊 Restored path length: \(restoredHistory.count) messages")
    }
    
    func testBuildActivePathFromMessageLogic() async throws {
        // Given - Initialize and get a message to build from
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // Test building from third assistant GPT-4 message (depth 2)
        let startMessage = thirdAssistantGPT4Id
        
        // When - Switch to this message (which internally uses buildActivePathFromMessage)
        _ = try await useCase.switchActiveMessage(
            sessionId: complexTestSessionId,
            fromMessageId: regenAssistantClaudeId,
            newActiveMessageId: startMessage!
        )
        
        // Then - Verify the path was built correctly from this message downward
        let resultingHistory = useCase.getActivePathMessage(for: complexTestSessionId)
        _ = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        
        // The path should include:
        // 1. editedUserMessageId (depth 0)
        // 2. secondAssistantClaudeId (depth 1) - our start message
        // 3. thirdUserMessageId (depth 2) - because secondAssistantClaudeId.isReplied = true
        // 4. Some third assistant message (depth 3)
        
        XCTAssertTrue(resultingHistory.contains { $0.id == editedUserMessageId }, "Path should include edited user message")
        XCTAssertTrue(resultingHistory.contains { $0.id == secondAssistantClaudeId }, "Path should include start message")
        XCTAssertTrue(resultingHistory.contains { $0.id == thirdUserMessageId }, "Path should continue to third user message")
        
        // Verify depths are correct and ascending
        for i in 1..<resultingHistory.count {
            XCTAssertGreaterThanOrEqual(resultingHistory[i].depth, resultingHistory[i-1].depth, 
                                       "Depths should be in ascending order")
        }
        
        print("🏗️ Build path test: From message \(startMessage!) built path with \(resultingHistory.count) messages")
        print("   Path: \(resultingHistory.map { "\($0.role.rawValue)(\($0.depth))" }.joined(separator: " -> "))")
    }
    
    func testMessageTreeCacheSortingLogic() async throws {
        // Given - Create multiple assistant messages with different priorities
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        let parentId = regenAssistantClaudeId
        let testMessages = [
            Message(
                sessionId: complexTestSessionId,
                parentId: parentId,
                timestamp: Date().addingTimeInterval(-100),
                role: .assistant,
                content: [ContentBlock.text("Low priority message")],
                depth: 4,
                llmInstanceId: customInstanceId,
                userFeedback: .none // Lower priority
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: parentId,
                timestamp: Date().addingTimeInterval(-200), // Older timestamp
                role: .assistant,
                content: [ContentBlock.text("High priority message")],
                depth: 4,
                llmInstanceId: claudeInstanceId,
                userFeedback: .liked, // High priority
                isReplied: true // Highest priority
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: parentId,
                timestamp: Date().addingTimeInterval(-50), // Newer timestamp
                role: .assistant,
                content: [ContentBlock.text("Medium priority message")],
                depth: 4,
                llmInstanceId: gpt4InstanceId,
                userFeedback: .liked // High priority but no isReplied
            ),
        ]
        
        // When - Add messages using the new batch adding logic
        await useCase.addMultipleMessagesAndUpdateTree(
            sessionId: complexTestSessionId,
            messages: testMessages
        )
        
        // Then - Verify sorting worked correctly
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let newAssistantLayer = updatedLayers.last!
        
        XCTAssertEqual(newAssistantLayer.role, .assistant, "Last layer should be assistant")
        XCTAssertEqual(newAssistantLayer.siblingMessages.count, 3, "Should have all 3 messages")
        
        // The message with isReplied=true + liked should be active (highest priority)
        XCTAssertEqual(newAssistantLayer.activeMessageId, testMessages[1].id, "High priority message should be active")
        
        // Verify the sorting order in the layer
        let sortedMessages = newAssistantLayer.siblingMessages
        XCTAssertEqual(sortedMessages[0].id, testMessages[1].id, "Highest priority (liked + isReplied) should be first")
        XCTAssertEqual(sortedMessages[1].id, testMessages[2].id, "Medium priority (liked only) should be second")
        XCTAssertEqual(sortedMessages[2].id, testMessages[0].id, "Low priority (none) should be last")
        
        print("🎯 Sorting test: Messages ordered correctly by priority")
        let priorityStrings = sortedMessages.map { m in 
            "\(m.userFeedback.rawValue)\(m.isReplied ? "+replied" : "")" 
        }
        print("   Order: \(priorityStrings.joined(separator: " -> "))")
    }
    
    func testActiveMessagePathConsistencyAfterSwitching() async throws {
        // Given - Initialize with complex tree
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        let _ = useCase.getActivePathMessage(for: complexTestSessionId)
        
        // Test multiple switches to verify path consistency
        let testSwitches = [
            (from: regenAssistantClaudeId, to: thirdAssistantCustomId), // Switch to first round
            (from: editedUserMessageId, to: rootUserMessageId), // Switch user message versions
            (from: firstAssistantGPT4Id, to: firstAssistantClaudeId), // Switch assistant in first round
        ]
        
        for (index, switchPair) in testSwitches.enumerated() {
            print("🔀 Test switch \(index + 1): \(switchPair.from!) -> \(switchPair.to!)")
            
            // When - Perform switch
            _ = try await useCase.switchActiveMessage(
                sessionId: complexTestSessionId,
                fromMessageId: switchPair.from!,
                newActiveMessageId: switchPair.to!
            )
            
            // Then - Verify consistency
            let currentHistory = useCase.getActivePathMessage(for: complexTestSessionId)
            let currentLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
            let currentPath = useCase.getActivePathId(for: complexTestSessionId)
            
            // Verify path integrity
            XCTAssertEqual(currentPath.count, currentHistory.count, "Path count should match history count")
            XCTAssertTrue(currentPath.contains(switchPair.to!), "Path should contain target message")
            
            // Verify tree integrity
            validateMessageTreeIntegrity(layers: currentLayers)
            
            // Verify database consistency
            try await Task.sleep(nanoseconds: 50_000_000) // Wait for DB update
            let dbSession = try await chatRepository.getChatSession(byId: complexTestSessionId)
            let deepestMessage = currentHistory.last
            XCTAssertEqual(dbSession?.activeMessageId, deepestMessage?.id, "Database should have deepest message as active")
            
            print("   ✅ Switch \(index + 1) successful, path length: \(currentHistory.count)")
        }
        
        print("🎉 All path consistency tests passed!")
    }
    
    func testCacheDirectSortingMethods() async throws {
        // Given - Get direct access to cache for testing its sorting methods
        try await useCase.initializeTreeFromRepository(sessionId: complexTestSessionId)
        
        // Create test messages with various characteristics for sorting
        let testMessages = [
            Message(
                sessionId: complexTestSessionId,
                parentId: nil,
                timestamp: Date().addingTimeInterval(-300),
                role: .assistant,
                content: [ContentBlock.text("Message A")],
                depth: 1,
                llmInstanceId: claudeInstanceId,
                userFeedback: .disliked // Should be filtered out
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: nil,
                timestamp: Date().addingTimeInterval(-200),
                role: .assistant,
                content: [ContentBlock.text("Message B")],
                depth: 1,
                llmInstanceId: gpt4InstanceId,
                userFeedback: .liked,
                isReplied: true, // Highest priority
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: nil,
                timestamp: Date().addingTimeInterval(-100), // Newest
                role: .assistant,
                content: [ContentBlock.text("Message C")],
                depth: 1,
                llmInstanceId: customInstanceId,
                userFeedback: .none,
            ),
            Message(
                sessionId: complexTestSessionId,
                parentId: nil,
                timestamp: Date().addingTimeInterval(-150),
                role: .assistant,
                content: [ContentBlock.text("Message D")],
                depth: 1,
                llmInstanceId: claudeInstanceId,
                userFeedback: .liked
            ),
        ]
        
        // When - Test the cache methods directly
        // Since the methods were moved to MessageTreeCache, we need to test them differently
        // Add messages to trigger the sorting logic
        await useCase.addMultipleMessagesAndUpdateTree(
            sessionId: complexTestSessionId,
            messages: testMessages
        )
        
        // Then - Verify sorting results by checking the resulting layers
        let updatedLayers = await useCase.getMessageLayersForDisplay(sessionId: complexTestSessionId)
        let newAssistantLayer = updatedLayers.last!
        
        // Should filter out disliked message
        XCTAssertEqual(newAssistantLayer.siblingMessages.count, 4, "Keep disliked message")
        
        // Verify the highest priority message is active
        XCTAssertEqual(newAssistantLayer.activeMessageId, testMessages[1].id, "Message with liked + isReplied should be active")
        
        // Verify sorting order in displayed messages
        let displayedMessages = newAssistantLayer.siblingMessages
        XCTAssertEqual(displayedMessages[0].id, testMessages[1].id, "Highest priority (liked + isReplied) should be first")
        
        // Verify priority-based ordering
        for i in 1..<displayedMessages.count {
            let prevPriority = calculatePriority(displayedMessages[i-1])
            let currPriority = calculatePriority(displayedMessages[i])
            XCTAssertGreaterThanOrEqual(prevPriority, currPriority, "Priority should be in descending order")
        }
        
        print("📋 Cache sorting test: \(testMessages.count) messages -> \(displayedMessages.count) filtered/sorted")
        print("   Priority order: \(displayedMessages.map { "\($0.userFeedback.rawValue)\($0.isReplied ? "+replied" : "")" }.joined(separator: " -> "))")
    }
    
    // Helper method for testing priority calculation
    private func calculatePriority(_ message: Message) -> Int {
        var score = 0
        if message.isReplied { score += 100 }
        switch message.userFeedback {
        case .liked: score += 10
        case .none: score += 5
        case .disliked: score += 0
        }
        return score
    }
}
