import XCTest
@testable import lavachat

final class ShareableItemTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var chatRepository: ChatRepository!
    var llmRepository: LLMInstanceRepository!
    var testUserId: UUID!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        let container = DIContainer(context: PersistenceController.shared.container.viewContext)
        chatRepository = container.getSharedChatRepository()
        llmRepository = container.getSharedLLMInstanceRepository()
        testUserId = UUID()
    }
    
    override func tearDown() {
        super.tearDown()
        chatRepository = nil
        llmRepository = nil
        testUserId = nil
    }
    
    // MARK: - LLMProvider ShareableItem Tests
    
    func testLLMProviderShareableItem() async throws {
        // Given
        let provider = LLMProvider(
            name: "Test Provider",
            apiBaseUrl: "https://api.test.com",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: false,
            metadata: ["test": "value"]
        )

        // When & Then
        XCTAssertEqual(provider.shareContentType, .llmInstance)
        XCTAssertEqual(provider.shareDisplayName, "Test Provider")

        let shareableData = try await provider.toShareableData()
        XCTAssertEqual(shareableData.shareType, .llmInstance)
        XCTAssertNotNil(shareableData.data.provider)
        XCTAssertEqual(shareableData.data.provider?.id, provider.id)
        XCTAssertEqual(shareableData.data.provider?.name, provider.name)

        // Verify validation works
        try provider.validateForSharing()
    }
    
    // MARK: - LLMModel ShareableItem Tests
    
    func testLLMModelShareableItem() async throws {
        // Given
        let model = LLMModel(
            providerId: UUID(),
            modelIdentifier: "test-model-v1",
            name: "Test Model",
            contextWindowSize: 4096,
            maxOutputTokens: 2048,
            isUserCreated: true,
            isUserModified: false,
            metadata: ["version": "1.0"]
        )

        // When & Then
        XCTAssertEqual(model.shareContentType, .llmInstance)
        XCTAssertEqual(model.shareDisplayName, "Test Model")

        let shareableData = try await model.toShareableData()
        XCTAssertEqual(shareableData.shareType, .llmInstance)
        XCTAssertNotNil(shareableData.data.model)
        XCTAssertEqual(shareableData.data.model?.id, model.id)
        XCTAssertEqual(shareableData.data.model?.name, model.name)
        XCTAssertEqual(shareableData.data.model?.contextWindowSize, 4096)

        // Verify validation works
        try model.validateForSharing()
    }
    
    // MARK: - LLMInstance ShareableItem Tests
    
    func testLLMInstanceShareableItem() async throws {
        // Given
        let instance = LLMInstance(
            modelId: UUID(),
            name: "Test Instance",
            systemPrompt: "You are a helpful assistant",
            isUserModified: false,
            metadata: ["custom": "setting"]
        )

        // When & Then
        XCTAssertEqual(instance.shareContentType, .llmInstance)
        XCTAssertEqual(instance.shareDisplayName, "Test Instance")

        let shareableData = try await instance.toShareableData()
        XCTAssertEqual(shareableData.shareType, .llmInstance)
        XCTAssertNotNil(shareableData.data.instance)
        XCTAssertEqual(shareableData.data.instance?.id, instance.id)
        XCTAssertEqual(shareableData.data.instance?.name, instance.name)

        // Verify validation works
        try instance.validateForSharing()
    }
    
    // MARK: - MessageAction ShareableItem Tests
    
    func testMessageActionShareableItem() async throws {
        // Given
        let action = MessageAction(
            name: "Test Action",
            icon: "star.fill",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt"],
            targetLLMInstanceId: nil,
            metadata: ["category": "test"]
        )

        // When & Then
        XCTAssertEqual(action.shareContentType, .messageAction)
        XCTAssertEqual(action.shareDisplayName, "Test Action")

        let shareableData = try await action.toShareableData()
        XCTAssertEqual(shareableData.shareType, .messageAction)
        XCTAssertNotNil(shareableData.data.messageAction)
        XCTAssertEqual(shareableData.data.messageAction?.id, action.id)
        XCTAssertEqual(shareableData.data.messageAction?.name, action.name)
        XCTAssertEqual(shareableData.data.messageAction?.prompts, ["Test prompt"])

        // Verify validation works
        try action.validateForSharing()
    }

    // MARK: - ChatSessionSetting ShareableItem Tests

    func testChatSessionSettingShareableItem() async throws {
        // Given
        let messageActionSettings = MessageActionSettings(
            actionPanelActions: [],
            userMessageActions: [],
            assistantMessageCardActions: [UUID(), UUID()],
            assistantMessageMenuActions: []
        )

        let setting = ChatSessionSetting(
            name: "Test Setting",
            isSystemDefault: false,
            shouldExpandThinking: true,
            messageActionSettings: messageActionSettings,
            shouldAutoGenerateTitle: true,
            contextMessageCount: 10,
            metadata: ["test": "setting"]
        )

        // When & Then
        XCTAssertEqual(setting.shareContentType, .chatSessionSetting)
        XCTAssertEqual(setting.shareDisplayName, "Test Setting")

        let shareableData = try await setting.toShareableData()
        XCTAssertEqual(shareableData.shareType, .chatSessionSetting)
        XCTAssertNotNil(shareableData.data.chatSessionSetting)
        XCTAssertEqual(shareableData.data.chatSessionSetting?.id, setting.id)
        XCTAssertEqual(shareableData.data.chatSessionSetting?.name, setting.name)
        XCTAssertEqual(shareableData.data.chatSessionSetting?.shouldExpandThinking, true)

        // Verify validation works
        try setting.validateForSharing()
    }
    
    // MARK: - ChatSession ShareableItem Tests
    
    func testChatSessionShareableItem() async throws {
        // Given
        let session = ChatSession(
            title: "Test Chat Session",
            activeLLMInstanceIds: [UUID()],
            usedLLMInstanceIds: [UUID()],
            settingsId: UUID(),
            userId: testUserId,
            metadata: ["type": "test"]
        )

        // When & Then
        XCTAssertEqual(session.shareContentType, .chatSession)
        XCTAssertEqual(session.shareDisplayName, "Test Chat Session")

        let shareableData = try await session.toShareableData()
        XCTAssertEqual(shareableData.shareType, .chatSession)
        XCTAssertNotNil(shareableData.data.chatSession)
        XCTAssertEqual(shareableData.data.chatSession?.id, session.id)
        XCTAssertEqual(shareableData.data.chatSession?.title, session.title)
        XCTAssertEqual(shareableData.data.chatSession?.userId, testUserId)

        // Verify validation works
        try session.validateForSharing()
    }
    
    // MARK: - ShareableData Metadata Tests
    
    func testShareableDataMetadata() async throws {
        // Given
        let provider = LLMProvider(
            name: "Test Provider",
            apiBaseUrl: "https://api.test.com",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: false
        )

        // When
        let shareableData = try await provider.toShareableData()

        // Then
        XCTAssertNotNil(shareableData.metadata)
        XCTAssertNotNil(shareableData.metadata.appVersion)
        XCTAssertNotNil(shareableData.metadata.deviceInfo)
    }
}
