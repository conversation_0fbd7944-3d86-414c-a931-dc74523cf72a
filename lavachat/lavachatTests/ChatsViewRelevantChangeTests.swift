import XCTest
import Combine
@testable import lavachat

final class ChatsViewRelevantChangeTests: XCTestCase {
    private var cancellables = Set<AnyCancellable>()
    private var mockRepository: MockChatRepository!
    private var observeChatsChangesUseCase: ObserveChatsChangesUseCase!
    
    override func setUpWithError() throws {
        mockRepository = MockChatRepository()
        observeChatsChangesUseCase = ObserveChatsChangesUseCase(chatRepository: mockRepository)
    }

    override func tearDownWithError() throws {
        cancellables.removeAll()
        mockRepository = nil
        observeChatsChangesUseCase = nil
    }

    func testChatsViewRelevantChangeEnum() throws {
        // Test that ChatsViewRelevantChange enum has correct cases
        let sessionCreated = ChatsViewRelevantChange.sessionCreated
        let sessionDeleted = ChatsViewRelevantChange.sessionDeleted
        
        // Should be able to create instances
        XCTAssertNotNil(sessionCreated)
        XCTAssertNotNil(sessionDeleted)
    }

    func testObserveChatsViewRelevantChangesSessionCreated() async throws {
        let expectation = XCTestExpectation(description: "Session created notification")
        
        // Start observing
        observeChatsChangesUseCase.executeForChatsView()
            .sink { change in
                if case .sessionCreated = change {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // Create a session
        let session = MockChatPreviewData.exampleSession1
        try await mockRepository.createChatSession(session)
        
        await fulfillment(of: [expectation], timeout: 2.0)
    }

    func testObserveChatsViewRelevantChangesSessionDeleted() async throws {
        let expectation = XCTestExpectation(description: "Session deleted notification")
        
        // Create a session first
        let session = MockChatPreviewData.exampleSession1
        try await mockRepository.createChatSession(session)
        
        // Start observing
        observeChatsChangesUseCase.executeForChatsView()
            .sink { change in
                if case .sessionDeleted = change {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // Delete the session
        try await mockRepository.deleteChatSession(byId: session.id)
        
        await fulfillment(of: [expectation], timeout: 2.0)
    }

    func testBatchOperations() async throws {
        let sessionCreatedExpectation = XCTestExpectation(description: "Session created in batch")
        let sessionDeletedExpectation = XCTestExpectation(description: "Session deleted in batch")
        
        var receivedChanges: [ChatsViewRelevantChange] = []
        
        // Start observing
        observeChatsChangesUseCase.executeForChatsView()
            .sink { change in
                receivedChanges.append(change)
                
                switch change {
                case .sessionCreated:
                    sessionCreatedExpectation.fulfill()
                case .sessionDeleted:
                    sessionDeletedExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // Perform batch operations
        await mockRepository.beginBatchUpdate()
        
        let session1 = MockChatPreviewData.exampleSession1
        let session2 = MockChatPreviewData.exampleSession2
        
        try await mockRepository.createChatSession(session1)
        try await mockRepository.createChatSession(session2)
        try await mockRepository.deleteChatSession(byId: session1.id)
        
        try await mockRepository.endBatchUpdateAndPublish()
        
        await fulfillment(of: [sessionCreatedExpectation, sessionDeletedExpectation], timeout: 2.0)
        
        // Should receive consolidated notifications
        XCTAssertTrue(receivedChanges.contains(where: { if case .sessionCreated = $0 { return true }; return false }))
        XCTAssertTrue(receivedChanges.contains(where: { if case .sessionDeleted = $0 { return true }; return false }))
    }
}
