import XCTest
import UIKit
@testable import lavachat

final class MessageEditingUtilsTests: XCTestCase {
    
    func testMessageWithImageAndFileContent() throws {
        // Create test image data
        let testImageData = createTestImageData()
        
        // Create test file data
        let testFileData = "Test file content".data(using: .utf8)!
        let testFileName = "test.txt"
        
        // Create ImageInfo and FileInfo
        let imageInfo = ImageInfo(
            data: testImageData,
            mimeType: "image/jpeg",
            base64Data: testImageData.base64EncodedString(),
            fileSizeBytes: Int64(testImageData.count)
        )
        
        let fileInfo = FileInfo(
            fileName: testFileName,
            mimeType: "text/plain",
            base64Data: testFileData.base64EncodedString(),
            fileSizeBytes: Int64(testFileData.count),
            fileType: .document
        )
        
        // Create a test message with text, image, and file content
        let testMessage = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("Test message with attachments"),
                .image(imageInfo),
                .file(fileInfo)
            ],
            depth: 0
        )
        
        // Test that message has the expected content
        XCTAssertEqual(testMessage.content.count, 3)
        XCTAssertEqual(testMessage.textContent, "Test message with attachments")
        
        // Test extracting image blocks
        let imageBlocks = testMessage.content.compactMap { block in
            if case .image(let imageInfo) = block {
                return imageInfo
            }
            return nil
        }
        XCTAssertEqual(imageBlocks.count, 1)
        XCTAssertEqual(imageBlocks.first?.mimeType, "image/jpeg")
        XCTAssertEqual(imageBlocks.first?.fileSizeBytes, Int64(testImageData.count))
        
        // Test extracting file blocks
        let fileBlocks = testMessage.content.compactMap { block in
            if case .file(let fileInfo) = block {
                return fileInfo
            }
            return nil
        }
        XCTAssertEqual(fileBlocks.count, 1)
        XCTAssertEqual(fileBlocks.first?.fileName, testFileName)
        XCTAssertEqual(fileBlocks.first?.mimeType, "text/plain")
        XCTAssertEqual(fileBlocks.first?.fileType, .document)
    }
    
    func testMessageWithOnlyTextContent() throws {
        let testMessage = Message(
            sessionId: UUID(),
            role: .user,
            content: [.text("Simple text message")],
            depth: 0
        )
        
        XCTAssertEqual(testMessage.content.count, 1)
        XCTAssertEqual(testMessage.textContent, "Simple text message")
        
        // Test that no image or file blocks exist
        let imageBlocks = testMessage.content.compactMap { block in
            if case .image = block { return block }
            return nil
        }
        XCTAssertEqual(imageBlocks.count, 0)
        
        let fileBlocks = testMessage.content.compactMap { block in
            if case .file = block { return block }
            return nil
        }
        XCTAssertEqual(fileBlocks.count, 0)
    }
    
    func testImageInfoDataReconstruction() throws {
        let testImageData = createTestImageData()
        
        // Test with data field
        let imageInfoWithData = ImageInfo(
            data: testImageData,
            mimeType: "image/jpeg",
            fileSizeBytes: Int64(testImageData.count)
        )
        
        XCTAssertNotNil(imageInfoWithData.data)
        XCTAssertEqual(imageInfoWithData.data, testImageData)
        
        // Test with base64Data field
        let imageInfoWithBase64 = ImageInfo(
            mimeType: "image/jpeg",
            base64Data: testImageData.base64EncodedString(),
            fileSizeBytes: Int64(testImageData.count)
        )
        
        XCTAssertNotNil(imageInfoWithBase64.base64Data)
        if let base64Data = imageInfoWithBase64.base64Data,
           let reconstructedData = Data(base64Encoded: base64Data) {
            XCTAssertEqual(reconstructedData, testImageData)
        } else {
            XCTFail("Failed to reconstruct data from base64")
        }
    }
    
    func testFileInfoDataReconstruction() throws {
        let testFileData = "Test file content".data(using: .utf8)!
        let testFileName = "test.txt"
        
        let fileInfo = FileInfo(
            fileName: testFileName,
            mimeType: "text/plain",
            base64Data: testFileData.base64EncodedString(),
            fileSizeBytes: Int64(testFileData.count),
            fileType: .document
        )
        
        XCTAssertEqual(fileInfo.fileName, testFileName)
        XCTAssertEqual(fileInfo.mimeType, "text/plain")
        XCTAssertEqual(fileInfo.fileType, .document)
        
        // Test data reconstruction from base64
        if let reconstructedData = Data(base64Encoded: fileInfo.base64Data) {
            XCTAssertEqual(reconstructedData, testFileData)
            
            // Test content reconstruction
            if let reconstructedContent = String(data: reconstructedData, encoding: .utf8) {
                XCTAssertEqual(reconstructedContent, "Test file content")
            } else {
                XCTFail("Failed to reconstruct string content from data")
            }
        } else {
            XCTFail("Failed to reconstruct data from base64")
        }
    }
    
    func testEditingStateCreation() throws {
        let testMessage = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("Test message"),
                .image(ImageInfo(mimeType: "image/jpeg", base64Data: "test")),
                .file(FileInfo(fileName: "test.txt", mimeType: "text/plain", base64Data: "dGVzdA==", fileSizeBytes: 4, fileType: .document))
            ],
            depth: 0
        )
        
        let sourceRowViewModelId = UUID()
        let editingState = EditingState(message: testMessage, sourceRowViewModelId: sourceRowViewModelId)
        
        XCTAssertEqual(editingState.messageId, testMessage.id)
        XCTAssertEqual(editingState.originalText, "Test message")
        XCTAssertEqual(editingState.sourceRowViewModelId, sourceRowViewModelId)
        XCTAssertEqual(editingState.originalMessage.id, testMessage.id)
    }
    
    // MARK: - Helper Methods
    
    private func createTestImageData() -> Data {
        // Create a simple 1x1 pixel image data
        let image = UIImage(systemName: "photo") ?? UIImage()
        return image.jpegData(compressionQuality: 0.8) ?? Data()
    }
}
