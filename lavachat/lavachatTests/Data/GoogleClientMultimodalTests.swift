import XCTest
@testable import lavachat

final class GoogleClientMultimodalTests: XCTestCase {

    // MARK: - Message to Google Content Conversion Tests

    func testConvertTextOnlyMessage() {
        let sessionId = UUID()
        let message = Message(
            sessionId: sessionId,
            role: .user,
            content: [.text("Hello, world!")],
            depth: 0
        )

        let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
        let content = GoogleContent(role: "user", parts: parts)

        XCTAssertEqual(content.role, "user")
        XCTAssertEqual(content.parts.count, 1)
        XCTAssertEqual(content.parts[0].text, "Hello, world!")
        XCTAssertNil(content.parts[0].inlineData)
    }
    
    func testConvertImageMessage() {
        let sessionId = UUID()
        let imageInfo = ImageInfo(
            mimeType: "image/jpeg",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 1024
        )

        let message = Message(
            sessionId: sessionId,
            role: .user,
            content: [
                .text("Describe this image:"),
                .image(imageInfo)
            ],
            depth: 0
        )

        let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
        let content = GoogleContent(role: "user", parts: parts)

        XCTAssertEqual(content.role, "user")
        XCTAssertEqual(content.parts.count, 2)

        // Text part
        XCTAssertEqual(content.parts[0].text, "Describe this image:")
        XCTAssertNil(content.parts[0].inlineData)

        // Image part
        XCTAssertNil(content.parts[1].text)
        XCTAssertNotNil(content.parts[1].inlineData)
        XCTAssertEqual(content.parts[1].inlineData?.mimeType, "image/jpeg")
        XCTAssertEqual(content.parts[1].inlineData?.data, "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
    }
    
    func testConvertDocumentMessage() {
        let sessionId = UUID()
        let fileInfo = FileInfo(
            fileName: "document.pdf",
            mimeType: "application/pdf",
            base64Data: "JVBERi0xLjQKJcOkw7zDtsO4DQo=", // Sample PDF header in base64
            fileSizeBytes: 2048,
            fileType: .document
        )

        let message = Message(
            sessionId: sessionId,
            role: .user,
            content: [
                .text("Analyze this document:"),
                .file(fileInfo)
            ],
            depth: 0
        )

        let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
        let content = GoogleContent(role: "user", parts: parts)

        XCTAssertEqual(content.role, "user")
        XCTAssertEqual(content.parts.count, 2)

        // Text part
        XCTAssertEqual(content.parts[0].text, "Analyze this document:")
        XCTAssertNil(content.parts[0].inlineData)

        // Document part
        XCTAssertNil(content.parts[1].text)
        XCTAssertNotNil(content.parts[1].inlineData)
        XCTAssertEqual(content.parts[1].inlineData?.mimeType, "application/pdf")
        XCTAssertEqual(content.parts[1].inlineData?.data, "JVBERi0xLjQKJcOkw7zDtsO4DQo=")
    }
    
    func testConvertMixedContentMessage() {
        let sessionId = UUID()
        let imageInfo = ImageInfo(mimeType: "image/png", base64Data: "pngdata", fileSizeBytes: 512)
        let fileInfo = FileInfo(fileName: "report.pdf", mimeType: "application/pdf",
                               base64Data: "pdfdata", fileSizeBytes: 1024, fileType: .document)

        let message = Message(
            sessionId: sessionId,
            role: .user,
            content: [
                .text("Compare this image:"),
                .image(imageInfo),
                .text("With this document:"),
                .file(fileInfo),
                .text("What are the similarities?")
            ],
            depth: 0
        )

        let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
        let content = GoogleContent(role: "user", parts: parts)

        XCTAssertEqual(content.role, "user")
        XCTAssertEqual(content.parts.count, 5)

        // Verify the sequence of parts
        XCTAssertEqual(content.parts[0].text, "Compare this image:")
        XCTAssertEqual(content.parts[1].inlineData?.mimeType, "image/png")
        XCTAssertEqual(content.parts[2].text, "With this document:")
        XCTAssertEqual(content.parts[3].inlineData?.mimeType, "application/pdf")
        XCTAssertEqual(content.parts[4].text, "What are the similarities?")
    }
    
    func testConvertAssistantMessageWithThinking() {
        let sessionId = UUID()
        let message = Message(
            sessionId: sessionId,
            role: .assistant,
            content: [
                .thinking("Let me analyze this carefully..."),
                .text("Based on my analysis, I can see that...")
            ],
            depth: 1
        )

        let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
        let content = GoogleContent(role: "model", parts: parts)
        
        print(content)
        print(content.parts.count)

        XCTAssertEqual(content.role, "model")
        XCTAssertEqual(content.parts.count, 1)

        // Regular text part
        XCTAssertEqual(content.parts[0].text, "Based on my analysis, I can see that...")
        XCTAssertNil(content.parts[0].thought)
    }
    
    func testConvertMultipleContentTypes() {
        let sessionId = UUID()
        let imageInfo = ImageInfo(mimeType: "image/jpeg", base64Data: "imagedata", fileSizeBytes: 1024)

        let userMessage = Message(
            sessionId: sessionId,
            role: .user,
            content: [
                .text("What's in this image?"),
                .image(imageInfo)
            ],
            depth: 0
        )

        let assistantMessage = Message(
            sessionId: sessionId,
            role: .assistant,
            content: [
                .thinking("I can see an image has been provided..."),
                .text("I can see a beautiful landscape in this image.")
            ],
            depth: 1
        )

        // Test user message conversion
        let userParts = MessageContentExtractor.extractGoogleParts(from: userMessage.content)
        let userContent = GoogleContent(role: "user", parts: userParts)

        XCTAssertEqual(userContent.role, "user")
        XCTAssertEqual(userContent.parts.count, 2)
        XCTAssertEqual(userContent.parts[0].text, "What's in this image?")
        XCTAssertEqual(userContent.parts[1].inlineData?.mimeType, "image/jpeg")

        // Test assistant message conversion
        let assistantParts = MessageContentExtractor.extractGoogleParts(from: assistantMessage.content)
        let assistantContent = GoogleContent(role: "model", parts: assistantParts)

        XCTAssertEqual(assistantContent.role, "model")
        XCTAssertEqual(assistantContent.parts.count, 1)
        XCTAssertEqual(assistantContent.parts[0].text, "I can see a beautiful landscape in this image.")
    }

    func testSkipsEmptyContent() {
        let sessionId = UUID()
        let emptyMessage = Message(
            sessionId: sessionId,
            role: .user,
            content: [],
            depth: 0
        )

        let parts = MessageContentExtractor.extractGoogleParts(from: emptyMessage.content)

        XCTAssertEqual(parts.count, 0)
    }
}
