import XCTest
@testable import lavachat

final class MessageContentExtractorTests: XCTestCase {
    
    // MARK: - Text Content Extraction Tests
    
    func testExtractTextContentFromTextBlocks() {
        let contentBlocks: [ContentBlock] = [
            .text("Hello"),
            .text("World")
        ]
        
        let result = MessageContentExtractor.extractTextContent(from: contentBlocks)
        XCTAssertEqual(result, "Hello\n\nWorld")
    }
    
    func testExtractTextContentFromAttachedFileContent() {
        let contentBlocks: [ContentBlock] = [
            .attachedFileContent(fileName: "test.txt", content: "File content here")
        ]
        
        let result = MessageContentExtractor.extractTextContent(from: contentBlocks)
        XCTAssertEqual(result, "File: test.txt\n\nFile content here")
    }
    
    func testExtractTextContentIgnoresMultimodalBlocks() {
        let imageInfo = ImageInfo(mimeType: "image/jpeg", base64Data: "base64data")
        let fileInfo = FileInfo(fileName: "doc.pdf", mimeType: "application/pdf", 
                               base64Data: "pdfdata", fileSizeBytes: 1024, fileType: .document)
        
        let contentBlocks: [ContentBlock] = [
            .text("Hello"),
            .image(imageInfo),
            .file(fileInfo),
            .text("World")
        ]
        
        let result = MessageContentExtractor.extractTextContent(from: contentBlocks)
        XCTAssertEqual(result, "Hello\n\nWorld")
    }
    
    func testExtractTextContentFromEmptyBlocks() {
        let contentBlocks: [ContentBlock] = []
        
        let result = MessageContentExtractor.extractTextContent(from: contentBlocks)
        XCTAssertEqual(result, "")
    }
    
    // MARK: - Google Parts Extraction Tests
    
    func testExtractGooglePartsFromTextBlocks() {
        let contentBlocks: [ContentBlock] = [
            .text("Hello"),
            .text("World")
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 2)
        XCTAssertEqual(parts[0].text, "Hello")
        XCTAssertEqual(parts[1].text, "World")
        XCTAssertNil(parts[0].inlineData)
        XCTAssertNil(parts[1].inlineData)
    }
    
    func testExtractGooglePartsFromImageBlock() {
        let imageInfo = ImageInfo(mimeType: "image/jpeg", base64Data: "base64imagedata")
        let contentBlocks: [ContentBlock] = [
            .image(imageInfo)
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 1)
        XCTAssertNil(parts[0].text)
        XCTAssertNotNil(parts[0].inlineData)
        XCTAssertEqual(parts[0].inlineData?.mimeType, "image/jpeg")
        XCTAssertEqual(parts[0].inlineData?.data, "base64imagedata")
    }
    
    func testExtractGooglePartsFromFileBlock() {
        let fileInfo = FileInfo(fileName: "document.pdf", mimeType: "application/pdf",
                               base64Data: "base64pdfdata", fileSizeBytes: 2048, fileType: .document)
        let contentBlocks: [ContentBlock] = [
            .file(fileInfo)
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 1)
        XCTAssertNil(parts[0].text)
        XCTAssertNotNil(parts[0].inlineData)
        XCTAssertEqual(parts[0].inlineData?.mimeType, "application/pdf")
        XCTAssertEqual(parts[0].inlineData?.data, "base64pdfdata")
    }
    
    func testExtractGooglePartsFromAttachedFileContent() {
        let contentBlocks: [ContentBlock] = [
            .attachedFileContent(fileName: "config.json", content: "{\"key\": \"value\"}")
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 1)
        XCTAssertEqual(parts[0].text, "File: config.json\n\n{\"key\": \"value\"}")
        XCTAssertNil(parts[0].inlineData)
    }
    
    func testExtractGooglePartsFromThinkingBlock() {
        let contentBlocks: [ContentBlock] = [
            .thinking("Let me analyze this...")
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 0)
    }
    
    func testExtractGooglePartsFromMixedContent() {
        let imageInfo = ImageInfo(mimeType: "image/png", base64Data: "pngdata")
        let fileInfo = FileInfo(fileName: "report.pdf", mimeType: "application/pdf",
                               base64Data: "pdfdata", fileSizeBytes: 1024, fileType: .document)
        
        let contentBlocks: [ContentBlock] = [
            .text("Please analyze this image:"),
            .image(imageInfo),
            .text("And this document:"),
            .file(fileInfo),
            .thinking("I need to examine both carefully...")
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 4)
        
        // Text part
        XCTAssertEqual(parts[0].text, "Please analyze this image:")
        XCTAssertNil(parts[0].inlineData)
        
        // Image part
        XCTAssertNil(parts[1].text)
        XCTAssertEqual(parts[1].inlineData?.mimeType, "image/png")
        XCTAssertEqual(parts[1].inlineData?.data, "pngdata")
        
        // Text part
        XCTAssertEqual(parts[2].text, "And this document:")
        XCTAssertNil(parts[2].inlineData)
        
        // File part
        XCTAssertNil(parts[3].text)
        XCTAssertEqual(parts[3].inlineData?.mimeType, "application/pdf")
        XCTAssertEqual(parts[3].inlineData?.data, "pdfdata")
    }
    
    func testExtractGooglePartsSkipsEmptyContent() {
        let imageInfo = ImageInfo(mimeType: "image/jpeg", base64Data: "") // Empty base64
        let contentBlocks: [ContentBlock] = [
            .text(""), // Empty text
            .image(imageInfo), // Empty image data
            .text("Valid text")
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 1)
        XCTAssertEqual(parts[0].text, "Valid text")
    }
    
    func testExtractGooglePartsSkipsImageWithoutRequiredFields() {
        let imageInfoNoMimeType = ImageInfo(base64Data: "validdata") // Missing mimeType
        let imageInfoNoData = ImageInfo(mimeType: "image/jpeg") // Missing base64Data
        
        let contentBlocks: [ContentBlock] = [
            .image(imageInfoNoMimeType),
            .image(imageInfoNoData),
            .text("Valid text")
        ]
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 1)
        XCTAssertEqual(parts[0].text, "Valid text")
    }
    
    func testExtractGooglePartsFromEmptyBlocks() {
        let contentBlocks: [ContentBlock] = []
        
        let parts = MessageContentExtractor.extractGoogleParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 0)
    }
}
