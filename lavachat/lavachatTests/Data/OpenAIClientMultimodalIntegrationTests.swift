import XCTest
import os.log
@testable import lavachat

// IMPORTANT: To run these tests, you must provide your own OpenRouter API key.
// This key is for local testing only and should NOT be committed to version control.
// The tests will automatically be skipped if the key is not provided.
// Replace "YOUR_OPENROUTER_API_KEY_HERE" with your actual API key to run real API tests.
private let openRouterApiKey = "YOUR_OPENROUTER_API_KEY_HERE"

// MARK: - Mock KeychainRepository for OpenAI Testing

class OpenAIMockKeychainRepository: KeychainRepository {
    private let testApiKey: String

    init(testApiKey: String) {
        self.testApiKey = testApiKey
    }

    func getApiKey(for providerId: UUID) async throws -> String? {
        return testApiKey
    }

    func saveApiKey(_ apiKey: String, for providerId: UUID) async throws {
        // No-op for testing
    }

    func updateApiKey(_ apiKey: String, for providerId: UUID) async throws {
        // No-op for testing
    }

    func deleteApiKey(for providerId: UUID) async throws {
        // No-op for testing
    }

    func checkApiKeyExists(for providerId: UUID) async throws -> Bool {
        return !testApiKey.isEmpty
    }
}

/// Integration tests for OpenAI Compatible API multimodal functionality
/// These tests require real API keys and network connectivity
final class OpenAIClientMultimodalIntegrationTests: XCTestCase {

    private let logger = Logger(subsystem: "lavachat.tests", category: "OpenAIClientMultimodalIntegrationTests")

    private var client: OpenAICompatibleClient!
    private var keychainRepository: KeychainRepository!
    private var instanceContext: LLMInstanceContext!

    private var modelIdentifier: String = "deepseek/deepseek-chat-v3-0324:free"
    private var modelName: String = "DeepSeek V3 0324 Free"
    // private var modelIdentifier: String = "google/gemini-2.5-flash-lite-preview-06-17"
    // private var modelName: String = "Gemini 2.5 Flash Lite Preview 06-17"
    // private var modelIdentifier: String = "openai/gpt-4o-mini"
    // private var modelName: String = "GPT-4o-mini"
    
    override func setUp() async throws {
        try await super.setUp()

        // Initialize dependencies with mock keychain
        keychainRepository = OpenAIMockKeychainRepository(testApiKey: openRouterApiKey)
        client = OpenAICompatibleClient(keychainRepository: keychainRepository)
        
        // Set up test instance context for OpenRouter with DeepSeek R1
        let provider = LLMProvider(
            id: UUID(),
            name: "OpenRouter",
            apiBaseUrl: "https://openrouter.ai/api/v1",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .openaiCompatible
        )

        let model = LLMModel(
            id: UUID(),
            providerId: provider.id,
            modelIdentifier: modelIdentifier,
            name: modelName,
            contextWindowSize: 32768,
            maxOutputTokens: 4096
        )

        let instance = LLMInstance(
            id: UUID(),
            modelId: model.id,
            name: "Test OpenRouter Instance"
        )

        instanceContext = LLMInstanceContext(
            instance: instance,
            model: model,
            provider: provider
        )
        
        logger.info("Test setup completed with OpenRouter/DeepSeek R1 configuration")
    }
    
    override func tearDown() async throws {
        client = nil
        keychainRepository = nil
        instanceContext = nil
        try await super.tearDown()
    }
    
    // MARK: - Non-Network Tests
    
    func testClientSupportsOpenAICompatibleAPI() {
        XCTAssertTrue(client.canHandle(instanceContext: instanceContext))
        XCTAssertEqual(client.supportedAPIStyle, .openaiCompatible)
    }
    
    func testBuildOpenAIRequestWithTextOnly() throws {
        let sessionId = UUID()
        let messages = [
            Message(
                sessionId: sessionId,
                role: .user,
                content: [.text("Hello, how are you?")],
                depth: 0
            )
        ]
        
        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages,
            overrideSystemPrompt: "You are a helpful assistant."
        )
        
        // This should not throw
        let openaiRequest = try client.buildOpenAIRequest(from: request, instanceContext: instanceContext)
        
        XCTAssertEqual(openaiRequest.model, modelIdentifier)
        XCTAssertTrue(openaiRequest.stream)
        XCTAssertEqual(openaiRequest.messages.count, 2) // system + user message
        
        // Check system message
        XCTAssertEqual(openaiRequest.messages[0].role, "system")
        if case .text(let systemText) = openaiRequest.messages[0].content {
            XCTAssertEqual(systemText, "You are a helpful assistant.")
        } else {
            XCTFail("Expected text content for system message")
        }
        
        // Check user message
        XCTAssertEqual(openaiRequest.messages[1].role, "user")
        if case .text(let userText) = openaiRequest.messages[1].content {
            XCTAssertEqual(userText, "Hello, how are you?")
        } else {
            XCTFail("Expected text content for user message")
        }
    }
    
    func testBuildOpenAIRequestWithImage() throws {
        let sessionId = UUID()
        let imageInfo = ImageInfo(
            mimeType: "image/jpeg",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 1024
        )
        
        let messages = [
            Message(
                sessionId: sessionId,
                role: .user,
                content: [
                    .text("What do you see in this image?"),
                    .image(imageInfo)
                ],
                depth: 0
            )
        ]
        
        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages
        )
        
        let openaiRequest = try client.buildOpenAIRequest(from: request, instanceContext: instanceContext)
        
        XCTAssertEqual(openaiRequest.messages.count, 1) // Only user message
        
        // Check multimodal user message
        XCTAssertEqual(openaiRequest.messages[0].role, "user")
        if case .multimodal(let parts) = openaiRequest.messages[0].content {
            XCTAssertEqual(parts.count, 2)
            
            XCTAssertEqual(parts[0].type, "text")
            XCTAssertEqual(parts[0].text, "What do you see in this image?")
            
            XCTAssertEqual(parts[1].type, "image_url")
            XCTAssertEqual(parts[1].imageUrl?.url, "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
        } else {
            XCTFail("Expected multimodal content for user message")
        }
    }
    
    func testBuildOpenAIRequestWithFile() throws {
        let sessionId = UUID()
        let fileInfo = FileInfo(
            fileName: "test.pdf",
            mimeType: "application/pdf",
            base64Data: "JVBERi0xLjQKJcOkw7zDtsO4DQo=",
            fileSizeBytes: 2048,
            fileType: .document
        )
        
        let messages = [
            Message(
                sessionId: sessionId,
                role: .user,
                content: [
                    .text("Analyze this document:"),
                    .file(fileInfo)
                ],
                depth: 0
            )
        ]
        
        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages
        )
        
        let openaiRequest = try client.buildOpenAIRequest(from: request, instanceContext: instanceContext)
        
        XCTAssertEqual(openaiRequest.messages.count, 1)
        
        // Check multimodal user message with file
        XCTAssertEqual(openaiRequest.messages[0].role, "user")
        if case .multimodal(let parts) = openaiRequest.messages[0].content {
            XCTAssertEqual(parts.count, 2)
            
            XCTAssertEqual(parts[0].type, "text")
            XCTAssertEqual(parts[0].text, "Analyze this document:")
            
            XCTAssertEqual(parts[1].type, "file")
            XCTAssertEqual(parts[1].file?.filename, "test.pdf")
            XCTAssertEqual(parts[1].file?.fileData, "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4DQo=")
        } else {
            XCTFail("Expected multimodal content for user message")
        }
    }
    
    // MARK: - Real API Tests (Require API Key)
    
    func testRealAPITextOnlyRequest() async throws {
        // Skip this test if API key is not provided (but allow invalid keys for testing)
        try XCTSkipIf(openRouterApiKey == "YOUR_OPENROUTER_API_KEY_HERE" || openRouterApiKey.isEmpty,
                      "OpenRouter API key is not provided. Skipping real API tests.")
        
        let sessionId = UUID()
        let messages = [
            Message(
                sessionId: sessionId,
                role: .user,
                content: [.text("Say 'Hello from OpenAI Compatible API test' in exactly those words.")],
                depth: 0
            )
        ]
        
        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages,
            overrideSystemPrompt: "You are a helpful assistant. Follow instructions exactly."
        )
        
        logger.info("Starting real API text-only request test")
        
        var responses: [LLMStreamingResponse] = []
        let stream = client.sendStreamingRequest(request: request, instanceContext: instanceContext)
        
        do {
            for try await response in stream {
                responses.append(response)
                logger.info("Received response: \(String(describing: response))")

                if case .error(let error) = response.responseType {
                    logger.error("Received error response: \(error)")
                    XCTFail("Received error response: \(error)")
                    return
                }
            }
        } catch {
            logger.error("Stream failed with error: \(error)")
            XCTFail("Stream failed with error: \(error)")
            return
        }
        
        // Verify we received responses
        XCTAssertFalse(responses.isEmpty, "Should receive at least one response")
        
        // Check for content responses
        let contentResponses = responses.compactMap { response -> String? in
            if case .contentDelta(let content) = response.responseType {
                return content
            }
            return nil
        }
        
        XCTAssertFalse(contentResponses.isEmpty, "Should receive content responses")
        
        let fullContent = contentResponses.joined()
        logger.info("Full response content: \(fullContent)")
        
        // Verify the response contains expected text
        XCTAssertTrue(fullContent.contains("Hello from OpenAI Compatible API test"),
                     "Response should contain the requested text")
    }

    /// Test using the actual OpenAI Compatible Client with image content
    func testRealAPIWithImageContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(openRouterApiKey == "YOUR_OPENROUTER_API_KEY_HERE" || openRouterApiKey.isEmpty,
                      "OpenRouter API key is not provided. Skipping real API tests.")

        logger.info("Starting real API image content test")

        // Create test image content (small 1x1 PNG)
        let imageInfo = ImageInfo(
            mimeType: "image/png",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 68
        )

        let messages = [
            Message(
                sessionId: UUID(),
                role: .user,
                content: [
                    .text("What do you see in this image? Please describe it briefly."),
                    .image(imageInfo)
                ],
                depth: 0
            )
        ]

        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages,
            overrideSystemPrompt: "You are a helpful assistant that can analyze images."
        )

        var responses: [LLMStreamingResponse] = []
        let stream = client.sendStreamingRequest(request: request, instanceContext: instanceContext)

        do {
            for try await response in stream {
                responses.append(response)
                logger.info("Received response: \(String(describing: response))")

                if case .error(let error) = response.responseType {
                    XCTFail("Received error response: \(error)")
                    return
                }
            }
        } catch {
            XCTFail("Stream failed with error: \(error)")
            return
        }

        // Verify we received responses
        XCTAssertFalse(responses.isEmpty, "Should receive at least one response")

        // Check for content responses
        let contentResponses = responses.compactMap { response -> String? in
            if case .contentDelta(let content) = response.responseType {
                return content
            }
            return nil
        }

        XCTAssertFalse(contentResponses.isEmpty, "Should receive content responses")

        let fullContent = contentResponses.joined()
        logger.info("Full response content: \(fullContent)")

        // Verify the response is reasonable for image analysis
        XCTAssertFalse(fullContent.isEmpty, "Response should not be empty")
    }

    /// Test using the actual OpenAI Compatible Client with text file content
    ///  Openrouter does not support plain text, it expected to fail
    func testRealAPIWithTextFileContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(openRouterApiKey == "YOUR_OPENROUTER_API_KEY_HERE" || openRouterApiKey.isEmpty,
                      "OpenRouter API key is not provided. Skipping real API tests.")

        logger.info("Starting real API text file content test")

        // Create test text file content
        let testFileContent = "This is a test document.\nIt contains multiple lines.\nPlease analyze this content."
        let base64Content = Data(testFileContent.utf8).base64EncodedString()

        let fileInfo = FileInfo(
            fileName: "test_document.txt",
            mimeType: "text/plain",
            base64Data: base64Content,
            fileSizeBytes: Int64(testFileContent.utf8.count),
            fileType: .document
        )

        let messages = [
            Message(
                sessionId: UUID(),
                role: .user,
                content: [
                    .text("Please analyze the content of this text file and summarize it:"),
                    .file(fileInfo)
                ],
                depth: 0
            )
        ]

        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages,
            overrideSystemPrompt: "You are a helpful assistant that can analyze documents."
        )

        var responses: [LLMStreamingResponse] = []
        let stream = client.sendStreamingRequest(request: request, instanceContext: instanceContext)

        do {
            for try await response in stream {
                responses.append(response)
                logger.info("Received response: \(String(describing: response))")

                if case .error(let error) = response.responseType {
                    XCTFail("Received error response: \(error)")
                    return
                }
            }
        } catch {
            XCTFail("Stream failed with error: \(error)")
            return
        }

        // Verify we received responses
        XCTAssertFalse(responses.isEmpty, "Should receive at least one response")

        // Check for content responses
        let contentResponses = responses.compactMap { response -> String? in
            if case .contentDelta(let content) = response.responseType {
                return content
            }
            return nil
        }

        XCTAssertFalse(contentResponses.isEmpty, "Should receive content responses")

        let fullContent = contentResponses.joined()
        logger.info("Full response content: \(fullContent)")

        // Verify the response is reasonable for document analysis
        XCTAssertFalse(fullContent.isEmpty, "Response should not be empty")
    }

    /// Creates a simple PDF file for testing
    private func createTestPDFBase64() -> String {
        // Create a minimal PDF document
        // This is a very basic PDF structure that contains "Hello PDF Test"
        let pdfContent = """
        %PDF-1.4
        1 0 obj
        <<
        /Type /Catalog
        /Pages 2 0 R
        >>
        endobj

        2 0 obj
        <<
        /Type /Pages
        /Kids [3 0 R]
        /Count 1
        >>
        endobj

        3 0 obj
        <<
        /Type /Page
        /Parent 2 0 R
        /MediaBox [0 0 612 792]
        /Contents 4 0 R
        /Resources <<
        /Font <<
        /F1 5 0 R
        >>
        >>
        >>
        endobj

        4 0 obj
        <<
        /Length 44
        >>
        stream
        BT
        /F1 12 Tf
        100 700 Td
        (Hello PDF Test) Tj
        ET
        endstream
        endobj

        5 0 obj
        <<
        /Type /Font
        /Subtype /Type1
        /BaseFont /Helvetica
        >>
        endobj

        xref
        0 6
        0000000000 65535 f
        0000000009 00000 n
        0000000058 00000 n
        0000000115 00000 n
        0000000274 00000 n
        0000000369 00000 n
        trailer
        <<
        /Size 6
        /Root 1 0 R
        >>
        startxref
        447
        %%EOF
        """

        let data = pdfContent.data(using: .utf8)!
        return data.base64EncodedString()
    }

    /// Test using the actual OpenAI Compatible Client with PDF content
    func testRealAPIWithPDFContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(openRouterApiKey == "YOUR_OPENROUTER_API_KEY_HERE" || openRouterApiKey.isEmpty,
                      "OpenRouter API key is not provided. Skipping real API tests.")

        logger.info("Starting real API PDF content test")

        // Create minimal PDF content (simplified PDF header)
        let base64Content = createTestPDFBase64()

        let fileInfo = FileInfo(
            fileName: "test_document.pdf",
            mimeType: "application/pdf",
            base64Data: base64Content,
            fileSizeBytes: Int64(base64Content.count),
            fileType: .document
        )

        let messages = [
            Message(
                sessionId: UUID(),
                role: .user,
                content: [
                    .text("Please describe this PDF document:"),
                    .file(fileInfo)
                ],
                depth: 0
            )
        ]

        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages,
            overrideSystemPrompt: "You are a helpful assistant."
        )

        var responses: [LLMStreamingResponse] = []
        let stream = client.sendStreamingRequest(request: request, instanceContext: instanceContext)

        do {
            for try await response in stream {
                responses.append(response)
                logger.info("Received response: \(String(describing: response))")

                if case .error(let error) = response.responseType {
                    XCTFail("Received error response: \(error)")
                    return
                }
            }
        } catch {
            XCTFail("Stream failed with error: \(error)")
            return
        }

        // Verify we received responses
        XCTAssertFalse(responses.isEmpty, "Should receive at least one response")

        // Check for content responses
        let contentResponses = responses.compactMap { response -> String? in
            if case .contentDelta(let content) = response.responseType {
                return content
            }
            return nil
        }

        XCTAssertFalse(contentResponses.isEmpty, "Should receive content responses")

        let fullContent = contentResponses.joined()
        logger.info("Full response content: \(fullContent)")

        // Verify the response is reasonable for PDF analysis
        XCTAssertFalse(fullContent.isEmpty, "Response should not be empty")
    }

    /// Test using the actual OpenAI Compatible Client with mixed content (text + image + file)
    ///  Openrouter does not support plain text, it expected to fail 
    func testRealAPIWithMixedContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(openRouterApiKey == "YOUR_OPENROUTER_API_KEY_HERE" || openRouterApiKey.isEmpty,
                      "OpenRouter API key is not provided. Skipping real API tests.")

        logger.info("Starting real API mixed content test")

        // Create test image content (small 1x1 PNG)
        let imageInfo = ImageInfo(
            mimeType: "image/png",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 68
        )

        // Create test text file content
        let testFileContent = "Analysis Report:\n- Key findings\n- Recommendations\n- Conclusion"
        let base64Content = Data(testFileContent.utf8).base64EncodedString()

        let fileInfo = FileInfo(
            fileName: "analysis_report.txt",
            mimeType: "text/plain",
            base64Data: base64Content,
            fileSizeBytes: Int64(testFileContent.utf8.count),
            fileType: .document
        )

        let messages = [
            Message(
                sessionId: UUID(),
                role: .user,
                content: [
                    .text("Please analyze both the image and the document, then provide a comprehensive summary:"),
                    .image(imageInfo),
                    .text("Here's the related document:"),
                    .file(fileInfo),
                    .text("What insights can you provide from both sources?")
                ],
                depth: 0
            )
        ]

        let request = LLMStreamingRequest(
            id: UUID(),
            instanceId: UUID(),
            messageHistory: messages,
            overrideSystemPrompt: "You are a helpful assistant that can analyze multiple types of content including images and documents."
        )

        var responses: [LLMStreamingResponse] = []
        let stream = client.sendStreamingRequest(request: request, instanceContext: instanceContext)

        do {
            for try await response in stream {
                responses.append(response)
                logger.info("Received response: \(String(describing: response))")

                if case .error(let error) = response.responseType {
                    XCTFail("Received error response: \(error)")
                    return
                }
            }
        } catch {
            XCTFail("Stream failed with error: \(error)")
            return
        }

        // Verify we received responses
        XCTAssertFalse(responses.isEmpty, "Should receive at least one response")

        // Check for content responses
        let contentResponses = responses.compactMap { response -> String? in
            if case .contentDelta(let content) = response.responseType {
                return content
            }
            return nil
        }

        XCTAssertFalse(contentResponses.isEmpty, "Should receive content responses")

        let fullContent = contentResponses.joined()
        logger.info("Full response content: \(fullContent)")

        // Verify the response is reasonable for mixed content analysis
        XCTAssertFalse(fullContent.isEmpty, "Response should not be empty")
    }
}
