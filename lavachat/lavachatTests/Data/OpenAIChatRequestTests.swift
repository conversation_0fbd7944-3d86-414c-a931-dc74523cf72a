import XCTest
@testable import lavachat

final class OpenAIChatRequestTests: XCTestCase {
    
    func testOpenAIChatRequestWithAdditionalParameters() throws {
        // Given
        let messages = [
            OpenAIMessage(role: "user", text: "Hello")
        ]
        
        let additionalParams: [String: AnyCodable] = [
            "reasoning_effort": AnyCodable("high"),
            "thinking_budget": AnyCodable(1000),
            "custom_param": AnyCodable(true)
        ]
        
        let request = OpenAIChatRequest(
            model: "deepseek-reasoner",
            messages: messages,
            stream: true,
            temperature: 0.7,
            maxTokens: 2048,
            additionalParameters: additionalParams
        )
        
        // When
        let encoder = JSONEncoder()
        encoder.keyEncodingStrategy = .convertToSnakeCase
        let data = try encoder.encode(request)
        
        // Then
        let jsonObject = try JSONSerialization.jsonObject(with: data, options: []) as! [String: Any]

        print("jsonObject: \(jsonObject)")
        
        // Verify predefined parameters
        XCTAssertEqual(jsonObject["model"] as? String, "deepseek-reasoner")
        XCTAssertEqual(jsonObject["stream"] as? Bool, true)
        XCTAssertEqual(jsonObject["temperature"] as? Double, 0.7)
        XCTAssertEqual(jsonObject["max_tokens"] as? Int, 2048)
        
        // Verify additional parameters are flattened to root level
        XCTAssertEqual(jsonObject["reasoning_effort"] as? String, "high")
        XCTAssertEqual(jsonObject["thinking_budget"] as? Int, 1000)
        XCTAssertEqual(jsonObject["custom_param"] as? Bool, true)
        
        // Verify messages array
        let messagesArray = jsonObject["messages"] as! [[String: Any]]
        XCTAssertEqual(messagesArray[0]["role"] as? String, "user")
        XCTAssertEqual(messagesArray[0]["content"] as? String, "Hello")
    }
    
    func testOpenAIChatRequestDecoding() throws {
        // Given
        let jsonString = """
        {
            "model": "gpt-4",
            "messages": [{"role": "user", "content": "Test"}],
            "stream": true,
            "temperature": 0.8,
            "max_tokens": 1024,
            "reasoning_effort": "medium",
            "custom_setting": 42,
            "enable_feature": true
        }
        """
        
        // When
        let data = jsonString.data(using: .utf8)!
        let decoder = JSONDecoder()
        let request = try decoder.decode(OpenAIChatRequest.self, from: data)

        print("request: \(request)")
        
        // Then
        XCTAssertEqual(request.model, "gpt-4")
        XCTAssertEqual(request.stream, true)
        XCTAssertEqual(request.temperature, 0.8)
        XCTAssertEqual(request.maxTokens, 1024)
        
        // Verify additional parameters
        let additionalParams = request.additionalParameters!
        XCTAssertEqual(additionalParams["reasoning_effort"]?.stringValue, "medium")
        XCTAssertEqual(additionalParams["custom_setting"]?.intValue, 42)
        XCTAssertEqual(additionalParams["enable_feature"]?.boolValue, true)
    }
} 
