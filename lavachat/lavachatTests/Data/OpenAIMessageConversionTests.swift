import XCTest
@testable import lavachat

/// Tests for converting domain Message objects to OpenAI API format
final class OpenAIMessageConversionTests: XCTestCase {
    
    // MARK: - Text-Only Message Conversion Tests
    
    func testConvertTextOnlyUserMessage() {
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [.text("Hello, how are you?")],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "Hello, how are you?")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testConvertTextOnlyAssistantMessage() {
        let message = Message(
            sessionId: UUID(),
            role: .assistant,
            content: [.text("I'm doing well, thank you!")],
            depth: 1
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "assistant")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "I'm doing well, thank you!")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testConvertTextOnlySystemMessage() {
        let message = Message(
            sessionId: UUID(),
            role: .system,
            content: [.text("You are a helpful assistant.")],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "system")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "You are a helpful assistant.")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testConvertMultipleTextBlocks() {
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("First part."),
                .text("Second part.")
            ],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "First part.\n\nSecond part.")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    // MARK: - Multimodal Message Conversion Tests
    
    func testConvertMessageWithImage() {
        let imageInfo = ImageInfo(
            mimeType: "image/jpeg",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 1024
        )
        
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("What do you see in this image?"),
                .image(imageInfo)
            ],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .multimodal(let parts) = openaiMessage.content {
            XCTAssertEqual(parts.count, 2)
            
            // Check text part
            XCTAssertEqual(parts[0].type, "text")
            XCTAssertEqual(parts[0].text, "What do you see in this image?")
            
            // Check image part
            XCTAssertEqual(parts[1].type, "image_url")
            XCTAssertEqual(parts[1].imageUrl?.url, "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
        } else {
            XCTFail("Expected multimodal content")
        }
    }
    
    func testConvertMessageWithFile() {
        let fileInfo = FileInfo(
            fileName: "document.pdf",
            mimeType: "application/pdf",
            base64Data: "JVBERi0xLjQKJcOkw7zDtsO4DQo=",
            fileSizeBytes: 2048,
            fileType: .document
        )
        
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("Please analyze this document:"),
                .file(fileInfo)
            ],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .multimodal(let parts) = openaiMessage.content {
            XCTAssertEqual(parts.count, 2)
            
            // Check text part
            XCTAssertEqual(parts[0].type, "text")
            XCTAssertEqual(parts[0].text, "Please analyze this document:")
            
            // Check file part
            XCTAssertEqual(parts[1].type, "file")
            XCTAssertEqual(parts[1].file?.filename, "document.pdf")
            XCTAssertEqual(parts[1].file?.fileData, "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4DQo=")
        } else {
            XCTFail("Expected multimodal content")
        }
    }
    
    func testConvertMessageWithImageAndFile() {
        let imageInfo = ImageInfo(
            mimeType: "image/png",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 1024
        )
        
        let fileInfo = FileInfo(
            fileName: "report.pdf",
            mimeType: "application/pdf",
            base64Data: "JVBERi0xLjQKJcOkw7zDtsO4DQo=",
            fileSizeBytes: 2048,
            fileType: .document
        )
        
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("Compare this image with the document:"),
                .image(imageInfo),
                .text("Analysis:"),
                .file(fileInfo)
            ],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .multimodal(let parts) = openaiMessage.content {
            XCTAssertEqual(parts.count, 4)
            
            XCTAssertEqual(parts[0].type, "text")
            XCTAssertEqual(parts[0].text, "Compare this image with the document:")
            
            XCTAssertEqual(parts[1].type, "image_url")
            XCTAssertEqual(parts[1].imageUrl?.url, "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
            
            XCTAssertEqual(parts[2].type, "text")
            XCTAssertEqual(parts[2].text, "Analysis:")
            
            XCTAssertEqual(parts[3].type, "file")
            XCTAssertEqual(parts[3].file?.filename, "report.pdf")
            XCTAssertEqual(parts[3].file?.fileData, "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4DQo=")
        } else {
            XCTFail("Expected multimodal content")
        }
    }
    
    // MARK: - Role Conversion Tests
    
    func testConvertMergedAssistantRole() {
        let message = Message(
            sessionId: UUID(),
            role: .mergedAssistant,
            content: [.text("Merged response")],
            depth: 1
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "assistant")
    }
    
    func testConvertToolRole() {
        let message = Message(
            sessionId: UUID(),
            role: .tool,
            content: [.text("Tool result")],
            depth: 1
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "tool")
    }
    
    // MARK: - Edge Cases
    
    func testConvertEmptyMessage() {
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "No content")
        } else {
            XCTFail("Expected text content with 'No content'")
        }
    }
    
    func testConvertMessageWithOnlyEmptyText() {
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [.text(""), .text("   ")],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "\n\n   ")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testConvertMessageWithInvalidImage() {
        let imageInfo = ImageInfo() // No base64Data or mimeType
        
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("Valid text"),
                .image(imageInfo)
            ],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        // Should fall back to text-only since image is invalid
        XCTAssertEqual(openaiMessage.role, "user")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "Valid text")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testConvertMessageWithThinkingContent() {
        let message = Message(
            sessionId: UUID(),
            role: .assistant,
            content: [
                .text("Let me answer that."),
                .thinking("I need to think about this carefully..."),
                .text("Here's my response.")
            ],
            depth: 1
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "assistant")
        if case .text(let text) = openaiMessage.content {
            // Thinking content should be filtered out in text extraction
            XCTAssertEqual(text, "Let me answer that.\n\nHere's my response.")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testConvertMessageWithAttachedFileContent() {
        let message = Message(
            sessionId: UUID(),
            role: .user,
            content: [
                .text("Here's the file:"),
                .attachedFileContent(fileName: "data.txt", content: "File content here")
            ],
            depth: 0
        )
        
        let openaiMessage = message.toOpenAIMessage()
        
        XCTAssertEqual(openaiMessage.role, "user")
        if case .text(let text) = openaiMessage.content {
            XCTAssertEqual(text, "Here's the file:\n\nFile: data.txt\n\nFile content here")
        } else {
            XCTFail("Expected text content")
        }
    }
}
