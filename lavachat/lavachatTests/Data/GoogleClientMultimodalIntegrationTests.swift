import XCTest
import os
@testable import lavachat

// IMPORTANT: To run these tests, you must provide your own Google API key.
// This key is for local testing only and should NOT be committed to version control.
// The tests will automatically be skipped if the key is not provided.
// Replace "YOUR_GOOGLE_API_KEY_HERE" with your actual API key to run real API tests.
// Replace "fake_api_key_for_testing" with your actual API key to run real API tests.
private let googleApiKey = "YOUR_GOOGLE_API_KEY_HERE"

// MARK: - Mock KeychainRepository for Testing

class MockKeychainRepository: KeychainRepository {
    private let testApiKey: String

    init(testApiKey: String) {
        self.testApiKey = testApiKey
    }

    func getApiKey(for providerId: UUID) async throws -> String? {
        return testApiKey
    }

    func saveApiKey(_ apiKey: String, for providerId: UUID) async throws {
        // No-op for testing
    }

    func updateApiKey(_ apiKey: String, for providerId: UUID) async throws {
        // No-op for testing
    }

    func deleteApiKey(for providerId: UUID) async throws {
        // No-op for testing
    }

    func checkApiKeyExists(for providerId: UUID) async throws -> Bool {
        return !testApiKey.isEmpty
    }
}

final class GoogleClientMultimodalIntegrationTests: XCTestCase {

    private let logger = Logger(subsystem: "lavachat.tests", category: "GoogleClientMultimodalIntegrationTests")
    private var testModelIdentifier: String!
    private var streamingUrl: URL!
    
    override func setUpWithError() throws {
        try super.setUpWithError()

        testModelIdentifier = "gemini-2.5-flash"
        if let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/\(testModelIdentifier!):streamGenerateContent?key=\(googleApiKey)&alt=sse") {
            streamingUrl = url
        } else {
            XCTFail("Failed to create test URL.")
        }
    }
    
    // MARK: - Test Data Creation
    
    /// Creates a small test image in base64 format (1x1 pixel PNG)
    private func createTestImageBase64() -> String {
        // 1x1 pixel red PNG image in base64
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    }
    
    /// Creates a small test text file in base64 format
    private func createTestTextFileBase64() -> String {
        let testText = "This is a test document for multimodal testing.\nIt contains some sample text content."
        let data = testText.data(using: .utf8)!
        return data.base64EncodedString()
    }

    /// Creates a simple PDF file for testing
    private func createTestPDFBase64() -> String {
        // Create a minimal PDF document
        // This is a very basic PDF structure that contains "Hello PDF Test"
        let pdfContent = """
        %PDF-1.4
        1 0 obj
        <<
        /Type /Catalog
        /Pages 2 0 R
        >>
        endobj

        2 0 obj
        <<
        /Type /Pages
        /Kids [3 0 R]
        /Count 1
        >>
        endobj

        3 0 obj
        <<
        /Type /Page
        /Parent 2 0 R
        /MediaBox [0 0 612 792]
        /Contents 4 0 R
        /Resources <<
        /Font <<
        /F1 5 0 R
        >>
        >>
        >>
        endobj

        4 0 obj
        <<
        /Length 44
        >>
        stream
        BT
        /F1 12 Tf
        100 700 Td
        (Hello PDF Test) Tj
        ET
        endstream
        endobj

        5 0 obj
        <<
        /Type /Font
        /Subtype /Type1
        /BaseFont /Helvetica
        >>
        endobj

        xref
        0 6
        0000000000 65535 f
        0000000009 00000 n
        0000000058 00000 n
        0000000115 00000 n
        0000000274 00000 n
        0000000369 00000 n
        trailer
        <<
        /Size 6
        /Root 1 0 R
        >>
        startxref
        447
        %%EOF
        """

        let data = pdfContent.data(using: .utf8)!
        return data.base64EncodedString()
    }
    
    /// Creates a test request body with image content
    private func buildImageTestRequestBody() -> Data {
        let imageBase64 = createTestImageBase64()
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "role": "user",
                    "parts": [
                        ["text": "What do you see in this image?"],
                        [
                            "inlineData": [
                                "mimeType": "image/png",
                                "data": imageBase64
                            ]
                        ]
                    ]
                ]
            ],
            "generationConfig": [
                "maxOutputTokens": 1024,
                "temperature": 0.7
            ]
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody, options: .prettyPrinted)
            logger.info("Image test request body: \(String(data: jsonData, encoding: .utf8) ?? "Failed to encode")")
            return jsonData
        } catch {
            XCTFail("Failed to create image test request body: \(error)")
            return Data()
        }
    }
    
    /// Creates a test request body with text file content
    private func buildTextFileTestRequestBody() -> Data {
        let fileBase64 = createTestTextFileBase64()
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "role": "user",
                    "parts": [
                        ["text": "Please summarize the content of this document:"],
                        [
                            "inlineData": [
                                "mimeType": "text/plain",
                                "data": fileBase64
                            ]
                        ]
                    ]
                ]
            ],
            "generationConfig": [
                "maxOutputTokens": 1024,
                "temperature": 0.7
            ]
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody, options: .prettyPrinted)
            logger.info("Text file test request body: \(String(data: jsonData, encoding: .utf8) ?? "Failed to encode")")
            return jsonData
        } catch {
            XCTFail("Failed to create text file test request body: \(error)")
            return Data()
        }
    }
    
    /// Creates a test request body with mixed content (text + image + file)
    private func buildMixedContentTestRequestBody() -> Data {
        let imageBase64 = createTestImageBase64()
        let fileBase64 = createTestTextFileBase64()
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "role": "user",
                    "parts": [
                        ["text": "I'm sending you an image and a text document. Please analyze both:"],
                        [
                            "inlineData": [
                                "mimeType": "image/png",
                                "data": imageBase64
                            ]
                        ],
                        ["text": "Here's the document:"],
                        [
                            "inlineData": [
                                "mimeType": "text/plain",
                                "data": fileBase64
                            ]
                        ],
                        ["text": "What can you tell me about these?"]
                    ]
                ]
            ],
            "generationConfig": [
                "maxOutputTokens": 1024,
                "temperature": 0.7
            ]
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody, options: .prettyPrinted)
            logger.info("Mixed content test request body: \(String(data: jsonData, encoding: .utf8) ?? "Failed to encode")")
            return jsonData
        } catch {
            XCTFail("Failed to create mixed content test request body: \(error)")
            return Data()
        }
    }
    
    // MARK: - Test Cases

    /// Test multimodal request body construction without making actual API calls
    func testMultimodalRequestBodyConstruction() {
        // This test doesn't require an API key, so we don't skip it
        logger.info("Testing multimodal request body construction")

        // Test image request body
        let imageRequestBody = buildImageTestRequestBody()
        XCTAssertFalse(imageRequestBody.isEmpty, "Image request body should not be empty")

        if let imageJson = try? JSONSerialization.jsonObject(with: imageRequestBody) as? [String: Any],
           let contents = imageJson["contents"] as? [[String: Any]],
           let firstContent = contents.first,
           let parts = firstContent["parts"] as? [[String: Any]] {

            // Should have text part and inline data part
            XCTAssertEqual(parts.count, 2, "Should have 2 parts: text and inline data")

            let textPart = parts[0]
            let imagePart = parts[1]

            XCTAssertNotNil(textPart["text"], "First part should contain text")
            XCTAssertNotNil(imagePart["inlineData"], "Second part should contain inline data")

            if let inlineData = imagePart["inlineData"] as? [String: Any] {
                XCTAssertEqual(inlineData["mimeType"] as? String, "image/png", "Should have correct MIME type")
                XCTAssertNotNil(inlineData["data"], "Should have base64 data")
            }
        } else {
            XCTFail("Failed to parse image request body JSON")
        }

        // Test text file request body
        let fileRequestBody = buildTextFileTestRequestBody()
        XCTAssertFalse(fileRequestBody.isEmpty, "File request body should not be empty")

        // Test mixed content request body
        let mixedRequestBody = buildMixedContentTestRequestBody()
        XCTAssertFalse(mixedRequestBody.isEmpty, "Mixed content request body should not be empty")

        if let mixedJson = try? JSONSerialization.jsonObject(with: mixedRequestBody) as? [String: Any],
           let contents = mixedJson["contents"] as? [[String: Any]],
           let firstContent = contents.first,
           let parts = firstContent["parts"] as? [[String: Any]] {

            // Should have 5 parts: text, image, text, file, text
            XCTAssertEqual(parts.count, 5, "Should have 5 parts in mixed content")

            let hasImagePart = parts.contains { part in
                if let inlineData = part["inlineData"] as? [String: Any],
                   let mimeType = inlineData["mimeType"] as? String {
                    return mimeType == "image/png"
                }
                return false
            }

            let hasTextFilePart = parts.contains { part in
                if let inlineData = part["inlineData"] as? [String: Any],
                   let mimeType = inlineData["mimeType"] as? String {
                    return mimeType == "text/plain"
                }
                return false
            }

            XCTAssertTrue(hasImagePart, "Mixed content should contain image part")
            XCTAssertTrue(hasTextFilePart, "Mixed content should contain text file part")
        } else {
            XCTFail("Failed to parse mixed content request body JSON")
        }

        logger.info("Multimodal request body construction test completed successfully")
    }

    // MARK: - Real GoogleClient Tests

    /// Test using the actual GoogleClient with image content
    func testGoogleClientWithImageContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping real API tests.")

        // Create test content blocks
        let imageBase64 = createTestImageBase64()
        let imageInfo = ImageInfo(mimeType: "image/png", base64Data: imageBase64)
        let imageBlock = ContentBlock.image(imageInfo)
        let textBlock = ContentBlock.text("What do you see in this image?")

        try await performGoogleClientTest(
            testName: "Image Content",
            contentBlocks: [textBlock, imageBlock],
            expectedContentKeywords: ["image", "pixel", "color"]
        )
    }

    /// Test using the actual GoogleClient with text file content
    func testGoogleClientWithTextFileContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping real API tests.")

        // Create test content blocks
        let fileBase64 = createTestTextFileBase64()
        let fileInfo = FileInfo(
            fileName: "test.txt",
            mimeType: "text/plain",
            base64Data: fileBase64,
            fileSizeBytes: Int64(fileBase64.count),
            fileType: .document
        )
        let fileBlock = ContentBlock.file(fileInfo)
        let textBlock = ContentBlock.text("Please summarize the content of this document:")

        try await performGoogleClientTest(
            testName: "Text File Content",
            contentBlocks: [textBlock, fileBlock],
            expectedContentKeywords: ["test", "document", "content"]
        )
    }

    /// Test using the actual GoogleClient with PDF content
    func testGoogleClientWithPDFContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping real API tests.")

        // Create test content blocks
        let pdfBase64 = createTestPDFBase64()
        let pdfInfo = FileInfo(
            fileName: "test.pdf",
            mimeType: "application/pdf",
            base64Data: pdfBase64,
            fileSizeBytes: Int64(pdfBase64.count),
            fileType: .document
        )
        let pdfBlock = ContentBlock.file(pdfInfo)
        let textBlock = ContentBlock.text("Please analyze this PDF document and tell me what it contains:")

        try await performGoogleClientTest(
            testName: "PDF Content",
            contentBlocks: [textBlock, pdfBlock],
            expectedContentKeywords: ["pdf", "document", "content"]
        )
    }

    /// Test using the actual GoogleClient with mixed content (text + image + file)
    func testGoogleClientWithMixedContent() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping real API tests.")
        
        // Create test content blocks
        let imageBase64 = createTestImageBase64()
        let imageInfo = ImageInfo(mimeType: "image/png", base64Data: imageBase64)
        let imageBlock = ContentBlock.image(imageInfo)

        let fileBase64 = createTestTextFileBase64()
        let fileInfo = FileInfo(
            fileName: "test.txt",
            mimeType: "text/plain",
            base64Data: fileBase64,
            fileSizeBytes: Int64(fileBase64.count),
            fileType: .document
        )
        let fileBlock = ContentBlock.file(fileInfo)

        let textBlocks = [
            ContentBlock.text("I'm sending you an image and a text document. Please analyze both:"),
            ContentBlock.text("Here's the document:"),
            ContentBlock.text("What can you tell me about these?")
        ]

        let allBlocks = [textBlocks[0], imageBlock, textBlocks[1], fileBlock, textBlocks[2]]

        try await performGoogleClientTest(
            testName: "Mixed Content",
            contentBlocks: allBlocks,
            expectedContentKeywords: ["image", "document", "content"]
        )
    }

    // MARK: - Helper Methods

    /// Creates common test infrastructure for GoogleClient tests
    private func createTestInfrastructure() -> (GoogleClient, LLMInstanceContext) {
        // Create mock keychain repository with test API key
        let mockKeychain = MockKeychainRepository(testApiKey: googleApiKey)
        let googleClient = GoogleClient(keychainRepository: mockKeychain)

        // Create test provider and instance context
        let testProvider = LLMProvider(
            id: UUID(),
            name: "Test Google Provider",
            apiBaseUrl: "https://generativelanguage.googleapis.com",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .google,
            apiEndpointPath: "/v1beta/models"
        )

        let testModel = LLMModel(
            id: UUID(),
            providerId: testProvider.id,
            modelIdentifier: "gemini-2.5-flash",
            name: "Gemini 2.5 Flash",
            isUserModified: false
        )

        let testInstance = LLMInstance(
            id: UUID(),
            modelId: testModel.id,
            name: "Test Gemini Instance",
            isUserModified: false
        )

        let instanceContext = LLMInstanceContext(
            instance: testInstance,
            model: testModel,
            provider: testProvider
        )

        return (googleClient, instanceContext)
    }

    /// Generic test runner for GoogleClient multimodal tests
    private func performGoogleClientTest(
        testName: String,
        contentBlocks: [ContentBlock],
        expectedContentKeywords: [String] = []
    ) async throws {
        logger.info("[\(testName)] Starting test")

        let (googleClient, instanceContext) = createTestInfrastructure()

        // Create test message
        let testMessage = Message(
            id: UUID(),
            sessionId: UUID(),
            role: .user,
            content: contentBlocks,
            depth: 0
        )

        // Create streaming request
        let streamingRequest = LLMStreamingRequest(
            instanceId: instanceContext.instance.id,
            messageHistory: [testMessage]
        )

        // Test the streaming request
        var receivedResponses: [LLMStreamingResponse] = []
        var streamError: Error?

        do {
            let stream = googleClient.sendStreamingRequest(
                request: streamingRequest,
                instanceContext: instanceContext
            )

            for try await response in stream {
                logger.info("[\(testName)] Received response: \(String(describing: response))")
                receivedResponses.append(response)

                // Break after receiving a few responses to avoid infinite loop
                if receivedResponses.count >= 5 {
                    break
                }
            }

            logger.info("[\(testName)] GoogleClient stream completed successfully")
        } catch {
            logger.error("[\(testName)] GoogleClient stream failed with error: \(error)")
            streamError = error
        }

        // Assertions
        if let error = streamError {
            // Check if it's an API key error (expected with fake key)
            let errorDescription = error.localizedDescription.lowercased()
            if errorDescription.contains("api") && errorDescription.contains("key") ||
               errorDescription.contains("unauthorized") ||
               errorDescription.contains("403") ||
               errorDescription.contains("401") {
                logger.info("[\(testName)] Received expected API key error: \(error)")
                XCTAssertTrue(true, "Successfully detected API key error as expected")
                return
            } else {
                XCTFail("[\(testName)] Unexpected error: \(error)")
                return
            }
        }

        XCTAssertFalse(receivedResponses.isEmpty, "[\(testName)] Should receive at least one response")

        // Check if we received content
        let hasContent = receivedResponses.contains { response in
            switch response.responseType {
            case .contentDelta(let content):
                return !content.isEmpty
            default:
                return false
            }
        }

        XCTAssertTrue(hasContent, "[\(testName)] Should receive content response from GoogleClient")

        logger.info("[\(testName)] Test completed successfully")
    }
}
