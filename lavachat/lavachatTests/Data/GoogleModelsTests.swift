import XCTest
import os
@testable import lavachat

final class GoogleModelsTests: XCTestCase {

    private let logger = Logger(subsystem: "lavachat.tests", category: "GoogleModelsTests")
    
    // MARK: - GooglePart Tests
    
    func testGooglePartTextInitialization() {
        let part = GooglePart(text: "Hello, world!")
        
        XCTAssertEqual(part.text, "Hello, world!")
        XCTAssertNil(part.thought)
        XCTAssertNil(part.inlineData)
    }
    
    func testGooglePartThinkingInitialization() {
        let part = GooglePart(text: "Let me think...", thought: true)
        
        XCTAssertEqual(part.text, "Let me think...")
        XCTAssertEqual(part.thought, true)
        XCTAssertNil(part.inlineData)
    }
    
    func testGooglePartInlineDataInitialization() {
        let inlineData = GoogleInlineData(mimeType: "image/jpeg", data: "base64data")
        let part = GooglePart(inlineData: inlineData)
        
        XCTAssertNil(part.text)
        XCTAssertNil(part.thought)
        XCTAssertNotNil(part.inlineData)
        XCTAssertEqual(part.inlineData?.mimeType, "image/jpeg")
        XCTAssertEqual(part.inlineData?.data, "base64data")
    }
    
    // MARK: - GoogleInlineData Tests
    
    func testGoogleInlineDataInitialization() {
        let inlineData = GoogleInlineData(mimeType: "image/png", data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
        
        XCTAssertEqual(inlineData.mimeType, "image/png")
        XCTAssertEqual(inlineData.data, "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
    }
    
    // MARK: - GoogleContent Tests
    
    func testGoogleContentWithTextPart() {
        let textPart = GooglePart(text: "Hello")
        let content = GoogleContent(role: "user", parts: [textPart])
        
        XCTAssertEqual(content.role, "user")
        XCTAssertEqual(content.parts.count, 1)
        XCTAssertEqual(content.parts.first?.text, "Hello")
    }
    
    func testGoogleContentWithMultipleParts() {
        let textPart = GooglePart(text: "Describe this image:")
        let inlineData = GoogleInlineData(mimeType: "image/jpeg", data: "base64imagedata")
        let imagePart = GooglePart(inlineData: inlineData)
        
        let content = GoogleContent(role: "user", parts: [textPart, imagePart])
        
        XCTAssertEqual(content.role, "user")
        XCTAssertEqual(content.parts.count, 2)
        XCTAssertEqual(content.parts[0].text, "Describe this image:")
        XCTAssertEqual(content.parts[1].inlineData?.mimeType, "image/jpeg")
        XCTAssertEqual(content.parts[1].inlineData?.data, "base64imagedata")
    }
    
    // MARK: - Codable Tests
    
    func testGooglePartCodable() throws {
        let originalPart = GooglePart(text: "Test text", thought: true)
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalPart)
        
        let decoder = JSONDecoder()
        let decodedPart = try decoder.decode(GooglePart.self, from: data)
        
        XCTAssertEqual(decodedPart.text, originalPart.text)
        XCTAssertEqual(decodedPart.thought, originalPart.thought)
        XCTAssertNil(decodedPart.inlineData)
    }
    
    func testGoogleInlineDataCodable() throws {
        let originalData = GoogleInlineData(mimeType: "image/png", data: "testdata")
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalData)
        
        let decoder = JSONDecoder()
        let decodedData = try decoder.decode(GoogleInlineData.self, from: data)
        
        XCTAssertEqual(decodedData.mimeType, originalData.mimeType)
        XCTAssertEqual(decodedData.data, originalData.data)
    }
    
    func testGooglePartWithInlineDataCodable() throws {
        let inlineData = GoogleInlineData(mimeType: "image/jpeg", data: "base64data")
        let originalPart = GooglePart(inlineData: inlineData)
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalPart)
        
        let decoder = JSONDecoder()
        let decodedPart = try decoder.decode(GooglePart.self, from: data)
        
        XCTAssertNil(decodedPart.text)
        XCTAssertNil(decodedPart.thought)
        XCTAssertNotNil(decodedPart.inlineData)
        XCTAssertEqual(decodedPart.inlineData?.mimeType, "image/jpeg")
        XCTAssertEqual(decodedPart.inlineData?.data, "base64data")
    }
    
    func testGoogleContentCodable() throws {
        let textPart = GooglePart(text: "Hello")
        let inlineData = GoogleInlineData(mimeType: "image/png", data: "imagedata")
        let imagePart = GooglePart(inlineData: inlineData)
        
        let originalContent = GoogleContent(role: "user", parts: [textPart, imagePart])
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalContent)
        
        let decoder = JSONDecoder()
        let decodedContent = try decoder.decode(GoogleContent.self, from: data)
        
        XCTAssertEqual(decodedContent.role, originalContent.role)
        XCTAssertEqual(decodedContent.parts.count, originalContent.parts.count)
        XCTAssertEqual(decodedContent.parts[0].text, "Hello")
        XCTAssertEqual(decodedContent.parts[1].inlineData?.mimeType, "image/png")
        XCTAssertEqual(decodedContent.parts[1].inlineData?.data, "imagedata")
    }
    
    // MARK: - JSON Structure Tests
    
    func testGooglePartJSONStructure() throws {
        let inlineData = GoogleInlineData(mimeType: "image/jpeg", data: "testdata")
        let part = GooglePart(inlineData: inlineData)

        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        let jsonData = try encoder.encode(part)
        let jsonString = String(data: jsonData, encoding: .utf8)!

        logger.info("JSON String: \(jsonString)")

        // Test the actual structure - Swift Codable omits nil values by default
        XCTAssertTrue(jsonString.contains("\"inlineData\""), "JSON should contain 'inlineData' field. Actual JSON: \(jsonString)")

        // Test nested structure
        XCTAssertTrue(jsonString.contains("\"mimeType\""), "JSON should contain 'mimeType' field. Actual JSON: \(jsonString)")
        XCTAssertTrue(jsonString.contains("\"data\""), "JSON should contain 'data' field. Actual JSON: \(jsonString)")
        // Handle escaped forward slash in JSON
        XCTAssertTrue(jsonString.contains("\"image\\/jpeg\"") || jsonString.contains("\"image/jpeg\""), "JSON should contain 'image/jpeg' value (with or without escaped slash). Actual JSON: \(jsonString)")
        XCTAssertTrue(jsonString.contains("\"testdata\""), "JSON should contain 'testdata' value. Actual JSON: \(jsonString)")

        // Verify that nil fields are omitted (this is expected behavior)
        XCTAssertFalse(jsonString.contains("\"text\""), "JSON should not contain 'text' field when it's nil")
        XCTAssertFalse(jsonString.contains("\"thought\""), "JSON should not contain 'thought' field when it's nil")
    }
}
