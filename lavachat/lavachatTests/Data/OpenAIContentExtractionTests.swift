import XCTest
@testable import lavachat

/// Tests for OpenAI Compatible API content extraction from domain models
final class OpenAIContentExtractionTests: XCTestCase {
    
    // MARK: - Text Content Extraction Tests
    
    func testExtractOpenAIContentPartsTextOnly() {
        let contentBlocks: [ContentBlock] = [
            .text("Hello, world!"),
            .text("This is a test.")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 2)
        XCTAssertEqual(parts[0].type, "text")
        XCTAssertEqual(parts[0].text, "Hello, world!")
        XCTAssertEqual(parts[1].type, "text")
        XCTAssertEqual(parts[1].text, "This is a test.")
    }
    
    func testExtractOpenAIContentPartsEmptyText() {
        let contentBlocks: [ContentBlock] = [
            .text(""),
            .text("Valid text")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        // Empty text should be filtered out
        XCTAssertEqual(parts.count, 2)
        XCTAssertEqual(parts[0].text, "")
        XCTAssertEqual(parts[1].text, "Valid text")
    }
    
    // MARK: - Image Content Extraction Tests
    
    func testExtractOpenAIContentPartsWithImage() {
        let imageInfo = ImageInfo(
            mimeType: "image/jpeg",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 1024
        )
        
        let contentBlocks: [ContentBlock] = [
            .text("Describe this image:"),
            .image(imageInfo)
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 2)
        
        // Check text part
        XCTAssertEqual(parts[0].type, "text")
        XCTAssertEqual(parts[0].text, "Describe this image:")
        
        // Check image part
        XCTAssertEqual(parts[1].type, "image_url")
        XCTAssertNotNil(parts[1].imageUrl)
        XCTAssertEqual(parts[1].imageUrl?.url, "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
    }
    
    func testExtractOpenAIContentPartsImageMissingData() {
        let imageInfo = ImageInfo(mimeType: "image/jpeg") // No base64Data
        
        let contentBlocks: [ContentBlock] = [
            .text("Text before"),
            .image(imageInfo),
            .text("Text after")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        // Image without base64Data should be filtered out
        XCTAssertEqual(parts.count, 2)
        XCTAssertEqual(parts[0].text, "Text before")
        XCTAssertEqual(parts[1].text, "Text after")
    }
    
    func testExtractOpenAIContentPartsImageMissingMimeType() {
        let imageInfo = ImageInfo(base64Data: "validbase64data") // No mimeType
        
        let contentBlocks: [ContentBlock] = [
            .text("Text"),
            .image(imageInfo)
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        // Image without mimeType should be filtered out
        XCTAssertEqual(parts.count, 1)
        XCTAssertEqual(parts[0].text, "Text")
    }
    
    // MARK: - File Content Extraction Tests
    
    func testExtractOpenAIContentPartsWithFile() {
        let fileInfo = FileInfo(
            fileName: "document.pdf",
            mimeType: "application/pdf",
            base64Data: "JVBERi0xLjQKJcOkw7zDtsO4DQo=",
            fileSizeBytes: 2048,
            fileType: .document
        )
        
        let contentBlocks: [ContentBlock] = [
            .text("Analyze this document:"),
            .file(fileInfo)
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 2)
        
        // Check text part
        XCTAssertEqual(parts[0].type, "text")
        XCTAssertEqual(parts[0].text, "Analyze this document:")
        
        // Check file part (file_data contains data URL)
        XCTAssertEqual(parts[1].type, "file")
        XCTAssertNotNil(parts[1].file)
        XCTAssertEqual(parts[1].file?.filename, "document.pdf")
        XCTAssertEqual(parts[1].file?.fileData, "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4DQo=")
    }
    
    func testExtractOpenAIContentPartsFileEmptyData() {
        let fileInfo = FileInfo(
            fileName: "empty.pdf",
            mimeType: "application/pdf",
            base64Data: "", // Empty data
            fileSizeBytes: 0,
            fileType: .document
        )
        
        let contentBlocks: [ContentBlock] = [
            .text("Text"),
            .file(fileInfo)
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        // File with empty data should be filtered out
        XCTAssertEqual(parts.count, 1)
        XCTAssertEqual(parts[0].text, "Text")
    }
    
    // MARK: - Mixed Content Tests
    
    func testExtractOpenAIContentPartsMixedContent() {
        let imageInfo = ImageInfo(
            mimeType: "image/png",
            base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            fileSizeBytes: 1024
        )
        
        let fileInfo = FileInfo(
            fileName: "report.pdf",
            mimeType: "application/pdf",
            base64Data: "JVBERi0xLjQKJcOkw7zDtsO4DQo=",
            fileSizeBytes: 2048,
            fileType: .document
        )
        
        let contentBlocks: [ContentBlock] = [
            .text("Please analyze this image and document:"),
            .image(imageInfo),
            .text("The image shows:"),
            .file(fileInfo),
            .text("What are the key findings?")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 5)
        
        // Check sequence
        XCTAssertEqual(parts[0].type, "text")
        XCTAssertEqual(parts[0].text, "Please analyze this image and document:")
        
        XCTAssertEqual(parts[1].type, "image_url")
        XCTAssertEqual(parts[1].imageUrl?.url, "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
        
        XCTAssertEqual(parts[2].type, "text")
        XCTAssertEqual(parts[2].text, "The image shows:")
        
        XCTAssertEqual(parts[3].type, "file")
        XCTAssertEqual(parts[3].file?.filename, "report.pdf")
        XCTAssertEqual(parts[3].file?.fileData, "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4DQo=")
        
        XCTAssertEqual(parts[4].type, "text")
        XCTAssertEqual(parts[4].text, "What are the key findings?")
    }
    
    // MARK: - Attached File Content Tests
    
    func testExtractOpenAIContentPartsAttachedFileContent() {
        let contentBlocks: [ContentBlock] = [
            .text("Here's the file content:"),
            .attachedFileContent(fileName: "notes.txt", content: "These are my notes\nLine 2\nLine 3")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 2)
        XCTAssertEqual(parts[0].type, "text")
        XCTAssertEqual(parts[0].text, "Here's the file content:")
        
        XCTAssertEqual(parts[1].type, "text")
        XCTAssertEqual(parts[1].text, "File: notes.txt\n\nThese are my notes\nLine 2\nLine 3")
    }
    
    // MARK: - Thinking Content Tests
    
    func testExtractOpenAIContentPartsSkipsThinking() {
        let contentBlocks: [ContentBlock] = [
            .text("User question"),
            .thinking("Let me think about this..."),
            .text("My response")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        // Thinking content should be skipped
        XCTAssertEqual(parts.count, 2)
        XCTAssertEqual(parts[0].text, "User question")
        XCTAssertEqual(parts[1].text, "My response")
    }
    
    // MARK: - Empty Input Tests
    
    func testExtractOpenAIContentPartsEmptyInput() {
        let contentBlocks: [ContentBlock] = []
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        XCTAssertEqual(parts.count, 0)
    }
    
    func testExtractOpenAIContentPartsOnlyEmptyContent() {
        let contentBlocks: [ContentBlock] = [
            .text(""),
            .image(ImageInfo()), // No data
            .attachedFileContent(fileName: "empty.txt", content: "")
        ]
        
        let parts = MessageContentExtractor.extractOpenAIContentParts(from: contentBlocks)
        
        // All empty content should be filtered out except the empty attached file content
        XCTAssertEqual(parts.count, 1)
    }
}
