import XCTest
@testable import lavachat

// IMPORTANT: To run these tests, you must provide your own Google API key.
// This key is for local testing only and should NOT be committed to version control.
// The tests will automatically be skipped if the key is not provided.
private let googleApiKey = "YOUR_GOOGLE_API_KEY_HERE"

final class GoogleClientTests: XCTestCase {

    private var testModelIdentifier: String!
    private var streamingUrl: URL!

    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Skip all tests in this class if the API key is missing.
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping GoogleClientTests.")

        testModelIdentifier = "gemini-2.5-flash-preview-05-20"
        if let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/\(testModelIdentifier!):streamGenerateContent?key=\(googleApiKey)&alt=sse") {
            streamingUrl = url
        } else {
            XCTFail("Failed to create test URL.")
        }
    }

    /// Builds a minimal, valid request body for testing.
    private func buildTestRequestBody() -> Data {
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "role": "user",
                    "parts": [["text": "Hello!"]]
                ]
            ]
        ]
        return try! JSONSerialization.data(withJSONObject: requestBody, options: [])
    }
    
    // MARK: - Test Cases

//    /// **Test Case 1: Simulates the current app behavior using `.default` URLSession configuration.**
//    /// This test helps determine if the issue is inherent to the default session setup on the simulator.
//    func testStreamConnectionWithDefaultConfiguration() async {
//        print("\n--- [Test Case 1: Default Configuration] ---")
//        let configuration = URLSessionConfiguration.default
//        let session = URLSession(configuration: configuration)
//        
//        await performStreamingTest(session: session, caseIdentifier: "Default")
//    }
    
    /// **Test Case 2: Uses an `.ephemeral` URLSession configuration.**
    /// This configuration is isolated, uses no caches or persistent storage, and can sometimes bypass
    /// network stack issues related to connection state or caching.
    func testStreamConnectionWithEphemeralConfiguration() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping real API tests.")
        
        print("\n--- [Test Case 2: Ephemeral Configuration] ---")
        let configuration = URLSessionConfiguration.ephemeral
        let session = URLSession(configuration: configuration)

        await performStreamingTest(session: session, caseIdentifier: "Ephemeral")
    }
    
    /// **Test Case 3: Uses the app's actual SSEParser and its underlying delegate.**
    /// This test is the most accurate simulation of the app's behavior. If this fails while the
    /// others pass, it strongly indicates the issue is within our custom `SSEStreamDelegate`.
    func testStreamConnectionWithSSEParserDelegate() async throws {
        // Skip this test if API key is not provided
        try XCTSkipIf(googleApiKey == "YOUR_GOOGLE_API_KEY_HERE" || googleApiKey.isEmpty,
                      "Google API key is not provided. Skipping real API tests.")
        
        print("\n--- [Test Case 3: SSEParser Delegate] ---")
        
        var request = URLRequest(url: streamingUrl)
        request.httpMethod = "POST"
        request.httpBody = buildTestRequestBody()
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        print("[SSEParser] Sending request via SSEParser.createEventStream")
        print("[SSEParser] URL: \(request.url!.absoluteString)")
        print("[SSEParser] Headers: \(request.allHTTPHeaderFields ?? [:])")

        var receivedEvents: [SSEEvent] = []
        var streamError: Error?
        let expectation = XCTestExpectation(description: "Stream should complete.")

        do {
            let sseStream = SSEParser.createEventStream(for: request)
            for try await event in sseStream {
                print("[SSEParser] Received event: \(event)")
                receivedEvents.append(event)
            }
            print("[SSEParser] Stream finished without throwing an error.")
        } catch {
            print("[SSEParser] Stream finished with an error: \(error)")
            streamError = error
        }
        
        expectation.fulfill()
        await fulfillment(of: [expectation], timeout: 15.0)
        
        // --- Assertions ---
        XCTAssertNil(streamError, "[SSEParser] The SSE stream should complete without error. Received: \(streamError?.localizedDescription ?? "N/A")")
        XCTAssertFalse(receivedEvents.isEmpty, "[SSEParser] Should have received at least one SSE event.")
        
        // It's possible to get multiple events, we check if any of them have the data
        let hasDataEvent = receivedEvents.contains { !$0.data.isEmpty }
        XCTAssertTrue(hasDataEvent, "[SSEParser] At least one event should contain data.")
        
        let jsonDataString = receivedEvents.first(where: { !$0.data.isEmpty })?.data
        XCTAssertNotNil(jsonDataString, "[SSEParser] The event data should not be nil.")
        XCTAssertTrue(jsonDataString?.contains("\"candidates\"") ?? false, "[SSEParser] The response JSON should contain a 'candidates' key.")

        print("--- [Test Case SSEParser Finished] ---\n")
    }
    
    // MARK: - Helper Method

    /// Generic test runner for a given URLSession instance.
    private func performStreamingTest(session: URLSession, caseIdentifier: String) async {
        var request = URLRequest(url: streamingUrl)
        request.httpMethod = "POST"
        request.httpBody = buildTestRequestBody()
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        print("[\(caseIdentifier)] Sending request to: \(streamingUrl.absoluteString)")
        print("[\(caseIdentifier)] Request Headers: \(request.allHTTPHeaderFields ?? [:])")
        print("[\(caseIdentifier)] Request Body: \(String(data: request.httpBody!, encoding: .utf8)!)")

        let expectation = XCTestExpectation(description: "Receive at least one data chunk from the stream for \(caseIdentifier) case.")
        var receivedData = Data()
        var finalError: Error?
        var httpResponse: HTTPURLResponse?

        let task = session.dataTask(with: request) { data, response, error in
            finalError = error
            if let resp = response as? HTTPURLResponse {
                httpResponse = resp
                print("[\(caseIdentifier)] Received response with status code: \(resp.statusCode)")
            }
            
            if let data = data, !data.isEmpty {
                receivedData.append(data)
                print("[\(caseIdentifier)] Received data chunk: \(String(data: data, encoding: .utf8) ?? "Non-UTF8 data")")
                expectation.fulfill()
            }
            
            // If there's an error or it's the final chunk, fulfill expectation
            if error != nil {
                print("[\(caseIdentifier)] Task completed with error: \(error!)")
                expectation.fulfill()
            }
        }

        task.resume()
        
        // Wait for the expectation to be fulfilled, or time out.
        await fulfillment(of: [expectation], timeout: 15.0)
        
        // Teardown the session
        session.finishTasksAndInvalidate()

        // --- Assertions ---
        XCTAssertNil(finalError, "[\(caseIdentifier)] The request should complete without a network error. Received: \(finalError?.localizedDescription ?? "N/A")")
        
        guard let response = httpResponse else {
            XCTFail("[\(caseIdentifier)] Failed to get an HTTPURLResponse.")
            return
        }
        
        XCTAssertEqual(response.statusCode, 200, "[\(caseIdentifier)] Expected HTTP status code 200, but got \(response.statusCode).")
        
        XCTAssertFalse(receivedData.isEmpty, "[\(caseIdentifier)] Should have received some data from the stream.")
        
        // Check if the received data contains expected SSE format/content
        let receivedString = String(data: receivedData, encoding: .utf8)
        XCTAssertNotNil(receivedString, "[\(caseIdentifier)] Received data should be a valid UTF-8 string.")
        XCTAssertTrue(receivedString?.contains("data:") ?? false, "[\(caseIdentifier)] The response string should contain 'data:' indicating SSE format.")
        XCTAssertTrue(receivedString?.contains("\"candidates\"") ?? false, "[\(caseIdentifier)] The response JSON should contain a 'candidates' key.")
        
        print("--- [Test Case \(caseIdentifier) Finished] ---\n")
    }
} 
