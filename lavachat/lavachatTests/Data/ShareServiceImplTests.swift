import XCTest
@testable import lavachat

final class ShareServiceImplTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var shareService: ShareServiceImpl!
    var chatRepository: ChatRepository!
    var llmRepository: LLMInstanceRepository!
    var testUserId: UUID!
    
    // Test data
    var testProvider: LLMProvider!
    var testModel: LLMModel!
    var testInstance: LLMInstance!
    var testAction: MessageAction!
    var testSetting: ChatSessionSetting!
    var testSession: ChatSession!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        let container = DIContainer(context: PersistenceController.shared.container.viewContext)
        chatRepository = container.getSharedChatRepository()
        llmRepository = container.getSharedLLMInstanceRepository()

        // Create mock share handlers for testing
        do {
            let fileShareHandler = try FileShareHandler()
            let cloudKitShareHandler = CloudKitShareHandler()
            let qrCodeShareHandler = QRCodeShareHandler()

            shareService = ShareServiceImpl(
                fileShareHandler: fileShareHandler,
                cloudKitShareHandler: cloudKitShareHandler,
                qrCodeShareHandler: qrCodeShareHandler,
                llmRepository: llmRepository,
                chatRepository: chatRepository
            )
        } catch {
            XCTFail("Failed to initialize FileShareHandler: \(error)")
        }

        testUserId = UUID()
        setupTestData()
    }
    
    override func tearDown() {
        super.tearDown()
        shareService = nil
        chatRepository = nil
        llmRepository = nil
        testUserId = nil
    }
    
    private func setupTestData() {
        testProvider = LLMProvider(
            name: "Test Provider",
            apiBaseUrl: "https://api.test.com",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: false
        )

        testModel = LLMModel(
            providerId: testProvider.id,
            modelIdentifier: "test-model-v1",
            name: "Test Model",
            contextWindowSize: 4096,
            maxOutputTokens: 2048,
            isUserCreated: true,
            isUserModified: false
        )

        testInstance = LLMInstance(
            modelId: testModel.id,
            name: "Test Instance",
            systemPrompt: "You are a helpful assistant",
            isUserModified: false
        )

        testAction = MessageAction(
            name: "Test Action",
            icon: "star.fill",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt"],
            targetLLMInstanceId: testInstance.id
        )

        let messageActionSettings = MessageActionSettings(
            actionPanelActions: [],
            userMessageActions: [],
            assistantMessageCardActions: [testAction.id],
            assistantMessageMenuActions: []
        )

        testSetting = ChatSessionSetting(
            name: "Test Setting",
            isSystemDefault: false,
            shouldExpandThinking: true,
            messageActionSettings: messageActionSettings,
            auxiliaryLLMInstanceId: testInstance.id,
            shouldAutoGenerateTitle: true,
            contextMessageCount: 10
        )

        testSession = ChatSession(
            title: "Test Chat Session",
            activeLLMInstanceIds: [testInstance.id],
            usedLLMInstanceIds: [testInstance.id],
            settingsId: testSetting.id,
            userId: testUserId
        )
    }
    
    // MARK: - Share LLMInstance Tests
    
    func testShareLLMInstanceWithDependencies() async throws {
        // Given - Create test data in repositories
        try await llmRepository.createProvider(testProvider)
        try await llmRepository.createModel(testModel)
        try await llmRepository.createInstance(testInstance)

        // When
        let configuration = ShareConfiguration(format: .file)
        let result = await shareService.shareItem(testInstance, configuration: configuration)

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.format, .file)
            XCTAssertEqual(successInfo.contentType, .llmInstance)
            XCTAssertNotNil(successInfo.fileURL)
        case .failure(let error):
            XCTFail("Share operation failed: \(error)")
        }
    }
    
    // MARK: - Share ChatSessionSetting Tests
    
    func testShareChatSessionSettingWithMessageActions() async throws {
        // Given - Create test data in repositories
        try await chatRepository.createMessageAction(testAction)
        try await chatRepository.createSetting(testSetting)

        // When
        let configuration = ShareConfiguration(format: .file)
        let result = await shareService.shareItem(testSetting, configuration: configuration)

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.format, .file)
            XCTAssertEqual(successInfo.contentType, .chatSessionSetting)
            XCTAssertNotNil(successInfo.fileURL)
        case .failure(let error):
            XCTFail("Share operation failed: \(error)")
        }
    }
    
    // MARK: - Share ChatSession Tests
    
    func testShareChatSessionWithDependencies() async throws {
        // Given - Create test data in repositories
        try await chatRepository.createMessageAction(testAction)
        try await chatRepository.createSetting(testSetting)
        try await chatRepository.createChatSession(testSession)

        // When
        let configuration = ShareConfiguration(format: .file)
        let result = await shareService.shareItem(testSession, configuration: configuration)

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.format, .file)
            XCTAssertEqual(successInfo.contentType, .chatSession)
            XCTAssertNotNil(successInfo.fileURL)
        case .failure(let error):
            XCTFail("Share operation failed: \(error)")
        }
    }
    
    // MARK: - Share Progress Tests
    
    func testShareProgress() async throws {
        // Given - Create test data in repositories
        try await chatRepository.createMessageAction(testAction)

        // When
        let configuration = ShareConfiguration(format: .file)
        let result = await shareService.shareItem(testAction, configuration: configuration)

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.format, .file)
            XCTAssertEqual(successInfo.contentType, .messageAction)
            XCTAssertNotNil(successInfo.fileURL)
        case .failure(let error):
            XCTFail("Share operation failed: \(error)")
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testShareItemWithQRCodeFormat() async throws {
        // Given - Create test data in repositories
        try await chatRepository.createMessageAction(testAction)

        // When
        let configuration = ShareConfiguration(format: .qrCode)
        let result = await shareService.shareItem(testAction, configuration: configuration)

        // Then - QR code sharing might fail if iCloud is not available, which is expected in tests
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.format, .qrCode)
            XCTAssertEqual(successInfo.contentType, .messageAction)
        case .failure(let error):
            // QR code sharing depends on iCloud, which might not be available in tests
            XCTAssertTrue(error is ShareError)
        }
    }
}


