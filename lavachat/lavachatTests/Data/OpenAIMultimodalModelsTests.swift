import XCTest
@testable import lavachat

/// Tests for OpenAI Compatible API multimodal models
final class OpenAIMultimodalModelsTests: XCTestCase {
    
    // MARK: - OpenAIMessageContent Tests
    
    func testOpenAIMessageContentTextEncoding() throws {
        let content = OpenAIMessageContent.text("Hello, world!")
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(content)
        let jsonString = String(data: data, encoding: .utf8)
        
        XCTAssertEqual(jsonString, "\"Hello, world!\"")
    }
    
    func testOpenAIMessageContentTextDecoding() throws {
        let jsonData = "\"Hello, world!\"".data(using: .utf8)!
        
        let decoder = JSONDecoder()
        let content = try decoder.decode(OpenAIMessageContent.self, from: jsonData)
        
        if case .text(let text) = content {
            XCTAssertEqual(text, "Hello, world!")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testOpenAIMessageContentMultimodalEncoding() throws {
        let parts = [
            OpenAIContentPart.text("Describe this image:"),
            OpenAIContentPart.image(url: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
        ]
        let content = OpenAIMessageContent.multimodal(parts)
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(content)
        
        // Should encode as an array
        let json = try JSONSerialization.jsonObject(with: data) as? [[String: Any]]
        XCTAssertNotNil(json)
        XCTAssertEqual(json?.count, 2)
        
        // Check first part (text)
        let firstPart = json?[0]
        XCTAssertEqual(firstPart?["type"] as? String, "text")
        XCTAssertEqual(firstPart?["text"] as? String, "Describe this image:")
        
        // Check second part (image)
        let secondPart = json?[1]
        XCTAssertEqual(secondPart?["type"] as? String, "image_url")
        let imageUrl = secondPart?["image_url"] as? [String: Any]
        XCTAssertEqual(imageUrl?["url"] as? String, "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
    }
    
    func testOpenAIMessageContentMultimodalDecoding() throws {
        let jsonString = """
        [
            {
                "type": "text",
                "text": "Describe this image:"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
                }
            }
        ]
        """
        let jsonData = jsonString.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        let content = try decoder.decode(OpenAIMessageContent.self, from: jsonData)
        
        if case .multimodal(let parts) = content {
            XCTAssertEqual(parts.count, 2)
            
            // Check first part
            XCTAssertEqual(parts[0].type, "text")
            XCTAssertEqual(parts[0].text, "Describe this image:")
            
            // Check second part
            XCTAssertEqual(parts[1].type, "image_url")
            XCTAssertEqual(parts[1].imageUrl?.url, "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
        } else {
            XCTFail("Expected multimodal content")
        }
    }
    
    // MARK: - OpenAIContentPart Tests
    
    func testOpenAIContentPartTextCreation() {
        let part = OpenAIContentPart.text("Hello")
        
        XCTAssertEqual(part.type, "text")
        XCTAssertEqual(part.text, "Hello")
        XCTAssertNil(part.imageUrl)
        XCTAssertNil(part.file)
    }
    
    func testOpenAIContentPartImageCreation() {
        let imageUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
        let part = OpenAIContentPart.image(url: imageUrl)
        
        XCTAssertEqual(part.type, "image_url")
        XCTAssertNil(part.text)
        XCTAssertEqual(part.imageUrl?.url, imageUrl)
        XCTAssertNil(part.file)
    }
    
    func testOpenAIContentPartFileCreation() {
        let part = OpenAIContentPart.file(filename: "document.pdf", data: "base64data...")
        
        XCTAssertEqual(part.type, "file")
        XCTAssertNil(part.text)
        XCTAssertNil(part.imageUrl)
        XCTAssertEqual(part.file?.filename, "document.pdf")
        XCTAssertEqual(part.file?.fileData, "base64data...")
    }
    
    // MARK: - OpenAIMessage Tests
    
    func testOpenAIMessageTextOnly() {
        let message = OpenAIMessage(role: "user", text: "Hello")
        
        XCTAssertEqual(message.role, "user")
        if case .text(let text) = message.content {
            XCTAssertEqual(text, "Hello")
        } else {
            XCTFail("Expected text content")
        }
    }
    
    func testOpenAIMessageMultimodal() {
        let parts = [
            OpenAIContentPart.text("Describe this:"),
            OpenAIContentPart.image(url: "data:image/jpeg;base64,...")
        ]
        let message = OpenAIMessage(role: "user", content: .multimodal(parts))
        
        XCTAssertEqual(message.role, "user")
        if case .multimodal(let messageParts) = message.content {
            XCTAssertEqual(messageParts.count, 2)
            XCTAssertEqual(messageParts[0].type, "text")
            XCTAssertEqual(messageParts[1].type, "image_url")
        } else {
            XCTFail("Expected multimodal content")
        }
    }
    
    // MARK: - OpenAIFile Tests
    
    func testOpenAIFileEncodingDecoding() throws {
        let file = OpenAIFile(filename: "test.pdf", fileData: "base64encodeddata")
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(file)
        
        let decoder = JSONDecoder()
        let decodedFile = try decoder.decode(OpenAIFile.self, from: data)
        
        XCTAssertEqual(decodedFile.filename, "test.pdf")
        XCTAssertEqual(decodedFile.fileData, "base64encodeddata")
    }
    
    func testOpenAIFileJSONKeys() throws {
        let file = OpenAIFile(filename: "test.pdf", fileData: "base64encodeddata")
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(file)
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        
        XCTAssertEqual(json?["filename"] as? String, "test.pdf")
        XCTAssertEqual(json?["file_data"] as? String, "base64encodeddata")
    }
    
    // MARK: - OpenAIImageUrl Tests
    
    func testOpenAIImageUrlBasic() {
        let imageUrl = OpenAIImageUrl(url: "data:image/jpeg;base64,...")
        
        XCTAssertEqual(imageUrl.url, "data:image/jpeg;base64,...")
        XCTAssertNil(imageUrl.detail)
    }
    
    func testOpenAIImageUrlWithDetail() {
        let imageUrl = OpenAIImageUrl(url: "data:image/jpeg;base64,...", detail: "high")
        
        XCTAssertEqual(imageUrl.url, "data:image/jpeg;base64,...")
        XCTAssertEqual(imageUrl.detail, "high")
    }
}
