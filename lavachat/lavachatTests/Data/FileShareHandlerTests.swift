import XCTest
@testable import lavachat

final class FileShareHandlerTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var fileShareHandler: FileShareHandler!
    var testUserId: UUID!
    
    // Test data
    var testProvider: LLMProvider!
    var testModel: LLMModel!
    var testInstance: LLMInstance!
    var testAction: MessageAction!
    var testSetting: ChatSessionSetting!
    var testSession: ChatSession!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        do {
            fileShareHandler = try FileShareHandler()
        } catch {
            XCTFail("Failed to initialize FileShareHandler: \(error)")
        }
        testUserId = UUID()
        setupTestData()
    }
    
    override func tearDown() {
        super.tearDown()
        fileShareHandler = nil
        testUserId = nil
    }
    
    private func setupTestData() {
        testProvider = LLMProvider(
            name: "Test Provider",
            apiBaseUrl: "https://api.test.com",
            providerType: .userApi<PERSON>ey,
            apiKeyStored: false, // Cleaned for sharing
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: false
        )

        testModel = LLMModel(
            providerId: testProvider.id,
            modelIdentifier: "test-model-v1",
            name: "Test Model",
            contextWindowSize: 4096,
            maxOutputTokens: 2048,
            isUserCreated: true,
            isUserModified: false
        )

        testInstance = LLMInstance(
            modelId: testModel.id,
            name: "Test Instance",
            systemPrompt: "You are a helpful assistant",
            isUserModified: false
        )

        testAction = MessageAction(
            name: "Test Action",
            icon: "star.fill",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt"],
            targetLLMInstanceId: testInstance.id
        )

        let messageActionSettings = MessageActionSettings(
            actionPanelActions: [],
            userMessageActions: [],
            assistantMessageCardActions: [testAction.id],
            assistantMessageMenuActions: []
        )
        
        testSetting = ChatSessionSetting(
            name: "Test Setting",
            isSystemDefault: false,
            shouldExpandThinking: true,
            messageActionSettings: messageActionSettings,
            auxiliaryLLMInstanceId: testInstance.id,
            shouldAutoGenerateTitle: true,
            contextMessageCount: 10
        )

        testSession = ChatSession(
            title: "Test Chat Session",
            activeLLMInstanceIds: [testInstance.id],
            usedLLMInstanceIds: [testInstance.id],
            settingsId: testSetting.id,
            userId: testUserId
        )
    }
    
    // MARK: - Export Tests
    
    func testExportLLMInstance() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .llmInstance,
            data: ShareableDataContent(
                instance: testInstance,
                model: testModel,
                provider: testProvider
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        // When
        let url = try await fileShareHandler.createShareFile(data: shareableData, fileName: "Test Instance.lavachat")
        
        // Then
        XCTAssertNotNil(url)
        XCTAssertTrue(url.pathExtension == "lavachat")
        XCTAssertTrue(url.lastPathComponent.contains("Test Instance"))
        
        // Verify file exists
        XCTAssertTrue(FileManager.default.fileExists(atPath: url.path))
        
        // Clean up
        try? FileManager.default.removeItem(at: url)
    }
    
    func testExportChatSessionWithDependencies() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .chatSession,
            data: ShareableDataContent(
                chatSession: testSession,
                messageActions: [testAction],
                chatSessionSetting: testSetting
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        // When
        let url = try await fileShareHandler.createShareFile(data: shareableData, fileName: "Test Chat Session.lavachat")
        
        // Then
        XCTAssertNotNil(url)
        XCTAssertTrue(url.pathExtension == "lavachat")
        XCTAssertTrue(url.lastPathComponent.contains("Test Chat Session"))
        
        // Verify file exists and has content
        XCTAssertTrue(FileManager.default.fileExists(atPath: url.path))
        let fileSize = try FileManager.default.attributesOfItem(atPath: url.path)[.size] as! Int64
        XCTAssertGreaterThan(fileSize, 0)
        
        // Clean up
        try? FileManager.default.removeItem(at: url)
    }
    
    // MARK: - Import Tests
    
    func testImportValidFile() async throws {
        // Given - First export a file
        let shareableData = ShareableData(
            shareType: .messageAction,
            data: ShareableDataContent(messageAction: testAction),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        let exportedURL = try await fileShareHandler.createShareFile(data: shareableData, fileName: "Test Action.lavachat")

        // When
        let importedData = try await fileShareHandler.readShareFile(from: exportedURL)
        
        // Then
        XCTAssertEqual(importedData.shareType, ShareContentType.messageAction)
        XCTAssertNotNil(importedData.data.messageAction)
        XCTAssertEqual(importedData.data.messageAction?.id, testAction.id)
        XCTAssertEqual(importedData.data.messageAction?.name, testAction.name)
        
        // Clean up
        try? FileManager.default.removeItem(at: exportedURL)
    }
    
    func testImportInvalidFile() async {
        // Given
        let tempURL = FileManager.default.temporaryDirectory
            .appendingPathComponent("invalid.lavachat")
        
        let invalidData = "This is not valid JSON".data(using: .utf8)!
        try! invalidData.write(to: tempURL)
        
        // When & Then
        do {
            _ = try await fileShareHandler.readShareFile(from: tempURL)
            XCTFail("Should throw error for invalid file")
        } catch {
            XCTAssertTrue(error is ImportError)
        }
        
        // Clean up
        try? FileManager.default.removeItem(at: tempURL)
    }
    
    // MARK: - Preview Tests
    
    func testPreviewLLMInstance() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .llmInstance,
            data: ShareableDataContent(
                instance: testInstance,
                model: testModel,
                provider: testProvider
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        let exportedURL = try await fileShareHandler.createShareFile(data: shareableData, fileName: "Test Instance.lavachat")

        // When
        let preview = await fileShareHandler.getFilePreview(exportedURL)

        // Then
        XCTAssertNotNil(preview)
        XCTAssertEqual(preview?.contentType, .llmInstance)
        XCTAssertEqual(preview?.itemCount, 3) // Instance + Model + Provider
        XCTAssertTrue(preview?.itemNames.contains("Test Instance") ?? false)
        XCTAssertTrue(preview?.itemNames.contains("Model: Test Model") ?? false)
        XCTAssertTrue(preview?.itemNames.contains("Provider: Test Provider") ?? false)
        
        // Clean up
        try? FileManager.default.removeItem(at: exportedURL)
    }
    
    func testPreviewChatSessionWithDependencies() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .chatSession,
            data: ShareableDataContent(
                chatSession: testSession,
                messageActions: [testAction],
                chatSessionSetting: testSetting
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        let exportedURL = try await fileShareHandler.createShareFile(data: shareableData, fileName: "Test Chat Session.lavachat")

        // When
        let preview = await fileShareHandler.getFilePreview(exportedURL)

        // Then
        XCTAssertNotNil(preview)
        XCTAssertEqual(preview?.contentType, .chatSession)
        XCTAssertEqual(preview?.itemCount, 3) // Session + Setting + Action
        XCTAssertTrue(preview?.itemNames.contains("Test Chat Session") ?? false)
        XCTAssertTrue(preview?.itemNames.contains("Setting: Test Setting") ?? false)
        XCTAssertTrue(preview?.itemNames.contains("Action: Test Action") ?? false)
        
        // Clean up
        try? FileManager.default.removeItem(at: exportedURL)
    }
    
    // MARK: - File Name Generation Tests

    func testFileNameGeneration() async throws {
        // Test various content types
        let instanceData = ShareableData(
            shareType: .llmInstance,
            data: ShareableDataContent(instance: testInstance),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test",
                exportedBy: "Test User"
            )
        )

        let actionData = ShareableData(
            shareType: .messageAction,
            data: ShareableDataContent(messageAction: testAction),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test",
                exportedBy: "Test User"
            )
        )

        let settingData = ShareableData(
            shareType: .chatSessionSetting,
            data: ShareableDataContent(chatSessionSetting: testSetting),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test",
                exportedBy: "Test User"
            )
        )

        // Test file creation instead of generateFileName (which doesn't exist)
        let instanceFileName = "Test Instance.lavachat"
        let actionFileName = "Test Action.lavachat"
        let settingFileName = "Test Setting.lavachat"

        let instanceFileURL = try await fileShareHandler.createShareFile(data: instanceData, fileName: instanceFileName)
        let actionFileURL = try await fileShareHandler.createShareFile(data: actionData, fileName: actionFileName)
        let settingFileURL = try await fileShareHandler.createShareFile(data: settingData, fileName: settingFileName)

        // Verify files were created with correct names
        XCTAssertTrue(instanceFileURL.lastPathComponent.contains("Test Instance"))
        XCTAssertTrue(actionFileURL.lastPathComponent.contains("Test Action"))
        XCTAssertTrue(settingFileURL.lastPathComponent.contains("Test Setting"))

        // All should have .lavachat extension
        XCTAssertTrue(instanceFileURL.pathExtension == "lavachat")
        XCTAssertTrue(actionFileURL.pathExtension == "lavachat")
        XCTAssertTrue(settingFileURL.pathExtension == "lavachat")

        // Clean up
        try? FileManager.default.removeItem(at: instanceFileURL)
        try? FileManager.default.removeItem(at: actionFileURL)
        try? FileManager.default.removeItem(at: settingFileURL)
    }
    
    // MARK: - Error Handling Tests
    
    func testImportNonExistentFile() async {
        // Given
        let nonExistentURL = URL(fileURLWithPath: "/tmp/nonexistent.lavachat")

        // When & Then
        do {
            _ = try await fileShareHandler.readShareFile(from: nonExistentURL)
            XCTFail("Should throw error for non-existent file")
        } catch {
            XCTAssertTrue(error is ImportError)
        }
    }
}
