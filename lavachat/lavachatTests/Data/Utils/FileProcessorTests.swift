import XCTest
import UIKit
@testable import lavachat

final class FileProcessorTests: XCTestCase {
    
    // MARK: - Test Data Setup
    
    private func createTestImageData(size: CGSize = CGSize(width: 100, height: 100)) -> Data {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            UIColor.red.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        return image.jpegData(compressionQuality: 1.0) ?? Data()
    }

    private func createCompressibleImageData() -> Data {
        // Create a larger, more complex image that will definitely need compression
        let size = CGSize(width: 4000, height: 3000) // Larger size
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            // Create a complex pattern with lots of detail that will be large
            for x in stride(from: 0, to: size.width, by: 10) { // Smaller steps for more detail
                for y in stride(from: 0, to: size.height, by: 10) {
                    let color = UIColor(
                        red: CGFloat(x) / size.width,
                        green: CGFloat(y) / size.height,
                        blue: sin(CGFloat(x + y) / 100) * 0.5 + 0.5, // More complex pattern
                        alpha: 1.0
                    )
                    color.setFill()
                    context.fill(CGRect(x: x, y: y, width: 10, height: 10))
                }
            }
        }
        // Use PNG format which is typically larger and more compressible
        let pngData = image.pngData() ?? Data()

        // Ensure the image is large enough to trigger compression
        // If it's still too small, create an even larger one
        if pngData.count < 6 * 1024 * 1024 { // If less than 6MB
            let largerSize = CGSize(width: 6000, height: 4000)
            let largerRenderer = UIGraphicsImageRenderer(size: largerSize)
            let largerImage = largerRenderer.image { context in
                for x in stride(from: 0, to: largerSize.width, by: 5) {
                    for y in stride(from: 0, to: largerSize.height, by: 5) {
                        let color = UIColor(
                            red: CGFloat(x) / largerSize.width,
                            green: CGFloat(y) / largerSize.height,
                            blue: sin(CGFloat(x + y) / 50) * 0.5 + 0.5,
                            alpha: 1.0
                        )
                        color.setFill()
                        context.fill(CGRect(x: x, y: y, width: 5, height: 5))
                    }
                }
            }
            return largerImage.pngData() ?? pngData
        }

        return pngData
    }
    
    private func createTestPDFData() -> Data {
        // Create a larger PDF that meets minimum size requirements (1KB+)
        var pdfContent = "%PDF-1.4\n"
        pdfContent += "1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
        pdfContent += "2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n"
        pdfContent += "3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n"
        pdfContent += "/Contents 4 0 R\n/Resources <<\n/Font <<\n/F1 5 0 R\n>>\n>>\n>>\nendobj\n"
        pdfContent += "4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(Hello World) Tj\nET\nendstream\nendobj\n"
        pdfContent += "5 0 obj\n<<\n/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica\n>>\nendobj\n"

        // Add some padding to ensure we meet minimum size
        let padding = String(repeating: "% This is padding to meet minimum file size requirements\n", count: 20)
        pdfContent += padding

        pdfContent += "xref\n0 6\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n"
        pdfContent += "0000000120 00000 n \n0000000179 00000 n \n0000000364 00000 n \n"
        pdfContent += "trailer\n<<\n/Size 6\n/Root 1 0 R\n>>\nstartxref\n492\n%%EOF\n"

        return Data(pdfContent.utf8)
    }
    
    private func createLargeImageData() -> Data {
        return createCompressibleImageData()
    }
    
    // MARK: - Processing Result Tests
    
    func testProcessingResultProperties() {
        let originalSize: Int64 = 1000
        let processedSize: Int64 = 800
        let result = FileProcessor.ProcessingResult(
            contentBlock: .text("test"),
            originalSize: originalSize,
            processedSize: processedSize,
            wasCompressed: true,
            processingTime: 0.5
        )
        
        XCTAssertEqual(result.compressionRatio, 0.8, accuracy: 0.001)
        XCTAssertEqual(result.sizeSavings, 200)
    }
    
    func testProcessingResultZeroOriginalSize() {
        let result = FileProcessor.ProcessingResult(
            contentBlock: .text("test"),
            originalSize: 0,
            processedSize: 100,
            wasCompressed: false,
            processingTime: 0.1
        )
        
        XCTAssertEqual(result.compressionRatio, 1.0)
        XCTAssertEqual(result.sizeSavings, -100)
    }
    
    // MARK: - Main Processing Tests
    
    func testProcessFileValidImage() throws {
        let imageData = createTestImageData()
        let fileName = "test.jpg"
        let mimeType = "image/jpeg"
        
        let result = try FileProcessor.processFile(
            data: imageData,
            fileName: fileName,
            mimeType: mimeType,
            for: .google
        )
        
        XCTAssertEqual(result.originalSize, Int64(imageData.count))
        XCTAssertFalse(result.wasCompressed) // Small image shouldn't need compression
        XCTAssertGreaterThan(result.processingTime, 0)
        
        if case .image(let imageInfo) = result.contentBlock {
            XCTAssertEqual(imageInfo.mimeType, mimeType)
            XCTAssertNotNil(imageInfo.base64Data)
            XCTAssertEqual(imageInfo.fileSizeBytes, Int64(imageData.count))
        } else {
            XCTFail("Expected image content block")
        }
    }
    
    func testProcessFileValidDocument() throws {
        let pdfData = createTestPDFData()
        let fileName = "document.pdf"
        let mimeType = "application/pdf"
        
        let result = try FileProcessor.processFile(
            data: pdfData,
            fileName: fileName,
            mimeType: mimeType,
            for: .google
        )
        
        XCTAssertEqual(result.originalSize, Int64(pdfData.count))
        XCTAssertFalse(result.wasCompressed) // Documents are not compressed
        
        if case .file(let fileInfo) = result.contentBlock {
            XCTAssertEqual(fileInfo.fileName, fileName)
            XCTAssertEqual(fileInfo.mimeType, mimeType)
            XCTAssertEqual(fileInfo.fileType, .document)
            XCTAssertEqual(fileInfo.fileSizeBytes, Int64(pdfData.count))
        } else {
            XCTFail("Expected file content block")
        }
    }
    
    func testProcessFileUnsupportedFormat() {
        let data = Data("test".utf8)
        let fileName = "test.unknown"
        let mimeType = "application/unknown"
        
        XCTAssertThrowsError(try FileProcessor.processFile(
            data: data,
            fileName: fileName,
            mimeType: mimeType,
            for: .google
        )) { error in
            if case ChatError.fileProcessingFailed(let fileError) = error,
               case FileProcessingError.unsupportedFormat(let errorMimeType, let apiStyle) = fileError {
                XCTAssertEqual(errorMimeType, mimeType)
                XCTAssertEqual(apiStyle, .google)
            } else {
                XCTFail("Expected unsupportedFormat error wrapped in ChatError")
            }
        }
    }
    
    func testProcessFileExceedsSizeLimit() {
        let largeData = Data(repeating: 0x41, count: 25 * 1024 * 1024) // 25MB
        let fileName = "large.pdf"
        let mimeType = "application/pdf"
        
        XCTAssertThrowsError(try FileProcessor.processFile(
            data: largeData,
            fileName: fileName,
            mimeType: mimeType,
            for: .google // 20MB limit
        )) { error in
            if case ChatError.fileProcessingFailed(let fileError) = error,
               case FileProcessingError.fileTooLarge = fileError {
                // Expected error
            } else {
                XCTFail("Expected fileTooLarge error wrapped in ChatError")
            }
        }
    }
    
    func testProcessFileImageCompression() throws {
        let largeImageData = createLargeImageData()
        let fileName = "large.png"
        let mimeType = "image/png"

        // Debug: Print actual file size
        print("Test image size: \(largeImageData.count) bytes (\(FileSizeValidator.humanReadableSize(Int64(largeImageData.count))))")
        print("Anthropic limit: \(APIStyle.anthropic.maxFileSizeBytes) bytes (\(FileSizeValidator.humanReadableSize(APIStyle.anthropic.maxFileSizeBytes)))")
        print("Target compression size: \(FileSizeValidator.targetCompressionSize(for: .anthropic)) bytes")

        let result = try FileProcessor.processFile(
            data: largeImageData,
            fileName: fileName,
            mimeType: mimeType,
            for: .anthropic // 5MB limit, should trigger compression
        )

        print("Original size: \(result.originalSize), Processed size: \(result.processedSize), Was compressed: \(result.wasCompressed)")

        XCTAssertEqual(result.originalSize, Int64(largeImageData.count))

        // Only assert compression if the original image was actually larger than the target
        if Int64(largeImageData.count) > FileSizeValidator.targetCompressionSize(for: .anthropic) {
            XCTAssertLessThan(result.processedSize, result.originalSize)
            XCTAssertTrue(result.wasCompressed)
        }

        if case .image(let imageInfo) = result.contentBlock {
            // After compression, it should be converted to JPEG for better compression
            XCTAssertTrue(imageInfo.mimeType == "image/jpeg" || imageInfo.mimeType == "image/png")
            XCTAssertNotNil(imageInfo.base64Data)
        } else {
            XCTFail("Expected image content block")
        }
    }
    
    // MARK: - Specific Processing Tests
    
    func testProcessImageValid() throws {
        let imageData = createTestImageData()
        let mimeType = "image/jpeg"
        let caption = "Test image"
        
        let result = try FileProcessor.processImage(
            data: imageData,
            mimeType: mimeType,
            for: .google,
            caption: caption
        )
        
        if case .image(let imageInfo) = result.contentBlock {
            XCTAssertEqual(imageInfo.caption, caption)
            XCTAssertEqual(imageInfo.mimeType, mimeType)
        } else {
            XCTFail("Expected image content block")
        }
    }
    
    func testProcessImageUnsupportedFormat() {
        let imageData = createTestImageData()
        let mimeType = "image/bmp" // Not supported by any API
        
        XCTAssertThrowsError(try FileProcessor.processImage(
            data: imageData,
            mimeType: mimeType,
            for: .google
        )) { error in
            if case ChatError.fileProcessingFailed(let fileError) = error,
               case FileProcessingError.unsupportedFormat = fileError {
                // Expected error
            } else {
                XCTFail("Expected unsupportedFormat error")
            }
        }
    }
    
    func testProcessImageInvalidData() {
        let invalidData = Data("Not an image".utf8)
        let mimeType = "image/jpeg"
        
        XCTAssertThrowsError(try FileProcessor.processImage(
            data: invalidData,
            mimeType: mimeType,
            for: .google
        )) { error in
            if case ChatError.fileProcessingFailed(let fileError) = error,
               case FileProcessingError.invalidFileData = fileError {
                // Expected error
            } else {
                XCTFail("Expected invalidFileData error")
            }
        }
    }
    
    func testProcessDocumentValid() throws {
        let pdfData = createTestPDFData()
        let fileName = "test.pdf"
        let mimeType = "application/pdf"
        
        let result = try FileProcessor.processDocument(
            data: pdfData,
            fileName: fileName,
            mimeType: mimeType,
            for: .google
        )
        
        XCTAssertFalse(result.wasCompressed)
        
        if case .file(let fileInfo) = result.contentBlock {
            XCTAssertEqual(fileInfo.fileName, fileName)
            XCTAssertEqual(fileInfo.mimeType, mimeType)
            XCTAssertEqual(fileInfo.fileType, .document)
        } else {
            XCTFail("Expected file content block")
        }
    }
    
    func testProcessDocumentUnsupportedFormat() {
        let data = Data("test".utf8)
        let fileName = "test.doc"
        let mimeType = "application/msword"
        
        XCTAssertThrowsError(try FileProcessor.processDocument(
            data: data,
            fileName: fileName,
            mimeType: mimeType,
            for: .openaiCompatible // Only supports PDF
        )) { error in
            if case ChatError.fileProcessingFailed(let fileError) = error,
               case FileProcessingError.unsupportedFormat = fileError {
                // Expected error
            } else {
                XCTFail("Expected unsupportedFormat error")
            }
        }
    }
    
    // MARK: - Batch Processing Tests
    
    func testProcessFilesMultiple() throws {
        let imageData = createTestImageData()
        let pdfData = createTestPDFData()
        
        let files = [
            (data: imageData, fileName: "image.jpg", mimeType: "image/jpeg"),
            (data: pdfData, fileName: "document.pdf", mimeType: "application/pdf")
        ]
        
        let results = try FileProcessor.processFiles(files, for: .google)
        
        XCTAssertEqual(results.count, 2)
        
        // Check first result (image)
        if case .image(let imageInfo) = results[0].contentBlock {
            XCTAssertEqual(imageInfo.mimeType, "image/jpeg")
        } else {
            XCTFail("Expected image content block")
        }
        
        // Check second result (document)
        if case .file(let fileInfo) = results[1].contentBlock {
            XCTAssertEqual(fileInfo.fileName, "document.pdf")
            XCTAssertEqual(fileInfo.mimeType, "application/pdf")
        } else {
            XCTFail("Expected file content block")
        }
    }
    
    func testProcessFilesWithError() {
        let validData = createTestImageData()
        let invalidData = Data("test".utf8)
        
        let files = [
            (data: validData, fileName: "valid.jpg", mimeType: "image/jpeg"),
            (data: invalidData, fileName: "invalid.unknown", mimeType: "application/unknown")
        ]
        
        XCTAssertThrowsError(try FileProcessor.processFiles(files, for: .google)) { error in
            // Should fail on the second file
            XCTAssertTrue(error is ChatError)
        }
    }
    
    // MARK: - Validation Tests
    
    func testValidateFileValid() {
        let imageData = createTestImageData()
        let mimeType = "image/jpeg"
        
        let validation = FileProcessor.validateFile(
            data: imageData,
            mimeType: mimeType,
            for: .google
        )
        
        XCTAssertTrue(validation.isValid)
        XCTAssertTrue(validation.errors.isEmpty)
        // May have recommendations for compression
    }
    
    func testValidateFileInvalidFormat() {
        let data = Data("test".utf8)
        let mimeType = "application/unknown"
        
        let validation = FileProcessor.validateFile(
            data: data,
            mimeType: mimeType,
            for: .google
        )
        
        XCTAssertFalse(validation.isValid)
        XCTAssertFalse(validation.errors.isEmpty)
        
        let hasUnsupportedFormatError = validation.errors.contains { error in
            if case .unsupportedFormat = error {
                return true
            }
            return false
        }
        XCTAssertTrue(hasUnsupportedFormatError)
    }
    
    func testValidateFileTooLarge() {
        let largeData = Data(repeating: 0x41, count: 25 * 1024 * 1024) // 25MB
        let mimeType = "application/pdf"
        
        let validation = FileProcessor.validateFile(
            data: largeData,
            mimeType: mimeType,
            for: .google // 20MB limit
        )
        
        XCTAssertFalse(validation.isValid)
        XCTAssertFalse(validation.errors.isEmpty)
        
        let hasFileTooLargeError = validation.errors.contains { error in
            if case .fileTooLarge = error {
                return true
            }
            return false
        }
        XCTAssertTrue(hasFileTooLargeError)
    }
    
    func testValidateFileInvalidImageData() {
        let invalidImageData = Data("Not an image".utf8)
        let mimeType = "image/jpeg"
        
        let validation = FileProcessor.validateFile(
            data: invalidImageData,
            mimeType: mimeType,
            for: .google
        )
        
        XCTAssertFalse(validation.isValid)
        XCTAssertFalse(validation.errors.isEmpty)
        
        let hasInvalidDataError = validation.errors.contains { error in
            if case .invalidFileData = error {
                return true
            }
            return false
        }
        XCTAssertTrue(hasInvalidDataError)
    }
    
    func testValidateFileWithRecommendations() {
        let largeImageData = createLargeImageData()
        let mimeType = "image/png"

        let validation = FileProcessor.validateFile(
            data: largeImageData,
            mimeType: mimeType,
            for: .google
        )

        // Should be valid but may have compression recommendations
        XCTAssertTrue(validation.isValid)
        XCTAssertTrue(validation.errors.isEmpty)
        // May have recommendations depending on size
    }
    
    // MARK: - Integration Tests
    
    func testFullWorkflowImageProcessing() throws {
        let largeImageData = createLargeImageData()
        let fileName = "large_image.png"
        let mimeType = "image/png"

        // Debug: Print actual file size
        print("Test image size: \(largeImageData.count) bytes (\(FileSizeValidator.humanReadableSize(Int64(largeImageData.count))))")
        print("Anthropic limit: \(APIStyle.anthropic.maxFileSizeBytes) bytes (\(FileSizeValidator.humanReadableSize(APIStyle.anthropic.maxFileSizeBytes)))")

        // First validate - should fail for large image with Anthropic's 5MB limit
        let validation = FileProcessor.validateFile(
            data: largeImageData,
            mimeType: mimeType,
            for: .anthropic
        )

        // Validation should fail because the image is too large
        XCTAssertFalse(validation.isValid, "Validation should fail for large image")
        XCTAssertTrue(validation.errors.contains { error in
            if case .fileTooLarge = error { return true }
            return false
        }, "Should contain fileTooLarge error")

        // But processing should still work because it includes compression
        let result = try FileProcessor.processFile(
            data: largeImageData,
            fileName: fileName,
            mimeType: mimeType,
            for: .anthropic
        )

        // Should be compressed for Anthropic's smaller limit
        XCTAssertTrue(result.wasCompressed)
        XCTAssertLessThan(result.processedSize, result.originalSize)
        XCTAssertLessThanOrEqual(result.processedSize, APIStyle.anthropic.maxFileSizeBytes)

        if case .image(let imageInfo) = result.contentBlock {
            XCTAssertNotNil(imageInfo.base64Data)
            XCTAssertEqual(imageInfo.mimeType, mimeType)
        } else {
            XCTFail("Expected image content block")
        }
    }
    
    // MARK: - Performance Tests
    
    func testProcessingPerformance() {
        let imageData = createTestImageData()
        let fileName = "test.jpg"
        let mimeType = "image/jpeg"
        
        measure {
            _ = try? FileProcessor.processFile(
                data: imageData,
                fileName: fileName,
                mimeType: mimeType,
                for: .google
            )
        }
    }
}
