import XCTest
import UIKit
@testable import lavachat

final class ImageCompressorTests: XCTestCase {
    
    // MARK: - Test Data Setup
    
    private func createTestImage(size: CGSize = CGSize(width: 100, height: 100), color: UIColor = .red) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
    }
    
    private func createTestImageData(size: CGSize = CGSize(width: 100, height: 100), quality: CGFloat = 1.0) -> Data {
        let image = createTestImage(size: size)
        return image.jpegData(compressionQuality: quality) ?? Data()
    }
    
    private func createLargeImageData() -> Data {
        // Create a large image that would exceed typical size limits
        let largeImage = createTestImage(size: CGSize(width: 4000, height: 3000))
        let data = largeImage.jpegData(compressionQuality: 1.0) ?? Data()

        // Ensure the image is actually large enough to need compression for Anthropic (5MB limit)
        // If it's not large enough, create a larger one
        if data.count < 6 * 1024 * 1024 { // 6MB to ensure it exceeds 5MB limit
            // Create an even larger image
            let veryLargeImage = createTestImage(size: CGSize(width: 6000, height: 4500))
            return veryLargeImage.jpegData(compressionQuality: 1.0) ?? data
        }

        return data
    }
    
    // MARK: - Compression Options Tests
    
    func testDefaultCompressionOptions() {
        let options = ImageCompressor.CompressionOptions.default
        
        XCTAssertEqual(options.targetSizeBytes, 5 * 1024 * 1024) // 5MB
        XCTAssertEqual(options.maxDimension, 2048)
        XCTAssertEqual(options.qualitySteps, [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3])
        XCTAssertTrue(options.preserveAspectRatio)
    }
    
    func testCompressionOptionsForAPIStyle() {
        let googleOptions = ImageCompressor.CompressionOptions.forAPIStyle(.google)
        let expectedTargetSize = FileSizeValidator.targetCompressionSize(for: .google)
        XCTAssertEqual(googleOptions.targetSizeBytes, expectedTargetSize)
        
        let anthropicOptions = ImageCompressor.CompressionOptions.forAPIStyle(.anthropic)
        let expectedAnthropicSize = FileSizeValidator.targetCompressionSize(for: .anthropic)
        XCTAssertEqual(anthropicOptions.targetSizeBytes, expectedAnthropicSize)
    }
    
    // MARK: - Main Compression Tests
    
    func testCompressImageWithinTargetSize() throws {
        let smallImageData = createTestImageData(size: CGSize(width: 100, height: 100))
        let options = ImageCompressor.CompressionOptions(
            targetSizeBytes: Int64(smallImageData.count + 1000), // Target larger than current
            maxDimension: 2048,
            qualitySteps: [0.9, 0.8, 0.7],
            preserveAspectRatio: true
        )
        
        let result = try ImageCompressor.compressImage(smallImageData, options: options)
        
        // Should return original data since it's already within target
        XCTAssertEqual(result, smallImageData)
    }
    
    func testCompressImageExceedsTargetSize() throws {
        let largeImageData = createLargeImageData()
        let options = ImageCompressor.CompressionOptions(
            targetSizeBytes: 100 * 1024, // 100KB target
            maxDimension: 1024,
            qualitySteps: [0.8, 0.6, 0.4, 0.2],
            preserveAspectRatio: true
        )
        
        let result = try ImageCompressor.compressImage(largeImageData, options: options)
        
        XCTAssertLessThanOrEqual(Int64(result.count), options.targetSizeBytes)
        XCTAssertLessThan(result.count, largeImageData.count)
        
        // Verify the result is still a valid image
        XCTAssertNotNil(UIImage(data: result))
    }
    
    func testCompressImageForAPI() throws {
        let largeImageData = createLargeImageData()
        
        let result = try ImageCompressor.compressImageForAPI(largeImageData, apiStyle: .anthropic)
        
        let targetSize = FileSizeValidator.targetCompressionSize(for: .anthropic)
        XCTAssertLessThanOrEqual(Int64(result.count), targetSize)
        
        // Verify the result is still a valid image
        XCTAssertNotNil(UIImage(data: result))
    }
    
    func testCompressImageInvalidData() {
        let invalidData = Data("Not an image".utf8)
        let options = ImageCompressor.CompressionOptions.default
        
        XCTAssertThrowsError(try ImageCompressor.compressImage(invalidData, options: options)) { error in
            if case FileProcessingError.invalidFileData(let reason) = error {
                XCTAssertEqual(reason, "Cannot create UIImage from data")
            } else {
                XCTFail("Expected invalidFileData error")
            }
        }
    }
    
    // MARK: - Resizing Tests
    
    func testResizeImageIfNeeded() throws {
        let largeImage = createTestImage(size: CGSize(width: 3000, height: 2000))
        let maxDimension: CGFloat = 1500
        
        let result = try ImageCompressor.resizeImageIfNeeded(largeImage, maxDimension: maxDimension)
        
        XCTAssertLessThanOrEqual(result.size.width, maxDimension)
        XCTAssertLessThanOrEqual(result.size.height, maxDimension)
        
        // Verify aspect ratio is maintained
        let originalAspectRatio = largeImage.size.width / largeImage.size.height
        let resultAspectRatio = result.size.width / result.size.height
        XCTAssertEqual(originalAspectRatio, resultAspectRatio, accuracy: 0.01)
    }
    
    func testResizeImageIfNeededWithinLimits() throws {
        let smallImage = createTestImage(size: CGSize(width: 500, height: 300))
        let maxDimension: CGFloat = 1000
        
        let result = try ImageCompressor.resizeImageIfNeeded(smallImage, maxDimension: maxDimension)
        
        // Should return the same image since it's within limits
        XCTAssertEqual(result.size, smallImage.size)
    }
    
    func testResizeImageAggressively() throws {
        let largeImage = createTestImage(size: CGSize(width: 2000, height: 1500))
        let targetSize: Int64 = 50 * 1024 // 50KB
        
        let result = try ImageCompressor.resizeImageAggressively(largeImage, targetSize: targetSize)
        
        // Should be significantly smaller than original
        XCTAssertLessThan(result.size.width, largeImage.size.width)
        XCTAssertLessThan(result.size.height, largeImage.size.height)
        
        // Test that compressed result might meet target size
        if let compressedData = result.jpegData(compressionQuality: 0.5) {
            // This is a heuristic test - aggressive resizing should help meet target
            XCTAssertLessThan(compressedData.count, Int(largeImage.jpegData(compressionQuality: 0.5)?.count ?? Int.max))
        }
    }
    
    func testResizeImageToSpecificSize() throws {
        let originalImage = createTestImage(size: CGSize(width: 1000, height: 800))
        let targetSize = CGSize(width: 500, height: 400)
        
        let result = try ImageCompressor.resizeImage(originalImage, to: targetSize)
        
        XCTAssertEqual(result.size, targetSize)
    }
    
    func testResizeImageInvalidSize() {
        let image = createTestImage()
        let invalidSize = CGSize(width: 0, height: 0)
        
        XCTAssertThrowsError(try ImageCompressor.resizeImage(image, to: invalidSize)) { error in
            if case FileProcessingError.compressionFailed(let reason) = error {
                XCTAssertEqual(reason, "Resized image has invalid dimensions")
            } else {
                XCTFail("Expected compressionFailed error")
            }
        }
    }
    
    // MARK: - Format Conversion Tests
    
    func testConvertToJPEG() throws {
        let image = createTestImage()
        let quality: CGFloat = 0.8
        
        let result = try ImageCompressor.convertToJPEG(image, quality: quality)
        
        XCTAssertGreaterThan(result.count, 0)
        
        // Verify it's a valid JPEG by creating UIImage from it
        let recreatedImage = UIImage(data: result)
        XCTAssertNotNil(recreatedImage)
    }
    
    func testConvertToPNG() throws {
        let image = createTestImage()
        
        let result = try ImageCompressor.convertToPNG(image)
        
        XCTAssertGreaterThan(result.count, 0)
        
        // Verify it's a valid PNG by creating UIImage from it
        let recreatedImage = UIImage(data: result)
        XCTAssertNotNil(recreatedImage)
    }
    
    // MARK: - Utility Methods Tests
    
    func testEstimateCompressedSize() {
        let image = createTestImage(size: CGSize(width: 500, height: 500))
        
        let highQualitySize = ImageCompressor.estimateCompressedSize(image, quality: 1.0)
        let lowQualitySize = ImageCompressor.estimateCompressedSize(image, quality: 0.1)
        
        XCTAssertGreaterThan(highQualitySize, 0)
        XCTAssertGreaterThan(lowQualitySize, 0)
        XCTAssertGreaterThan(highQualitySize, lowQualitySize)
    }
    
    func testOptimalQuality() {
        let image = createTestImage(size: CGSize(width: 1000, height: 1000))
        let targetSize: Int64 = 100 * 1024 // 100KB
        
        let optimalQuality = ImageCompressor.optimalQuality(for: image, targetSize: targetSize)
        
        if let quality = optimalQuality {
            XCTAssertGreaterThanOrEqual(quality, 0.0)
            XCTAssertLessThanOrEqual(quality, 1.0)
            
            // Verify that this quality produces a size within target
            let estimatedSize = ImageCompressor.estimateCompressedSize(image, quality: quality)
            XCTAssertLessThanOrEqual(estimatedSize, targetSize)
        }
        // If nil, it means the image cannot be compressed to target size even at lowest quality
    }
    
    func testOptimalQualityImpossibleTarget() {
        let image = createTestImage(size: CGSize(width: 100, height: 100))
        let impossibleTargetSize: Int64 = 100 // 100 bytes - too small for any reasonable image
        
        let optimalQuality = ImageCompressor.optimalQuality(for: image, targetSize: impossibleTargetSize)
        
        // Should return nil for impossible targets
        XCTAssertNil(optimalQuality)
    }
    
    func testIsValidImageData() {
        let validImageData = createTestImageData()
        XCTAssertTrue(ImageCompressor.isValidImageData(validImageData))
        
        let invalidData = Data("Not an image".utf8)
        XCTAssertFalse(ImageCompressor.isValidImageData(invalidData))
        
        let emptyData = Data()
        XCTAssertFalse(ImageCompressor.isValidImageData(emptyData))
    }
    
    // MARK: - Error Handling Tests
    
    func testCompressionFailureWhenCannotMeetTarget() {
        let imageData = createTestImageData(size: CGSize(width: 100, height: 100))
        let impossibleOptions = ImageCompressor.CompressionOptions(
            targetSizeBytes: 10, // 10 bytes - impossible for any image
            maxDimension: 10,
            qualitySteps: [0.1],
            preserveAspectRatio: true
        )
        
        XCTAssertThrowsError(try ImageCompressor.compressImage(imageData, options: impossibleOptions)) { error in
            if case FileProcessingError.compressionFailed(let reason) = error {
                XCTAssertTrue(reason?.contains("Unable to compress") == true)
            } else {
                XCTFail("Expected compressionFailed error")
            }
        }
    }
    
    // MARK: - Integration Tests
    
    func testFullCompressionWorkflow() throws {
        // Create a large image that needs compression
        let largeImageData = createLargeImageData()
        let originalSize = largeImageData.count
        
        // Compress for Anthropic API (5MB limit)
        let compressedData = try ImageCompressor.compressImageForAPI(largeImageData, apiStyle: .anthropic)
        let compressedSize = compressedData.count
        
        // Verify compression worked
        XCTAssertLessThan(compressedSize, originalSize)
        XCTAssertLessThanOrEqual(Int64(compressedSize), APIStyle.anthropic.maxFileSizeBytes)
        
        // Verify the compressed image is still valid
        let compressedImage = UIImage(data: compressedData)
        XCTAssertNotNil(compressedImage)
        
        // Verify it's smaller than original but still reasonable quality
        if let originalImage = UIImage(data: largeImageData) {
            XCTAssertLessThanOrEqual(compressedImage!.size.width, originalImage.size.width)
            XCTAssertLessThanOrEqual(compressedImage!.size.height, originalImage.size.height)
        }
    }
    
    // MARK: - Performance Tests
    
    func testCompressionPerformance() {
        let largeImageData = createLargeImageData()
        
        measure {
            _ = try? ImageCompressor.compressImageForAPI(largeImageData, apiStyle: .google)
        }
    }
}
