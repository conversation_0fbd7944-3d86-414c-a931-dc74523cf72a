{"originHash": "9f870fd7ac7214986c0172751b1162040754776f46e8500fac45a27e843e8390", "pins": [{"identity": "networkimage", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/NetworkImage", "state": {"revision": "2849f5323265386e200484b0d0f896e73c3411b9", "version": "6.0.1"}}, {"identity": "splash", "kind": "remoteSourceControl", "location": "https://github.com/JohnSundell/Splash", "state": {"revision": "7f4df436eb78fe64fe2c32c58006e9949fa28ad8", "version": "0.16.0"}}, {"identity": "swift-cmark", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-cmark", "state": {"revision": "b022b08312decdc46585e0b3440d97f6f22ef703", "version": "0.6.0"}}, {"identity": "swift-markdown-ui", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/swift-markdown-ui", "state": {"revision": "5f613358148239d0292c0cef674a3c2314737f9e", "version": "2.4.1"}}, {"identity": "symbolpicker", "kind": "remoteSourceControl", "location": "https://github.com/xnth97/SymbolPicker.git", "state": {"revision": "8bb08c982235b8e601bc500a41e770d7e198759b", "version": "1.6.2"}}], "version": 3}