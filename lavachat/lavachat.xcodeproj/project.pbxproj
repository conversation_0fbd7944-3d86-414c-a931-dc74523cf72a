// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		65DC143E2E2699FA0017F797 /* SymbolPicker in Frameworks */ = {isa = PBXBuildFile; productRef = 65DC143D2E2699FA0017F797 /* SymbolPicker */; };
		65FB77D52DD8CCC000730C44 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 65FB77D42DD8CCC000730C44 /* MarkdownUI */; };
		65FB77D82DD8DEA600730C44 /* Splash in Frameworks */ = {isa = PBXBuildFile; productRef = 65FB77D72DD8DEA600730C44 /* Splash */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		657DDEDD2DAF516F00B58F56 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 657DDEC02DAF516E00B58F56 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 657DDEC72DAF516E00B58F56;
			remoteInfo = lavachat;
		};
		657DDEE72DAF516F00B58F56 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 657DDEC02DAF516E00B58F56 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 657DDEC72DAF516E00B58F56;
			remoteInfo = lavachat;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		657DDEC82DAF516E00B58F56 /* lavachat.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = lavachat.app; sourceTree = BUILT_PRODUCTS_DIR; };
		657DDEDC2DAF516F00B58F56 /* lavachatTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = lavachatTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		657DDEE62DAF516F00B58F56 /* lavachatUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = lavachatUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		657DDEEE2DAF516F00B58F56 /* Exceptions for "lavachat" folder in "lavachat" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 657DDEC72DAF516E00B58F56 /* lavachat */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		657DDECA2DAF516E00B58F56 /* lavachat */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				657DDEEE2DAF516F00B58F56 /* Exceptions for "lavachat" folder in "lavachat" target */,
			);
			path = lavachat;
			sourceTree = "<group>";
		};
		657DDEDF2DAF516F00B58F56 /* lavachatTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = lavachatTests;
			sourceTree = "<group>";
		};
		657DDEE92DAF516F00B58F56 /* lavachatUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = lavachatUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		657DDEC52DAF516E00B58F56 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65FB77D52DD8CCC000730C44 /* MarkdownUI in Frameworks */,
				65FB77D82DD8DEA600730C44 /* Splash in Frameworks */,
				65DC143E2E2699FA0017F797 /* SymbolPicker in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		657DDED92DAF516F00B58F56 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		657DDEE32DAF516F00B58F56 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		657DDEBF2DAF516E00B58F56 = {
			isa = PBXGroup;
			children = (
				657DDECA2DAF516E00B58F56 /* lavachat */,
				657DDEDF2DAF516F00B58F56 /* lavachatTests */,
				657DDEE92DAF516F00B58F56 /* lavachatUITests */,
				65FB77D32DD8CCC000730C44 /* Frameworks */,
				657DDEC92DAF516E00B58F56 /* Products */,
			);
			sourceTree = "<group>";
		};
		657DDEC92DAF516E00B58F56 /* Products */ = {
			isa = PBXGroup;
			children = (
				657DDEC82DAF516E00B58F56 /* lavachat.app */,
				657DDEDC2DAF516F00B58F56 /* lavachatTests.xctest */,
				657DDEE62DAF516F00B58F56 /* lavachatUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		65FB77D32DD8CCC000730C44 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		657DDEC72DAF516E00B58F56 /* lavachat */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 657DDEEF2DAF516F00B58F56 /* Build configuration list for PBXNativeTarget "lavachat" */;
			buildPhases = (
				657DDEC42DAF516E00B58F56 /* Sources */,
				657DDEC52DAF516E00B58F56 /* Frameworks */,
				657DDEC62DAF516E00B58F56 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				657DDECA2DAF516E00B58F56 /* lavachat */,
			);
			name = lavachat;
			packageProductDependencies = (
				65FB77D42DD8CCC000730C44 /* MarkdownUI */,
				65FB77D72DD8DEA600730C44 /* Splash */,
				65DC143D2E2699FA0017F797 /* SymbolPicker */,
			);
			productName = lavachat;
			productReference = 657DDEC82DAF516E00B58F56 /* lavachat.app */;
			productType = "com.apple.product-type.application";
		};
		657DDEDB2DAF516F00B58F56 /* lavachatTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 657DDEF42DAF516F00B58F56 /* Build configuration list for PBXNativeTarget "lavachatTests" */;
			buildPhases = (
				657DDED82DAF516F00B58F56 /* Sources */,
				657DDED92DAF516F00B58F56 /* Frameworks */,
				657DDEDA2DAF516F00B58F56 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				657DDEDE2DAF516F00B58F56 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				657DDEDF2DAF516F00B58F56 /* lavachatTests */,
			);
			name = lavachatTests;
			packageProductDependencies = (
			);
			productName = lavachatTests;
			productReference = 657DDEDC2DAF516F00B58F56 /* lavachatTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		657DDEE52DAF516F00B58F56 /* lavachatUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 657DDEF72DAF516F00B58F56 /* Build configuration list for PBXNativeTarget "lavachatUITests" */;
			buildPhases = (
				657DDEE22DAF516F00B58F56 /* Sources */,
				657DDEE32DAF516F00B58F56 /* Frameworks */,
				657DDEE42DAF516F00B58F56 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				657DDEE82DAF516F00B58F56 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				657DDEE92DAF516F00B58F56 /* lavachatUITests */,
			);
			name = lavachatUITests;
			packageProductDependencies = (
			);
			productName = lavachatUITests;
			productReference = 657DDEE62DAF516F00B58F56 /* lavachatUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		657DDEC02DAF516E00B58F56 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					657DDEC72DAF516E00B58F56 = {
						CreatedOnToolsVersion = 16.3;
					};
					657DDEDB2DAF516F00B58F56 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 657DDEC72DAF516E00B58F56;
					};
					657DDEE52DAF516F00B58F56 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 657DDEC72DAF516E00B58F56;
					};
				};
			};
			buildConfigurationList = 657DDEC32DAF516E00B58F56 /* Build configuration list for PBXProject "lavachat" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 657DDEBF2DAF516E00B58F56;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				65FB77D22DD8CCA200730C44 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
				65FB77D62DD8DE9900730C44 /* XCRemoteSwiftPackageReference "Splash" */,
				65DC143C2E2699FA0017F797 /* XCRemoteSwiftPackageReference "SymbolPicker" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 657DDEC92DAF516E00B58F56 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				657DDEC72DAF516E00B58F56 /* lavachat */,
				657DDEDB2DAF516F00B58F56 /* lavachatTests */,
				657DDEE52DAF516F00B58F56 /* lavachatUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		657DDEC62DAF516E00B58F56 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		657DDEDA2DAF516F00B58F56 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		657DDEE42DAF516F00B58F56 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		657DDEC42DAF516E00B58F56 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		657DDED82DAF516F00B58F56 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		657DDEE22DAF516F00B58F56 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		657DDEDE2DAF516F00B58F56 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 657DDEC72DAF516E00B58F56 /* lavachat */;
			targetProxy = 657DDEDD2DAF516F00B58F56 /* PBXContainerItemProxy */;
		};
		657DDEE82DAF516F00B58F56 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 657DDEC72DAF516E00B58F56 /* lavachat */;
			targetProxy = 657DDEE72DAF516F00B58F56 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		657DDEF02DAF516F00B58F56 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = lavachat/lavachat.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = lavachat/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LavaChat;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.github.alwayskeepcoding.lavachat;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		657DDEF12DAF516F00B58F56 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = lavachat/lavachat.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = lavachat/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LavaChat;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.github.alwayskeepcoding.lavachat;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		657DDEF22DAF516F00B58F56 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		657DDEF32DAF516F00B58F56 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		657DDEF52DAF516F00B58F56 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.github.alwayskeepcoding.lavachatTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/lavachat.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/lavachat";
			};
			name = Debug;
		};
		657DDEF62DAF516F00B58F56 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.github.alwayskeepcoding.lavachatTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/lavachat.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/lavachat";
			};
			name = Release;
		};
		657DDEF82DAF516F00B58F56 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.github.alwayskeepcoding.lavachatUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = lavachat;
			};
			name = Debug;
		};
		657DDEF92DAF516F00B58F56 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FVYT9W5M9Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.github.alwayskeepcoding.lavachatUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = lavachat;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		657DDEC32DAF516E00B58F56 /* Build configuration list for PBXProject "lavachat" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				657DDEF22DAF516F00B58F56 /* Debug */,
				657DDEF32DAF516F00B58F56 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		657DDEEF2DAF516F00B58F56 /* Build configuration list for PBXNativeTarget "lavachat" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				657DDEF02DAF516F00B58F56 /* Debug */,
				657DDEF12DAF516F00B58F56 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		657DDEF42DAF516F00B58F56 /* Build configuration list for PBXNativeTarget "lavachatTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				657DDEF52DAF516F00B58F56 /* Debug */,
				657DDEF62DAF516F00B58F56 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		657DDEF72DAF516F00B58F56 /* Build configuration list for PBXNativeTarget "lavachatUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				657DDEF82DAF516F00B58F56 /* Debug */,
				657DDEF92DAF516F00B58F56 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		65DC143C2E2699FA0017F797 /* XCRemoteSwiftPackageReference "SymbolPicker" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/xnth97/SymbolPicker.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.6.2;
			};
		};
		65FB77D22DD8CCA200730C44 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		65FB77D62DD8DE9900730C44 /* XCRemoteSwiftPackageReference "Splash" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/JohnSundell/Splash";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.16.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		65DC143D2E2699FA0017F797 /* SymbolPicker */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65DC143C2E2699FA0017F797 /* XCRemoteSwiftPackageReference "SymbolPicker" */;
			productName = SymbolPicker;
		};
		65FB77D42DD8CCC000730C44 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65FB77D22DD8CCA200730C44 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		65FB77D72DD8DEA600730C44 /* Splash */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65FB77D62DD8DE9900730C44 /* XCRemoteSwiftPackageReference "Splash" */;
			productName = Splash;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 657DDEC02DAF516E00B58F56 /* Project object */;
}
