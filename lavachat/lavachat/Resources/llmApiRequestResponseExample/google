// MARK: - Google Gemini API
// Streaming Request Example:
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent?alt=sse&key=$GEMINI_API_KEY" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "system_instruction": {
        "parts": [
          {
            "text": "You are a helpful assistant."
          }
        ]
      },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello"
          }
        ]
      },
      {
        "role": "model",
        "parts": [
          {
            "text": "Great to meet you. What would you like to know?"
          }
        ]
      },
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello again."
          }
        ]
      }
    ],
    "generationConfig": {
        "thinkingConfig": {
            "thinkingBudget": 0
        }
    }
  }'

// Streaming Response Example:
data: {"candidates": [{"content": {"parts": [{"text": "Hello there"}],"role": "model"},"index": 0}],"usageMetadata": {"promptTokenCount": 25,"candidatesTokenCount": 2,"totalTokenCount": 27,"promptTokensDetails": [{"modality": "TEXT","tokenCount": 25}]},"modelVersion": "models/gemini-2.5-flash-preview-05-20","responseId": "SWpAaKLaPL_C1MkPnPuQoAc"}

data: {"candidates": [{"content": {"parts": [{"text": "! How can I help you today? Is there something specific you'd like to talk"}],"role": "model"},"index": 0}],"usageMetadata": {"promptTokenCount": 25,"candidatesTokenCount": 20,"totalTokenCount": 45,"promptTokensDetails": [{"modality": "TEXT","tokenCount": 25}]},"modelVersion": "models/gemini-2.5-flash-preview-05-20","responseId": "SWpAaKLaPL_C1MkPnPuQoAc"}

data: {"candidates": [{"content": {"parts": [{"text": " about or are you just saying hi?"}],"role": "model"},"finishReason": "STOP","index": 0}],"usageMetadata": {"promptTokenCount": 25,"candidatesTokenCount": 28,"totalTokenCount": 53,"promptTokensDetails": [{"modality": "TEXT","tokenCount": 25}]},"modelVersion": "models/gemini-2.5-flash-preview-05-20","responseId": "SWpAaKLaPL_C1MkPnPuQoAc"}

// Reasoning Streaming Request Example:
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent?alt=sse&key=$GEMINI_API_KEY" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "system_instruction": {
        "parts": [
          {
            "text": "You are a helpful assistant."
          }
        ]
      },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello"
          }
        ]
      },
      {
        "role": "model",
        "parts": [
          {
            "text": "Great to meet you. What would you like to know?"
          }
        ]
      },
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello again."
          }
        ]
      }
    ],
    "generationConfig": {
        "temperature": 1.0,
        "thinkingConfig": {
            "thinkingBudget": 1024,
            "includeThoughts": true
        }
    }
  }'

// Reasoning Streaming Response Example:
data: {"candidates": [{"content": {"parts": [{"text": "**Greeting Response Refined**\n\nI've streamlined the initial response. Now, a simple \"Hello!\" serves to acknowledge the user's greeting, prompting them to share their needs. My aim is to be approachable and direct in guiding the conversation.\n\n\n","thought": true}],"role": "model"},"index": 0}],"usageMetadata": {"promptTokenCount": 25,"totalTokenCount": 94,"promptTokensDetails": [{"modality": "TEXT","tokenCount": 25}],"thoughtsTokenCount": 69},"modelVersion": "models/gemini-2.5-flash-preview-05-20","responseId": "NGtAaIbMMt7C1MkP497ikAc"}

data: {"candidates": [{"content": {"parts": [{"text": "**Responding, Prompting, Engaging**\n\nI'm ready to move the conversation forward after receiving a \"Hello again.\" My focus is on acknowledging the user's continued presence with a friendly greeting, perhaps even a welcoming remark. I'll then proactively ask the user how I can assist or what they'd like to talk about to maintain engagement.\n\n\n","thought": true}],"role": "model"},"index": 0}],"usageMetadata": {"promptTokenCount": 25,"totalTokenCount": 150,"promptTokensDetails": [{"modality": "TEXT","tokenCount": 25}],"thoughtsTokenCount": 125},"modelVersion": "models/gemini-2.5-flash-preview-05-20","responseId": "NGtAaIbMMt7C1MkP497ikAc"}

data: {"candidates": [{"content": {"parts": [{"text": "Hello there! Welcome back.\n\nWhat can I help you with today?"}],"role": "model"},"finishReason": "STOP","index": 0}],"usageMetadata": {"promptTokenCount": 25,"candidatesTokenCount": 13,"totalTokenCount": 163,"promptTokensDetails": [{"modality": "TEXT","tokenCount": 25}],"thoughtsTokenCount": 125},"modelVersion": "models/gemini-2.5-flash-preview-05-20","responseId": "NGtAaIbMMt7C1MkP497ikAc"}