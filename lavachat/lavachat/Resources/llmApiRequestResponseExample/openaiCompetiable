// MARK: - OpenAI API
// Streaming Request Example:
curl https://api.openai.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -d '{
    "model": "gpt-4.1",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "Hello!"
      }
    ],
    "stream": true
  }'

// Streaming Response Example:
{"id":"chatcmpl-123","object":"chat.completion.chunk","created":1694268190,"model":"gpt-4o-mini", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{"role":"assistant","content":""},"logprobs":null,"finish_reason":null}]}

{"id":"chatcmpl-123","object":"chat.completion.chunk","created":1694268190,"model":"gpt-4o-mini", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{"content":"Hello"},"logprobs":null,"finish_reason":null}]}

....

{"id":"chatcmpl-123","object":"chat.completion.chunk","created":1694268190,"model":"gpt-4o-mini", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}

// MARK: - OpenRouter OpenAI Compatible API
// Streaming Request Example:
curl -X POST https://openrouter.ai/api/v1/chat/completions \
     -H "Authorization: Bearer sk-or-v1-c4f....5d072dfec9c3" \
     -H "Content-Type: application/json" \
     -d '{
  "model": "deepseek/deepseek-chat-v3-0324:free",
  "messages": [
    {
      "content": "You are a helpful assistant.",
      "role": "system"
    },
    {
      "content": "Hi",
      "role": "user"
    }
  ],
  "stream": true
}'

// Streaming Response Example:
data: {"id":"gen-**********-wMsT9w5NIv5xxCoEUgxk","provider":"Chutes","model":"deepseek/deepseek-chat-v3-0324:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-wMsT9w5NIv5xxCoEUgxk","provider":"Chutes","model":"deepseek/deepseek-chat-v3-0324:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"Hello"},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

...

data: {"id":"gen-**********-wMsT9w5NIv5xxCoEUgxk","provider":"Chutes","model":"deepseek/deepseek-chat-v3-0324:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":" mind"},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-wMsT9w5NIv5xxCoEUgxk","provider":"Chutes","model":"deepseek/deepseek-chat-v3-0324:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"."},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-wMsT9w5NIv5xxCoEUgxk","provider":"Chutes","model":"deepseek/deepseek-chat-v3-0324:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":"stop","native_finish_reason":"stop","logprobs":null}]}

data: {"id":"gen-**********-wMsT9w5NIv5xxCoEUgxk","provider":"Chutes","model":"deepseek/deepseek-chat-v3-0324:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null,"native_finish_reason":null,"logprobs":null}],"usage":{"prompt_tokens":12,"completion_tokens":41,"total_tokens":53,"prompt_tokens_details":null}}

data: [DONE]

// Reasoning Streaming Request Example:
curl -X POST https://openrouter.ai/api/v1/chat/completions \
     -H "Authorization: Bearer sk-or-v1-c4f....5d072dfec9c3" \
     -H "Content-Type: application/json" \
     -d '{
  "model": "deepseek/deepseek-r1-0528:free",
  "messages": [
    {
      "content": "You are a helpful assistant.",
      "role": "system"
    },
    {
      "content": "Hi",
      "role": "user"
    },
    {
      "content": "Hello",
      "role": "assistant"
    },
    {
      "content": "Hello again",
      "role": "user"
    }
  ],
  "stream": true
}'

// Reasoning Streaming Response Example:
data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"Okay"},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

...

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":" again"},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":".\n"},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":""},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":""},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"Hello","reasoning":null},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

...

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"?","reasoning":null},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":null},"finish_reason":"stop","native_finish_reason":"stop","logprobs":null}]}

data: {"id":"gen-**********-hRn8to90pfp4hc4Y3owQ","provider":"Chutes","model":"deepseek/deepseek-r1-0528:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null,"native_finish_reason":null,"logprobs":null}],"usage":{"prompt_tokens":19,"completion_tokens":205,"total_tokens":224}}

data: [DONE]


// MARK: - xAI OpenAI Compatible API
// Streaming Request Example:
curl https://api.x.ai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $XAI_API_KEY" \
  -d '{
        "messages": [
          {
            "role": "system",
            "content": "You are Grok, a chatbot inspired by the Hitchhikers Guide to the Galaxy."
          },
          {
            "role": "user",
            "content": "What is the meaning of life, the universe, and everything?"
          }
        ],
        "model": "grok-3-latest",
        "stream": true,
        "temperature": 0
      }'

// Streaming Response Example:
data: {
"id":"<completion_id>","object":"chat.completion.chunk","created":<creation_time>,
"model":"grok-3",
"choices":[{"index":0,"delta":{"content":"Ah","role":"assistant"}}],
"usage":{"prompt_tokens":41,"completion_tokens":1,"total_tokens":42,
"prompt_tokens_details":{"text_tokens":41,"audio_tokens":0,"image_tokens":0,"cached_tokens":0}},
"system_fingerprint":"fp_xxxxxxxxxx"}

data: {
"id":"<completion_id>","object":"chat.completion.chunk","created":<creation_time>,
"model":"grok-3",
"choices":[{"index":0,"delta":{"content":",","role":"assistant"}}],
"usage":{"prompt_tokens":41,"completion_tokens":2,"total_tokens":43,
"prompt_tokens_details":{"text_tokens":41,"audio_tokens":0,"image_tokens":0,"cached_tokens":0}},
"system_fingerprint":"fp_xxxxxxxxxx"
}

data: [DONE]
    