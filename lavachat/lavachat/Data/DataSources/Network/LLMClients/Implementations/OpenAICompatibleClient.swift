import Foundation

/// OpenAI-compatible LLM API client
/// Supports OpenAI API and other OpenAI-compatible APIs
final class OpenAICompatibleClient: InternalLLMClientProtocol {
    
    // MARK: - Properties
    
    let supportedAPIStyle: APIStyle = .openaiCompatible
    let clientName: String = "OpenAI Compatible Client"
    
    // MARK: - Dependencies
    
    let keychainRepository: KeychainRepository
    
    // MARK: - Content Buffering
    
    private let contentBuffer = ContentBuffer()
    
    // MARK: - Request State Management
    
    private enum StreamState {
        case streaming
        case finishReceived(reason: String)
        case readyToComplete
    }
    
    /// Request-specific state to avoid conflicts between concurrent requests
    private class RequestState {
        var streamState: StreamState = .streaming
        var pendingFinishReason: String?
        var latestUsage: OpenAIUsage?
        var completionTimer: Task<Void, Never>?
        
        init() {}
        
        func cleanup() {
            completionTimer?.cancel()
            completionTimer = nil
        }
    }
    
    /// Thread-safe storage for request states
    private actor RequestStateManager {
        private var states: [UUID: RequestState] = [:]
        
        func getState(for requestId: UUID) -> RequestState {
            if let state = states[requestId] {
                return state
            } else {
                let newState = RequestState()
                states[requestId] = newState
                return newState
            }
        }
        
        func removeState(for requestId: UUID) {
            states[requestId]?.cleanup()
            states.removeValue(forKey: requestId)
        }
    }
    
    private let requestStateManager = RequestStateManager()
    
    // MARK: - Initialization
    
    init(keychainRepository: KeychainRepository) {
        self.keychainRepository = keychainRepository
    }
    
    // MARK: - Client Protocol Implementation
    
    func canHandle(instanceContext: LLMInstanceContext) -> Bool {
        return instanceContext.provider.apiStyle == .openaiCompatible
    }
    
    func sendStreamingRequest(
        request: LLMStreamingRequest,
        instanceContext: LLMInstanceContext
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        
        return AsyncThrowingStream { continuation in
            let streamingTask = Task {
                do {
                    // Validate request and get API key
                    try validateRequest(request: request, instanceContext: instanceContext)
                    
                    let apiKey = try await getAPIKey(for: instanceContext)
                    
                    // Build the request
                    let openaiRequest = try buildOpenAIRequest(
                        from: request,
                        instanceContext: instanceContext
                    )
                    
                    let httpRequestBuilder = try HTTPRequestBuilder.from(
                        instanceContext: instanceContext,
                        apiKey: apiKey
                    )
                    
                    let urlRequest = try httpRequestBuilder.buildStreamingRequest(
                        requestBody: openaiRequest
                    )
                    print("[OpenAICompatibleClient] urlRequest: \(urlRequest)")
                    print("[OpenAICompatibleClient] openaiRequest: \(String(describing: openaiRequest).prefix(5000))")
                    
                    // Create and start streaming
                    let sseStream = SSEParser.createEventStream(for: urlRequest)
                    
                    for try await event in sseStream {
                        // print("[OpenAICompatibleClient] event: \(event)")
                        
                        if event.isDone {
                            await handleDoneEvent(continuation: continuation, requestId: request.id, instanceId: request.instanceId)
                            return
                        }
                        
                        if event.hasJSONData,
                           let chunk = SSEParser.parseJSON(event, as: OpenAIStreamingChunk.self) {
                            print("[OpenAICompatibleClient] chunk: \(chunk)")
                            await processChunk(chunk, continuation: continuation, requestId: request.id, instanceId: request.instanceId)
                        } else if event.hasJSONData {
                            print("[OpenAICompatibleClient] Failed to parse JSON chunk")
                            print("  - raw data: \(event.data)")
                        }
                    }
                    
                    // Clean up state when stream ends normally
                    await requestStateManager.removeState(for: request.id)
                    continuation.finish()
                } catch {
                    // Clean up state on error
                    print("[OpenAICompatibleClient] Error: \(error)")
                    await requestStateManager.removeState(for: request.id)
                    await contentBuffer.cleanup(requestId: request.id)
                    let apiError = convertToAPIError(error)
                    continuation.finish(throwing: apiError)
                }
            }
            
            // Handle cancellation - propagate cancellation signal to internal task and SSE stream
            continuation.onTermination = { @Sendable _ in
                print("[OpenAICompatibleClient] Stream cancellation requested for request: \(request.id)")
                streamingTask.cancel()
                
                // Clean up request state on cancellation
                Task {
                    await self.requestStateManager.removeState(for: request.id)
                    await self.contentBuffer.cleanup(requestId: request.id)
                    print("[OpenAICompatibleClient] Request state cleaned up after cancellation: \(request.id)")
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    internal func buildOpenAIRequest(
        from request: LLMStreamingRequest,
        instanceContext: LLMInstanceContext
    ) throws -> OpenAIChatRequest {
        
        let model = instanceContext.model
        let instance = instanceContext.instance
        
        // Process parameters and system prompt using unified processor
        let processedParams = ParameterProcessor.processParameters(
            instance: instance,
            overrides: request.overrideModelParameters,
            overrideSystemPrompt: request.overrideSystemPrompt
        )
        
        // Apply capability controls and get potentially modified parameters and message history
        var controlledParameters = processedParams.allParameters
        let controlledMessageHistory = ParameterProcessor.processThinkingControl(
            model: model,
            instance: instance,
            instanceId: request.instanceId,
            thinkingControls: request.thinkingControls,
            parameters: &controlledParameters,
            messageHistory: request.messageHistory
        )
        
        // Apply searching control
        ParameterProcessor.processSearchingControl(
            model: model,
            instance: instance,
            instanceId: request.instanceId,
            searchingControls: request.searchingControls,
            parameters: &controlledParameters
        )
        
        // Build messages list with proper system prompt handling
        let openaiMessages = buildOpenAIMessages(
            messageHistory: controlledMessageHistory,
            effectiveSystemPrompt: processedParams.effectiveSystemPrompt
        )
        
        // Extract OpenAI-specific parameters
        let openaiKnownKeys: Set<String> = ["temperature", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "stop", "user"]
        let (extractedParams, additionalParams) = ParameterProcessor.extractKnownParameters(
            from: controlledParameters,
            knownKeys: openaiKnownKeys
        )
        
        // Extract individual parameters with proper types
        let temperature = extractedParams["temperature"]?.doubleValue
        let maxTokens = extractedParams["max_tokens"]?.int64Value
        let topP = extractedParams["top_p"]?.doubleValue
        let frequencyPenalty = extractedParams["frequency_penalty"]?.doubleValue
        let presencePenalty = extractedParams["presence_penalty"]?.doubleValue
        let stop = extractedParams["stop"]?.arrayValue as? [String]
        let user = extractedParams["user"]?.stringValue
        
        let log = """
        [OpenAICompatibleClient] request: \(request)
        [OpenAICompatibleClient] controlledParameters: \(controlledParameters)
        [OpenAICompatibleClient] additionalParams: \(additionalParams)
        [OpenAICompatibleClient] effectiveSystemPrompt: \(processedParams.effectiveSystemPrompt ?? "nil")
        """
        print(log)
        
        return OpenAIChatRequest(
            model: model.modelIdentifier,
            messages: openaiMessages,
            stream: true,
            temperature: temperature,
            maxTokens: maxTokens,
            topP: topP,
            frequencyPenalty: frequencyPenalty,
            presencePenalty: presencePenalty,
            stop: stop,
            user: user,
            additionalParameters: additionalParams.isEmpty ? nil : additionalParams
        )
    }
    
    // MARK: - Message Building
    
    /// Build OpenAI messages array with system prompt and message history
    /// - Parameters:
    ///   - messageHistory: Array of domain messages to convert
    ///   - effectiveSystemPrompt: Resolved system prompt (if any)
    /// - Returns: Array of OpenAI messages ready for API request
    private func buildOpenAIMessages(
        messageHistory: [Message],
        effectiveSystemPrompt: String?
    ) -> [OpenAIMessage] {
        var openaiMessages: [OpenAIMessage] = []
        
        // Add system message if present
        if let systemPrompt = effectiveSystemPrompt, !systemPrompt.isEmpty {
            openaiMessages.append(OpenAIMessage(role: "system", text: systemPrompt))
        }
        
        // Convert and add message history
        openaiMessages.append(contentsOf: messageHistory.map { $0.toOpenAIMessage() })
        
        return openaiMessages
    }

    
    // MARK: - Request State Management Methods
    
    private func processChunk(
        _ chunk: OpenAIStreamingChunk,
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation,
        requestId: UUID,
        instanceId: UUID
    ) async {
        let analysis = chunk.analyzeChunk()
        let state = await requestStateManager.getState(for: requestId)

        print("[OpenAICompatibleClient] analysis: \(analysis)")
        
        // Handle content delta first (before changing stream state) - use buffer
        if analysis.shouldGenerateContentResponse, case .streaming = state.streamState {
            print("[OpenAICompatibleClient] shouldGenerateContentResponse")
            await contentBuffer.appendContent(
                requestId: requestId,
                content: analysis.contentDelta!
            ) { [weak self] bufferedContent, bufferedThinking in
                guard let self = self else { return }
                
                // Send buffered content if available
                if !bufferedContent.isEmpty {
                    print("[OpenAICompatibleClient] bufferedContent: \(bufferedContent)")
                    if let response = self.createContentDeltaResponse(
                        content: bufferedContent,
                        chunk: chunk,
                        requestId: requestId,
                        instanceId: instanceId
                    ) {
                        continuation.yield(response)
                    }
                }
                
                // Send buffered thinking if available
                if !bufferedThinking.isEmpty {
                    print("[OpenAICompatibleClient] bufferedThinking: \(bufferedThinking)")
                    if let response = self.createThinkingDeltaResponse(
                        thinking: bufferedThinking,
                        chunk: chunk,
                        requestId: requestId,
                        instanceId: instanceId
                    ) {
                        continuation.yield(response)
                    }
                }
            }
        }
        
        // Handle thinking delta (only in streaming state) - use buffer
        if analysis.shouldGenerateThinkingResponse, case .streaming = state.streamState {
            print("[OpenAICompatibleClient] shouldGenerateThinkingResponse")
            await contentBuffer.appendThinking(
                requestId: requestId,
                thinking: analysis.thinkingDelta!
            ) { [weak self] bufferedContent, bufferedThinking in
                guard let self = self else { return }
                
                // Send buffered content if available
                if !bufferedContent.isEmpty {
                    print("[OpenAICompatibleClient] shouldGenerateThinkingResponse bufferedContent: \(bufferedContent)")
                    if let response = self.createContentDeltaResponse(
                        content: bufferedContent,
                        chunk: chunk,
                        requestId: requestId,
                        instanceId: instanceId
                    ) {
                        continuation.yield(response)
                    }
                }
                
                // Send buffered thinking if available
                if !bufferedThinking.isEmpty {
                    print("[OpenAICompatibleClient] shouldGenerateThinkingResponse bufferedThinking: \(bufferedThinking)")
                    if let response = self.createThinkingDeltaResponse(
                        thinking: bufferedThinking,
                        chunk: chunk,
                        requestId: requestId,
                        instanceId: instanceId
                    ) {
                        continuation.yield(response)
                    }
                }
            }
        }

        // Handle finish reason (after content processing)
        if let finishReason = analysis.finishReason {
            await handleFinishReason(finishReason, state: state, continuation: continuation, requestId: requestId, instanceId: instanceId)
        }

        // Handle usage info (always update, don't trigger completion)
        if let usage = analysis.usage {
            await handleUsageInfo(usage, state: state, requestId: requestId)
        }
    }
    
    private func handleFinishReason(
        _ finishReason: String,
        state: RequestState,
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation,
        requestId: UUID,
        instanceId: UUID
    ) async {
        print("[OpenAICompatibleClient] Received finish_reason: \(finishReason) for request: \(requestId)")
        
        state.streamState = .finishReceived(reason: finishReason)
        state.pendingFinishReason = finishReason
        
        // Start 3-second completion timer
        state.completionTimer?.cancel()
        state.completionTimer = Task { [weak self] in
            do {
                try await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
                print("[OpenAICompatibleClient] Completion timeout reached for request: \(requestId)")
                await self?.handleCompletionTimeout(state: state, continuation: continuation, requestId: requestId, instanceId: instanceId)
            } catch {
                // Task was cancelled, ignore
            }
        }
        
        print("[OpenAICompatibleClient] Started 3-second completion timer for request: \(requestId)")
    }
    
    private func handleUsageInfo(_ usage: OpenAIUsage, state: RequestState, requestId: UUID) async {
        print("[OpenAICompatibleClient] Updating usage info for request: \(requestId)")
        state.latestUsage = usage
    }
    
    private func handleDoneEvent(
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation,
        requestId: UUID,
        instanceId: UUID
    ) async {
        print("[OpenAICompatibleClient] Handling [DONE] event for request: \(requestId)")
        
        let state = await requestStateManager.getState(for: requestId)
        
        // Cancel timer if running
        state.completionTimer?.cancel()
        state.completionTimer = nil
        
        // Flush any remaining buffered content before completion
        await contentBuffer.flushBuffer(requestId: requestId)
        
        // Send final completion response
        await sendFinalCompletionResponse(
            state: state,
            continuation: continuation,
            requestId: requestId,
            instanceId: instanceId,
            reason: "DONE event"
        )
        
        // Clean up state
        await requestStateManager.removeState(for: requestId)
        await contentBuffer.cleanup(requestId: requestId)
        
        // Finish the stream
        continuation.finish()
    }
    
    private func handleCompletionTimeout(
        state: RequestState,
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation,
        requestId: UUID,
        instanceId: UUID
    ) async {
        print("[OpenAICompatibleClient] Handling completion timeout for request: \(requestId)")
        
        // Flush any remaining buffered content before completion
        await contentBuffer.flushBuffer(requestId: requestId)
        
        // Send final completion response
        await sendFinalCompletionResponse(
            state: state,
            continuation: continuation,
            requestId: requestId,
            instanceId: instanceId,
            reason: "timeout"
        )
        
        // Clean up state
        await requestStateManager.removeState(for: requestId)
        await contentBuffer.cleanup(requestId: requestId)
        
        // Finish the stream
        continuation.finish()
    }
    
    private func sendFinalCompletionResponse(
        state: RequestState,
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation,
        requestId: UUID,
        instanceId: UUID,
        reason: String
    ) async {
        let finishReason = state.pendingFinishReason ?? "stop"
        
        print("[OpenAICompatibleClient] Sending final completion response for request: \(requestId)")
        print("  - finish_reason: \(finishReason)")
        print("  - trigger: \(reason)")
        print("  - usage available: \(state.latestUsage != nil)")
        
        let completionInfo = CompletionInfo(
            promptTokens: state.latestUsage?.promptTokens,
            completionTokens: state.latestUsage?.completionTokens,
            totalTokens: state.latestUsage?.totalTokens,
            finishReason: finishReason,
            modelUsed: nil // Will be set by the service if needed
        )
        
        let metadata = ResponseMetadata(
            modelIdentifier: nil,
            providerId: nil,
            processingTimeMs: nil,
            sequence: nil,
            additionalInfo: ["completion_trigger": reason]
        )
        
        let response = LLMStreamingResponse.completion(
            requestId: requestId,
            instanceId: instanceId,
            completionInfo: completionInfo,
            metadata: metadata
        )
        
        continuation.yield(response)
    }
    
    private func createContentDeltaResponse(
        content: String,
        chunk: OpenAIStreamingChunk,
        requestId: UUID,
        instanceId: UUID
    ) -> LLMStreamingResponse? {
        let metadata = ResponseMetadata(
            modelIdentifier: chunk.model,
            providerId: nil,
            processingTimeMs: nil,
            sequence: nil,
            additionalInfo: chunk.systemFingerprint.map { ["system_fingerprint": $0] }
        )
        
        return LLMStreamingResponse.contentDelta(
            requestId: requestId,
            instanceId: instanceId,
            content: content,
            metadata: metadata
        )
    }
    
    private func createThinkingDeltaResponse(
        thinking: String,
        chunk: OpenAIStreamingChunk,
        requestId: UUID,
        instanceId: UUID
    ) -> LLMStreamingResponse? {
        let metadata = ResponseMetadata(
            modelIdentifier: chunk.model,
            providerId: nil,
            processingTimeMs: nil,
            sequence: nil,
            additionalInfo: chunk.systemFingerprint.map { ["system_fingerprint": $0] }
        )
        
        return LLMStreamingResponse.thinkingDelta(
            requestId: requestId,
            instanceId: instanceId,
            content: thinking,
            metadata: metadata
        )
    }
    
    // MARK: - Error Handling
    
    private func convertToAPIError(_ error: Error) -> APIError {
        if let apiError = error as? APIError {
            return apiError
        }
        
        if let urlError = error as? URLError {
            return .networkError(code: urlError.code.rawValue)
        }
        
        if error is CancellationError {
            return .cancelled
        }
        
        return .unknown(code: -1, message: error.localizedDescription)
    }
}

// MARK: - Configuration Validation

extension OpenAICompatibleClient {
    
    /// Validate that the instance configuration is complete and correct
    private func validateInstanceConfiguration(_ instanceContext: LLMInstanceContext) throws {
        let provider = instanceContext.provider
        let model = instanceContext.model
        
        // Validate provider configuration
        guard let baseURL = provider.apiBaseUrl, !baseURL.isEmpty else {
            throw APIError.invalidRequest("Provider \(provider.name) has no base URL configured")
        }
        
        guard URL(string: baseURL) != nil else {
            throw APIError.invalidRequest("Provider \(provider.name) has invalid base URL: \(baseURL)")
        }
        
        // Validate model configuration
        guard !model.modelIdentifier.isEmpty else {
            throw APIError.invalidRequest("Model \(model.name) has no model identifier configured")
        }
        
        // Validate API style compatibility
        guard provider.apiStyle == .openaiCompatible else {
            throw APIError.invalidRequest("Provider \(provider.name) API style \(provider.apiStyle) is not supported by OpenAI client")
        }
        
        // Validate API key availability for user-provided keys
        if provider.providerType == .userApiKey && !provider.apiKeyStored {
            throw APIError.authenticationFailed("No API key stored for provider \(provider.name)")
        }
    }
    
    /// Enhanced validation that includes configuration checks
    func validateRequest(
        request: LLMStreamingRequest,
        instanceContext: LLMInstanceContext
    ) throws {
        // Call protocol default validation first
        guard !request.messageHistory.isEmpty else {
            throw APIError.invalidRequest("Message history cannot be empty")
        }
        
        guard canHandle(instanceContext: instanceContext) else {
            throw APIError.invalidRequest("Client \(clientName) cannot handle instance with API style: \(instanceContext.provider.apiStyle)")
        }
        
        // Add client-specific validation
        try validateInstanceConfiguration(instanceContext)
        
        // Validate message content
        for message in request.messageHistory {
            guard !message.content.isEmpty else {
                throw APIError.invalidRequest("Message content cannot be empty")
            }
        }
    }
} 
