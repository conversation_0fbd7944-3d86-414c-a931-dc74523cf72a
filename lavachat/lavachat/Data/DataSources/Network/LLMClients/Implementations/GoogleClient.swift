import Foundation

/// Google Gemini API client implementation
final class GoogleClient: InternalLLMClientProtocol {
    
    // MARK: - Dependencies
    
    let keychainRepository: KeychainRepository
    
    // MARK: - Content Buffering
    
    private let contentBuffer = ContentBuffer()
    
    // MARK: - Protocol Properties
    
    var supportedAPIStyle: APIStyle {
        return .google
    }
    
    var clientName: String {
        return "Google Gemini Client"
    }
    
    // MARK: - Initialization
    
    init(keychainRepository: KeychainRepository) {
        self.keychainRepository = keychainRepository
    }
    
    // MARK: - Core Streaming Interface
    
    func sendStreamingRequest(
        request: LLMStreamingRequest,
        instanceContext: LLMInstanceContext
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        
        return AsyncThrowingStream { continuation in
            let streamingTask = Task {
                do {
                    // Validate request
                    try validateRequest(request: request, instanceContext: instanceContext)
                    
                    // Get API key
                    let apiKey = try await getAPIKey(for: instanceContext)
                    
                    // Build Google request
                    let googleRequest = try buildGoogleRequest(
                        from: request,
                        instanceContext: instanceContext
                    )
                    
                    // Create HTTP request
                    let httpRequestBuilder = try HTTPRequestBuilder.from(
                        instanceContext: instanceContext,
                        apiKey: apiKey
                    )
                    
                    let urlRequest = try httpRequestBuilder.buildStreamingRequest(
                        requestBody: googleRequest
                    )
                    
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Sending request to: \(urlRequest.url?.absoluteString ?? "unknown")")
                    print("\(Date().timeIntervalSince1970) [GoogleClient] request body: \(String(data: urlRequest.httpBody ?? Data(), encoding: .utf8)!.prefix(5000))")
                    print("\(Date().timeIntervalSince1970) [GoogleClient] request headers: \(urlRequest.allHTTPHeaderFields ?? [:])")
                    
                    // Create and start streaming using SSEParser
                    let sseStream = SSEParser.createEventStream(for: urlRequest)
                    
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Successfully created SSE stream, processing events...")
                    
                    // Process streaming response
                    await processStreamingResponse(
                        sseStream: sseStream,
                        requestId: request.id,
                        instanceId: request.instanceId,
                        continuation: continuation
                    )
                    
                } catch {
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Request failed: \(error)")
                    await contentBuffer.cleanup(requestId: request.id)
                    let apiError = mapError(error)
                    let errorResponse = LLMStreamingResponse.error(
                        requestId: request.id,
                        instanceId: request.instanceId,
                        error: apiError
                    )
                    continuation.yield(errorResponse)
                    continuation.finish()
                }
            }
            
            // Handle cancellation - propagate cancellation signal to internal task and SSE stream
            continuation.onTermination = { @Sendable _ in
                print("\(Date().timeIntervalSince1970) [GoogleClient] Stream cancellation requested for request: \(request.id)")
                streamingTask.cancel()
                
                // Clean up buffer on cancellation
                Task {
                    await self.contentBuffer.cleanup(requestId: request.id)
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Request buffer cleaned up after cancellation: \(request.id)")
                }
            }
        }
    }
    
    // MARK: - Configuration and Validation
    
    func canHandle(instanceContext: LLMInstanceContext) -> Bool {
        return instanceContext.provider.apiStyle == .google
    }
    

    
    // MARK: - Private Methods - Request Building
    
    private func buildGoogleRequest(
        from request: LLMStreamingRequest,
        instanceContext: LLMInstanceContext
    ) throws -> GoogleChatRequest {
        
        // Process parameters and system prompt using unified processor
        let processedParams = ParameterProcessor.processParameters(
            instance: instanceContext.instance,
            overrides: request.overrideModelParameters,
            overrideSystemPrompt: request.overrideSystemPrompt
        )
        
        // Apply capability controls and get potentially modified parameters and message history
        var controlledParameters = processedParams.allParameters
        let controlledMessageHistory = ParameterProcessor.processThinkingControl(
            model: instanceContext.model,
            instance: instanceContext.instance,
            instanceId: request.instanceId,
            thinkingControls: request.thinkingControls,
            parameters: &controlledParameters,
            messageHistory: request.messageHistory
        )
        
        // Apply searching control
        ParameterProcessor.processSearchingControl(
            model: instanceContext.model,
            instance: instanceContext.instance,
            instanceId: request.instanceId,
            searchingControls: request.searchingControls,
            parameters: &controlledParameters
        )
        
        // Separate searching parameters from generation config parameters
        let (searchingParameters, generationParameters) = separateSearchingParameters(
            from: controlledParameters,
            model: instanceContext.model
        )
        
        // Convert messages to Google format with effective system prompt
        let (contents, systemInstruction) = buildGoogleMessages(
            controlledMessageHistory,
            effectiveSystemPrompt: processedParams.effectiveSystemPrompt
        )
        
        // Build generation config with non-searching parameters
        let generationConfig = buildGenerationConfig(
            allParameters: generationParameters,
            model: instanceContext.model
        )
        
        return GoogleChatRequest(
            contents: contents,
            systemInstruction: systemInstruction,
            generationConfig: generationConfig,
            safetySettings: nil, // Use default safety settings for now
            additionalParameters: searchingParameters.isEmpty ? nil : searchingParameters
        )
    }
    
    private func buildGoogleMessages(
        _ messages: [Message],
        effectiveSystemPrompt: String?
    ) -> ([GoogleContent], GoogleContent?) {
        var contents: [GoogleContent] = []
        var systemInstruction: GoogleContent?
        
        // Use effective system prompt if available, otherwise extract from message history
        if let systemPrompt = effectiveSystemPrompt, !systemPrompt.isEmpty {
            systemInstruction = GoogleContent(
                role: "user", // Google uses "user" for system instructions
                parts: [GooglePart(text: systemPrompt)]
            )
        }
        
        for message in messages {
            switch message.role {
            case .system:
                // Only use system message if no effective system prompt was provided
                if systemInstruction == nil {
                    let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
                    if !parts.isEmpty {
                        systemInstruction = GoogleContent(
                            role: "user", // Google uses "user" for system instructions
                            parts: parts
                        )
                    }
                }

            case .user:
                let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
                if !parts.isEmpty {
                    contents.append(GoogleContent(
                        role: "user",
                        parts: parts
                    ))
                }

            case .assistant, .mergedAssistant:
                let parts = MessageContentExtractor.extractGoogleParts(from: message.content)
                if !parts.isEmpty {
                    contents.append(GoogleContent(
                        role: "model",
                        parts: parts
                    ))
                }

            default:
                // Skip other message types for now
                continue
            }
        }
        
        return (contents, systemInstruction)
    }

    private func buildGenerationConfig(
        allParameters: [String: AnyCodable],
        model: LLMModel
    ) -> GoogleGenerationConfig? {
        
        // If no parameters, return nil
        guard !allParameters.isEmpty else {
            return nil
        }
        
        // Build thinking config if needed
        let thinkingConfig = buildThinkingConfig(
            model: model,
            parameters: allParameters
        )
        
        // Filter out thinking-related parameters from main config
        var filteredParameters = allParameters
        if let capabilities = model.thinkingCapabilities,
           let paramName = capabilities.parameterName {
            filteredParameters.removeValue(forKey: paramName)
            filteredParameters.removeValue(forKey: "includeThoughts")
        }
        
        // Extract Google-specific parameters
        let googleKnownKeys: Set<String> = [
            "temperature", "topP", "top_p", "topK", "top_k", 
            "maxOutputTokens", "max_output_tokens", "max_tokens",
            "candidateCount", "candidate_count", "presencePenalty", "presence_penalty",
            "frequencyPenalty", "frequency_penalty", "seed", "stop", "stopSequences"
        ]
        let (extractedParams, additionalParameters) = ParameterProcessor.extractKnownParameters(
            from: filteredParameters,
            knownKeys: googleKnownKeys
        )
        
        // Extract individual parameters with proper types
        let temperature = extractedParams["temperature"]?.doubleValue
        let topP = extractedParams["topP"]?.doubleValue ?? extractedParams["top_p"]?.doubleValue
        let topK = extractedParams["topK"]?.intValue ?? extractedParams["top_k"]?.intValue
        let maxOutputTokens = extractedParams["maxOutputTokens"]?.intValue ?? 
                             extractedParams["max_output_tokens"]?.intValue ??
                             extractedParams["max_tokens"]?.intValue
        let candidateCount = extractedParams["candidateCount"]?.intValue ?? 
                           extractedParams["candidate_count"]?.intValue
        let presencePenalty = extractedParams["presencePenalty"]?.doubleValue ?? 
                            extractedParams["presence_penalty"]?.doubleValue
        let frequencyPenalty = extractedParams["frequencyPenalty"]?.doubleValue ?? 
                             extractedParams["frequency_penalty"]?.doubleValue
        
        // Handle stop sequences
        var stopSequences: [String]?
        if let stopValue = extractedParams["stop"] ?? extractedParams["stopSequences"] {
            if let stopArray = stopValue.arrayValue {
                stopSequences = stopArray.compactMap { $0 as? String }
            } else if let stopString = stopValue.stringValue {
                stopSequences = [stopString]
            }
        }
        
        return GoogleGenerationConfig(
            temperature: temperature,
            topP: topP,
            topK: topK,
            maxOutputTokens: maxOutputTokens,
            candidateCount: candidateCount,
            stopSequences: stopSequences,
            presencePenalty: presencePenalty,
            frequencyPenalty: frequencyPenalty,
            thinkingConfig: thinkingConfig,
            additionalParameters: additionalParameters.isEmpty ? nil : additionalParameters
        )
    }
    
    private func buildThinkingConfig(
        model: LLMModel,
        parameters: [String: AnyCodable]
    ) -> GoogleThinkingConfig? {
        
        guard let capabilities = model.thinkingCapabilities,
              let controlType = capabilities.controlType else {
            return nil
        }
        
        switch controlType {
        case .thinkingBudget:
            // Check for thinkingBudget parameter
            var budget: Int? = nil
            if let capabilities = model.thinkingCapabilities,
               let paramName = capabilities.parameterName {
                budget = parameters[paramName]?.intValue
            }
            let includeThoughts = parameters["includeThoughts"]?.boolValue ?? true

            if model.modelIdentifier.contains("gemini-2.5-pro") {
                if let thinkingBudget = budget, thinkingBudget < 128 {
                    budget = 128
                }
            }
            
            if budget != nil || includeThoughts {
                return GoogleThinkingConfig(
                    thinkingBudget: budget,
                    includeThoughts: includeThoughts
                )
            }
            
        case .defaultOn:
            // Always include thoughts for default-on models
            return GoogleThinkingConfig(
                thinkingBudget: nil,
                includeThoughts: true
            )
            
        default:
            // Other thinking control types not supported yet
            break
        }
        
        return nil
    }
    
    // MARK: - Private Methods - Parameter Separation
    
    /// Separate searching parameters from generation config parameters
    /// Searching parameters belong to the top-level GoogleChatRequest, not generationConfig
    private func separateSearchingParameters(
        from allParameters: [String: AnyCodable],
        model: LLMModel
    ) -> ([String: AnyCodable], [String: AnyCodable]) {
        
        var searchingParameters: [String: AnyCodable] = [:]
        var generationParameters: [String: AnyCodable] = [:]
        
        // Get searching parameter name if model supports it
        let searchingParamName = model.searchingCapabilities?.parameterName
        
        for (key, value) in allParameters {
            if let searchingParam = searchingParamName, key == searchingParam {
                // This is a searching parameter, goes to top-level request
                searchingParameters[key] = value
            } else {
                // This is a generation parameter, goes to generationConfig
                generationParameters[key] = value
            }
        }
        
        return (searchingParameters, generationParameters)
    }
    
    // MARK: - Private Methods - Response Processing
    
    private func processStreamingResponse(
        sseStream: AsyncThrowingStream<SSEEvent, Error>,
        requestId: UUID,
        instanceId: UUID,
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation
    ) async {
        
        var isThinkingMode = false
        
        do {
            for try await event in sseStream {
                // Check for cancellation before processing each event
                if Task.isCancelled {
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Task cancelled, stopping SSE event processing")
                    continuation.finish()
                    return
                }
                
                print("\(Date().timeIntervalSince1970) [GoogleClient] SSE event: \(event)")
                
                if event.isDone {
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Stream completed via [DONE] event")
                    await contentBuffer.flushBuffer(requestId: requestId)
                    await contentBuffer.cleanup(requestId: requestId)
                    continuation.finish()
                    return
                }
                
                if event.hasJSONData {
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Processing JSON data: \(event.data)")
                    
                    // Parse JSON response
                    if let response = parseGoogleResponse(event.data) {
                        // Process the response and convert to LLMStreamingResponse
                        let streamingResponses = convertGoogleResponse(
                            response,
                            requestId: requestId,
                            instanceId: instanceId,
                            isThinkingMode: &isThinkingMode
                        )
                        
                        var hasCompletionMarker = false
                        for streamingResponse in streamingResponses {
                            print("\(Date().timeIntervalSince1970) [GoogleClient] Processing response: \(streamingResponse)")
                            
                            // Check if this is a completion marker first
                            if streamingResponse.responseType.isCompletion {
                                hasCompletionMarker = true
                                // Flush buffer before sending completion
                                await contentBuffer.flushBuffer(requestId: requestId)
                                continuation.yield(streamingResponse)
                            } else {
                                // Handle content and thinking responses with buffering
                                switch streamingResponse.responseType {
                                case .contentDelta(let content):
                                    await contentBuffer.appendContent(
                                        requestId: requestId,
                                        content: content
                                    ) { bufferedContent, bufferedThinking in
                                        if !bufferedContent.isEmpty {
                                            let bufferedResponse = LLMStreamingResponse(
                                                requestId: requestId,
                                                instanceId: instanceId,
                                                responseType: .contentDelta(bufferedContent),
                                                metadata: streamingResponse.metadata
                                            )
                                            continuation.yield(bufferedResponse)
                                        }
                                        if !bufferedThinking.isEmpty {
                                            let bufferedResponse = LLMStreamingResponse(
                                                requestId: requestId,
                                                instanceId: instanceId,
                                                responseType: .thinkingDelta(bufferedThinking),
                                                metadata: streamingResponse.metadata
                                            )
                                            continuation.yield(bufferedResponse)
                                        }
                                    }
                                case .thinkingDelta(let thinking):
                                    await contentBuffer.appendThinking(
                                        requestId: requestId,
                                        thinking: thinking
                                    ) { bufferedContent, bufferedThinking in
                                        if !bufferedContent.isEmpty {
                                            let bufferedResponse = LLMStreamingResponse(
                                                requestId: requestId,
                                                instanceId: instanceId,
                                                responseType: .contentDelta(bufferedContent),
                                                metadata: streamingResponse.metadata
                                            )
                                            continuation.yield(bufferedResponse)
                                        }
                                        if !bufferedThinking.isEmpty {
                                            let bufferedResponse = LLMStreamingResponse(
                                                requestId: requestId,
                                                instanceId: instanceId,
                                                responseType: .thinkingDelta(bufferedThinking),
                                                metadata: streamingResponse.metadata
                                            )
                                            continuation.yield(bufferedResponse)
                                        }
                                    }
                                default:
                                    // For other response types, yield directly
                                    continuation.yield(streamingResponse)
                                }
                            }
                        }
                        
                        // If we received a completion marker, finish the stream
                        if hasCompletionMarker {
                            print("\(Date().timeIntervalSince1970) [GoogleClient] Stream completed via completion marker")
                            await contentBuffer.cleanup(requestId: requestId)
                            continuation.finish()
                            return
                        }
                    } else if !event.data.isEmpty {
                        print("\(Date().timeIntervalSince1970) [GoogleClient] Failed to parse JSON response")
                        print("  - raw data: \(event.data)")
                    }
                } else {
                    print("\(Date().timeIntervalSince1970) [GoogleClient] Non-JSON event data: \(event.data)")
                }
            }
            
            print("\(Date().timeIntervalSince1970) [GoogleClient] Stream ended naturally")
            await contentBuffer.flushBuffer(requestId: requestId)
            await contentBuffer.cleanup(requestId: requestId)
            continuation.finish()
            
        } catch {
            print("\(Date().timeIntervalSince1970) [GoogleClient] Stream error: \(error)")
            
            // Check if this is a cancellation error
            if error is CancellationError {
                print("\(Date().timeIntervalSince1970) [GoogleClient] Stream cancelled")
                await contentBuffer.cleanup(requestId: requestId)
                continuation.finish()
                return
            }
            
            await contentBuffer.cleanup(requestId: requestId)
            let apiError = mapError(error)
            let errorResponse = LLMStreamingResponse.error(
                requestId: requestId,
                instanceId: instanceId,
                error: apiError
            )
            continuation.yield(errorResponse)
            continuation.finish()
        }
    }
    
    private func parseGoogleResponse(_ jsonString: String) -> GoogleStreamingResponse? {
        guard let data = jsonString.data(using: .utf8) else {
            print("\(Date().timeIntervalSince1970) [GoogleClient] Failed to convert JSON string to data")
            return nil
        }
        
        do {
            let decoder = JSONDecoder()
            let response = try decoder.decode(GoogleStreamingResponse.self, from: data)
            return response
        } catch {
            print("\(Date().timeIntervalSince1970) [GoogleClient] JSON parsing error: \(error)")
            print("\(Date().timeIntervalSince1970) [GoogleClient] Raw JSON: \(jsonString)")
            return nil
        }
    }
    
    private func convertGoogleResponse(
        _ response: GoogleStreamingResponse,
        requestId: UUID,
        instanceId: UUID,
        isThinkingMode: inout Bool
    ) -> [LLMStreamingResponse] {
        
        var results: [LLMStreamingResponse] = []
        
        guard let candidates = response.candidates,
              let firstCandidate = candidates.first else {
            return results
        }
        
        // Process content parts FIRST (before checking completion)
        if let content = firstCandidate.content {
            for part in content.parts {
                if let text = part.text, !text.isEmpty {
                    // Check if this is thinking content based on the actual Google API field
                    let isThinking = part.isThinking
                    
                    if isThinking != isThinkingMode {
                        isThinkingMode = isThinking
                    }
                    
                    let responseType: LLMStreamingResponse.ResponseType = isThinking ? 
                        .thinkingDelta(text) : .contentDelta(text)
                    
                    let streamingResponse = LLMStreamingResponse(
                        requestId: requestId,
                        instanceId: instanceId,
                        responseType: responseType
                    )
                    
                    results.append(streamingResponse)
                }
            }
        }
        
        // Check for completion AFTER processing content
        if firstCandidate.isComplete {
            if let usageMetadata = response.usageMetadata {
                let completionInfo = CompletionInfo(
                    promptTokens: usageMetadata.promptTokenCount.map { Int64($0) },
                    completionTokens: usageMetadata.candidatesTokenCount.map { Int64($0) },
                    totalTokens: usageMetadata.effectiveTotalTokenCount,
                    finishReason: firstCandidate.finishReason,
                    modelUsed: response.modelVersion
                )
                
                let completionResponse = LLMStreamingResponse.completion(
                    requestId: requestId,
                    instanceId: instanceId,
                    completionInfo: completionInfo
                )
                results.append(completionResponse)
            }
        }
        
        return results
    }
    

    
    // MARK: - Error Handling
    
    private func mapError(_ error: Error) -> APIError {
        if let apiError = error as? APIError {
            return apiError
        }
        
        if error is CancellationError {
            return .cancelled
        }
        
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return .networkError(code: urlError.errorCode)
            case .timedOut:
                return .timeout
            case .cancelled:
                return .cancelled
            default:
                return .networkError(code: urlError.errorCode)
            }
        }
        
        return .unknown(code: 0, message: error.localizedDescription)
    }
} 
