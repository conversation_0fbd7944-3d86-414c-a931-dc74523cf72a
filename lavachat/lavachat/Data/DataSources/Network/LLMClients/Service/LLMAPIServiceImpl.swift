import Foundation
import Combine

/// Implementation of LLMAPIServiceProtocol providing unified access to multiple LLM APIs
final class LLMAPIServiceImpl: LLMAPIServiceProtocol {
    
    // MARK: - Dependencies
    
    private let llmInstanceRepository: LLMInstanceRepository
    private let keychainRepository: KeychainRepository
    private let networkMonitor: NetworkMonitor
    private let clientFactory: LLMClientFactory
    
    // MARK: - Request Management
    
    private var activeRequests: [UUID: Task<Void, Never>] = [:]
    private let requestsQueue = DispatchQueue(label: "LLMAPIService.requests", attributes: .concurrent)
    
    // MARK: - Caching
    
    private let contextCache: InstanceContextCache
    
    // MARK: - Initialization
    
    init(
        llmInstanceRepository: LLMInstanceRepository,
        keychainRepository: KeychainRepository,
        clientFactory: LLMClientFactory,
        networkMonitor: NetworkMonitor,
        cacheTTL: TimeInterval = 1.0,
        maxCacheSize: Int = 100
    ) {
        self.llmInstanceRepository = llmInstanceRepository
        self.keychainRepository = keychainRepository
        self.clientFactory = clientFactory
        self.networkMonitor = networkMonitor
        self.contextCache = InstanceContextCache(ttl: cacheTTL, maxCacheSize: maxCacheSize)
    }
    
    // MARK: - LLMAPIServiceProtocol Implementation
    
    func sendMessage(request: LLMStreamingRequest) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        return AsyncThrowingStream { continuation in
            let task = Task {
                do {
                    // Check network connectivity before proceeding
                    let isConnected = await networkMonitor.checkNetworkStatus()
                    if !isConnected {
                        throw APIError.networkError(code: nil)
                    }

                    // Get instance context
                    let instanceContext = try await getInstanceContext(for: request.instanceId)

                    // Get appropriate client
                    let client = try clientFactory.createClient(for: instanceContext.provider.apiStyle)

                    // Validate request
                    try client.validateRequest(request: request, instanceContext: instanceContext)

                    // Start streaming
                    let responseStream = client.sendStreamingRequest(
                        request: request,
                        instanceContext: instanceContext
                    )

                    for try await response in responseStream {
                        continuation.yield(response)
                    }

                    continuation.finish()
                } catch {
                    continuation.finish(throwing: error)
                }

                // Clean up request tracking
                await removeActiveRequest(request.id)
            }
            
            // Track active request
            Task {
                await addActiveRequest(request.id, task: task)
            }
            
            // Handle cancellation
            continuation.onTermination = { @Sendable _ in
                task.cancel()
            }
        }
    }
    
    func sendMessageToMultipleInstances(requests: [LLMStreamingRequest]) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        return AsyncThrowingStream { continuation in
            let groupTask = Task {
                // Check network connectivity before proceeding
                let isConnected = await networkMonitor.checkNetworkStatus()
                if !isConnected {
                    continuation.finish(throwing: APIError.networkError(code: nil))
                    return
                }

                // Preload instance contexts for better performance
                await preloadInstanceContexts(instanceIds: requests.map { $0.instanceId })
                
                await withTaskGroup(of: Void.self) { group in
                    for request in requests {
                        group.addTask {
                            do {
                                let responseStream = self.sendMessage(request: request)
                                for try await response in responseStream {
                                    continuation.yield(response)
                                }
                            } catch {
                                let errorResponse: LLMStreamingResponse

                                if let apiError = error as? APIError {
                                    print("=== sendMessageToMultipleInstances ERROR DEBUG INFO ===")
                                    print("Error Type: \(error)")
                                    print("Error Description: \(error.localizedDescription)")
                                    print("================================================")
                                    errorResponse = LLMStreamingResponse.error(
                                        requestId: request.id,
                                        instanceId: request.instanceId,
                                        error: apiError
                                    )
                                } else {
                                    errorResponse = LLMStreamingResponse.error(
                                        requestId: request.id,
                                        instanceId: request.instanceId,
                                        error: APIError.unknown(code: -1, message: error.localizedDescription)
                                    )
                                }
                                continuation.yield(errorResponse)
                            }
                        }
                    }
                }
                
                continuation.finish()
            }
            
            // Handle cancellation
            continuation.onTermination = { @Sendable _ in
                groupTask.cancel()
            }
        }
    }
    
    func cancelRequest(requestId: UUID) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                if let task = self.activeRequests[requestId] {
                    task.cancel()
                    self.activeRequests.removeValue(forKey: requestId)
                }
                continuation.resume()
            }
        }
    }
    
    func cancelInstanceRequests(instanceId: UUID) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                let requestsToCancel = self.activeRequests.filter { _, task in
                    // In a full implementation, we'd track instanceId per request
                    // For MVP, we'll cancel all requests as a simplification
                    return true
                }
                
                for (requestId, task) in requestsToCancel {
                    task.cancel()
                    self.activeRequests.removeValue(forKey: requestId)
                }
                
                continuation.resume()
            }
        }
    }
    
    func cancelAllRequests() async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                for (_, task) in self.activeRequests {
                    task.cancel()
                }
                self.activeRequests.removeAll()
                continuation.resume()
            }
        }
    }
    
    func getActiveRequests() -> [UUID] {
        return requestsQueue.sync {
            return Array(activeRequests.keys)
        }
    }
    
    func getActiveRequests(for instanceId: UUID) -> [UUID] {
        return requestsQueue.sync {
            // In a full implementation, we'd filter by instanceId
            // For MVP, return all active requests
            return Array(activeRequests.keys)
        }
    }
    
    func isRequestActive(requestId: UUID) -> Bool {
        return requestsQueue.sync {
            return activeRequests[requestId] != nil
        }
    }
    
    var isServiceAvailable: Bool {
        return networkMonitor.currentIsConnected
    }
    
    func getServiceStatus() -> [String: Any] {
        let activeRequestCount = getActiveRequests().count
        let networkStatus = networkMonitor.getDetailedStatus()
        
        return [
            "activeRequestCount": activeRequestCount,
            "networkStatus": networkStatus,
            "serviceVersion": "1.0.0-MVP"
        ]
    }
    
    // MARK: - Private Methods
    
    private func getInstanceContext(for instanceId: UUID) async throws -> LLMInstanceContext {
        // Check cache first
        if let cachedContext = await contextCache.get(instanceId: instanceId) {
            return cachedContext
        }
        
        // Cache miss, fetch from database
        guard let instance = try await llmInstanceRepository.getInstance(byId: instanceId) else {
            throw APIError.invalidRequest("LLM Instance not found: \(instanceId)")
        }
        
        guard let model = try await llmInstanceRepository.getModel(byId: instance.modelId) else {
            throw APIError.invalidRequest("LLM Model not found: \(instance.modelId)")
        }
        
        guard let provider = try await llmInstanceRepository.getProvider(byId: model.providerId) else {
            throw APIError.invalidRequest("LLM Provider not found: \(model.providerId)")
        }
        
        let context = LLMInstanceContext(
            instance: instance,
            model: model,
            provider: provider
        )
        
        // Store in cache
        await contextCache.set(instanceId: instanceId, context: context)
        
        return context
    }
    
    /// Preload instance contexts for multiple instances to optimize batch operations
    private func preloadInstanceContexts(instanceIds: [UUID]) async {
        do {
            // Get unique instance IDs
            let uniqueInstanceIds = Array(Set(instanceIds))
            
            // Check which instances are already cached and not expired
            var uncachedInstanceIds: [UUID] = []
            
            for instanceId in uniqueInstanceIds {
                if await contextCache.get(instanceId: instanceId) == nil {
                    uncachedInstanceIds.append(instanceId)
                }
            }
            
            // If all instances are cached, nothing to do
            guard !uncachedInstanceIds.isEmpty else { return }
            
            // Batch fetch uncached instances
            let instanceContexts = try await llmInstanceRepository.getInstancesWithRelatedEntities(instanceIds: uncachedInstanceIds)
            
            // Convert to dictionary and cache
            var contextDict: [UUID: LLMInstanceContext] = [:]
            for context in instanceContexts {
                contextDict[context.instance.id] = context
            }
            
            // Batch cache the results
            await contextCache.setBatch(contextDict)
            
        } catch {
            // Preloading failed, but don't throw - individual requests will handle their own context loading
            print("Warning: Failed to preload instance contexts: \(error)")
        }
    }
    
    private func addActiveRequest(_ requestId: UUID, task: Task<Void, Never>) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                self.activeRequests[requestId] = task
                continuation.resume()
            }
        }
    }
    
    private func removeActiveRequest(_ requestId: UUID) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                self.activeRequests.removeValue(forKey: requestId)
                continuation.resume()
            }
        }
    }
}

// MARK: - Client Factory

/// Factory for creating LLM API clients based on API style
final class LLMClientFactory {
    
    private let keychainRepository: KeychainRepository
    
    init(keychainRepository: KeychainRepository) {
        self.keychainRepository = keychainRepository
    }
    
    func createClient(for apiStyle: APIStyle) throws -> InternalLLMClientProtocol {
        switch apiStyle {
        case .openaiCompatible:
            return OpenAICompatibleClient(keychainRepository: keychainRepository)
        case .anthropic:
            throw APIError.notImplemented("Anthropic client not implemented in MVP")
        case .google:
            return GoogleClient(keychainRepository: keychainRepository)
        case .openaiImageGeneration, .openaiImageEdit:
            throw APIError.notImplemented("Image generation clients not implemented in MVP")
        }
    }
} 
