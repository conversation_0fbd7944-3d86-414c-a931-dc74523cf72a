import Foundation

/// Internal protocol for LLM API clients
/// This protocol is implemented by specific API clients (OpenAI, Anthropic, etc.)
/// and provides a unified interface for the LLMAPIService
protocol InternalLLMClientProtocol {
    
    // MARK: - Dependencies
    
    /// Keychain repository for API key management
    var keychainRepository: KeychainRepository { get }
    
    // MARK: - Core Streaming Interface
    
    /// Send a streaming request to the LLM API
    /// - Parameter request: The standardized streaming request
    /// - Parameter instanceContext: The instance context containing model and provider info
    /// - Returns: An async throwing stream of responses
    func sendStreamingRequest(
        request: LLMStreamingRequest,
        instanceContext: LLMInstanceContext
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error>
    
    // MARK: - Configuration and Validation
    
    /// Check if this client can handle the given instance context
    /// - Parameter instanceContext: The instance context to validate
    /// - Returns: True if this client can handle the request
    func canHandle(instanceContext: LLMInstanceContext) -> Bool
    
    /// Validate the request parameters before sending
    /// - Parameter request: The request to validate
    /// - Parameter instanceContext: The instance context
    /// - Throws: APIError if validation fails
    func validateRequest(request: LLMStreamingRequest, instanceContext: LLMInstanceContext) throws
    
    /// Get API key for the given instance context
    /// - Parameter instanceContext: The instance context containing provider info
    /// - Returns: The API key string
    /// - Throws: APIError if authentication fails
    func getAPIKey(for instanceContext: LLMInstanceContext) async throws -> String
    
    // MARK: - API Style Support
    
    /// The API style this client supports
    var supportedAPIStyle: APIStyle { get }
    
    /// Human-readable name for this client
    var clientName: String { get }
}

// MARK: - Default Implementations

extension InternalLLMClientProtocol {
    
    /// Default validation implementation
    func validateRequest(request: LLMStreamingRequest, instanceContext: LLMInstanceContext) throws {
        // Basic validation
        guard !request.messageHistory.isEmpty else {
            throw APIError.invalidRequest("Message history cannot be empty")
        }
        
        guard canHandle(instanceContext: instanceContext) else {
            throw APIError.invalidRequest("Client \(clientName) cannot handle instance with API style: \(instanceContext.provider.apiStyle)")
        }
    }
    
    /// Default API key retrieval implementation
    func getAPIKey(for instanceContext: LLMInstanceContext) async throws -> String {
        let provider = instanceContext.provider
        
        switch provider.providerType {
        case .userApiKey:
            guard let apiKey = try await keychainRepository.getApiKey(for: provider.id) else {
                throw APIError.authenticationFailed("No API key found for provider \(provider.name)")
            }
            return apiKey
            
        case .subscriptionBased:
            throw APIError.authenticationFailed("Subscription-based access not implemented")
        }
    }
} 