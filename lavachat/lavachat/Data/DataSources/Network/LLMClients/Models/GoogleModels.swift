import Foundation

// MARK: - Google API Request Models

/// Request model for Google Gemini API
struct GoogleChatRequest: Codable {
    let contents: [GoogleContent]
    let systemInstruction: GoogleContent?
    let generationConfig: GoogleGenerationConfig?
    let safetySettings: [GoogleSafetySetting]?
    let additionalParameters: [String: AnyCodable]? // For top-level parameters like search capabilities
    
    init(
        contents: [GoogleContent],
        systemInstruction: GoogleContent? = nil,
        generationConfig: GoogleGenerationConfig? = nil,
        safetySettings: [GoogleSafetySetting]? = nil,
        additionalParameters: [String: AnyCodable]? = nil
    ) {
        self.contents = contents
        self.systemInstruction = systemInstruction
        self.generationConfig = generationConfig
        self.safetySettings = safetySettings
        self.additionalParameters = additionalParameters
    }
    
    // Custom encoding to handle additionalParameters at top level
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(contents, forKey: .contents)
        try container.encodeIfPresent(systemInstruction, forKey: .systemInstruction)
        try container.encodeIfPresent(generationConfig, forKey: .generationConfig)
        try container.encodeIfPresent(safetySettings, forKey: .safetySettings)
        
        // Encode additional parameters at the top level
        if let additionalParams = additionalParameters {
            for (key, value) in additionalParams {
                // Use a dynamic container to encode additional keys
                var dynamicContainer = encoder.container(keyedBy: DynamicCodingKey.self)
                try dynamicContainer.encode(value, forKey: DynamicCodingKey(stringValue: key)!)
            }
        }
    }
    
    // Custom decoding to handle additionalParameters
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        contents = try container.decode([GoogleContent].self, forKey: .contents)
        systemInstruction = try container.decodeIfPresent(GoogleContent.self, forKey: .systemInstruction)
        generationConfig = try container.decodeIfPresent(GoogleGenerationConfig.self, forKey: .generationConfig)
        safetySettings = try container.decodeIfPresent([GoogleSafetySetting].self, forKey: .safetySettings)
        
        // For simplicity in this implementation, we'll set additionalParameters to nil during decoding
        // A full implementation would collect all unknown keys
        additionalParameters = nil
    }
    
    private enum CodingKeys: String, CodingKey {
        case contents
        case systemInstruction
        case generationConfig
        case safetySettings
    }
}

/// Content structure for Google API
struct GoogleContent: Codable {
    let role: String // "user" or "model"
    let parts: [GooglePart]
    
    init(role: String, parts: [GooglePart]) {
        self.role = role
        self.parts = parts
    }
}

/// Part structure for Google API content
struct GooglePart: Codable {
    let text: String?
    let thought: Bool? // Indicates if this part contains thinking content
    let inlineData: GoogleInlineData? // For multimodal content (images, documents)

    init(text: String, thought: Bool? = nil) {
        self.text = text
        self.thought = thought
        self.inlineData = nil
    }

    init(inlineData: GoogleInlineData) {
        self.text = nil
        self.thought = nil
        self.inlineData = inlineData
    }
}

/// Inline data structure for Google API multimodal content
struct GoogleInlineData: Codable {
    let mimeType: String
    let data: String // Base64 encoded data

    init(mimeType: String, data: String) {
        self.mimeType = mimeType
        self.data = data
    }
}

/// Generation configuration for Google API
struct GoogleGenerationConfig: Codable {
    // Basic parameters
    let temperature: Double?
    let topP: Double?
    let topK: Int?
    let maxOutputTokens: Int?
    let candidateCount: Int?
    
    // Control parameters
    let stopSequences: [String]?
    let responseMimeType: String?
    let seed: Int?
    
    // Penalty parameters
    let presencePenalty: Double?
    let frequencyPenalty: Double?
    
    // Response configuration
    let responseLogprobs: Bool?
    let logprobs: Int?
    
    // Thinking configuration
    let thinkingConfig: GoogleThinkingConfig?
    
    // Additional parameters for forward compatibility
    let additionalParameters: [String: AnyCodable]?
    
    init(
        temperature: Double? = nil,
        topP: Double? = nil,
        topK: Int? = nil,
        maxOutputTokens: Int? = nil,
        candidateCount: Int? = nil,
        stopSequences: [String]? = nil,
        responseMimeType: String? = nil,
        seed: Int? = nil,
        presencePenalty: Double? = nil,
        frequencyPenalty: Double? = nil,
        responseLogprobs: Bool? = nil,
        logprobs: Int? = nil,
        thinkingConfig: GoogleThinkingConfig? = nil,
        additionalParameters: [String: AnyCodable]? = nil
    ) {
        self.temperature = temperature
        self.topP = topP
        self.topK = topK
        self.maxOutputTokens = maxOutputTokens
        self.candidateCount = candidateCount
        self.stopSequences = stopSequences
        self.responseMimeType = responseMimeType
        self.seed = seed
        self.presencePenalty = presencePenalty
        self.frequencyPenalty = frequencyPenalty
        self.responseLogprobs = responseLogprobs
        self.logprobs = logprobs
        self.thinkingConfig = thinkingConfig
        self.additionalParameters = additionalParameters
    }
    
    // Custom encoding (simplified for MVP)
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encodeIfPresent(temperature, forKey: .temperature)
        try container.encodeIfPresent(topP, forKey: .topP)
        try container.encodeIfPresent(topK, forKey: .topK)
        try container.encodeIfPresent(maxOutputTokens, forKey: .maxOutputTokens)
        try container.encodeIfPresent(candidateCount, forKey: .candidateCount)
        try container.encodeIfPresent(stopSequences, forKey: .stopSequences)
        try container.encodeIfPresent(responseMimeType, forKey: .responseMimeType)
        try container.encodeIfPresent(seed, forKey: .seed)
        try container.encodeIfPresent(presencePenalty, forKey: .presencePenalty)
        try container.encodeIfPresent(frequencyPenalty, forKey: .frequencyPenalty)
        try container.encodeIfPresent(responseLogprobs, forKey: .responseLogprobs)
        try container.encodeIfPresent(logprobs, forKey: .logprobs)
        try container.encodeIfPresent(thinkingConfig, forKey: .thinkingConfig)
        
        // Note: additionalParameters are ignored in MVP
        // In a full implementation, we'd need to use KeyedEncodingContainer with dynamic keys
    }
    
    // Custom decoding to handle additional parameters
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        temperature = try container.decodeIfPresent(Double.self, forKey: .temperature)
        topP = try container.decodeIfPresent(Double.self, forKey: .topP)
        topK = try container.decodeIfPresent(Int.self, forKey: .topK)
        maxOutputTokens = try container.decodeIfPresent(Int.self, forKey: .maxOutputTokens)
        candidateCount = try container.decodeIfPresent(Int.self, forKey: .candidateCount)
        stopSequences = try container.decodeIfPresent([String].self, forKey: .stopSequences)
        responseMimeType = try container.decodeIfPresent(String.self, forKey: .responseMimeType)
        seed = try container.decodeIfPresent(Int.self, forKey: .seed)
        presencePenalty = try container.decodeIfPresent(Double.self, forKey: .presencePenalty)
        frequencyPenalty = try container.decodeIfPresent(Double.self, forKey: .frequencyPenalty)
        responseLogprobs = try container.decodeIfPresent(Bool.self, forKey: .responseLogprobs)
        logprobs = try container.decodeIfPresent(Int.self, forKey: .logprobs)
        thinkingConfig = try container.decodeIfPresent(GoogleThinkingConfig.self, forKey: .thinkingConfig)
        
        // Decode additional parameters
        // For simplicity in MVP, we'll just set additionalParameters to nil
        // In a full implementation, we'd collect all unknown keys
        additionalParameters = nil
    }
    
    private enum CodingKeys: String, CodingKey {
        case temperature, topP, topK, maxOutputTokens, candidateCount
        case stopSequences, responseMimeType, seed
        case presencePenalty, frequencyPenalty
        case responseLogprobs, logprobs
        case thinkingConfig
    }
}



/// Thinking configuration for Google API
struct GoogleThinkingConfig: Codable {
    let thinkingBudget: Int?
    let includeThoughts: Bool?
    
    init(
        thinkingBudget: Int? = nil,
        includeThoughts: Bool? = nil
    ) {
        self.thinkingBudget = thinkingBudget
        self.includeThoughts = includeThoughts
    }
}

/// Safety setting for Google API
struct GoogleSafetySetting: Codable {
    let category: String
    let threshold: String
    
    init(category: String, threshold: String) {
        self.category = category
        self.threshold = threshold
    }
}

// MARK: - Google API Response Models

/// Streaming response from Google Gemini API
struct GoogleStreamingResponse: Codable {
    let candidates: [GoogleCandidate]?
    let usageMetadata: GoogleUsageMetadata?
    let modelVersion: String?
    let responseId: String?
    
    init(
        candidates: [GoogleCandidate]? = nil,
        usageMetadata: GoogleUsageMetadata? = nil,
        modelVersion: String? = nil,
        responseId: String? = nil
    ) {
        self.candidates = candidates
        self.usageMetadata = usageMetadata
        self.modelVersion = modelVersion
        self.responseId = responseId
    }
}

/// Candidate response from Google API
struct GoogleCandidate: Codable {
    let content: GoogleContent?
    let finishReason: String?
    let index: Int?
    
    init(
        content: GoogleContent? = nil,
        finishReason: String? = nil,
        index: Int? = nil
    ) {
        self.content = content
        self.finishReason = finishReason
        self.index = index
    }
}

/// Usage metadata from Google API
struct GoogleUsageMetadata: Codable {
    let promptTokenCount: Int?
    let candidatesTokenCount: Int?
    let totalTokenCount: Int?
    let thoughtsTokenCount: Int?
    let promptTokensDetails: [GoogleModalityTokenCount]?
    
    init(
        promptTokenCount: Int? = nil,
        candidatesTokenCount: Int? = nil,
        totalTokenCount: Int? = nil,
        thoughtsTokenCount: Int? = nil,
        promptTokensDetails: [GoogleModalityTokenCount]? = nil
    ) {
        self.promptTokenCount = promptTokenCount
        self.candidatesTokenCount = candidatesTokenCount
        self.totalTokenCount = totalTokenCount
        self.thoughtsTokenCount = thoughtsTokenCount
        self.promptTokensDetails = promptTokensDetails
    }
}

/// Modality token count for Google API
struct GoogleModalityTokenCount: Codable {
    let modality: String?
    let tokenCount: Int?
    
    init(modality: String? = nil, tokenCount: Int? = nil) {
        self.modality = modality
        self.tokenCount = tokenCount
    }
}

// MARK: - Extensions for Content Detection

extension GooglePart {
    /// Check if this part contains thinking content
    var isThinking: Bool {
        // In Google's actual response, thinking content is identified by a "thought" field
        return thought == true
    }
}

extension GoogleCandidate {
    /// Check if this candidate represents a completion
    var isComplete: Bool {
        return finishReason != nil && !finishReason!.isEmpty
    }
    
    /// Get the text content from this candidate
    var textContent: String? {
        return content?.parts.compactMap { $0.text }.joined()
    }
}

extension GoogleUsageMetadata {
    /// Get total token count, preferring the provided total or calculating if needed
    var effectiveTotalTokenCount: Int64? {
        if let total = totalTokenCount {
            return Int64(total)
        }
        
        let prompt = promptTokenCount ?? 0
        let completion = candidatesTokenCount ?? 0
        return Int64(prompt + completion)
    }
} 