import Foundation

// MARK: - OpenAI Request Models

/// OpenAI chat completion request
struct OpenAIChatRequest: Codable {
    let model: String
    let messages: [OpenAIMessage]
    let stream: Bool
    let temperature: Double?
    let maxTokens: Int64?
    let topP: Double?
    let frequencyPenalty: Double?
    let presencePenalty: Double?
    let stop: [String]?
    let user: String?
    let additionalParameters: [String: AnyCodable]?
    
    enum CodingKeys: String, CodingKey, CaseIterable {
        case model, messages, stream, temperature, stop, user
        case maxTokens = "max_tokens"
        case topP = "top_p"
        case frequencyPenalty = "frequency_penalty"
        case presencePenalty = "presence_penalty"
        // Note: additionalParameters is not included in CodingKeys
    }
    
    init(
        model: String,
        messages: [OpenAIMessage],
        stream: Bool,
        temperature: Double? = nil,
        maxTokens: Int64? = nil,
        topP: Double? = nil,
        frequencyPenalty: Double? = nil,
        presencePenalty: Double? = nil,
        stop: [String]? = nil,
        user: String? = nil,
        additionalParameters: [String: AnyCodable]? = nil
    ) {
        self.model = model
        self.messages = messages
        self.stream = stream
        self.temperature = temperature
        self.maxTokens = maxTokens
        self.topP = topP
        self.frequencyPenalty = frequencyPenalty
        self.presencePenalty = presencePenalty
        self.stop = stop
        self.user = user
        self.additionalParameters = additionalParameters
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // Encode predefined parameters
        try container.encode(model, forKey: .model)
        try container.encode(messages, forKey: .messages)
        try container.encode(stream, forKey: .stream)
        try container.encodeIfPresent(temperature, forKey: .temperature)
        try container.encodeIfPresent(maxTokens, forKey: .maxTokens)
        try container.encodeIfPresent(topP, forKey: .topP)
        try container.encodeIfPresent(frequencyPenalty, forKey: .frequencyPenalty)
        try container.encodeIfPresent(presencePenalty, forKey: .presencePenalty)
        try container.encodeIfPresent(stop, forKey: .stop)
        try container.encodeIfPresent(user, forKey: .user)
        
        // Flatten additional parameters to root level
        if let additional = additionalParameters {
            var dynamicContainer = encoder.container(keyedBy: DynamicCodingKey.self)
            for (key, value) in additional {
                if let codingKey = DynamicCodingKey(stringValue: key) {
                    try dynamicContainer.encode(value, forKey: codingKey)
                }
            }
        }
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Decode predefined parameters
        model = try container.decode(String.self, forKey: .model)
        messages = try container.decode([OpenAIMessage].self, forKey: .messages)
        stream = try container.decode(Bool.self, forKey: .stream)
        temperature = try container.decodeIfPresent(Double.self, forKey: .temperature)
        maxTokens = try container.decodeIfPresent(Int64.self, forKey: .maxTokens)
        topP = try container.decodeIfPresent(Double.self, forKey: .topP)
        frequencyPenalty = try container.decodeIfPresent(Double.self, forKey: .frequencyPenalty)
        presencePenalty = try container.decodeIfPresent(Double.self, forKey: .presencePenalty)
        stop = try container.decodeIfPresent([String].self, forKey: .stop)
        user = try container.decodeIfPresent(String.self, forKey: .user)
        
        // Collect all unknown keys as additional parameters
        let allKeys = try decoder.container(keyedBy: DynamicCodingKey.self)
        var additional: [String: AnyCodable] = [:]
        
        let predefinedKeys = Set(CodingKeys.allCases.map { $0.stringValue })
        
        for key in allKeys.allKeys {
            if !predefinedKeys.contains(key.stringValue) {
                additional[key.stringValue] = try allKeys.decode(AnyCodable.self, forKey: key)
            }
        }
        
        additionalParameters = additional.isEmpty ? nil : additional
    }
}

/// OpenAI message format - supports both text-only and multimodal content
struct OpenAIMessage: Codable {
    let role: String
    let content: OpenAIMessageContent
    let name: String?

    init(role: String, content: OpenAIMessageContent, name: String? = nil) {
        self.role = role
        self.content = content
        self.name = name
    }

    // Convenience initializer for text-only messages
    init(role: String, text: String, name: String? = nil) {
        self.role = role
        self.content = .text(text)
        self.name = name
    }
}

/// OpenAI message content - can be either simple text or multimodal array
enum OpenAIMessageContent: Codable {
    case text(String)
    case multimodal([OpenAIContentPart])

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case .text(let text):
            try container.encode(text)
        case .multimodal(let parts):
            try container.encode(parts)
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        // Try to decode as string first
        if let text = try? container.decode(String.self) {
            self = .text(text)
            return
        }

        // Try to decode as array of content parts
        if let parts = try? container.decode([OpenAIContentPart].self) {
            self = .multimodal(parts)
            return
        }

        throw DecodingError.dataCorrupted(
            DecodingError.Context(
                codingPath: decoder.codingPath,
                debugDescription: "Content must be either a string or an array of content parts"
            )
        )
    }
}

/// OpenAI content part for multimodal messages
struct OpenAIContentPart: Codable {
    let type: String
    let text: String?
    let imageUrl: OpenAIImageUrl?
    let file: OpenAIFile?

    enum CodingKeys: String, CodingKey {
        case type, text
        case imageUrl = "image_url"
        case file
    }

    init(type: String, text: String? = nil, imageUrl: OpenAIImageUrl? = nil, file: OpenAIFile? = nil) {
        self.type = type
        self.text = text
        self.imageUrl = imageUrl
        self.file = file
    }

    // Convenience initializers
    static func text(_ text: String) -> OpenAIContentPart {
        return OpenAIContentPart(type: "text", text: text)
    }

    static func image(url: String) -> OpenAIContentPart {
        return OpenAIContentPart(type: "image_url", imageUrl: OpenAIImageUrl(url: url))
    }

    static func file(filename: String, data: String) -> OpenAIContentPart {
        return OpenAIContentPart(type: "file", file: OpenAIFile(filename: filename, fileData: data))
    }
}

/// OpenAI image URL structure for vision API
struct OpenAIImageUrl: Codable {
    let url: String
    let detail: String?

    init(url: String, detail: String? = nil) {
        self.url = url
        self.detail = detail
    }
}

/// OpenAI file structure for document processing
struct OpenAIFile: Codable {
    let filename: String
    let fileData: String

    enum CodingKeys: String, CodingKey {
        case filename
        case fileData = "file_data"
    }

    init(filename: String, fileData: String) {
        self.filename = filename
        self.fileData = fileData
    }
}

// MARK: - OpenAI API Response Models

/// OpenAI streaming response chunk model
struct OpenAIStreamingChunk: Codable {
    let id: String
    let object: String
    let created: Int64
    let model: String
    let systemFingerprint: String?
    let choices: [OpenAIChoice]
    let usage: OpenAIUsage?
    
    enum CodingKeys: String, CodingKey {
        case id, object, created, model, choices, usage
        case systemFingerprint = "system_fingerprint"
    }
}

/// OpenAI choice in streaming response
struct OpenAIChoice: Codable {
    let index: Int
    let delta: OpenAIDelta
    let finishReason: String?
    let logprobs: OpenAILogprobs?
    
    enum CodingKeys: String, CodingKey {
        case index, delta, logprobs
        case finishReason = "finish_reason"
    }
}

/// OpenAI delta content in streaming response
struct OpenAIDelta: Codable {
    let role: String?
    let content: String?
    let reasoning: String? // For api like OpenRouter
    let reasoningContent: String? // For api like SiliconFlow
    
    enum CodingKeys: String, CodingKey {
        case role, content, reasoning
        case reasoningContent = "reasoning_content"
    }
}

/// OpenAI logprobs (placeholder, not used in MVP)
struct OpenAILogprobs: Codable {
    // Implementation details can be added later if needed
}

/// OpenAI usage information
struct OpenAIUsage: Codable {
    let promptTokens: Int64?
    let completionTokens: Int64?
    let totalTokens: Int64?
    let promptTokensDetails: OpenAIPromptTokensDetails?
    
    enum CodingKeys: String, CodingKey {
        case totalTokens = "total_tokens"
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case promptTokensDetails = "prompt_tokens_details"
    }
}

/// OpenAI prompt tokens details
struct OpenAIPromptTokensDetails: Codable {
    let textTokens: Int64?
    let audioTokens: Int64?
    let imageTokens: Int64?
    let cachedTokens: Int64?
    
    enum CodingKeys: String, CodingKey {
        case textTokens = "text_tokens"
        case audioTokens = "audio_tokens"
        case imageTokens = "image_tokens"
        case cachedTokens = "cached_tokens"
    }
}

// MARK: - Chunk Analysis Result

/// Result of analyzing an OpenAI streaming chunk
struct ChunkAnalysisResult {
    let contentDelta: String?
    let thinkingDelta: String?
    let finishReason: String?
    let usage: OpenAIUsage?
    let shouldGenerateContentResponse: Bool
    let shouldGenerateThinkingResponse: Bool
    
    init(
        contentDelta: String? = nil,
        thinkingDelta: String? = nil,
        finishReason: String? = nil,
        usage: OpenAIUsage? = nil,
        shouldGenerateContentResponse: Bool = false,
        shouldGenerateThinkingResponse: Bool = false
    ) {
        self.contentDelta = contentDelta
        self.thinkingDelta = thinkingDelta
        self.finishReason = finishReason
        self.usage = usage
        self.shouldGenerateContentResponse = shouldGenerateContentResponse
        self.shouldGenerateThinkingResponse = shouldGenerateThinkingResponse
    }
}

// MARK: - Response Mapping Extensions

extension OpenAIStreamingChunk {
    
    /// Analyze OpenAI streaming chunk and extract relevant information
    func analyzeChunk() -> ChunkAnalysisResult {
        guard let choice = choices.first else {
            print("[OpenAIResponse] No choices in chunk")
            return ChunkAnalysisResult()
        }
        
        var result = ChunkAnalysisResult()
        
        // Extract finish reason
        if let finishReason = choice.finishReason, !finishReason.isEmpty {
            result = ChunkAnalysisResult(
                contentDelta: result.contentDelta,
                thinkingDelta: result.thinkingDelta,
                finishReason: finishReason,
                usage: usage,
                shouldGenerateContentResponse: result.shouldGenerateContentResponse,
                shouldGenerateThinkingResponse: result.shouldGenerateThinkingResponse
            )
        }
        
        // Extract usage (may come separately from finish_reason)
        if usage != nil {
            result = ChunkAnalysisResult(
                contentDelta: result.contentDelta,
                thinkingDelta: result.thinkingDelta,
                finishReason: result.finishReason,
                usage: usage,
                shouldGenerateContentResponse: result.shouldGenerateContentResponse,
                shouldGenerateThinkingResponse: result.shouldGenerateThinkingResponse
            )
        }
        
        // Extract content delta
        if let content = choice.delta.content, !content.isEmpty {
            result = ChunkAnalysisResult(
                contentDelta: content,
                thinkingDelta: result.thinkingDelta,
                finishReason: result.finishReason,
                usage: result.usage,
                shouldGenerateContentResponse: true,
                shouldGenerateThinkingResponse: result.shouldGenerateThinkingResponse
            )
        }
        
        // Extract reasoning/thinking delta
        if let reasoning = choice.delta.reasoning, !reasoning.isEmpty {
            result = ChunkAnalysisResult(
                contentDelta: result.contentDelta,
                thinkingDelta: reasoning,
                finishReason: result.finishReason,
                usage: result.usage,
                shouldGenerateContentResponse: result.shouldGenerateContentResponse,
                shouldGenerateThinkingResponse: true
            )
        } else if let reasoningContent = choice.delta.reasoningContent, !reasoningContent.isEmpty {
            result = ChunkAnalysisResult(
                contentDelta: result.contentDelta,
                thinkingDelta: reasoningContent,
                finishReason: result.finishReason,
                usage: result.usage,
                shouldGenerateContentResponse: result.shouldGenerateContentResponse,
                shouldGenerateThinkingResponse: true
            )
        }
        
        return result
    }
}

extension Message {

    /// Convert domain Message to OpenAI message format with multimodal support
    func toOpenAIMessage() -> OpenAIMessage {
        let roleString: String
        switch role {
        case .user:
            roleString = "user"
        case .assistant, .mergedAssistant:
            roleString = "assistant"
        case .system:
            roleString = "system"
        case .tool:
            roleString = "tool"
        }

        // Extract content parts using OpenAI format
        let contentParts = MessageContentExtractor.extractOpenAIContentParts(from: content)

        // Check if we have any non-text content (images, files)
        let hasNonTextContent = contentParts.contains { part in
            part.type != "text"
        }

        if hasNonTextContent {
            // Use multimodal format when we have images/files
            return OpenAIMessage(
                role: roleString,
                content: .multimodal(contentParts)
            )
        } else {
            // Use simple text format - combine all text parts
            let textContent = contentParts.compactMap { $0.text }.joined(separator: "\n\n")
            return OpenAIMessage(
                role: roleString,
                content: .text(textContent.isEmpty ? "No content" : textContent)
            )
        }
    }
}
