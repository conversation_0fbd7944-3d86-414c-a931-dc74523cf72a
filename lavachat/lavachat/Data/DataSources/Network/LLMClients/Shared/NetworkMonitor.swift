import Foundation
import Network

/// Monitor network connectivity status using Network.framework
/// Uses on-demand monitoring to minimize battery usage
@available(iOS 16.0, *)
final class NetworkMonitor: ObservableObject {

    // MARK: - Published Properties

    @Published var isConnected: Bool = false
    @Published var connectionType: ConnectionType = .unknown
    @Published var isExpensive: Bool = false
    @Published var isConstrained: Bool = false

    // MARK: - Private Properties

    private let monitor: NWPathMonitor
    private let queue = DispatchQueue(label: "NetworkMonitor", qos: .utility)
    private var isMonitoring = false
    private var stopTimer: Task<Void, Never>?

    // MARK: - Shared Instance

    static let shared = NetworkMonitor()

    // MARK: - Initialization

    init() {
        monitor = NWPathMonitor()
        // Don't start monitoring automatically - use on-demand approach
    }

    deinit {
        stopMonitoring()
    }
    
    // MARK: - Public Methods

    /// Check network status on-demand
    /// Starts monitoring if not already active and returns current status
    /// - Returns: Current network connection status
    func checkNetworkStatus() async -> Bool {
        await ensureMonitoring()
        scheduleAutoStop()
        return isConnected
    }

    /// Get current network status synchronously (may be stale if not monitoring)
    /// For cases where async is not possible
    var currentIsConnected: Bool {
        return isConnected
    }

    /// Start monitoring network status
    private func startMonitoring() {
        guard !isMonitoring else { return }

        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateStatus(with: path)
            }
        }

        monitor.start(queue: queue)
        isMonitoring = true

        #if DEBUG
        print("NetworkMonitor: Started monitoring")
        #endif
    }

    /// Stop monitoring network status
    private func stopMonitoring() {
        guard isMonitoring else { return }

        monitor.cancel()
        isMonitoring = false
        stopTimer?.cancel()
        stopTimer = nil

        #if DEBUG
        print("NetworkMonitor: Stopped monitoring")
        #endif
    }
    
    /// Ensure monitoring is active and wait for initial status
    private func ensureMonitoring() async {
        if !isMonitoring {
            startMonitoring()
            // Wait for initial network status update
            await waitForInitialStatus()
        }
    }

    /// Wait for the first network status update
    private func waitForInitialStatus() async {
        // If we already have a status, return immediately
        guard connectionType == .unknown else { return }

        return await withCheckedContinuation { continuation in
            var hasResumed = false

            // Set up a timeout
            let timeoutTask = Task {
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second timeout
                if !hasResumed {
                    hasResumed = true
                    continuation.resume()
                }
            }

            // Listen for the first status update
            let cancellable = $connectionType.sink { connectionType in
                if connectionType != .unknown && !hasResumed {
                    hasResumed = true
                    timeoutTask.cancel()
                    continuation.resume()
                }
            }

            // Clean up when done
            Task {
                await timeoutTask.value
                cancellable.cancel()
            }
        }
    }

    /// Schedule automatic stop after a delay
    private func scheduleAutoStop() {
        // Cancel existing timer
        stopTimer?.cancel()

        // Schedule stop after 5 seconds of inactivity
        stopTimer = Task { [weak self] in
            do {
                try await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
                await MainActor.run {
                    self?.stopMonitoring()
                }
            } catch {
                // Task was cancelled, ignore
            }
        }
    }

    /// Check if a specific host is reachable
    /// - Parameter host: The hostname to check
    /// - Returns: True if the host appears reachable
    func isHostReachable(_ host: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let monitor = NWPathMonitor()
            monitor.pathUpdateHandler = { path in
                let isReachable = path.status == .satisfied
                monitor.cancel()
                continuation.resume(returning: isReachable)
            }
            monitor.start(queue: queue)
        }
    }
    
    /// Get detailed network status information
    /// - Returns: Dictionary containing network status details
    func getDetailedStatus() -> [String: Any] {
        return [
            "isConnected": isConnected,
            "connectionType": connectionType.rawValue,
            "isExpensive": isExpensive,
            "isConstrained": isConstrained,
            "timestamp": Date().timeIntervalSince1970
        ]
    }
    
    // MARK: - Private Methods
    
    private func updateStatus(with path: NWPath) {
        // Update connection status
        isConnected = path.status == .satisfied
        
        // Update connection type
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = isConnected ? .other : .unknown
        }
        
        // Update connection characteristics
        isExpensive = path.isExpensive
        isConstrained = path.isConstrained
        
        #if DEBUG
        print("NetworkMonitor: Status updated - Connected: \(isConnected), Type: \(connectionType), Expensive: \(isExpensive), Constrained: \(isConstrained)")
        #endif
    }
}

// MARK: - Connection Type

enum ConnectionType: String, CaseIterable {
    case wifi = "wifi"
    case cellular = "cellular"
    case ethernet = "ethernet"
    case other = "other"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .wifi:
            return "Wi-Fi"
        case .cellular:
            return "Cellular"
        case .ethernet:
            return "Ethernet"
        case .other:
            return "Other"
        case .unknown:
            return "Unknown"
        }
    }
    
    var isWireless: Bool {
        return self == .wifi || self == .cellular
    }
}

// MARK: - Network Status Extensions

extension NetworkMonitor {
    
    /// Check if the current connection is suitable for large data transfers
    var isSuitableForLargeTransfers: Bool {
        return isConnected && !isExpensive && !isConstrained
    }
    
    /// Check if the current connection should use reduced data
    var shouldUseReducedData: Bool {
        return isExpensive || isConstrained || connectionType == .cellular
    }
    
    /// Get a user-friendly description of the current network status
    var statusDescription: String {
        guard isConnected else {
            return "No connection"
        }
        
        var description = connectionType.displayName
        
        if isExpensive {
            description += " (Expensive)"
        }
        
        if isConstrained {
            description += " (Limited)"
        }
        
        return description
    }
}

// MARK: - Async Extensions

extension NetworkMonitor {
    
    /// Wait for network connection to become available
    /// - Parameter timeout: Maximum time to wait in seconds
    /// - Returns: True if connection became available within timeout
    func waitForConnection(timeout: TimeInterval = 10.0) async -> Bool {
        guard !isConnected else {
            return true
        }
        
        return await withCheckedContinuation { continuation in
            var hasResumed = false
            let timeoutTask = Task {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                if !hasResumed {
                    hasResumed = true
                    continuation.resume(returning: false)
                }
            }
            
            let cancellable = $isConnected.sink { connected in
                if connected && !hasResumed {
                    hasResumed = true
                    timeoutTask.cancel()
                    continuation.resume(returning: true)
                }
            }
            
            // Clean up when done
            Task {
                await timeoutTask.value
                cancellable.cancel()
            }
        }
    }
} 