import Foundation

/// Parser for Server-Sent Events (SSE) data streams
struct SSEParser {
    
    // MARK: - Constants
    
    private static let dataPrefix = "data: "
    private static let eventPrefix = "event: "
    private static let idPrefix = "id: "
    private static let retryPrefix = "retry: "
    private static let doneMarker = "[DONE]"
    
    // MARK: - Parsing Methods
    
    /// Parse raw SSE data into structured events
    /// - Parameter data: Raw data from the stream
    /// - Returns: Array of parsed SSE events
    static func parseSSEData(_ data: Data) -> [SSEEvent] {
        guard let string = String(data: data, encoding: .utf8) else {
            return []
        }
        
        return parseSSEString(string)
    }
    
    /// Parse SSE string into structured events
    /// - Parameter string: Raw SSE string
    /// - Returns: Array of parsed SSE events
    static func parseSSEString(_ string: String) -> [SSEEvent] {
        var events: [SSEEvent] = []
        var currentEvent = SSEEvent()
        
        // Split by double newlines to separate events
        let lines = string.components(separatedBy: .newlines)
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)
            
            // Empty line indicates end of current event
            if trimmedLine.isEmpty {
                if !currentEvent.isEmpty {
                    events.append(currentEvent)
                    currentEvent = SSEEvent()
                }
                continue
            }
            
            // Parse different field types
            if trimmedLine.hasPrefix(dataPrefix) {
                let dataContent = String(trimmedLine.dropFirst(dataPrefix.count))
                
                // Check for DONE marker
                if dataContent.trimmingCharacters(in: .whitespaces) == doneMarker {
                    currentEvent.isDone = true
                } else {
                    // Append to existing data with newline separator for multi-line data
                    if currentEvent.data.isEmpty {
                        currentEvent.data = dataContent
                    } else {
                        currentEvent.data += "\n" + dataContent
                    }
                }
            } else if trimmedLine.hasPrefix(eventPrefix) {
                currentEvent.event = String(trimmedLine.dropFirst(eventPrefix.count))
            } else if trimmedLine.hasPrefix(idPrefix) {
                currentEvent.id = String(trimmedLine.dropFirst(idPrefix.count))
            } else if trimmedLine.hasPrefix(retryPrefix) {
                if let retryTime = Int(trimmedLine.dropFirst(retryPrefix.count)) {
                    currentEvent.retry = retryTime
                }
            }
            // Lines starting with ':' are comments and are ignored
        }
        
        // Add the last event if it exists
        if !currentEvent.isEmpty {
            events.append(currentEvent)
        }
        
        return events
    }
    
    /// Parse JSON data from SSE event
    /// - Parameter event: The SSE event containing JSON data
    /// - Returns: Parsed JSON object or nil if parsing fails
    static func parseJSON<T: Codable>(_ event: SSEEvent, as type: T.Type) -> T? {
        guard !event.data.isEmpty else {
            return nil
        }
        
        guard let jsonData = event.data.data(using: .utf8) else {
            return nil
        }
        
        do {
            let decoder = JSONDecoder()
            return try decoder.decode(type, from: jsonData)
        } catch {
            // In development, we might want to log this
            #if DEBUG
            print("SSEParser: Failed to parse JSON: \(error)")
            print("SSEParser: Raw data: \(event.data)")
            #endif
            return nil
        }
    }
}

// MARK: - SSE Event Model

/// Represents a single Server-Sent Event
struct SSEEvent {
    var event: String = ""
    var data: String = ""
    var id: String = ""
    var retry: Int?
    var isDone: Bool = false
    
    /// Check if the event is empty (no meaningful data)
    var isEmpty: Bool {
        return event.isEmpty && data.isEmpty && id.isEmpty && retry == nil && !isDone
    }
    
    /// Check if this event contains JSON data
    var hasJSONData: Bool {
        return !data.isEmpty && (data.hasPrefix("{") || data.hasPrefix("["))
    }
}

// MARK: - Stream Processing Extension

extension SSEParser {
    
    /// Create an async stream that parses SSE data from a URLSession data task
    /// - Parameter request: The URLRequest for the SSE endpoint
    /// - Returns: AsyncThrowingStream of parsed SSE events
    static func createEventStream(for request: URLRequest) -> AsyncThrowingStream<SSEEvent, Error> {
        return AsyncThrowingStream { continuation in
            
            // Create a dedicated URLSession with delegate for streaming
            let delegate = SSEStreamDelegate(continuation: continuation)
            
            // Use .ephemeral configuration instead of .default for two key reasons:
            //
            // 1. Reliability: It prevents connection pooling issues (e.g., NSURLErrorDomain Code=-1005)
            //    that were intermittently observed in the iOS Simulator when making streaming requests.
            //    For low-frequency, long-running streaming requests, the performance cost of creating a
            //    new connection each time is negligible, while the gain in reliability is significant.
            //
            // 2. Security & Privacy: API requests and responses contain sensitive user data. The .ephemeral
            //    configuration prevents caching any session data (like conversation content) to disk,
            //    enhancing privacy by minimizing the data footprint on the device.
            let session = URLSession(configuration: .ephemeral, delegate: delegate, delegateQueue: nil)
            let task = session.dataTask(with: request)
            
            // Store task in delegate for cancellation
            delegate.task = task
            
            // Handle cancellation
            continuation.onTermination = { @Sendable _ in
                task.cancel()
                session.invalidateAndCancel()
            }
            
            task.resume()
        }
    }
    

}

// MARK: - Streaming Delegate

/// URLSessionDataDelegate for handling streaming SSE data
private final class SSEStreamDelegate: NSObject, URLSessionDataDelegate {
    
    private let continuation: AsyncThrowingStream<SSEEvent, Error>.Continuation
    private var buffer = ""
    private var isErrorResponse = false
    private var errorStatusCode: Int = 200
    private var errorResponseData = Data()
    var task: URLSessionDataTask?
    
    init(continuation: AsyncThrowingStream<SSEEvent, Error>.Continuation) {
        self.continuation = continuation
        super.init()
    }
    
    // MARK: - URLSessionDataDelegate
    
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        
        // Check response status
        if let httpResponse = response as? HTTPURLResponse {
            print("[SSE] Response Status: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode != 200 {
                // Mark as error response but continue receiving data to get error details
                isErrorResponse = true
                errorStatusCode = httpResponse.statusCode
                print("[SSE] Error status detected, collecting response body...")
                completionHandler(.allow)
                return
            }
        }
        
        completionHandler(.allow)
    }
    
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        // If this is an error response, collect the response body and finish immediately
        if isErrorResponse {
            errorResponseData.append(data)
            if let errorString = String(data: data, encoding: .utf8) {
                print("[SSE] Error response data: \(errorString)")
                print("[SSE] Finishing stream with error response")
                
                let apiError = APIError.apiResponseError(
                    statusCode: errorStatusCode,
                    message: errorString
                )
                continuation.finish(throwing: apiError)
            }
            return
        }
        
        guard let string = String(data: data, encoding: .utf8) else {
            continuation.finish(throwing: APIError.parseError("Invalid UTF-8 data"))
            return
        }        
        buffer += string
        
        // Process complete events (support both \r\n\r\n and \n\n separators)
        // Try Google format (\r\n\r\n) first, then OpenAI format (\n\n)
        var parts = buffer.components(separatedBy: "\r\n\r\n")
        var usedSeparator = "\r\n\r\n"
        
        if parts.count <= 1 {
            // If no \r\n\r\n separator found, try \n\n (OpenAI format)
            parts = buffer.components(separatedBy: "\n\n")
            usedSeparator = "\n\n"
        }

        // print("[SSE] Parts: \(parts)")
        
        if parts.count > 1 {
            // Keep the last part as it might be incomplete
            buffer = parts.last ?? ""
            
            // Process complete event blocks
            for i in 0..<(parts.count - 1) {
                let eventBlock = parts[i]
                if !eventBlock.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    let events = SSEParser.parseSSEString(eventBlock + usedSeparator)
                    
                    for event in events {
                        if event.isDone {
                            print("[SSE] Received [DONE] marker - yielding event")
                            continuation.yield(event)
                            return
                        } else if !event.isEmpty {
                            continuation.yield(event)
                        }
                    }
                }
            }
        }
    }
    
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didCompleteWithError error: Error?) {
        // Handle error response case first
        if isErrorResponse {
            let errorMessage = String(data: errorResponseData, encoding: .utf8) ?? "Unknown error response"
            print("[SSE] Complete error response body: \(errorMessage)")
            
            let apiError = APIError.apiResponseError(
                statusCode: errorStatusCode,
                message: errorMessage
            )
            continuation.finish(throwing: apiError)
            return
        }
        
        // Handle normal completion
        if let error = error {
            if (error as NSError).code == NSURLErrorCancelled {
                print("[SSE] Task was cancelled")
                continuation.finish()
            } else {
                print("[SSE] Task completed with error: \(error)")
                continuation.finish(throwing: APIError.networkError(code: (error as NSError).code))
            }
        } else {
            print("[SSE] Task completed successfully")
            // Process any remaining data in buffer
            if !buffer.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                let events = SSEParser.parseSSEString(buffer)
                for event in events {
                    if !event.isEmpty && !event.isDone {
                        continuation.yield(event)
                    }
                }
            }
            continuation.finish()
        }
    }
} 
