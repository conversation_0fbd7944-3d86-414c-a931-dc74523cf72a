import Foundation

/// Utility for extracting and formatting content from message content blocks
/// Provides unified content extraction logic for different LLM API clients
///
/// Extensibility for future API clients:
/// - Add format strategy parameter to support API-specific formatting
/// - Consider adding contentTypeFilter parameter for selective extraction
struct MessageContentExtractor {

    /// Extract text content from content blocks with unified formatting
    /// - Parameter contentBlocks: Array of content blocks to process
    /// - Returns: Formatted text string with all text and file content
    ///
    /// Format rules:
    /// - `.text` blocks: returned as-is
    /// - `.attachedFileContent` blocks: formatted as "File: {fileName}\n\n{content}"
    /// - Other block types: ignored
    /// - Multiple blocks joined with "\n\n"
    static func extractTextContent(from contentBlocks: [ContentBlock]) -> String {
        return contentBlocks.compactMap { block in
            switch block {
            case .text(let text):
                return text
            case .attachedFileContent(let fileName, let fileContent):
                return "File: \(fileName)\n\n\(fileContent)"
            default:
                return nil
            }
        }.joined(separator: "\n\n")
    }

    // MARK: - Google API Content Extraction

    /// Extract content for Google Gemini API format
    /// - Parameter contentBlocks: Array of content blocks to process
    /// - Returns: Array of GooglePart objects for the API request
    static func extractGoogleParts(from contentBlocks: [ContentBlock]) -> [GooglePart] {
        var parts: [GooglePart] = []

        for block in contentBlocks {
            switch block {
            case .text(let text):
                if !text.isEmpty {
                    parts.append(GooglePart(text: text))
                }

            case .image(let imageInfo):
                if let base64Data = imageInfo.base64Data,
                   let mimeType = imageInfo.mimeType,
                   !base64Data.isEmpty {
                    let inlineData = GoogleInlineData(mimeType: mimeType, data: base64Data)
                    parts.append(GooglePart(inlineData: inlineData))
                }

            case .file(let fileInfo):
                if !fileInfo.base64Data.isEmpty {
                    let inlineData = GoogleInlineData(mimeType: fileInfo.mimeType, data: fileInfo.base64Data)
                    parts.append(GooglePart(inlineData: inlineData))
                }

            case .attachedFileContent(let fileName, let fileContent):
                if !fileContent.isEmpty {
                    let formattedText = "File: \(fileName)\n\n\(fileContent)"
                    parts.append(GooglePart(text: formattedText))
                }

            case .thinking(_):
                // skip thinking content for now
                // we will not add thinking content to the request for now
                continue

            default:
                // Skip other content types for now
                continue
            }
        }

        return parts
    }

    // MARK: - OpenAI API Content Extraction

    /// Extract content parts for OpenAI Compatible API format
    /// - Parameter contentBlocks: Array of content blocks to process
    /// - Returns: Array of OpenAIContentPart objects for multimodal requests
    static func extractOpenAIContentParts(from contentBlocks: [ContentBlock]) -> [OpenAIContentPart] {
        var parts: [OpenAIContentPart] = []

        for block in contentBlocks {
            switch block {
            case .text(let text):
                // Always include text content, even if empty
                parts.append(OpenAIContentPart.text(text))

            case .image(let imageInfo):
                if let base64Data = imageInfo.base64Data,
                   let mimeType = imageInfo.mimeType,
                   !base64Data.isEmpty {
                    // Format as data URL for OpenAI vision API
                    let dataUrl = "data:\(mimeType);base64,\(base64Data)"
                    parts.append(OpenAIContentPart.image(url: dataUrl))
                }

            case .file(let fileInfo):
                if !fileInfo.base64Data.isEmpty {
                    // Format as data URL for OpenAI Compatible API (like OpenRouter)
                    // According to OpenRouter docs, files should use data URL format in file_data field
                    let dataUrl = "data:\(fileInfo.mimeType);base64,\(fileInfo.base64Data)"
                    parts.append(OpenAIContentPart.file(
                        filename: fileInfo.fileName,
                        data: dataUrl
                    ))
                }

            case .attachedFileContent(let fileName, let fileContent):
                if !fileContent.isEmpty {
                    let formattedText = "File: \(fileName)\n\n\(fileContent)"
                    parts.append(OpenAIContentPart.text(formattedText))
                }

            case .thinking(_):
                // Skip thinking content for OpenAI requests
                // Thinking is handled differently in OpenAI responses
                continue

            default:
                // Skip other content types for now
                continue
            }
        }

        return parts
    }
}