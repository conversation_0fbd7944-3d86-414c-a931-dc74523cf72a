import Foundation

/// Builder for creating HTTP requests for LLM APIs
struct HTTPRequestBuilder {
    
    // MARK: - Configuration
    
    private let baseURL: String
    private let endpointPath: String
    private let authMethod: AuthenticationMethod
    private let defaultHeaders: [String: String]
    
    // MARK: - Initialization
    
    init(
        baseURL: String,
        endpointPath: String = "/chat/completions",
        authMethod: AuthenticationMethod,
        defaultHeaders: [String: String] = [:]
    ) {
        self.baseURL = baseURL.trimmingSuffix("/")
        self.endpointPath = endpointPath.hasPrefix("/") ? endpointPath : "/\(endpointPath)"
        self.authMethod = authMethod
        self.defaultHeaders = defaultHeaders
    }
    
    // MARK: - Request Building
    
    /// Build a URLRequest for the LLM API
    /// - Parameters:
    ///   - requestBody: The request body object to be JSON encoded
    ///   - additionalHeaders: Additional headers to include
    ///   - timeout: Request timeout in seconds
    /// - Returns: Configured URLRequest
    /// - Throws: APIError if request building fails
    func buildRequest<T: Encodable>(
        requestBody: T,
        additionalHeaders: [String: String] = [:],
        timeout: TimeInterval = 60.0
    ) throws -> URLRequest {
        
        // Build URL
        guard let url = buildURL() else {
            throw APIError.invalidRequest("Invalid base URL: \(baseURL)")
        }
        
        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.timeoutInterval = timeout
        
        // Set headers
        try setHeaders(on: &request, additionalHeaders: additionalHeaders)
        
        // Set body
        try setRequestBody(on: &request, body: requestBody)

        print("\(Date().timeIntervalSince1970) [HTTPRequestBuilder] request: \(request)")
        print("\(Date().timeIntervalSince1970) [HTTPRequestBuilder] requestBody: \(String(data: request.httpBody ?? Data(), encoding: .utf8)!.prefix(5000))")
        
        return request
    }
    
    /// Build a URLRequest for streaming
    /// - Parameters:
    ///   - requestBody: The request body object
    ///   - additionalHeaders: Additional headers
    ///   - timeout: Request timeout (longer for streaming)
    /// - Returns: Configured URLRequest for streaming
    func buildStreamingRequest<T: Encodable>(
        requestBody: T,
        additionalHeaders: [String: String] = [:]
    ) throws -> URLRequest {
        
        let streamingHeaders = additionalHeaders
        // Tested: Below headers are not needed for OpenAI compatible API
        // streamingHeaders["Accept"] = "text/event-stream"
        // streamingHeaders["Cache-Control"] = "no-cache"
        
        return try buildRequest(
            requestBody: requestBody,
            additionalHeaders: streamingHeaders,
            timeout: 300.0 // 5 minutes for streaming
        )
    }
    
    // MARK: - Private Methods
    
    private func buildURL() -> URL? {
        guard var urlComponents = URLComponents(string: baseURL) else {
            return nil
        }
        
        // Append endpoint path
        let existingPath = urlComponents.path
        if existingPath.isEmpty || existingPath == "/" {
            urlComponents.path = endpointPath
        } else {
            urlComponents.path = existingPath + endpointPath
        }
        
        // Add query parameters for authentication if needed
        if case .queryParameter(let name, let value) = authMethod {
            var queryItems = urlComponents.queryItems ?? []
            queryItems.append(URLQueryItem(name: name, value: value))
            urlComponents.queryItems = queryItems
        }
        
        // For Google API, add alt=sse query parameter for streaming
        if endpointPath.contains(":streamGenerateContent") {
            var queryItems = urlComponents.queryItems ?? []
            queryItems.append(URLQueryItem(name: "alt", value: "sse"))
            urlComponents.queryItems = queryItems
        }
        
        return urlComponents.url
    }
    
    private func setHeaders(
        on request: inout URLRequest,
        additionalHeaders: [String: String]
    ) throws {
        
        // Set default headers
        for (key, value) in defaultHeaders {
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Set content type
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Set authentication
        try setAuthentication(on: &request)
        
        // Set additional headers (can override defaults)
        for (key, value) in additionalHeaders {
            request.setValue(value, forHTTPHeaderField: key)
        }
    }
    
    private func setAuthentication(on request: inout URLRequest) throws {
        switch authMethod {
        case .apiKey(let key):
            guard !key.isEmpty else {
                throw APIError.authenticationFailed("API key is empty")
            }
            request.setValue("Bearer \(key)", forHTTPHeaderField: "Authorization")
            
        case .customHeader(let headerName, let value):
            guard !value.isEmpty else {
                throw APIError.authenticationFailed("Custom auth header value is empty")
            }
            request.setValue(value, forHTTPHeaderField: headerName)
            
        case .queryParameter(_, let value):
            guard !value.isEmpty else {
                throw APIError.authenticationFailed("Query parameter auth value is empty")
            }
            // Query parameter authentication is handled in buildURL()
            // No additional headers needed
        }
    }
    
    private func setRequestBody<T: Encodable>(on request: inout URLRequest, body: T) throws {
        do {
            let encoder = JSONEncoder()
            // encoder.keyEncodingStrategy = .convertToSnakeCase
            request.httpBody = try encoder.encode(body)
        } catch {
            throw APIError.parseError("Failed to encode request body: \(error.localizedDescription)")
        }
    }
}

// MARK: - Authentication Method

enum AuthenticationMethod {
    case apiKey(String)
    case customHeader(name: String, value: String)
    case queryParameter(name: String, value: String)
}

// MARK: - Builder Factory

extension HTTPRequestBuilder {
    
    /// Create a request builder from instance context
    /// - Parameter instanceContext: The LLM instance context
    /// - Parameter apiKey: The API key for authentication
    /// - Returns: Configured HTTP request builder
    /// - Throws: APIError if configuration is invalid
    static func from(
        instanceContext: LLMInstanceContext,
        apiKey: String
    ) throws -> HTTPRequestBuilder {
        
        let provider = instanceContext.provider
        let model = instanceContext.model
        
        // Validate base URL
        guard let baseURL = provider.apiBaseUrl, !baseURL.isEmpty else {
            throw APIError.invalidRequest("Provider \(provider.name) has no base URL configured")
        }
        
        // Determine endpoint path based on API style
        let endpointPath: String
        switch provider.apiStyle {
        case .google:
            // Google API requires model ID embedded in the path
            let basePath = provider.apiEndpointPath ?? "/v1beta/models"
            endpointPath = "\(basePath)/\(model.modelIdentifier):streamGenerateContent"
        default:
            endpointPath = provider.apiEndpointPath ?? "/chat/completions"
        }
        
        // Determine authentication method
        let authMethod: AuthenticationMethod
        switch provider.apiStyle {
        case .openaiCompatible:
            authMethod = .apiKey(apiKey)
        case .anthropic:
            authMethod = .customHeader(name: "x-api-key", value: apiKey)
        case .google:
            // Google uses query parameter authentication
            authMethod = .queryParameter(name: "key", value: apiKey)
        default:
            authMethod = .apiKey(apiKey)
        }
        
        // Set default headers based on API style
        var defaultHeaders: [String: String] = [:]
        switch provider.apiStyle {
        case .anthropic:
            defaultHeaders["anthropic-version"] = "2023-06-01"
        default:
            break
        }
        
        return HTTPRequestBuilder(
            baseURL: baseURL,
            endpointPath: endpointPath,
            authMethod: authMethod,
            defaultHeaders: defaultHeaders
        )
    }
}

// MARK: - String Extension

private extension String {
    func trimmingSuffix(_ suffix: String) -> String {
        if hasSuffix(suffix) {
            return String(dropLast(suffix.count))
        }
        return self
    }
} 
