import Foundation

/// Result of parameter processing containing all processed parameter sets
struct ProcessedParameters {
    /// All merged parameters (instance defaults + request overrides)
    let allParameters: [String: AnyCodable]
    
    /// Extracted known parameters for specific API
    let extractedParameters: [String: AnyCodable]
    
    /// Remaining parameters to be passed as additional/unknown parameters
    let additionalParameters: [String: AnyCodable]
    
    /// Resolved effective system prompt (request override takes precedence)
    let effectiveSystemPrompt: String?
}

/// Utility for processing LLM instance parameters and system prompts
/// Provides unified parameter merging and extraction logic for different LLM API clients
///
/// Extensibility for future API clients (e.g., Anthropic):
/// - Add API-specific parameter mapping strategies
/// - Support different parameter naming conventions
/// - Extensible system prompt formatting for different API requirements
struct ParameterProcessor {
    
    /// Process instance parameters with request overrides and system prompt resolution
    /// - Parameters:
    ///   - instance: LLM instance containing default parameters and system prompt
    ///   - overrides: Request-level parameter overrides
    ///   - overrideSystemPrompt: Request-level system prompt override
    /// - Returns: ProcessedParameters containing all processed parameter sets
    ///
    /// Processing logic:
    /// 1. Merge instance defaults with request overrides (overrides take precedence)
    /// 2. Resolve system prompt: overrideSystemPrompt ?? instance.systemPrompt
    /// 3. Return unified result for further API-specific extraction
    static func processParameters(
        instance: LLMInstance,
        overrides: [String: AnyCodable],
        overrideSystemPrompt: String?
    ) -> ProcessedParameters {
        
        // Merge parameters: instance defaults + request overrides
        var allParameters: [String: AnyCodable] = [:]
        
        // 1. First process instance default parameters (using AnyCodable.fromString)
        if let defaults = instance.defaultParameters {
            let parsedDefaults = AnyCodable.fromStringDictionary(defaults)
            allParameters.merge(parsedDefaults) { _, new in new }
        }
        
        // 2. Then process request override parameters (higher priority)
        for (key, value) in overrides {
            allParameters[key] = value
        }
        
        // 3. Resolve effective system prompt (override > instance default)
        let effectiveSystemPrompt = overrideSystemPrompt ?? instance.systemPrompt
        
        return ProcessedParameters(
            allParameters: allParameters,
            extractedParameters: [:], // Will be filled by extractKnownParameters
            additionalParameters: [:], // Will be filled by extractKnownParameters
            effectiveSystemPrompt: effectiveSystemPrompt
        )
    }
    
    /// Extract known parameters from all parameters and return separated sets
    /// - Parameters:
    ///   - allParameters: All merged parameters
    ///   - knownKeys: Set of parameter keys that should be extracted as known
    /// - Returns: Tuple of (extractedParameters, additionalParameters)
    ///
    /// Logic:
    /// - extractedParameters: Contains only keys present in knownKeys
    /// - additionalParameters: Contains all other keys not in knownKeys
    static func extractKnownParameters(
        from allParameters: [String: AnyCodable],
        knownKeys: Set<String>
    ) -> ([String: AnyCodable], [String: AnyCodable]) {
        
        var extractedParameters: [String: AnyCodable] = [:]
        var additionalParameters: [String: AnyCodable] = [:]
        
        for (key, value) in allParameters {
            if knownKeys.contains(key) {
                extractedParameters[key] = value
            } else {
                additionalParameters[key] = value
            }
        }
        
        return (extractedParameters, additionalParameters)
    }
    
    // MARK: - Capability Control Processing
    
    /// Process thinking capability control based on model capabilities and control switch
    /// - Parameters:
    ///   - model: LLM model with thinking capabilities
    ///   - instance: LLM instance with parameter values
    ///   - instanceId: Instance ID to check control for
    ///   - thinkingControls: Dictionary mapping instance IDs to thinking control switches
    ///   - parameters: Mutable parameters dictionary to modify
    /// - Returns: Modified message history if promptBased thinking is applied
    static func processThinkingControl(
        model: LLMModel,
        instance: LLMInstance,
        instanceId: UUID,
        thinkingControls: [UUID: Bool],
        parameters: inout [String: AnyCodable],
        messageHistory: [Message]
    ) -> [Message] {
        
        // Check if thinking is enabled for this instance (default: false)
        let thinkingEnabled = thinkingControls[instanceId] ?? false
        
        // Check if model has thinking capabilities
        guard let thinkingCapabilities = model.thinkingCapabilities,
              let controlType = thinkingCapabilities.controlType,
              let parameterName = thinkingCapabilities.parameterName else {
            return messageHistory // No thinking capabilities, return unchanged
        }
        
        // Get parameter value from instance defaults
        let parameterValue = instance.defaultParameters?[parameterName]
        
        var modifiedMessageHistory = messageHistory
        
        switch controlType {
        case .thinkingBudget:
            if thinkingEnabled, let parameterValue = parameterValue {
                // Use the parameter value from instance
                parameters[parameterName] = AnyCodable.fromString(parameterValue)
            } else {
                // Set to 0 to disable thinking
                parameters[parameterName] = AnyCodable(0)
            }
            
        case .reasoningEffort, .parameterBased:
            if thinkingEnabled, let parameterValue = parameterValue {
                // Use the parameter value from instance
                parameters[parameterName] = AnyCodable.fromString(parameterValue)
            } else {
                // Remove the parameter to disable thinking
                parameters.removeValue(forKey: parameterName)
            }
            
        case .promptBased:
            if thinkingEnabled, let parameterValue = parameterValue {
                // Add parameter value to the last user message
                modifiedMessageHistory = addThinkingPromptToLastUserMessage(
                    messageHistory: messageHistory,
                    thinkingPrompt: parameterValue
                )
            }
            // When disabled, no changes to message history
            
        case .none, .defaultOn:
            // No control needed for these types
            break
        }
        
        return modifiedMessageHistory
    }
    
    /// Process searching capability control based on model capabilities and control switch
    /// - Parameters:
    ///   - model: LLM model with searching capabilities
    ///   - instance: LLM instance with parameter values
    ///   - instanceId: Instance ID to check control for
    ///   - searchingControls: Dictionary mapping instance IDs to searching control switches
    ///   - parameters: Mutable parameters dictionary to modify
    static func processSearchingControl(
        model: LLMModel,
        instance: LLMInstance,
        instanceId: UUID,
        searchingControls: [UUID: Bool],
        parameters: inout [String: AnyCodable]
    ) {
        
        // Check if searching is enabled for this instance (default: false)
        let searchingEnabled = searchingControls[instanceId] ?? false
        
        // Check if model has searching capabilities
        guard let searchingCapabilities = model.searchingCapabilities,
              let controlType = searchingCapabilities.controlType,
              let parameterName = searchingCapabilities.parameterName else {
            return // No searching capabilities, no changes needed
        }
        
        // Get parameter value from instance defaults
        let parameterValue = instance.defaultParameters?[parameterName]
        
        switch controlType {
        case .parameterBased:
            if searchingEnabled, let parameterValue = parameterValue {
                // Use the parameter value from instance
                parameters[parameterName] = AnyCodable.fromString(parameterValue)
            } else {
                // Remove the parameter to disable searching
                parameters.removeValue(forKey: parameterName)
            }
            
        case .none, .appApi:
            // No parameter-based control needed for these types
            break
        }
    }
    
    // MARK: - Private Helper Methods
    
    /// Add thinking prompt text to the last user message
    private static func addThinkingPromptToLastUserMessage(
        messageHistory: [Message],
        thinkingPrompt: String
    ) -> [Message] {
        
        var modifiedHistory = messageHistory
        
        // Find the last user message
        if let lastUserIndex = modifiedHistory.lastIndex(where: { $0.role == .user }) {
            var lastUserMessage = modifiedHistory[lastUserIndex]
            
            // Extract current text content
            let currentText = MessageContentExtractor.extractTextContent(from: lastUserMessage.content)
            
            // Add thinking prompt on a new line
            let enhancedText = currentText + "\n" + thinkingPrompt
            
            // Create new content with enhanced text
            let newContent = [ContentBlock.text(enhancedText)]
            
            // Update the message
            lastUserMessage = Message(
                id: lastUserMessage.id,
                sessionId: lastUserMessage.sessionId,
                parentId: lastUserMessage.parentId,
                timestamp: lastUserMessage.timestamp,
                role: lastUserMessage.role,
                content: newContent,
                depth: lastUserMessage.depth,
                llmInstanceId: lastUserMessage.llmInstanceId,
                promptTokens: lastUserMessage.promptTokens,
                completionTokens: lastUserMessage.completionTokens,
                userId: lastUserMessage.userId,
                status: lastUserMessage.status,
                userFeedback: lastUserMessage.userFeedback,
                isReplied: lastUserMessage.isReplied,
                isFavorited: lastUserMessage.isFavorited,
                metadata: lastUserMessage.metadata
            )
            
            modifiedHistory[lastUserIndex] = lastUserMessage
        }
        
        return modifiedHistory
    }
} 
