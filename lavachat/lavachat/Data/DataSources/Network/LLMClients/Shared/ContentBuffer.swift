import Foundation

/// Time-windowed content buffer - reduces high-frequency UI updates
/// Optimizes UI performance by batching content chunks, avoiding stuttering caused by frequent single-character updates
final class ContentBuffer {
    
    // MARK: - Configuration
    
    /// Batch send interval: 500ms
    /// Balances responsiveness and performance, virtually imperceptible to users
    private let flushInterval: TimeInterval = 0.5
    
    // MARK: - Buffer Types
    
    /// Type of buffer content
    private enum BufferType {
        case content
        case thinking
    }
    
    /// Buffer state for a single content type - changed to reference type (class) to fix value copy issues
    private final class TypedBuffer {
        var buffer: String = ""
        var flushTimer: Task<Void, Never>?
        let onFlush: (String) -> Void
        let type: BufferType
        
        init(type: BufferType, onFlush: @escaping (String) -> Void) {
            self.type = type
            self.onFlush = onFlush
        }
        
        deinit {
            // Ensure timer is cancelled when buffer is deallocated
            flushTimer?.cancel()
        }
        
        /// Check if there's pending content to send
        var hasPendingContent: Bool {
            return !buffer.isEmpty
        }
        
        /// Flush buffered content immediately and clear buffer
        func flush() {
            let content = buffer
            
            // Clear buffer first
            buffer = ""
            
            // Cancel existing timer
            flushTimer?.cancel()
            flushTimer = nil
            
            // Send content if any
            if !content.isEmpty {
                onFlush(content)
            }
        }
        
        /// Schedule a flush operation with timer
        func scheduleFlush(after interval: TimeInterval) {
            // Don't schedule if timer is already running
            guard flushTimer == nil else { return }
            
            flushTimer = Task {
                do {
                    try await Task.sleep(nanoseconds: UInt64(interval * 1_000_000_000))
                    
                    // Check if still has content to flush (might have been flushed manually)
                    if hasPendingContent {
                        flush()
                    }
                } catch {
                    // Task was cancelled, ignore
                }
            }
        }
        
        /// Clean up timer and state
        func cleanup() {
            flushTimer?.cancel()
            flushTimer = nil
            buffer = ""
        }
    }
    
    /// Request-specific state containing separate buffers for content and thinking
    private final class RequestBuffers {
        var contentBuffer: TypedBuffer?
        var thinkingBuffer: TypedBuffer?
        
        init() {}
        
        deinit {
            contentBuffer?.cleanup()
            thinkingBuffer?.cleanup()
        }
        
        /// Clean up all buffers
        func cleanup() {
            contentBuffer?.cleanup()
            thinkingBuffer?.cleanup()
            contentBuffer = nil
            thinkingBuffer = nil
        }
        
        /// Flush all buffers
        func flushAll() {
            contentBuffer?.flush()
            thinkingBuffer?.flush()
        }
    }
    
    // MARK: - Thread-Safe Storage
    
    /// Thread-safe storage for request buffers using Actor
    private actor BufferStorage {
        private var buffers: [UUID: RequestBuffers] = [:]
        
        func getRequestBuffers(requestId: UUID) -> RequestBuffers {
            if let existing = buffers[requestId] {
                return existing
            } else {
                let newBuffers = RequestBuffers()
                buffers[requestId] = newBuffers
                return newBuffers
            }
        }
        
        func removeBuffer(requestId: UUID) {
            if let buffer = buffers[requestId] {
                buffer.cleanup()
                buffers.removeValue(forKey: requestId)
            }
        }
        
        func removeAllBuffers() {
            for buffer in buffers.values {
                buffer.cleanup()
            }
            buffers.removeAll()
        }
    }
    
    private let storage = BufferStorage()
    
    // MARK: - Public Interface
    
    /// Append content to buffer and batch send
    /// - Parameters:
    ///   - requestId: Request identifier
    ///   - content: Content to append
    ///   - source: Source identifier for debugging
    ///   - onFlush: Callback when buffer is flushed
    func appendContent(
        requestId: UUID,
        content: String,
        onFlush: @escaping (String, String) -> Void
    ) async {
        let requestBuffers = await storage.getRequestBuffers(requestId: requestId)
        
        // Create content buffer if needed
        if requestBuffers.contentBuffer == nil {
            requestBuffers.contentBuffer = TypedBuffer(type: .content) { bufferedContent in
                // When content buffer flushes, we need to pass both content and thinking
                // But since this is content-specific flush, thinking will be empty
                onFlush(bufferedContent, "")
            }
        }
        
        guard let contentBuffer = requestBuffers.contentBuffer else { return }
                
        // Append content
        contentBuffer.buffer += content
        
        // Schedule flush if not already scheduled
        if contentBuffer.flushTimer == nil {
            contentBuffer.scheduleFlush(after: flushInterval)
        }
    }
    
    /// Append thinking content to buffer and batch send
    /// - Parameters:
    ///   - requestId: Request identifier
    ///   - thinking: Thinking content to append  
    ///   - source: Source identifier for debugging
    ///   - onFlush: Callback when buffer is flushed
    func appendThinking(
        requestId: UUID,
        thinking: String,
        onFlush: @escaping (String, String) -> Void
    ) async {
        let requestBuffers = await storage.getRequestBuffers(requestId: requestId)
        
        // Create thinking buffer if needed
        if requestBuffers.thinkingBuffer == nil {
            requestBuffers.thinkingBuffer = TypedBuffer(type: .thinking) { bufferedThinking in
                // When thinking buffer flushes, we need to pass both content and thinking
                // But since this is thinking-specific flush, content will be empty
                onFlush("", bufferedThinking)
            }
        }
        
        guard let thinkingBuffer = requestBuffers.thinkingBuffer else { return }
                
        // Append thinking content
        thinkingBuffer.buffer += thinking
        
        // Schedule flush if not already scheduled
        if thinkingBuffer.flushTimer == nil {
            thinkingBuffer.scheduleFlush(after: flushInterval)
        }
    }
    
    /// Manually flush buffer for specific request
    /// - Parameter requestId: Request identifier
    func flushBuffer(requestId: UUID) async {
        let requestBuffers = await storage.getRequestBuffers(requestId: requestId)
        requestBuffers.flushAll()
    }
    
    /// Clean up buffer for specific request
    /// - Parameter requestId: Request identifier
    func cleanup(requestId: UUID) async {
        await storage.removeBuffer(requestId: requestId)
    }
    
    /// Clean up all buffers
    func cleanupAll() async {
        await storage.removeAllBuffers()
    }
} 