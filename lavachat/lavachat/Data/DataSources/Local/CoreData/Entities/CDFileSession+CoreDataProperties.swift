//
//  CDFileSession+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDFileSession {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDFileSession> {
        return NSFetchRequest<CDFileSession>(entityName: "CDFileSession")
    }

    @NSManaged public var activeLLMInstanceId: UUID?
    @NSManaged public var bookmarkData: Data?
    @NSManaged public var createdAt: Date?
    @NSManaged public var currentFileCheckpointId: UUID?
    @NSManaged public var id: UUID?
    @NSManaged public var lastModifiedAt: Date?
    @NSManaged public var metadata: Data?
    @NSManaged public var originalSourceInfo: Data?
    @NSManaged public var rootMessageId: UUID?
    @NSManaged public var settingsId: UUID?
    @NSManaged public var title: String?
    @NSManaged public var userId: UUID?
    @NSManaged public var checkpoints: NSSet?

}

// MARK: Generated accessors for checkpoints
extension CDFileSession {

    @objc(addCheckpointsObject:)
    @NSManaged public func addToCheckpoints(_ value: CDFileCheckpoint)

    @objc(removeCheckpointsObject:)
    @NSManaged public func removeFromCheckpoints(_ value: CDFileCheckpoint)

    @objc(addCheckpoints:)
    @NSManaged public func addToCheckpoints(_ values: NSSet)

    @objc(removeCheckpoints:)
    @NSManaged public func removeFromCheckpoints(_ values: NSSet)

}

extension CDFileSession : Identifiable {

}
