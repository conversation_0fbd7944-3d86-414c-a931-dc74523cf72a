//
//  CDLLMInstanceGroup+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDLLMInstanceGroup {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDLLMInstanceGroup> {
        return NSFetchRequest<CDLLMInstanceGroup>(entityName: "CDLLMInstanceGroup")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var description_: String?
    @NSManaged public var id: UUID?
    @NSManaged public var instanceIds: Data?
    @NSManaged public var isFavorited: Bool
    @NSManaged public var lastModifiedAt: Date?
    @NSManaged public var metadata: Data?
    @NSManaged public var name: String?
    @NSManaged public var customLogoData: Data?
    @NSManaged public var instances: NSSet?
    @NSManaged public var user: CDUser?

}

// MARK: Generated accessors for instances
extension CDLLMInstanceGroup {

    @objc(addInstancesObject:)
    @NSManaged public func addToInstances(_ value: CDLLMInstance)

    @objc(removeInstancesObject:)
    @NSManaged public func removeFromInstances(_ value: CDLLMInstance)

    @objc(addInstances:)
    @NSManaged public func addToInstances(_ values: NSSet)

    @objc(removeInstances:)
    @NSManaged public func removeFromInstances(_ values: NSSet)

}

extension CDLLMInstanceGroup : Identifiable {

}
