//
//  CDFileCheckpoint+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDFileCheckpoint {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDFileCheckpoint> {
        return NSFetchRequest<CDFileCheckpoint>(entityName: "CDFileCheckpoint")
    }

    @NSManaged public var basedOnCheckpointId: UUID?
    @NSManaged public var fileContent: String?
    @NSManaged public var fileSessionId: UUID?
    @NSManaged public var id: UUID?
    @NSManaged public var metadata: Data?
    @NSManaged public var pendingEditStates: Data?
    @NSManaged public var timestamp: Date?
    @NSManaged public var triggeringMessageId: UUID?
    @NSManaged public var fileSession: CDFileSession?

}

extension CDFileCheckpoint : Identifiable {

}
