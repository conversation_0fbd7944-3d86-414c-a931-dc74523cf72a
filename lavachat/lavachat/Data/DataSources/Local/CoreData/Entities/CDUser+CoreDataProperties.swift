//
//  CDUser+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDUser {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDUser> {
        return NSFetchRequest<CDUser>(entityName: "CDUser")
    }

    @NSManaged public var appSettings: Data?
    @NSManaged public var avatarIdentifier: String?
    @NSManaged public var defaultChatSettingsId: UUID?
    @NSManaged public var id: UUID?
    @NSManaged public var lastSeenAt: Date?
    @NSManaged public var lastSyncTimestamp: Date?
    @NSManaged public var memoryFileBookmarkData: Data?
    @NSManaged public var metadata: Data?
    @NSManaged public var nickname: String?
    @NSManaged public var registrationDate: Date?
    @NSManaged public var syncStatus: String?
    @NSManaged public var systemUtilityLLMInstanceId: UUID?
    @NSManaged public var chatSessions: NSSet?
    @NSManaged public var defaultChatSettings: CDChatSessionSetting?
    @NSManaged public var systemUtilityLLMInstance: CDLLMInstance?

}

// MARK: Generated accessors for chatSessions
extension CDUser {

    @objc(addChatSessionsObject:)
    @NSManaged public func addToChatSessions(_ value: CDChatSession)

    @objc(removeChatSessionsObject:)
    @NSManaged public func removeFromChatSessions(_ value: CDChatSession)

    @objc(addChatSessions:)
    @NSManaged public func addToChatSessions(_ values: NSSet)

    @objc(removeChatSessions:)
    @NSManaged public func removeFromChatSessions(_ values: NSSet)

}

extension CDUser : Identifiable {

}
