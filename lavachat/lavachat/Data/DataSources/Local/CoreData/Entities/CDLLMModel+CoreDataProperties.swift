//
//  CDLLMModel+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDLLMModel {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDLLMModel> {
        return NSFetchRequest<CDLLMModel>(entityName: "CDLLMModel")
    }

    @NSManaged public var thinkingCapabilities: Data?
    @NSManaged public var searchingCapabilities: Data?
    @NSManaged public var availabilityStatus: String?
    @NSManaged public var contextWindowSize: Int64
    @NSManaged public var logoImageName: String?
    @NSManaged public var customLogoData: Data?
    @NSManaged public var maxOutputTokens: Int64
    @NSManaged public var modelDescription: String?
    @NSManaged public var id: UUID?
    @NSManaged public var isDefaultRecommendation: Bool
    @NSManaged public var isUserCreated: Bool
    @NSManaged public var isUserModified: Bool
    @NSManaged public var metadata: Data?
    @NSManaged public var modelIdentifier: String?
    @NSManaged public var name: String?
    @NSManaged public var pricingInfo: String?
    @NSManaged public var providerId: UUID?
    @NSManaged public var inputModalities: Data?
    @NSManaged public var outputModalities: Data?
    @NSManaged public var group: String?
    @NSManaged public var instances: NSSet?
    @NSManaged public var provider: CDLLMProvider?
    @NSManaged public var apiConfigsOverride: Data?

}

// MARK: Generated accessors for instances
extension CDLLMModel {

    @objc(addInstancesObject:)
    @NSManaged public func addToInstances(_ value: CDLLMInstance)

    @objc(removeInstancesObject:)
    @NSManaged public func removeFromInstances(_ value: CDLLMInstance)

    @objc(addInstances:)
    @NSManaged public func addToInstances(_ values: NSSet)

    @objc(removeInstances:)
    @NSManaged public func removeFromInstances(_ values: NSSet)

}

extension CDLLMModel : Identifiable {

}
