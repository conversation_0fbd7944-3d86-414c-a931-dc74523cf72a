//
//  CDMessage+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDMessage {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDMessage> {
        return NSFetchRequest<CDMessage>(entityName: "CDMessage")
    }

    @NSManaged public var completionTokens: Int64
    @NSManaged public var content: Data?
    @NSManaged public var id: UUID?
    @NSManaged public var isFavorited: Bool
    @NSManaged public var isReplied: Bool
    @NSManaged public var depth: Int64
    @NSManaged public var llmInstanceId: UUID?
    @NSManaged public var metadata: Data?
    @NSManaged public var parentId: UUID?
    @NSManaged public var parsedFileOperations: Data?
    @NSManaged public var promptTokens: Int64
    @NSManaged public var rawResponseText: String?
    @NSManaged public var role: String?
    @NSManaged public var sessionId: UUID?
    @NSManaged public var status: String?
    @NSManaged public var timestamp: Date?
    @NSManaged public var userFeedback: String?
    @NSManaged public var userId: UUID?
    @NSManaged public var children: NSSet?
    @NSManaged public var llmInstance: CDLLMInstance?
    @NSManaged public var parent: CDMessage?
    @NSManaged public var session: CDChatSession?

}

// MARK: Generated accessors for children
extension CDMessage {

    @objc(addChildrenObject:)
    @NSManaged public func addToChildren(_ value: CDMessage)

    @objc(removeChildrenObject:)
    @NSManaged public func removeFromChildren(_ value: CDMessage)

    @objc(addChildren:)
    @NSManaged public func addToChildren(_ values: NSSet)

    @objc(removeChildren:)
    @NSManaged public func removeFromChildren(_ values: NSSet)

}

extension CDMessage : Identifiable {

}
