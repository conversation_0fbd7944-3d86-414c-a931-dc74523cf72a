//
//  CDChatSession+CoreDataClass.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


public class CDChatSession: NSManagedObject {
    override public func awakeFromInsert() {
        super.awakeFromInsert()
        setPrimitiveValue(UUID(), forKey: "id")
        setPrimitiveValue(Date(), forKey: #keyPath(CDChatSession.createdAt))
        setPrimitiveValue(Date(), forKey: #keyPath(CDChatSession.lastModifiedAt))
    }
}
