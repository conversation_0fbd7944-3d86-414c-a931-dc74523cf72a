//
//  CDChatSessionSetting+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDChatSessionSetting {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDChatSessionSetting> {
        return NSFetchRequest<CDChatSessionSetting>(entityName: "CDChatSessionSetting")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var defaultContextServerIds: Data?
    @NSManaged public var id: UUID?
    @NSManaged public var isSystemDefault: Bool
    @NSManaged public var lastModifiedAt: Date?
    @NSManaged public var llmParameterOverrides: Data?
    @NSManaged public var messageActionSettings: Data?
    @NSManaged public var metadata: Data?
    @NSManaged public var name: String?
    @NSManaged public var shouldExpandThinking: Bool
    @NSManaged public var uiThemeSettings: Data?
    @NSManaged public var savedPromptSegments: Data?
    @NSManaged public var auxiliaryLLMInstanceId: UUID?
    @NSManaged public var shouldAutoGenerateTitle: Bool
    @NSManaged public var contextMessageCount: Int64
    @NSManaged public var chatSessions: NSSet?
    @NSManaged public var defaultForUser: CDUser?

}

// MARK: Generated accessors for chatSessions
extension CDChatSessionSetting {

    @objc(addChatSessionsObject:)
    @NSManaged public func addToChatSessions(_ value: CDChatSession)

    @objc(removeChatSessionsObject:)
    @NSManaged public func removeFromChatSessions(_ value: CDChatSession)

    @objc(addChatSessions:)
    @NSManaged public func addToChatSessions(_ values: NSSet)

    @objc(removeChatSessions:)
    @NSManaged public func removeFromChatSessions(_ values: NSSet)

}

extension CDChatSessionSetting : Identifiable {

}
