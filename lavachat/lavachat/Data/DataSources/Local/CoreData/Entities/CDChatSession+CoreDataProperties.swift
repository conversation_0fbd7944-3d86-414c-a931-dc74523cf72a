//
//  CDChatSession+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDChatSession {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDChatSession> {
        return NSFetchRequest<CDChatSession>(entityName: "CDChatSession")
    }

    @NSManaged public var activeContextServerIds: Data?
    @NSManaged public var activeLLMInstanceIds: Data?
    @NSManaged public var usedLLMInstanceIds: Data?
    @NSManaged public var createdAt: Date?
    @NSManaged public var id: UUID?
    @NSManaged public var instanceSettings: Data?
    @NSManaged public var lastModifiedAt: Date?
    @NSManaged public var metadata: Data?
    @NSManaged public var settingsId: UUID?
    @NSManaged public var title: String?
    @NSManaged public var userId: UUID?
    @NSManaged public var messages: NSSet?
    @NSManaged public var settings: CDChatSessionSetting?
    @NSManaged public var user: CDUser?
    @NSManaged public var activeMessageId: UUID?

}

// MARK: Generated accessors for messages
extension CDChatSession {

    @objc(addMessagesObject:)
    @NSManaged public func addToMessages(_ value: CDMessage)

    @objc(removeMessagesObject:)
    @NSManaged public func removeFromMessages(_ value: CDMessage)

    @objc(addMessages:)
    @NSManaged public func addToMessages(_ values: NSSet)

    @objc(removeMessages:)
    @NSManaged public func removeFromMessages(_ values: NSSet)

}

extension CDChatSession : Identifiable {

}
