//
//  CDLLMProvider+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDLLMProvider {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDLLMProvider> {
        return NSFetchRequest<CDLLMProvider>(entityName: "CDLLMProvider")
    }

    @NSManaged public var apiBaseUrl: String?
    @NSManaged public var apiEndpointPath: String?
    @NSManaged public var apiDocumentationUrl: String?
    @NSManaged public var apiStyle: String
    @NSManaged public var logoImageName: String?
    @NSManaged public var customLogoData: Data?
    @NSManaged public var id: UUID?
    @NSManaged public var isUserCreated: Bool
    @NSManaged public var isUserModified: Bool
    @NSManaged public var metadata: Data?
    @NSManaged public var name: String?
    @NSManaged public var providerType: String?
    @NSManaged public var apiKeyStored: Bool
    @NSManaged public var websiteUrl: String?
    @NSManaged public var models: NSSet?

}

// MARK: Generated accessors for models
extension CDLL<PERSON><PERSON>ider {

    @objc(addModelsObject:)
    @NSManaged public func addToModels(_ value: CDLLMModel)

    @objc(removeModelsObject:)
    @NSManaged public func removeFromModels(_ value: CDLLMModel)

    @objc(addModels:)
    @NSManaged public func addToModels(_ values: NSSet)

    @objc(removeModels:)
    @NSManaged public func removeFromModels(_ values: NSSet)

}

extension CDLLMProvider : Identifiable {

}
