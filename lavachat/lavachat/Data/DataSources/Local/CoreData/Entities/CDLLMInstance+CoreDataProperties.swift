//
//  CDLLMInstance+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


extension CDLLMInstance {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDLLMInstance> {
        return NSFetchRequest<CDLLMInstance>(entityName: "CDLLMInstance")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var defaultParameters: Data?
    @NSManaged public var id: UUID?
    @NSManaged public var isFavorited: Bool
    @NSManaged public var isUserModified: Bool
    @NSManaged public var lastUsedAt: Date?
    @NSManaged public var metadata: Data?
    @NSManaged public var modelId: UUID?
    @NSManaged public var name: String?
    @NSManaged public var customLogoData: Data?
    @NSManaged public var systemPrompt: String?
    @NSManaged public var totalCompletionTokensUsed: Int64
    @NSManaged public var totalPromptTokensUsed: Int64
    @NSManaged public var groups: NSSet?
    @NSManaged public var messages: NSSet?
    @NSManaged public var model: CDLLMModel?
    @NSManaged public var user: CDUser?
    @NSManaged public var utilityForUser: CDUser?

}

// MARK: Generated accessors for groups
extension CDLLMInstance {

    @objc(addGroupsObject:)
    @NSManaged public func addToGroups(_ value: CDLLMInstanceGroup)

    @objc(removeGroupsObject:)
    @NSManaged public func removeFromGroups(_ value: CDLLMInstanceGroup)

    @objc(addGroups:)
    @NSManaged public func addToGroups(_ values: NSSet)

    @objc(removeGroups:)
    @NSManaged public func removeFromGroups(_ values: NSSet)

}

// MARK: Generated accessors for messages
extension CDLLMInstance {

    @objc(addMessagesObject:)
    @NSManaged public func addToMessages(_ value: CDMessage)

    @objc(removeMessagesObject:)
    @NSManaged public func removeFromMessages(_ value: CDMessage)

    @objc(addMessages:)
    @NSManaged public func addToMessages(_ values: NSSet)

    @objc(removeMessages:)
    @NSManaged public func removeFromMessages(_ values: NSSet)

}

extension CDLLMInstance : Identifiable {

}
