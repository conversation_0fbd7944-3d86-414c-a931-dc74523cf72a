//
//  CDLLMInstanceGroup+CoreDataClass.swift
//  lavachat
//
//  Created by ht on 2025/4/18.
//
//

import Foundation
import CoreData


public class CDLLMInstanceGroup: NSManagedObject {
    override public func awakeFromInsert() {
        super.awakeFromInsert()
        setPrimitiveValue(UUID(), forKey: "id")
        setPrimitiveValue(Date(), forKey: #keyPath(CDLLMInstanceGroup.createdAt))
        setPrimitiveValue(Date(), forKey: #keyPath(CDLLMInstanceGroup.lastModifiedAt))
    }
}
