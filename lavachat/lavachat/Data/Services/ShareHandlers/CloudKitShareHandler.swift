import Foundation
import CloudKit

/// Handler for CloudKit-based sharing operations
class CloudKitShareHandler {

    // MARK: - Properties

    private let container: CKContainer
    private let privateDatabase: CKDatabase
    private let sharedDatabase: CKDatabase
    private let customZone: CKRecordZone

    // MARK: - Initialization

    init(container: CKContainer = CKContainer.default()) {
        self.container = container
        self.privateDatabase = container.privateCloudDatabase
        self.sharedDatabase = container.sharedCloudDatabase
        // Create a custom zone for sharing - shares cannot exist in the default zone
        self.customZone = CKRecordZone(zoneName: "LavaChatShareZone")
    }
    
    // MARK: - Public Methods
    
    /// Check if CloudKit sharing is available
    /// - Returns: True if CloudKit is available and user is signed in
    func isAvailable() async -> Bool {
        do {
            let accountStatus = try await container.accountStatus()
            print("CloudKit account status: \(accountStatus)")
            
            switch accountStatus {
            case .available:
                return true
            case .noAccount:
                print("CloudKit: No iCloud account configured")
                return false
            case .restricted:
                print("CloudKit: iCloud account is restricted")
                return false
            case .couldNotDetermine:
                print("CloudKit: Could not determine iCloud account status")
                return false
            case .temporarilyUnavailable:
                print("CloudKit: iCloud is temporarily unavailable")
                return false
            @unknown default:
                print("CloudKit: Unknown account status")
                return false
            }
        } catch {
            print("CloudKit availability check failed: \(error)")
            return false
        }
    }
    
    /// Create an iCloud share for the provided data
    /// - Parameters:
    ///   - data: The shareable data to share
    ///   - permissions: Sharing permissions configuration
    /// - Returns: URL of the iCloud share
    func createICloudShare(
        data: ShareableData,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        // Check if CloudKit is available
        guard await isAvailable() else {
            throw ShareError.icloudUnavailable
        }

        do {
            // Ensure the custom zone exists before creating records
            try await ensureCustomZoneExists()

            // Create a record for the shareable data
            let record = try createShareRecord(from: data)

            // Create a share for the record (before saving)
            let share = CKShare(rootRecord: record)

            // Configure share permissions
            configureSharePermissions(share, permissions: permissions)

            // Save both the record and share in the same operation
            let modifyOperation = CKModifyRecordsOperation(
                recordsToSave: [record, share],
                recordIDsToDelete: nil
            )
            
            return try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<URL, Error>) in
                var savedShare: CKShare?

                // Handle individual record saves
                modifyOperation.perRecordSaveBlock = { recordID, saveResult in
                    print("perRecordSaveBlock for \(recordID): \(saveResult)")
                    switch saveResult {
                    case .success(let record):
                        print("Record saved successfully: \(record)")
                        // Check if this is the share record
                        if record.recordType == "cloudkit.share" {
                            savedShare = record as? CKShare
                            print("Share record saved: \(String(describing: savedShare))")
                        }
                    case .failure(let error):
                        print("Failed to save record \(recordID): \(error)")
                    }
                }

                // Handle overall operation result
                modifyOperation.modifyRecordsResultBlock = { result in
                    print("modifyRecordsResultBlock: \(result)")
                    switch result {
                    case .success:
                        // Get the share URL from the saved share
                        if let savedShare = savedShare, let shareURL = savedShare.url {
                            print("Share URL obtained: \(shareURL)")
                            continuation.resume(returning: shareURL)
                        } else {
                            print("Failed to get share URL from saved share")
                            continuation.resume(throwing: ShareError.icloudShareFailed("Failed to get share URL"))
                        }
                    case .failure(let error):
                        print("CloudKit operation failed: \(error)")
                        continuation.resume(throwing: ShareError.icloudShareFailed("CloudKit error: \(error.localizedDescription)"))
                    }
                }

                privateDatabase.add(modifyOperation)
            }
            
        } catch let error as CKError {
            throw ShareError.icloudShareFailed("CloudKit error: \(error.localizedDescription)")
        } catch {
            throw ShareError.icloudShareFailed("Unknown error: \(error.localizedDescription)")
        }
    }
    
    /// Accept an iCloud share and retrieve the shared data
    /// - Parameter shareURL: URL of the iCloud share
    /// - Returns: The shared data
    func acceptICloudShare(_ shareURL: URL) async throws -> ShareableData {
        do {
            print("🔄 CloudKitShareHandler: Getting share metadata for URL: \(shareURL)")

            // Get share metadata first
            let shareMetadata = try await container.shareMetadata(for: shareURL)

            print("🔄 CloudKitShareHandler: Share metadata obtained: \(shareMetadata)")
            print("🔄 CloudKitShareHandler: Root record ID: \(shareMetadata.rootRecordID.recordName)")
            print("🔄 CloudKitShareHandler: Zone ID: \(shareMetadata.rootRecordID.zoneID.zoneName)")

            // Accept the share
            let acceptShareOperation = CKAcceptSharesOperation(shareMetadatas: [shareMetadata])

            return try await withCheckedThrowingContinuation { continuation in
                acceptShareOperation.acceptSharesResultBlock = { result in
                    print("🔄 CloudKitShareHandler: Accept share result: \(result)")
                    switch result {
                    case .success:
                        // Share accepted, now fetch the data
                        Task {
                            do {
                                let data = try await self.fetchSharedData(from: shareURL)
                                continuation.resume(returning: data)
                            } catch {
                                print("🔄 CloudKitShareHandler: Failed to fetch shared data: \(error)")
                                continuation.resume(throwing: error)
                            }
                        }
                    case .failure(let error):
                        print("🔄 CloudKitShareHandler: Failed to accept share: \(error)")
                        continuation.resume(throwing: ShareError.icloudShareFailed("Failed to accept share: \(error.localizedDescription)"))
                    }
                }

                container.add(acceptShareOperation)
            }
            
        } catch let error as ShareError {
            throw error
        } catch {
            throw ShareError.icloudShareFailed("Failed to accept share: \(error.localizedDescription)")
        }
    }
    
    /// Fetch shared data from an accepted share
    /// - Parameter shareURL: URL of the accepted share
    /// - Returns: The shared data
    func fetchSharedData(from shareURL: URL) async throws -> ShareableData {
        do {
            // Extract the share metadata from URL
            let shareMetadata = try await container.shareMetadata(for: shareURL)

            // For accepted shares, we need to use the shared database
            // But first, we need to check if we're the owner or a participant
            let rootRecordID = shareMetadata.rootRecordID

            print("🔄 CloudKitShareHandler: Fetching record \(rootRecordID) from shared database")
            print("🔄 CloudKitShareHandler: Share metadata zone: \(shareMetadata.rootRecordID.zoneID.zoneName)")
            print("🔄 CloudKitShareHandler: Participant status: \(shareMetadata.participantStatus)")
            print("🔄 CloudKitShareHandler: Participant role: \(shareMetadata.participantRole)")

            // Try to fetch from shared database first
            do {
                let record = try await sharedDatabase.record(for: rootRecordID)
                let shareableData = try parseShareRecord(record)
                return shareableData
            } catch {
                print("🔄 CloudKitShareHandler: Failed to fetch from shared database: \(error)")

                // If we're the owner and it failed from shared database, try private database
                if shareMetadata.participantRole == .owner {
                    print("🔄 CloudKitShareHandler: Trying private database as owner")
                    let record = try await privateDatabase.record(for: rootRecordID)
                    let shareableData = try parseShareRecord(record)
                    return shareableData
                } else {
                    throw error
                }
            }
            
        } catch let error as CKError {
            throw ShareError.icloudShareFailed("CloudKit error: \(error.localizedDescription)")
        } catch {
            throw ShareError.icloudShareFailed("Failed to fetch shared data: \(error.localizedDescription)")
        }
    }
    
    /// Get information about a share without accepting it
    /// - Parameter shareURL: URL of the share
    /// - Returns: Preview information about the share
    func getSharePreview(_ shareURL: URL) async -> ImportPreview? {
        do {
            let shareMetadata = try await container.shareMetadata(for: shareURL)
            
            // Extract basic information from metadata
            let contentType: ShareContentType = .llmInstance // Default, should be extracted from metadata
            let itemNames = [shareMetadata.share.recordID.recordName] // Placeholder
            
            return ImportPreview(
                contentType: contentType,
                itemCount: 1,
                itemNames: itemNames,
                formatVersion: "1.0",
                exportedAt: shareMetadata.share.creationDate ?? Date(),
                metadata: ShareableMetadata()
            )
            
        } catch {
            return nil
        }
    }
    
    /// Stop sharing a previously shared item
    /// - Parameter shareURL: URL of the share to stop
    func stopSharing(_ shareURL: URL) async throws {
        do {
            let shareMetadata = try await container.shareMetadata(for: shareURL)
            let share = shareMetadata.share
            
            // Delete the share
            try await privateDatabase.deleteRecord(withID: share.recordID)
            
        } catch let error as CKError {
            throw ShareError.icloudShareFailed("Failed to stop sharing: \(error.localizedDescription)")
        } catch {
            throw ShareError.icloudShareFailed("Failed to stop sharing: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Private Methods

    /// Ensures that the custom zone exists in the private database
    private func ensureCustomZoneExists() async throws {
        do {
            // Check if the zone already exists
            let existingZones = try await privateDatabase.allRecordZones()
            let zoneExists = existingZones.contains { $0.zoneID == customZone.zoneID }

            if !zoneExists {
                // Create the custom zone
                let modifyZonesOperation = CKModifyRecordZonesOperation(
                    recordZonesToSave: [customZone],
                    recordZoneIDsToDelete: nil
                )

                try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                    modifyZonesOperation.modifyRecordZonesResultBlock = { result in
                        switch result {
                        case .success:
                            print("Custom zone created successfully: \(self.customZone.zoneID)")
                            continuation.resume()
                        case .failure(let error):
                            print("Failed to create custom zone: \(error)")
                            continuation.resume(throwing: error)
                        }
                    }

                    privateDatabase.add(modifyZonesOperation)
                }
            } else {
                print("Custom zone already exists: \(customZone.zoneID)")
            }
        } catch {
            print("Error ensuring custom zone exists: \(error)")
            throw error
        }
    }

    private func createShareRecord(from data: ShareableData) throws -> CKRecord {
        // Create a unique record ID in the custom zone
        let recordID = CKRecord.ID(recordName: "LavaChatShare_\(UUID().uuidString)", zoneID: customZone.zoneID)
        let record = CKRecord(recordType: "LavaChatShare", recordID: recordID)
        
        // Encode the data to JSON
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(data)
        
        // Store the data in the record
        record["shareData"] = jsonData
        record["shareType"] = data.shareType.rawValue
        record["formatVersion"] = data.formatVersion
        record["exportedAt"] = data.exportedAt
        
        // Add metadata for easier querying
        if let instance = data.data.instance {
            record["instanceName"] = instance.name
        }
        if let session = data.data.chatSession {
            record["sessionTitle"] = session.title
        }
        if let action = data.data.messageAction {
            record["actionName"] = action.name
        }
        if let setting = data.data.chatSessionSetting {
            record["settingName"] = setting.name
        }
        
        return record
    }
    
    private func parseShareRecord(_ record: CKRecord) throws -> ShareableData {
        guard let jsonData = record["shareData"] as? Data else {
            throw ShareError.icloudShareFailed("Invalid share record format")
        }
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        do {
            return try decoder.decode(ShareableData.self, from: jsonData)
        } catch {
            throw ShareError.icloudShareFailed("Failed to decode share data: \(error.localizedDescription)")
        }
    }
    
    private func configureSharePermissions(_ share: CKShare, permissions: ICloudSharePermissions) {
        // Set public permission
        if permissions.allowsReadOnly {
            share.publicPermission = .readOnly
        } else if permissions.allowsReadWrite {
            share.publicPermission = .readWrite
        } else {
            share.publicPermission = .none
        }
        
        // Set expiration date if provided
        if permissions.expirationDate != nil {
            // Note: CKShare doesn't have a direct expiration property
            // This would need to be handled at the application level
        }
    }
}
