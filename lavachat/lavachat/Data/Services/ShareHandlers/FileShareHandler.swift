import Foundation

/// Handler for file-based sharing operations
class FileShareHandler {
    
    // MARK: - Properties
    
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    private let shareDirectory: URL
    
    // MARK: - Initialization
    
    init() throws {
        // Get documents directory
        guard let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw ShareError.fileCreationFailed("Cannot access documents directory")
        }
        
        self.documentsDirectory = documentsURL
        self.shareDirectory = documentsURL.appendingPathComponent("LavaChatShares", isDirectory: true)
        
        // Create share directory if it doesn't exist
        try createShareDirectoryIfNeeded()
    }
    
    // MARK: - Public Methods
    
    /// Create a shareable file from the provided data
    /// - Parameters:
    ///   - data: The shareable data to write to file
    ///   - fileName: The desired file name (should include .lavachat extension)
    /// - Returns: URL of the created file
    func createShareFile(data: ShareableData, fileName: String) async throws -> URL {
        // Ensure the file name has the correct extension
        let sanitizedFileName = ensureLavaChatExtension(fileName)
        let fileURL = shareDirectory.appendingPathComponent(sanitizedFileName)
        
        do {
            // Encode the data to JSON
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
            
            let jsonData = try encoder.encode(data)
            
            // Write to file
            try jsonData.write(to: fileURL)
            
            return fileURL
            
        } catch {
            throw ShareError.fileCreationFailed("Failed to create share file: \(error.localizedDescription)")
        }
    }
    
    /// Read and parse a shareable file
    /// - Parameter fileURL: URL of the file to read
    /// - Returns: Parsed shareable data
    func readShareFile(from fileURL: URL) async throws -> ShareableData {
        do {
            // Check if file exists and is readable
            guard fileManager.fileExists(atPath: fileURL.path) else {
                throw ImportError.invalidFileFormat
            }
            
            // Read file data
            let jsonData = try Data(contentsOf: fileURL)
            
            // Decode the data
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let shareableData = try decoder.decode(ShareableData.self, from: jsonData)
            
            return shareableData
            
        } catch let error as ImportError {
            throw error
        } catch _ as DecodingError {
            throw ImportError.corruptedData
        } catch {
            throw ImportError.unknownError("Failed to read share file: \(error.localizedDescription)")
        }
    }
    
    /// Validate that a file can be imported
    /// - Parameter fileURL: URL of the file to validate
    /// - Returns: True if the file appears to be a valid LavaChat share file
    func canImportFile(_ fileURL: URL) async -> Bool {
        do {
            // Check file extension
            guard fileURL.pathExtension.lowercased() == "lavachat" else {
                return false
            }
            
            // Try to read and parse the file
            _ = try await readShareFile(from: fileURL)
            return true
            
        } catch {
            return false
        }
    }
    
    /// Get preview information about a shareable file
    /// - Parameter fileURL: URL of the file to preview
    /// - Returns: Preview information or nil if cannot be previewed
    func getFilePreview(_ fileURL: URL) async -> ImportPreview? {
        do {
            let shareableData = try await readShareFile(from: fileURL)
            
            // Extract item names based on content type
            var itemNames: [String] = []
            var itemCount = 0
            
            switch shareableData.shareType {
            case .llmInstance:
                if let instance = shareableData.data.instance {
                    itemNames.append(instance.name)
                    itemCount += 1
                }
                if let model = shareableData.data.model {
                    itemNames.append("Model: \(model.name)")
                    itemCount += 1
                }
                if let provider = shareableData.data.provider {
                    itemNames.append("Provider: \(provider.name)")
                    itemCount += 1
                }
                
            case .chatSession:
                if let session = shareableData.data.chatSession {
                    itemNames.append(session.title ?? "Untitled Session")
                    itemCount += 1
                }
                if let setting = shareableData.data.chatSessionSetting {
                    itemNames.append("Setting: \(setting.name)")
                    itemCount += 1
                }
                if let actions = shareableData.data.messageActions {
                    for action in actions {
                        itemNames.append("Action: \(action.name)")
                        itemCount += 1
                    }
                }

            case .messageAction:
                if let action = shareableData.data.messageAction {
                    itemNames.append(action.name)
                    itemCount = 1
                }

            case .chatSessionSetting:
                if let setting = shareableData.data.chatSessionSetting {
                    itemNames.append(setting.name)
                    itemCount += 1
                }
                if let actions = shareableData.data.messageActions {
                    for action in actions {
                        itemNames.append("Action: \(action.name)")
                        itemCount += 1
                    }
                }
            }
            
            return ImportPreview(
                contentType: shareableData.shareType,
                itemCount: itemCount,
                itemNames: itemNames,
                formatVersion: shareableData.formatVersion,
                exportedAt: shareableData.exportedAt,
                metadata: shareableData.metadata
            )
            
        } catch {
            return nil
        }
    }
    
    /// Clean up old share files to free up space
    /// - Parameter olderThan: Remove files older than this date
    func cleanupOldShareFiles(olderThan date: Date) async throws {
        do {
            let contents = try fileManager.contentsOfDirectory(
                at: shareDirectory,
                includingPropertiesForKeys: [.creationDateKey],
                options: []
            )
            
            for fileURL in contents {
                guard fileURL.pathExtension.lowercased() == "lavachat" else { continue }
                
                let resourceValues = try fileURL.resourceValues(forKeys: [.creationDateKey])
                if let creationDate = resourceValues.creationDate, creationDate < date {
                    try fileManager.removeItem(at: fileURL)
                }
            }
            
        } catch {
            throw ShareError.fileCreationFailed("Failed to cleanup old files: \(error.localizedDescription)")
        }
    }
    
    /// Get the size of all share files
    /// - Returns: Total size in bytes
    func getShareFilesTotalSize() async throws -> Int64 {
        do {
            let contents = try fileManager.contentsOfDirectory(
                at: shareDirectory,
                includingPropertiesForKeys: [.fileSizeKey],
                options: []
            )
            
            var totalSize: Int64 = 0
            
            for fileURL in contents {
                guard fileURL.pathExtension.lowercased() == "lavachat" else { continue }
                
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = resourceValues.fileSize {
                    totalSize += Int64(fileSize)
                }
            }
            
            return totalSize
            
        } catch {
            throw ShareError.fileCreationFailed("Failed to calculate files size: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Private Methods
    
    private func createShareDirectoryIfNeeded() throws {
        if !fileManager.fileExists(atPath: shareDirectory.path) {
            try fileManager.createDirectory(
                at: shareDirectory,
                withIntermediateDirectories: true,
                attributes: nil
            )
        }
    }
    
    private func ensureLavaChatExtension(_ fileName: String) -> String {
        if fileName.lowercased().hasSuffix(".lavachat") {
            return fileName
        } else {
            // Remove any existing extension and add .lavachat
            let nameWithoutExtension = (fileName as NSString).deletingPathExtension
            return "\(nameWithoutExtension).lavachat"
        }
    }
}
