import Foundation
import CoreImage
import UIKit
import Vision
import AVFoundation
import os.log

/// Handler for QR code generation and scanning operations
final class QRCodeShareHandler: @unchecked Sendable {

    // MARK: - Properties

    private let context = CIContext()
    private let logger = Logger(subsystem: "lavachat.tests", category: "QRCodeShareHandler")
    
    // MARK: - QR Code Generation
    
    /// Generate a QR code image for the given data
    /// - Parameters:
    ///   - data: The string data to encode in the QR code
    ///   - size: The desired size of the QR code image
    /// - Returns: PNG image data of the QR code
    func generateQRCode(for data: String, size: CGSize) async throws -> Data {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    let qrCodeData = try self.createQRCodeImage(from: data, size: size)
                    continuation.resume(returning: qrCodeData)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - QR Code Scanning
    
    /// Scan QR code from image data
    /// - Parameter imageData: The image data to scan
    /// - Returns: Array of detected QR code strings
    func scanQRCode(from imageData: Data) async throws -> [String] {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    let results = try self.detectQRCodes(in: imageData)
                    continuation.resume(returning: results)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    /// Scan QR code from UIImage
    /// - Parameter image: The UIImage to scan
    /// - Returns: Array of detected QR code strings
    func scanQRCode(from image: UIImage) async throws -> [String] {
        guard let imageData = image.pngData() else {
            throw ShareError.qrCodeGenerationFailed
        }
        
        return try await scanQRCode(from: imageData)
    }
    
    /// Validate if a string contains a valid LavaChat share URL
    /// - Parameter qrData: The string data from QR code
    /// - Returns: True if it's a valid LavaChat share URL
    func isValidLavaChatShareURL(_ qrData: String) -> Bool {
        // Check if it's a valid URL
        guard let url = URL(string: qrData) else { return false }
        
        // Check if it's an iCloud share URL or a custom LavaChat URL
        let host = url.host?.lowercased() ?? ""
        
        return host.contains("icloud.com") || 
               host.contains("lavachat.app") ||
               qrData.contains("lavachat://share")
    }
    
    // MARK: - Private Methods
    
    private func createQRCodeImage(from string: String, size: CGSize, includeLogo: Bool = true) throws -> Data {
        logger.info("Creating QR code for string")

        // Validate input
        guard !string.isEmpty else {
            logger.error("Empty string provided for QR code generation")
            throw ShareError.qrCodeGenerationFailed
        }

        // Create QR code filter
        guard let filter = CIFilter(name: "CIQRCodeGenerator") else {
            logger.error("Failed to create CIQRCodeGenerator filter")
            throw ShareError.qrCodeGenerationFailed
        }

        // Set input data
        let data = string.data(using: .utf8)
        filter.setValue(data, forKey: "inputMessage")
        filter.setValue("H", forKey: "inputCorrectionLevel") // High error correction
        logger.info("Set filter input data and correction level")

        // Get output image
        guard let outputImage = filter.outputImage else {
            logger.error("Failed to get output image from QR code filter")
            throw ShareError.qrCodeGenerationFailed
        }
        logger.info("Generated QR code image successfully")
        
        // Scale the image to desired size using nearest neighbor for crisp pixels
        let scaleX = size.width / outputImage.extent.width
        let scaleY = size.height / outputImage.extent.height
        let scaledImage = outputImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))
        logger.info("Scaled QR code image by \(scaleX)x\(scaleY)")

        // Create a high-quality context for rendering
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGImageAlphaInfo.premultipliedLast.rawValue

        guard let context = CGContext(
            data: nil,
            width: Int(size.width),
            height: Int(size.height),
            bitsPerComponent: 8,
            bytesPerRow: 0,
            space: colorSpace,
            bitmapInfo: bitmapInfo
        ) else {
            logger.error("Failed to create CGContext for QR code rendering")
            throw ShareError.qrCodeGenerationFailed
        }

        // Set interpolation to none for crisp QR code pixels
        context.interpolationQuality = .none

        // Render the scaled image
        let ciContext = CIContext(cgContext: context, options: nil)
        ciContext.draw(scaledImage, in: CGRect(origin: .zero, size: size), from: scaledImage.extent)

        // Get the rendered CGImage
        guard let cgImage = context.makeImage() else {
            logger.error("Failed to create CGImage from context")
            throw ShareError.qrCodeGenerationFailed
        }
        logger.info("Created CGImage successfully")

        // Convert to UIImage
        let uiImage = UIImage(cgImage: cgImage)
        
        // Add logo if requested
        let finalImage = includeLogo ? try addLogoToQRCode(uiImage, size: size) : uiImage
        
        guard let pngData = finalImage.pngData() else {
            logger.error("Failed to convert UIImage to PNG data")
            throw ShareError.qrCodeGenerationFailed
        }
        logger.info("Generated PNG data with size: \(pngData.count) bytes")

        return pngData
    }
    
    private func detectQRCodes(in imageData: Data) throws -> [String] {
        guard let image = UIImage(data: imageData),
              let cgImage = image.cgImage else {
            logger.error("Failed to create CGImage from image data")
            throw ShareError.qrCodeGenerationFailed
        }

        logger.info("Starting QR code detection on image with size: \(image.size.width)x\(image.size.height)")

        // Try Vision framework first
        if let visionResults = try? detectQRCodesWithVision(cgImage: cgImage) {
            return visionResults
        }

        // Fallback to Core Image detector if Vision fails
        logger.info("Vision framework failed, falling back to Core Image detector")
        return try detectQRCodesWithCoreImage(cgImage: cgImage)
    }

    private func detectQRCodesWithVision(cgImage: CGImage) throws -> [String] {
        // Create Vision request with more comprehensive symbologies
        let request = VNDetectBarcodesRequest()
        request.symbologies = [.qr]

        // Use software renderer for better simulator compatibility
        let options: [VNImageOption: Any] = [
            .ciContext: CIContext(options: [.useSoftwareRenderer: true])
        ]

        // Perform the request
        let handler = VNImageRequestHandler(cgImage: cgImage, options: options)

        try handler.perform([request])

        // Extract results
        guard let results = request.results else {
            throw ShareError.qrCodeGenerationFailed
        }

        logger.info("Vision framework returned \(results.count) barcode results")

        var qrStrings: [String] = []
        for result in results {
            if let payload = result.payloadStringValue {
                logger.info("Found payload with Vision: \(payload)")
                qrStrings.append(payload)
            }
        }

        return qrStrings
    }

    private func detectQRCodesWithCoreImage(cgImage: CGImage) throws -> [String] {
        // Create Core Image detector
        guard let detector = CIDetector(ofType: CIDetectorTypeQRCode,
                                       context: nil,
                                       options: [CIDetectorAccuracy: CIDetectorAccuracyHigh]) else {
            logger.error("Failed to create Core Image QR code detector")
            throw ShareError.qrCodeGenerationFailed
        }

        let ciImage = CIImage(cgImage: cgImage)
        let features = detector.features(in: ciImage)

        logger.info("Core Image detector returned \(features.count) features")

        var qrStrings: [String] = []
        for feature in features {
            if let qrFeature = feature as? CIQRCodeFeature,
               let messageString = qrFeature.messageString {
                logger.info("Found payload with Core Image: \(messageString)")
                qrStrings.append(messageString)
            }
        }

        if qrStrings.isEmpty {
            logger.warning("No QR codes detected with either Vision or Core Image")
        }

        return qrStrings
    }
    
    // MARK: - Logo Processing
    
    /// Add app logo to the center of QR code
    /// - Parameters:
    ///   - qrImage: The original QR code image
    ///   - size: The size of the QR code
    /// - Returns: QR code image with logo in center
    private func addLogoToQRCode(_ qrImage: UIImage, size: CGSize) throws -> UIImage {
        logger.info("Adding logo to QR code")
        
        // Get app icon
        guard let appIcon = getAppIcon() else {
            logger.warning("Failed to get app icon, returning original QR code")
            return qrImage
        }
        
        // Calculate logo size (20% of QR code size)
        let logoSize = CGSize(
            width: size.width * 0.2,
            height: size.height * 0.2
        )
        
        // Create graphics context
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else {
            logger.error("Failed to create graphics context for logo composition")
            throw ShareError.qrCodeGenerationFailed
        }
        
        // Draw the QR code first
        qrImage.draw(in: CGRect(origin: .zero, size: size))
        
        // Calculate logo position (center of QR code)
        let logoRect = CGRect(
            x: (size.width - logoSize.width) / 2,
            y: (size.height - logoSize.height) / 2,
            width: logoSize.width,
            height: logoSize.height
        )
        
        // Draw white background square for logo
        let backgroundRect = logoRect.insetBy(dx: -4, dy: -4) // 4pt padding
        context.setFillColor(UIColor.white.cgColor)
        context.fill(backgroundRect)
        
        // Draw logo with corner radius
        context.saveGState()
        
        // Create rounded rect path for logo
        let cornerRadius: CGFloat = logoSize.width * 0.15
        let logoPath = UIBezierPath(roundedRect: logoRect, cornerRadius: cornerRadius)
        logoPath.addClip()
        
        // Draw the app icon
        appIcon.draw(in: logoRect)
        
        context.restoreGState()
        
        // Get the final composed image
        guard let finalImage = UIGraphicsGetImageFromCurrentImageContext() else {
            logger.error("Failed to get final composed image")
            throw ShareError.qrCodeGenerationFailed
        }
        
        logger.info("Successfully added logo to QR code")
        return finalImage
    }
    
    /// Extract app icon from bundle
    /// - Returns: App icon UIImage or nil if not found
    private func getAppIcon() -> UIImage? {
        // Try to get app icon from bundle
        if let appIcon = UIImage(named: "AppIcon") {
            return appIcon
        }
        
        // Fallback: try to get from Assets
        if let appIcon = UIImage(named: "lavachat", in: Bundle.main, compatibleWith: nil) {
            return appIcon
        }
        
        // Last resort: try to get app icon from system
        if let iconsDictionary = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
           let primaryIconsDictionary = iconsDictionary["CFBundlePrimaryIcon"] as? [String: Any],
           let iconFiles = primaryIconsDictionary["CFBundleIconFiles"] as? [String],
           let lastIcon = iconFiles.last {
            return UIImage(named: lastIcon)
        }
        
        logger.warning("Could not find app icon")
        return nil
    }
}
