import Foundation
import UIKit
import CloudKit

/// Implementation of ShareServiceProtocol
final class ShareServiceImpl: ShareServiceProtocol, @unchecked Sendable {
    
    // MARK: - Dependencies
    
    private let fileShareHandler: FileShareHandler
    private let cloudKitShareHandler: CloudKitShareHandler
    private let qrCodeShareHandler: QRCodeShareHandler
    private let llmRepository: LLMInstanceRepository
    private let chatRepository: ChatRepository
    
    // MARK: - State Management
    
    private var activeOperations: [UUID: ShareOperation] = [:]
    private let operationQueue = DispatchQueue(label: "com.lavachat.share", qos: .userInitiated)
    
    // MARK: - Initialization
    
    init(
        fileShareHandler: FileShareHandler,
        cloudKitShareHandler: CloudKitShareHandler,
        qrCodeShareHandler: QRCodeShareHandler,
        llmRepository: LLMInstanceRepository,
        chatRepository: ChatRepository
    ) {
        self.fileShareHandler = fileShareHandler
        self.cloudKitShareHandler = cloudKitShareHandler
        self.qrCodeShareHandler = qrCodeShareHandler
        self.llmRepository = llmRepository
        self.chatRepository = chatRepository
    }
    
    // MARK: - Core Sharing Methods
    
    func shareItem<T: ShareableItem>(
        _ item: T,
        configuration: ShareConfiguration
    ) async -> ShareResult {
        let operationId = UUID()
        let operation = ShareOperation(id: operationId, item: item, configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        do {
            // Validate the item first
            try validateItem(item)
            
            // Update progress
            await updateProgress(operationId, progress: 0.1)
            
            // Prepare shareable data with dependencies
            let shareableData = try await prepareShareableData(for: item, configuration: configuration)
            
            await updateProgress(operationId, progress: 0.3)
            
            // Execute sharing based on format
            let successInfo: ShareSuccessInfo
            
            switch configuration.format {
            case .file:
                let fileURL = try await fileShareHandler.createShareFile(
                    data: shareableData,
                    fileName: configuration.customFileName ?? getDefaultFileName(for: item)
                )
                successInfo = ShareSuccessInfo(
                    format: .file,
                    contentType: item.shareContentType,
                    fileURL: fileURL
                )
                
            case .icloud:
                let icloudURL = try await cloudKitShareHandler.createICloudShare(
                    data: shareableData,
                    permissions: configuration.icloudPermissions ?? ICloudSharePermissions()
                )
                successInfo = ShareSuccessInfo(
                    format: .icloud,
                    contentType: item.shareContentType,
                    icloudURL: icloudURL
                )
                
            case .qrCode:
                // For QR code, we first create an iCloud share, then generate QR code for the URL
                let icloudURL = try await cloudKitShareHandler.createICloudShare(
                    data: shareableData,
                    permissions: configuration.icloudPermissions ?? ICloudSharePermissions()
                )
                let qrCodeData = try await qrCodeShareHandler.generateQRCode(
                    for: icloudURL.absoluteString,
                    size: CGSize(width: 300, height: 300)
                )
                successInfo = ShareSuccessInfo(
                    format: .qrCode,
                    contentType: item.shareContentType,
                    icloudURL: icloudURL,
                    qrCodeImage: qrCodeData
                )
            }
            
            await updateProgress(operationId, progress: 1.0)
            return .success(successInfo)
            
        } catch let error as ShareError {
            return .failure(error)
        } catch {
            return .failure(.unknownError(error.localizedDescription))
        }
    }
    
    func shareItems<T: ShareableItem>(
        _ items: [T],
        configuration: ShareConfiguration
    ) async -> ShareResult {
        // For now, we'll implement this as sharing the first item
        // In the future, this could be enhanced to create a bundle
        guard let firstItem = items.first else {
            return .failure(.invalidContent)
        }
        
        return await shareItem(firstItem, configuration: configuration)
    }
    
    // MARK: - Format-Specific Sharing
    
    func shareAsFile<T: ShareableItem>(
        _ item: T,
        fileName: String?
    ) async throws -> URL {
        let configuration = ShareConfiguration(
            format: .file,
            customFileName: fileName
        )
        
        let result = await shareItem(item, configuration: configuration)
        
        switch result {
        case .success(let info):
            guard let fileURL = info.fileURL else {
                throw ShareError.fileCreationFailed("No file URL returned")
            }
            return fileURL
        case .failure(let error):
            throw error
        }
    }
    
    func shareViaICloud<T: ShareableItem>(
        _ item: T,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        let configuration = ShareConfiguration(
            format: .icloud,
            icloudPermissions: permissions
        )
        
        let result = await shareItem(item, configuration: configuration)
        
        switch result {
        case .success(let info):
            guard let icloudURL = info.icloudURL else {
                throw ShareError.icloudShareFailed("No iCloud URL returned")
            }
            return icloudURL
        case .failure(let error):
            throw error
        }
    }
    
    func generateQRCode<T: ShareableItem>(
        for item: T,
        size: CGSize
    ) async throws -> Data {
        let configuration = ShareConfiguration(
            format: .qrCode
        )
        
        let result = await shareItem(item, configuration: configuration)
        
        switch result {
        case .success(let info):
            guard let qrCodeData = info.qrCodeImage else {
                throw ShareError.qrCodeGenerationFailed
            }
            return qrCodeData
        case .failure(let error):
            throw error
        }
    }
    
    // MARK: - Utility Methods
    
    func isFormatAvailable(_ format: ShareFormat) async -> Bool {
        switch format {
        case .file:
            return true // Always available
        case .icloud:
            return await cloudKitShareHandler.isAvailable()
        case .qrCode:
            return await cloudKitShareHandler.isAvailable() // QR codes depend on iCloud for sharing
        }
    }
    
    func getSupportedFormats() async -> [ShareFormat] {
        var formats: [ShareFormat] = [.file] // File sharing is always available
        
        if await cloudKitShareHandler.isAvailable() {
            formats.append(.icloud)
            formats.append(.qrCode)
        }
        
        return formats
    }
    
    func validateItem<T: ShareableItem>(_ item: T) throws {
        try item.validateForSharing()
    }
    
    func getDefaultFileName<T: ShareableItem>(for item: T) -> String {
        let sanitizedName = item.shareDisplayName
            .replacingOccurrences(of: "[^a-zA-Z0-9\\s\\-_]", with: "", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        let fileName = sanitizedName.isEmpty ? "LavaChat_Export" : sanitizedName
        return "\(fileName).lavachat"
    }
    
    // MARK: - Progress and Cancellation
    
    func cancelSharingOperation(_ operationId: UUID) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                if let operation = self.activeOperations[operationId] {
                    operation.isCancelled = true
                }
                continuation.resume()
            }
        }
    }
    
    func getSharingProgress(_ operationId: UUID) -> Double? {
        return activeOperations[operationId]?.progress
    }
    
    // MARK: - Private Helper Methods
    
    private func updateProgress(_ operationId: UUID, progress: Double) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId]?.progress = progress
                continuation.resume()
            }
        }
    }
    
    private func prepareShareableData<T: ShareableItem>(
        for item: T,
        configuration: ShareConfiguration
    ) async throws -> ShareableData {
        // Get the basic shareable data from the item
        var shareableData = try await item.toShareableData()
        
        // Enhance with dependencies based on the item type
        switch item.shareContentType {
        case .llmInstance:
            shareableData = try await enhanceWithLLMDependencies(shareableData, item: item)
        case .chatSession:
            shareableData = try await enhanceWithChatDependencies(shareableData, item: item)
        case .chatSessionSetting:
            shareableData = try await enhanceWithChatSessionSettingDependencies(shareableData, item: item)
        case .messageAction:
            // Single message actions don't need additional dependencies
            break
        }
        
        // Sanitize data if requested
        if configuration.sanitizeData {
            shareableData = sanitizeShareableData(shareableData)
        }
        
        return shareableData
    }
    
    private func enhanceWithLLMDependencies<T: ShareableItem>(
        _ shareableData: ShareableData,
        item: T
    ) async throws -> ShareableData {
        guard let instance = shareableData.data.instance else {
            throw ShareError.invalidContent
        }
        
        // Fetch associated model and provider
        let model = try await llmRepository.getModel(byId: instance.modelId)
        let provider: LLMProvider?
        if let model = model {
            provider = try await llmRepository.getProvider(byId: model.providerId)
        } else {
            provider = nil
        }
        
        guard let model = model, let provider = provider else {
            throw ShareError.missingDependencies("Associated model or provider not found")
        }
        
        // Create enhanced content
        let enhancedContent = ShareableDataContent(
            instance: instance,
            model: model,
            provider: provider
        )
        
        return ShareableData(
            shareType: shareableData.shareType,
            data: enhancedContent,
            metadata: shareableData.metadata
        )
    }
    
    private func enhanceWithChatDependencies<T: ShareableItem>(
        _ shareableData: ShareableData,
        item: T
    ) async throws -> ShareableData {
        guard let session = shareableData.data.chatSession else {
            throw ShareError.invalidContent
        }

        // Fetch associated chat session setting
        var setting: ChatSessionSetting? = nil
        if let settingsId = session.settingsId {
            setting = try await chatRepository.getSetting(byId: settingsId)
        }

        // Get related message actions if setting exists
        var relatedActions: [MessageAction] = []
        if let setting = setting,
           let messageActionSettings = setting.messageActionSettings {
            let uniqueActionIds = messageActionSettings.uniqueActionIds
            // Filter out system actions - only include non-system actions
            let nonSystemActionIds = uniqueActionIds.filter { actionId in
                !SystemMessageActions.actions.contains { $0.id == actionId }
            }
            if !nonSystemActionIds.isEmpty {
                relatedActions = try await chatRepository.getMessageActions(for: nonSystemActionIds)
            }
        }

        // Create enhanced content
        let enhancedContent = ShareableDataContent(
            chatSession: session,
            messageActions: relatedActions.isEmpty ? nil : relatedActions,
            chatSessionSetting: setting
        )

        return ShareableData(
            shareType: shareableData.shareType,
            data: enhancedContent,
            metadata: shareableData.metadata
        )
    }

    private func enhanceWithChatSessionSettingDependencies<T: ShareableItem>(
        _ shareableData: ShareableData,
        item: T
    ) async throws -> ShareableData {
        guard let setting = shareableData.data.chatSessionSetting else {
            throw ShareError.invalidContent
        }

        // Get related message actions
        var relatedActions: [MessageAction] = []
        if let messageActionSettings = setting.messageActionSettings {
            let uniqueActionIds = messageActionSettings.uniqueActionIds
            // Filter out system actions - only include non-system actions
            let nonSystemActionIds = uniqueActionIds.filter { actionId in
                !SystemMessageActions.actions.contains { $0.id == actionId }
            }
            if !nonSystemActionIds.isEmpty {
                relatedActions = try await chatRepository.getMessageActions(for: nonSystemActionIds)
            }
        }

        // Create enhanced content
        let enhancedContent = ShareableDataContent(
            messageActions: relatedActions.isEmpty ? nil : relatedActions,
            chatSessionSetting: setting
        )

        return ShareableData(
            shareType: shareableData.shareType,
            data: enhancedContent,
            metadata: shareableData.metadata
        )
    }
    
    private func sanitizeShareableData(_ shareableData: ShareableData) -> ShareableData {
        var sanitizedData = shareableData
        
        // Remove sensitive information from provider
        if var provider = sanitizedData.data.provider {
            provider.apiKeyStored = false
            // Clear any sensitive metadata
            provider.metadata = provider.metadata?.filter { key, _ in
                !key.lowercased().contains("key") && !key.lowercased().contains("secret")
            }
            
            let sanitizedContent = ShareableDataContent(
                instance: sanitizedData.data.instance,
                model: sanitizedData.data.model,
                provider: provider,
                chatSession: sanitizedData.data.chatSession,
                messageAction: sanitizedData.data.messageAction,
                chatSessionSetting: sanitizedData.data.chatSessionSetting
            )
            
            sanitizedData = ShareableData(
                shareType: sanitizedData.shareType,
                data: sanitizedContent,
                metadata: sanitizedData.metadata
            )
        }
        
        return sanitizedData
    }
}

// MARK: - Supporting Types

private final class ShareOperation: @unchecked Sendable {
    let id: UUID
    let item: any ShareableItem
    let configuration: ShareConfiguration
    var progress: Double = 0.0
    var isCancelled: Bool = false
    
    init(id: UUID, item: any ShareableItem, configuration: ShareConfiguration) {
        self.id = id
        self.item = item
        self.configuration = configuration
    }
}
