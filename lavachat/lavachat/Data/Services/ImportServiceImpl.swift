import Foundation

/// Implementation of ImportServiceProtocol
final class ImportServiceImpl: ImportServiceProtocol, @unchecked Sendable {
    
    // MARK: - Dependencies
    
    private let fileShareHandler: FileShareHandler
    private let cloudKitShareHandler: CloudKitShareHandler
    private let qrCodeShareHandler: QRCodeShareHandler
    private let llmRepository: LLMInstanceRepository
    private let chatRepository: ChatRepository
    
    // MARK: - State Management
    
    private var activeOperations: [UUID: ImportOperation] = [:]
    private let operationQueue = DispatchQueue(label: "com.lavachat.import", qos: .userInitiated)
    
    // MARK: - Initialization
    
    init(
        fileShareHandler: FileShareHandler,
        cloudKitShareHandler: CloudKitShareHandler,
        qrCodeShareHandler: QRCodeShareHandler,
        llmRepository: LLMInstanceRepository,
        chatRepository: ChatRepository
    ) {
        self.fileShareHandler = fileShareHandler
        self.cloudKitShareHandler = cloudKitShareHandler
        self.qrCodeShareHandler = qrCodeShareHandler
        self.llmRepository = llmRepository
        self.chatRepository = chatRepository
    }
    
    // MARK: - Core Import Methods
    
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = ImportOperation(id: operationId, source: .file(fileURL), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        do {
            await updateProgress(operationId, progress: 0.1)
            
            // Read and parse the file
            let shareableData = try await fileShareHandler.readShareFile(from: fileURL)
            
            await updateProgress(operationId, progress: 0.3)
            
            // Process the import
            let result = try await processImport(shareableData, configuration: configuration, operationId: operationId)
            
            await updateProgress(operationId, progress: 1.0)
            return .success(result)
            
        } catch let error as ImportError {
            return .failure(error)
        } catch {
            return .failure(.unknownError(error.localizedDescription))
        }
    }
    
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = ImportOperation(id: operationId, source: .icloud(shareURL), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        do {
            await updateProgress(operationId, progress: 0.1)
            
            // Accept and fetch the iCloud share
            let shareableData = try await cloudKitShareHandler.acceptICloudShare(shareURL)
            
            await updateProgress(operationId, progress: 0.3)
            
            // Process the import
            let result = try await processImport(shareableData, configuration: configuration, operationId: operationId)
            
            await updateProgress(operationId, progress: 1.0)
            return .success(result)
            
        } catch let error as ImportError {
            return .failure(error)
        } catch {
            return .failure(.unknownError(error.localizedDescription))
        }
    }
    
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        // Validate QR code data
        guard qrCodeShareHandler.isValidLavaChatShareURL(qrCodeData) else {
            return .failure(.invalidFileFormat)
        }
        
        // If it's an iCloud URL, delegate to iCloud import
        if let url = URL(string: qrCodeData), url.host?.contains("icloud.com") == true {
            return await importFromICloudShare(url, configuration: configuration)
        }
        
        // Handle other custom URL schemes if needed
        return .failure(.unsupportedVersion("QR code format not supported"))
    }
    
    // MARK: - Validation and Preview
    
    func canImportFile(_ fileURL: URL) async -> Bool {
        return await fileShareHandler.canImportFile(fileURL)
    }
    
    func getImportPreview(_ fileURL: URL) async -> ImportPreview? {
        return await fileShareHandler.getFilePreview(fileURL)
    }
    
    func canImportFromQRCode(_ qrCodeData: String) async -> Bool {
        return qrCodeShareHandler.isValidLavaChatShareURL(qrCodeData)
    }
    
    // MARK: - Progress and Cancellation
    
    func cancelImportOperation(_ operationId: UUID) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                if let operation = self.activeOperations[operationId] {
                    operation.isCancelled = true
                }
                continuation.resume()
            }
        }
    }
    
    func getImportProgress(_ operationId: UUID) -> Double? {
        return activeOperations[operationId]?.progress
    }
    
    // MARK: - Private Helper Methods
    
    private func updateProgress(_ operationId: UUID, progress: Double) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId]?.progress = progress
                continuation.resume()
            }
        }
    }
    
    private func processImport(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration,
        operationId: UUID
    ) async throws -> ImportSuccessInfo {
        // Validate format version
        guard shareableData.formatVersion == "1.0" else {
            throw ImportError.unsupportedVersion(shareableData.formatVersion)
        }

        var importedItemIds: [UUID] = []
        var conflictsResolved: [String] = []
        var skippedItemIds: [UUID] = []

        await updateProgress(operationId, progress: 0.4)

        // Process based on content type
        switch shareableData.shareType {
        case .llmInstance:
            let result = try await importLLMInstance(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
            skippedItemIds.append(contentsOf: result.skipped)

        case .chatSession:
            let result = try await importChatSession(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
            skippedItemIds.append(contentsOf: result.skipped)

        case .messageAction:
            let result = try await importMessageAction(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
            skippedItemIds.append(contentsOf: result.skipped)

        case .chatSessionSetting:
            let result = try await importChatSessionSetting(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
            skippedItemIds.append(contentsOf: result.skipped)
        }

        await updateProgress(operationId, progress: 0.9)

        return ImportSuccessInfo(
            contentType: shareableData.shareType,
            importedItemIds: importedItemIds,
            conflictsResolved: conflictsResolved,
            skippedItemIds: skippedItemIds,
            metadata: ["formatVersion": shareableData.formatVersion]
        )
    }
    
    private func importLLMInstance(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String], skipped: [UUID]) {
        guard let instance = shareableData.data.instance else {
            throw ImportError.missingRequiredFields("LLM Instance")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []
        var skippedIds: [UUID] = []

        // Import provider if included
        if let provider = shareableData.data.provider {
            let existingProvider = try await llmRepository.getProvider(byId: provider.id)

            if let existing = existingProvider {
                if existing == provider {
                    // Identical provider, skip import
                    skippedIds.append(provider.id)
                } else {
                    // Same ID but different content, create new provider with renamed
                    let importProvider = LLMProvider(
                        id: UUID(),
                        name: "\(provider.name) (Imported)",
                        logoImageName: provider.logoImageName,
                        customLogoData: provider.customLogoData,
                        websiteUrl: provider.websiteUrl,
                        apiDocumentationUrl: provider.apiDocumentationUrl,
                        apiBaseUrl: provider.apiBaseUrl,
                        providerType: provider.providerType,
                        apiKeyStored: false, // Never import API keys
                        apiStyle: provider.apiStyle,
                        apiEndpointPath: provider.apiEndpointPath,
                        isUserCreated: provider.isUserCreated,
                        isUserModified: provider.isUserModified,
                        metadata: provider.metadata
                    )
                    try await llmRepository.createProvider(importProvider)
                    importedIds.append(importProvider.id)
                    conflicts.append("Provider '\(provider.name)' renamed due to conflict")
                }
            } else {
                // Provider doesn't exist, import directly
                try await llmRepository.createProvider(provider)
                importedIds.append(provider.id)
            }
        }
        
        // Import model if included
        if let model = shareableData.data.model {
            let existingModel = try await llmRepository.getModel(byId: model.id)

            if let existing = existingModel {
                if existing == model {
                    // Identical model, skip import
                    skippedIds.append(model.id)
                } else {
                    // Same ID but different content, create new model with renamed
                    let importModel = LLMModel(
                        id: UUID(),
                        providerId: model.providerId,
                        modelIdentifier: model.modelIdentifier,
                        name: "\(model.name) (Imported)",
                        modelDescription: model.modelDescription,
                        logoImageName: model.logoImageName,
                        customLogoData: model.customLogoData,
                        contextWindowSize: model.contextWindowSize,
                        inputModalities: model.inputModalities,
                        outputModalities: model.outputModalities,
                        thinkingCapabilities: model.thinkingCapabilities,
                        searchingCapabilities: model.searchingCapabilities,
                        maxOutputTokens: model.maxOutputTokens,
                        pricingInfo: model.pricingInfo,
                        group: model.group,
                        availabilityStatus: model.availabilityStatus,
                        isDefaultRecommendation: model.isDefaultRecommendation,
                        isUserCreated: model.isUserCreated,
                        isUserModified: model.isUserModified,
                        apiConfigsOverride: model.apiConfigsOverride,
                        metadata: model.metadata
                    )
                    try await llmRepository.createModel(importModel)
                    importedIds.append(importModel.id)
                    conflicts.append("Model '\(model.name)' renamed due to conflict")
                }
            } else {
                // Model doesn't exist, import directly
                try await llmRepository.createModel(model)
                importedIds.append(model.id)
            }
        }
        
        // Import instance
        let existingInstance = try await llmRepository.getInstance(byId: instance.id)

        if let existing = existingInstance {
            if existing == instance {
                // Identical instance, skip import
                skippedIds.append(instance.id)
            } else {
                // Same ID but different content, create new instance with renamed
                let importInstance = LLMInstance(
                    id: UUID(),
                    modelId: instance.modelId,
                    name: "\(instance.name) (Imported)",
                    customLogoData: instance.customLogoData,
                    systemPrompt: instance.systemPrompt,
                    defaultParameters: instance.defaultParameters,
                    totalPromptTokensUsed: 0, // Reset usage stats
                    totalCompletionTokensUsed: 0,
                    createdAt: Date(),
                    lastUsedAt: nil,
                    isFavorited: false, // Reset favorite status
                    isUserModified: instance.isUserModified,
                    metadata: instance.metadata
                )
                try await llmRepository.createInstance(importInstance)
                importedIds.append(importInstance.id)
                conflicts.append("Instance '\(instance.name)' renamed due to conflict")
            }
        } else {
            // Instance doesn't exist, import directly
            try await llmRepository.createInstance(instance)
            importedIds.append(instance.id)
        }

        return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
    }
    
    private func importChatSession(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String], skipped: [UUID]) {
        guard let session = shareableData.data.chatSession else {
            throw ImportError.missingRequiredFields("Chat Session")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []
        var skippedIds: [UUID] = []

        // Import related message actions first
        if let messageActions = shareableData.data.messageActions {
            let actionResult = try await importMessageActions(messageActions, configuration: configuration)
            importedIds.append(contentsOf: actionResult.itemIds)
            conflicts.append(contentsOf: actionResult.conflicts)
            skippedIds.append(contentsOf: actionResult.skipped)
        }

        // Import chat session setting if included
        if let setting = shareableData.data.chatSessionSetting {
            let settingResult = try await importChatSessionSettingInternal(setting, configuration: configuration)
            importedIds.append(contentsOf: settingResult.itemIds)
            conflicts.append(contentsOf: settingResult.conflicts)
            skippedIds.append(contentsOf: settingResult.skipped)
        }

        // Import the chat session itself
        // Note: ChatSession doesn't support Equatable, so we always rename if ID exists
        let existingSession = try await chatRepository.getChatSession(byId: session.id)

        if existingSession != nil {
            // Always create new session with renamed title for ChatSession
            let importSession = ChatSession(
                id: UUID(),
                title: "\(session.title ?? "Untitled") (Imported)",
                createdAt: Date(),
                lastModifiedAt: Date(),
                activeMessageId: nil, // Reset active message
                activeLLMInstanceIds: session.activeLLMInstanceIds,
                usedLLMInstanceIds: session.usedLLMInstanceIds,
                activeContextServerIds: session.activeContextServerIds,
                settingsId: session.settingsId,
                userId: session.userId,
                instanceSettings: session.instanceSettings,
                metadata: session.metadata
            )
            try await chatRepository.createChatSession(importSession)
            importedIds.append(importSession.id)
            conflicts.append("Session '\(session.title ?? "Untitled")' renamed due to conflict")
        } else {
            // Session doesn't exist, import directly
            try await chatRepository.createChatSession(session)
            importedIds.append(session.id)
        }

        return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
    }
    
    private func importMessageAction(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String], skipped: [UUID]) {
        guard let action = shareableData.data.messageAction else {
            throw ImportError.missingRequiredFields("Message Action")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []
        var skippedIds: [UUID] = []

        // Skip system actions - they should not be imported
        if case .system = action.actionType {
            return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
        }

        let existingAction = try await chatRepository.getMessageAction(byId: action.id)

        if let existing = existingAction {
            if existing == action {
                // Identical action, skip import
                skippedIds.append(action.id)
            } else {
                // Same ID but different content, create new action with renamed
                let importAction = MessageAction(
                    id: UUID(),
                    name: "\(action.name) (Imported)",
                    icon: action.icon,
                    actionType: action.actionType,
                    prompts: action.prompts,
                    targetLLMInstanceId: action.targetLLMInstanceId,
                    metadata: action.metadata
                )
                try await chatRepository.createMessageAction(importAction)
                importedIds.append(importAction.id)
                conflicts.append("Action '\(action.name)' renamed due to conflict")
            }
        } else {
            // Action doesn't exist, import directly
            try await chatRepository.createMessageAction(action)
            importedIds.append(action.id)
        }

        return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
    }
    
    private func importChatSessionSetting(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String], skipped: [UUID]) {
        guard let setting = shareableData.data.chatSessionSetting else {
            throw ImportError.missingRequiredFields("Chat Session Setting")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []
        var skippedIds: [UUID] = []

        // Import related message actions first
        if let messageActions = shareableData.data.messageActions {
            let actionResult = try await importMessageActions(messageActions, configuration: configuration)
            importedIds.append(contentsOf: actionResult.itemIds)
            conflicts.append(contentsOf: actionResult.conflicts)
            skippedIds.append(contentsOf: actionResult.skipped)
        }

        // Import the chat session setting
        let settingResult = try await importChatSessionSettingInternal(setting, configuration: configuration)
        importedIds.append(contentsOf: settingResult.itemIds)
        conflicts.append(contentsOf: settingResult.conflicts)
        skippedIds.append(contentsOf: settingResult.skipped)

        return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
    }

    // MARK: - Helper Methods for MessageAction Import

    private func importMessageActions(
        _ messageActions: [MessageAction],
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String], skipped: [UUID]) {
        var importedIds: [UUID] = []
        var conflicts: [String] = []
        var skippedIds: [UUID] = []

        for action in messageActions {
            // Skip system actions - they should not be imported
            if case .system = action.actionType {
                continue
            }

            let existingAction = try await chatRepository.getMessageAction(byId: action.id)

            if let existing = existingAction {
                if existing == action {
                    // Identical action, skip import
                    skippedIds.append(action.id)
                } else {
                    // Same ID but different content, create new action with renamed
                    let importAction = MessageAction(
                        id: UUID(),
                        name: "\(action.name) (Imported)",
                        icon: action.icon,
                        actionType: action.actionType,
                        prompts: action.prompts,
                        targetLLMInstanceId: action.targetLLMInstanceId,
                        metadata: action.metadata
                    )
                    try await chatRepository.createMessageAction(importAction)
                    importedIds.append(importAction.id)
                    conflicts.append("Action '\(action.name)' renamed due to conflict")
                }
            } else {
                // Action doesn't exist, import directly
                try await chatRepository.createMessageAction(action)
                importedIds.append(action.id)
            }
        }

        return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
    }

    private func importChatSessionSettingInternal(
        _ setting: ChatSessionSetting,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String], skipped: [UUID]) {
        var importedIds: [UUID] = []
        var conflicts: [String] = []
        var skippedIds: [UUID] = []

        let existingSetting = try await chatRepository.getSetting(byId: setting.id)

        if let existing = existingSetting {
            if existing == setting {
                // Identical setting, skip import
                skippedIds.append(setting.id)
            } else {
                // Same ID but different content, create new setting with renamed
                let importSetting = ChatSessionSetting(
                    id: UUID(),
                    name: "\(setting.name) (Imported)",
                    isSystemDefault: false, // Never import as system default
                    createdAt: Date(),
                    lastModifiedAt: Date(),
                    llmParameterOverrides: setting.llmParameterOverrides,
                    defaultContextServerIds: setting.defaultContextServerIds,
                    shouldExpandThinking: setting.shouldExpandThinking,
                    uiThemeSettings: setting.uiThemeSettings,
                    messageActionSettings: setting.messageActionSettings,
                    savedPromptSegments: setting.savedPromptSegments,
                    auxiliaryLLMInstanceId: setting.auxiliaryLLMInstanceId,
                    shouldAutoGenerateTitle: setting.shouldAutoGenerateTitle,
                    contextMessageCount: setting.contextMessageCount,
                    metadata: setting.metadata
                )
                try await chatRepository.createSetting(importSetting)
                importedIds.append(importSetting.id)
                conflicts.append("Setting '\(setting.name)' renamed due to conflict")
            }
        } else {
            // Setting doesn't exist, import directly
            try await chatRepository.createSetting(setting)
            importedIds.append(setting.id)
        }

        return (itemIds: importedIds, conflicts: conflicts, skipped: skippedIds)
    }
}

// MARK: - Supporting Types

private final class ImportOperation: @unchecked Sendable {
    let id: UUID
    let source: ImportSource
    let configuration: ImportConfiguration
    var progress: Double = 0.0
    var isCancelled: Bool = false
    
    init(id: UUID, source: ImportSource, configuration: ImportConfiguration) {
        self.id = id
        self.source = source
        self.configuration = configuration
    }
}

private enum ImportSource {
    case file(URL)
    case icloud(URL)
    case qrCode(String)
}
