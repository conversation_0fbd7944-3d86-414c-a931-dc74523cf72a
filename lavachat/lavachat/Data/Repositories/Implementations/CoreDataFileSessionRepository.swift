import Foundation
import CoreData

/// Core Data implementation of the FileSessionRepository
class CoreDataFileSessionRepository: FileSessionRepository {
    private let context: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - FileSession Operations
    
    func createFileSession(_ session: FileSession) async throws {
        try await context.perform {
            let cdFileSession = FileSessionMapper.toCoreData(fileSession: session, context: self.context)
            try self.context.save()
        }
    }
    
    func getFileSession(byId id: UUID) async throws -> FileSession? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileSession> = CDFileSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { FileSessionMapper.toDomain(cdFileSession: $0) }
        }
    }
    
    func getAllFileSessions(for userId: UUID) async throws -> [FileSession] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileSession> = CDFileSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "userId == %@", userId as CVarArg)
            fetchRequest.sortDescriptors = [NSSortDescriptor(key: "lastModifiedAt", ascending: false)]
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { FileSessionMapper.toDomain(cdFileSession: $0) }
        }
    }
    
    func updateFileSession(_ session: FileSession) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileSession> = CDFileSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", session.id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdFileSession = results.first {
                FileSessionMapper.updateCoreData(cdFileSession: cdFileSession, with: session)
                
                // If currentFileCheckpointId has changed, update the FileSession's reference
                if let currentCheckpointId = session.currentFileCheckpointId {
                    let checkpointFetchRequest: NSFetchRequest<CDFileCheckpoint> = CDFileCheckpoint.fetchRequest()
                    checkpointFetchRequest.predicate = NSPredicate(format: "id == %@", currentCheckpointId as CVarArg)
                    checkpointFetchRequest.fetchLimit = 1
                    
                    let checkpointResults = try self.context.fetch(checkpointFetchRequest)
                    if let currentCheckpoint = checkpointResults.first {
                        cdFileSession.currentFileCheckpointId = currentCheckpoint.id
                    }
                }
                
                try self.context.save()
            } else {
                throw NSError(domain: "FileSessionRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "File session not found."])
            }
        }
    }
    
    func deleteFileSession(byId id: UUID) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileSession> = CDFileSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdFileSession = results.first {
                self.context.delete(cdFileSession)
                try self.context.save()
            }
        }
    }
    
    // MARK: - FileCheckpoint Operations
    
    func createCheckpoint(_ checkpoint: FileCheckpoint) async throws {
        try await context.perform {
            let cdCheckpoint = FileCheckpointMapper.toCoreData(checkpoint: checkpoint, context: self.context)
            
            // Update file session's currentFileCheckpointId if needed
            let sessionFetchRequest: NSFetchRequest<CDFileSession> = CDFileSession.fetchRequest()
            sessionFetchRequest.predicate = NSPredicate(format: "id == %@", checkpoint.fileSessionId as CVarArg)
            sessionFetchRequest.fetchLimit = 1
            
            let sessionResults = try self.context.fetch(sessionFetchRequest)
            if let cdFileSession = sessionResults.first {
                cdFileSession.currentFileCheckpointId = checkpoint.id
                cdFileSession.lastModifiedAt = Date()
            }
            
            try self.context.save()
        }
    }
    
    func getCheckpoint(byId id: UUID) async throws -> FileCheckpoint? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileCheckpoint> = CDFileCheckpoint.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { FileCheckpointMapper.toDomain(cdCheckpoint: $0) }
        }
    }
    
    func getCheckpoints(for fileSessionId: UUID, sortedBy dateSort: SortOrder?) async throws -> [FileCheckpoint] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileCheckpoint> = CDFileCheckpoint.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "fileSessionId == %@", fileSessionId as CVarArg)
            
            // Set sort order
            let ascending = dateSort == .forward
            fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: ascending)]
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { FileCheckpointMapper.toDomain(cdCheckpoint: $0) }
        }
    }
    
    func getFileContent(for checkpointId: UUID) async throws -> String? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileCheckpoint> = CDFileCheckpoint.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", checkpointId as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first?.fileContent
        }
    }
    
    func getPendingEdits(for checkpointId: UUID) async throws -> [PendingEditInfo]? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileCheckpoint> = CDFileCheckpoint.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", checkpointId as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdCheckpoint = results.first {
                return EntityMapper.decode(cdCheckpoint.pendingEditStates)
            }
            return nil
        }
    }
    
    func updateCheckpointFileState(checkpointId: UUID, newContent: String, newPendingEdits: [PendingEditInfo]?) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDFileCheckpoint> = CDFileCheckpoint.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", checkpointId as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdCheckpoint = results.first {
                cdCheckpoint.fileContent = newContent
                cdCheckpoint.pendingEditStates = EntityMapper.encode(newPendingEdits)
                try self.context.save()
                
                // Update the session's lastModifiedAt
                if let session = cdCheckpoint.fileSession {
                    session.lastModifiedAt = Date()
                    try self.context.save()
                }
            } else {
                throw NSError(domain: "FileSessionRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "Checkpoint not found."])
            }
        }
    }
} 
