import Foundation

/// Implementation of the KeychainRepository interface
class KeychainRepositoryImpl: KeychainRepository {
    
    private let service = "com.lavachat.apikeys"
    
    func saveApiKey(_ apiKey: String, for providerId: UUID) async throws {
        try KeychainHelper.saveString(apiKey, for: providerId.uuidString, withService: service)
    }
    
    func getApiKey(for providerId: UUID) async throws -> String? {
        return try KeychainHelper.getString(for: providerId.uuidString, withService: service)
    }
    
    func updateApiKey(_ apiKey: String, for providerId: UUID) async throws {
        try KeychainHelper.updateString(apiKey, for: providerId.uuidString, withService: service)
    }
    
    func deleteApiKey(for providerId: UUID) async throws {
        try KeychainHelper.deleteString(for: providerId.uuidString, withService: service)
    }
    
    func checkApiKeyExists(for providerId: UUID) async throws -> Bool {
        return try KeychainHelper.stringExists(for: providerId.uuidString, withService: service)
    }
}