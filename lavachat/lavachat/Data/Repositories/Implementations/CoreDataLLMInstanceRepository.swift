import Foundation
import CoreData
import Combine

/// Core Data implementation of the LLMInstanceRepository
class CoreDataLLMInstanceRepository: LLMInstanceRepository {
    private let context: NSManagedObjectContext
    private var isBatchUpdating: Bool = false
    
    // Internal subject to control the exposed publisher
    private let internalDataDidChangeSubject = PassthroughSubject<Void, Never>()
    lazy var dataDidChangePublisher: AnyPublisher<Void, Never> = {
        internalDataDidChangeSubject.eraseToAnyPublisher()
    }()
    
    private var cancellables = Set<AnyCancellable>()

    init(context: NSManagedObjectContext) {
        self.context = context
        subscribeToContextChanges()
    }

    private func subscribeToContextChanges() {
        NotificationCenter.default.publisher(for: NSNotification.Name.NSManagedObjectContextDidSave, object: nil) // Listen to all contexts
            .filter { [weak self] notification -> Bool in
                guard let self = self,
                      let savedContext = notification.object as? NSManagedObjectContext else {
                    return false
                }
                // Ensure the notification is from a relevant context
                return savedContext == self.context || savedContext.persistentStoreCoordinator == self.context.persistentStoreCoordinator
            }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                guard let self = self else { return }

                if self.isBatchUpdating { return }

                var relevantChangeOccurred = false
                let relevantKeys = [NSInsertedObjectsKey, NSUpdatedObjectsKey, NSDeletedObjectsKey]

                for key in relevantKeys {
                    if let changedObjects = notification.userInfo?[key] as? Set<NSManagedObject> {
                        if changedObjects.contains(where: { 
                            $0 is CDLLMProvider || 
                            $0 is CDLLMModel || 
                            $0 is CDLLMInstance || 
                            $0 is CDLLMInstanceGroup 
                        }) {
                            relevantChangeOccurred = true
                            break
                        }
                    }
                }

                if relevantChangeOccurred {
                    self.internalDataDidChangeSubject.send()
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Batch Operations Control
    
    func beginBatchUpdate() async {
        await context.perform {
            self.isBatchUpdating = true
        }
    }
    
    func endBatchUpdateAndPublish() async throws {
        await context.perform {
            self.isBatchUpdating = false
            self.internalDataDidChangeSubject.send()
        }
    }

    // MARK: - LLMProvider Operations
    
    func createProvider(_ provider: LLMProvider) async throws {
        try await context.perform {
            do {
                let _ = LLMProviderMapper.toCoreData(provider: provider, context: self.context)
                try self.context.save()
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getProvider(byId id: UUID) async throws -> LLMProvider? {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                return results.first.map { LLMProviderMapper.toDomain(cdProvider: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getAllProviders() async throws -> [LLMProvider] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                fetchRequest.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
                
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMProviderMapper.toDomain(cdProvider: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getProviders(isUserCreated: Bool) async throws -> [LLMProvider] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "isUserCreated == %@", NSNumber(value: isUserCreated))
                fetchRequest.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
                
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMProviderMapper.toDomain(cdProvider: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func updateProvider(_ provider: LLMProvider) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", provider.id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdProvider = results.first {
                    LLMProviderMapper.updateCoreData(cdProvider: cdProvider, with: provider)
                    try self.context.save()
                } else {
                    throw ModelManagementError.providerNotFound
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func deleteProvider(byId id: UUID) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdProvider = results.first {
                    // Check if this is a built-in provider
                    if !cdProvider.isUserCreated {
                        throw ModelManagementError.cannotDeleteBuiltInProvider
                    }
                    
                    self.context.delete(cdProvider)
                    try self.context.save()
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    // MARK: - LLMModel Operations
    
    func createModel(_ model: LLMModel) async throws {
        try await context.perform {
            do {
                let cdModel = LLMModelMapper.toCoreData(model: model, context: self.context)
                
                // Link to provider
                let providerFetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                providerFetchRequest.predicate = NSPredicate(format: "id == %@", model.providerId as CVarArg)
                providerFetchRequest.fetchLimit = 1
                
                let providerResults = try self.context.fetch(providerFetchRequest)
                if let cdProvider = providerResults.first {
                    cdModel.provider = cdProvider
                }
                
                try self.context.save()
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getModel(byId id: UUID) async throws -> LLMModel? {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                return results.first.map { LLMModelMapper.toDomain(cdModel: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }

    func getAllModels() async throws -> [LLMModel] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                fetchRequest.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
                
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMModelMapper.toDomain(cdModel: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getAllModels(for providerId: UUID) async throws -> [LLMModel] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "providerId == %@", providerId as CVarArg)
                fetchRequest.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
                
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMModelMapper.toDomain(cdModel: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func findModel(identifier: String, providerId: UUID) async throws -> LLMModel? {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "modelIdentifier == %@ AND providerId == %@", identifier, providerId as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                return results.first.map { LLMModelMapper.toDomain(cdModel: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func updateModel(_ model: LLMModel) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", model.id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdModel = results.first {
                    let oldProviderId = cdModel.providerId
                    
                    // Update the model data
                    LLMModelMapper.updateCoreData(cdModel: cdModel, with: model)
                    
                    // Update the provider relationship if providerId has changed
                    if oldProviderId != model.providerId {
                        let providerFetchRequest: NSFetchRequest<CDLLMProvider> = CDLLMProvider.fetchRequest()
                        providerFetchRequest.predicate = NSPredicate(format: "id == %@", model.providerId as CVarArg)
                        providerFetchRequest.fetchLimit = 1
                        
                        let providerResults = try self.context.fetch(providerFetchRequest)
                        if let cdProvider = providerResults.first {
                            cdModel.provider = cdProvider
                        } else {
                            throw ModelManagementError.providerNotFound
                        }
                    }
                    
                    try self.context.save()
                } else {
                    throw ModelManagementError.modelNotFound
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func deleteModel(byId id: UUID) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdModel = results.first {
                    // Check if this model is used by any instances
                    let instanceFetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                    instanceFetchRequest.predicate = NSPredicate(format: "modelId == %@", id as CVarArg)
                    instanceFetchRequest.fetchLimit = 1
                    
                    let instanceResults = try self.context.fetch(instanceFetchRequest)
                    if !instanceResults.isEmpty {
                        throw ModelManagementError.cannotDeleteModelUsedByInstances
                    }
                    
                    self.context.delete(cdModel)
                    try self.context.save()
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    // MARK: - LLMInstance Operations
    
    func createInstance(_ instance: LLMInstance) async throws {
        try await context.perform {
            do {
                let cdInstance = LLMInstanceMapper.toCoreData(instance: instance, context: self.context)
                
                // Link to model
                let modelFetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                modelFetchRequest.predicate = NSPredicate(format: "id == %@", instance.modelId as CVarArg)
                modelFetchRequest.fetchLimit = 1
                
                let modelResults = try self.context.fetch(modelFetchRequest)
                if let cdModel = modelResults.first {
                    cdInstance.model = cdModel
                }
                
                try self.context.save()
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getInstance(byId id: UUID) async throws -> LLMInstance? {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                return results.first.map { LLMInstanceMapper.toDomain(cdInstance: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getAllInstances() async throws -> [LLMInstance] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.sortDescriptors = [
                    NSSortDescriptor(key: "isFavorited", ascending: false),
                    NSSortDescriptor(key: "lastUsedAt", ascending: false)
                ]
                
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMInstanceMapper.toDomain(cdInstance: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }

    func getAllInstances(for modelId: UUID) async throws -> [LLMInstance] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "modelId == %@", modelId as CVarArg)
                 fetchRequest.sortDescriptors = [
                    NSSortDescriptor(key: "isFavorited", ascending: false),
                    NSSortDescriptor(key: "lastUsedAt", ascending: false)
                ]
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMInstanceMapper.toDomain(cdInstance: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getInstancesWithRelatedEntities(instanceIds: [UUID]) async throws -> [LLMInstanceContext] {
        try await context.perform {
            do {
                guard !instanceIds.isEmpty else {
                    return []
                }
                
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id IN %@", instanceIds)
                
                // Prefetch related entities to avoid N+1 queries
                fetchRequest.relationshipKeyPathsForPrefetching = ["model", "model.provider"]
                
                let results = try self.context.fetch(fetchRequest)
                
                var instanceContexts: [LLMInstanceContext] = []
                
                for cdInstance in results {
                    let instance = LLMInstanceMapper.toDomain(cdInstance: cdInstance)
                    
                    // Get the related model
                    guard let cdModel = cdInstance.model else {
                        print("❌ [CoreDataLLMInstanceRepository] Warning: Instance \(instance.id) has no associated model relationship")
                        continue
                    }
                    let model = LLMModelMapper.toDomain(cdModel: cdModel)
                    
                    // Get the related provider
                    guard let cdProvider = cdModel.provider else {
                        print("❌ [CoreDataLLMInstanceRepository] Warning: Model \(model.id) has no associated provider")
                        continue
                    }
                    let provider = LLMProviderMapper.toDomain(cdProvider: cdProvider)
                    
                    let instanceContext = LLMInstanceContext(
                        instance: instance,
                        model: model,
                        provider: provider
                    )
                    instanceContexts.append(instanceContext)
                }
                
                return instanceContexts
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func updateInstance(_ instance: LLMInstance) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", instance.id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdInstance = results.first {
                    let oldModelId = cdInstance.modelId
                    
                    // Update the instance data
                    LLMInstanceMapper.updateCoreData(cdInstance: cdInstance, with: instance)
                    
                    // Update the model relationship if modelId has changed
                    if oldModelId != instance.modelId {
                        let modelFetchRequest: NSFetchRequest<CDLLMModel> = CDLLMModel.fetchRequest()
                        modelFetchRequest.predicate = NSPredicate(format: "id == %@", instance.modelId as CVarArg)
                        modelFetchRequest.fetchLimit = 1
                        
                        let modelResults = try self.context.fetch(modelFetchRequest)
                        if let cdModel = modelResults.first {
                            cdInstance.model = cdModel
                        } else {
                            throw ModelManagementError.modelNotFound
                        }
                    }
                    
                    try self.context.save()
                } else {
                    throw ModelManagementError.instanceNotFound
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func deleteInstance(byId id: UUID) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdInstance = results.first {
                    self.context.delete(cdInstance)
                    try self.context.save()
                }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func updateInstanceTokenUsage(instanceId: UUID, promptTokens: Int, completionTokens: Int) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", instanceId as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdInstance = results.first {
                    cdInstance.totalPromptTokensUsed += Int64(promptTokens)
                    cdInstance.totalCompletionTokensUsed += Int64(completionTokens)
                    cdInstance.lastUsedAt = Date()
                    try self.context.save()
                } else {
                    throw ModelManagementError.instanceNotFound
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    // MARK: - LLMInstanceGroup Operations
    
    func createGroup(_ group: LLMInstanceGroup) async throws {
        try await context.perform {
            do {
                let cdGroup = LLMInstanceGroupMapper.toCoreData(group: group, context: self.context)
                
                // Add instances to group
                for instanceId in group.instanceIds {
                    let instanceFetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                    instanceFetchRequest.predicate = NSPredicate(format: "id == %@", instanceId as CVarArg)
                    instanceFetchRequest.fetchLimit = 1
                    
                    let instanceResults = try self.context.fetch(instanceFetchRequest)
                    if let cdInstance = instanceResults.first {
                        cdGroup.addToInstances(cdInstance)
                    }
                }
                
                try self.context.save()
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getGroup(byId id: UUID) async throws -> LLMInstanceGroup? {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstanceGroup> = CDLLMInstanceGroup.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                return results.first.map { LLMInstanceGroupMapper.toDomain(cdGroup: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func getAllGroups() async throws -> [LLMInstanceGroup] {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstanceGroup> = CDLLMInstanceGroup.fetchRequest()
                fetchRequest.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
                
                let results = try self.context.fetch(fetchRequest)
                return results.map { LLMInstanceGroupMapper.toDomain(cdGroup: $0) }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func updateGroup(_ group: LLMInstanceGroup) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstanceGroup> = CDLLMInstanceGroup.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", group.id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdGroup = results.first {
                    LLMInstanceGroupMapper.updateCoreData(cdGroup: cdGroup, with: group)
                    
                    // Update instances relationship
                    cdGroup.removeFromInstances(cdGroup.instances ?? NSSet())
                    
                    for instanceId in group.instanceIds {
                        let instanceFetchRequest: NSFetchRequest<CDLLMInstance> = CDLLMInstance.fetchRequest()
                        instanceFetchRequest.predicate = NSPredicate(format: "id == %@", instanceId as CVarArg)
                        instanceFetchRequest.fetchLimit = 1
                        
                        let instanceResults = try self.context.fetch(instanceFetchRequest)
                        if let cdInstance = instanceResults.first {
                            cdGroup.addToInstances(cdInstance)
                        }
                    }
                    
                    try self.context.save()
                } else {
                    throw ModelManagementError.groupNotFound
                }
            } catch let error as ModelManagementError {
                throw error
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
    
    func deleteGroup(byId id: UUID) async throws {
        try await context.perform {
            do {
                let fetchRequest: NSFetchRequest<CDLLMInstanceGroup> = CDLLMInstanceGroup.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
                fetchRequest.fetchLimit = 1
                
                let results = try self.context.fetch(fetchRequest)
                if let cdGroup = results.first {
                    self.context.delete(cdGroup)
                    try self.context.save()
                }
            } catch {
                throw ModelManagementError.persistenceError(error)
            }
        }
    }
} 