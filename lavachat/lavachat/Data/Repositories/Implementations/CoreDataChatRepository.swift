import Foundation
import CoreData
import Combine

/// Core Data implementation of the ChatRepository
class CoreDataChatRepository: ChatRepository {
    private let context: NSManagedObjectContext
    private var isBatchUpdating: Bool = false

    // Internal subject to control the exposed publisher
    private let internalDataDidChangeSubject = PassthroughSubject<Void, Never>()
    lazy var dataDidChangePublisher: AnyPublisher<Void, Never> = {
        internalDataDidChangeSubject.eraseToAnyPublisher()
    }()
    
    // ChatView specific change publisher
    private let chatViewRelevantChangeSubject = PassthroughSubject<(UUID, ChatViewRelevantChange), Never>()

    // ChatsView specific change publisher
    private let chatsViewRelevantChangeSubject = PassthroughSubject<ChatsViewRelevantChange, Never>()
    
    // Batch operation state tracking for ChatsView
    private var batchSessionCreated = false
    private var batchSessionDeleted = false

    private var cancellables = Set<AnyCancellable>()
    
    // A simple struct to hold change information safely across threads.
    private struct ObjectChange {
        let objectID: NSManagedObjectID
        let entityName: String
        let changeType: String // e.g., NSUpdatedObjectsKey
        let changedValues: [String: Any]
        // Pre-capture the session's UUID for reliable handling of deletions.
        let sessionUUID: UUID?
    }

    init(context: NSManagedObjectContext) {
        self.context = context
        subscribeToContextChanges()
    }

    private func subscribeToContextChanges() {
        NotificationCenter.default.publisher(for: .NSManagedObjectContextObjectsDidChange, object: context)
            .compactMap { [weak self] notification -> [ObjectChange]? in
                // Using weak self to avoid retain cycle if any logic here needs self
                guard let self = self, let userInfo = notification.userInfo else { return nil }                
                var changes: [ObjectChange] = []
                let keys = [NSInsertedObjectsKey, NSUpdatedObjectsKey, NSDeletedObjectsKey]

                for key in keys {
                    if let objects = userInfo[key] as? Set<NSManagedObject> {
                        for object in objects {
                            guard self.isRelevantObject(object) else { continue }
                            
                            var sessionUUID: UUID? = nil
                            if let session = object as? CDChatSession {
                                sessionUUID = session.id
                            }
                            
                            let change = ObjectChange(
                                objectID: object.objectID,
                                entityName: object.entity.name ?? "Unknown",
                                changeType: key,
                                changedValues: object.changedValues(),
                                sessionUUID: sessionUUID
                            )
                            changes.append(change)
                        }
                    }
                }
                
                return changes.isEmpty ? nil : changes
            }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] changes in
                guard let self = self else { return }

                var relevantChangeOccurred = false
                var chatsViewSessionCreated = false
                var chatsViewSessionDeleted = false
                
                for change in changes {
                    // **Handle deletions first, using the pre-captured UUID.**
                    if change.changeType == NSDeletedObjectsKey {
                        relevantChangeOccurred = true
                        self.handleChatViewRelevantChanges(for: nil, with: change)
                        
                        // Check for ChatsView relevant changes
                        if change.entityName == "CDChatSession" {
                            chatsViewSessionDeleted = true
                        }
                        continue // Skip to the next change
                    }
                    
                    // **For updates and insertions, get the object from the context.**
                    // The compiler was right, this doesn't return an Optional.
                    let object = self.context.object(with: change.objectID)
                    
                    // Check if the object is a deletion fault, just in case.
                    if object.isDeleted {
                        continue
                    }

                    relevantChangeOccurred = true
                    self.handleChatViewRelevantChanges(for: object, with: change)
                    
                    // Check for ChatsView relevant changes
                    if change.entityName == "CDChatSession" && change.changeType == NSInsertedObjectsKey {
                        chatsViewSessionCreated = true
                    }
                }

                // Handle ChatsView notifications
                if self.isBatchUpdating {
                    // During batch operation, set flags instead of sending notifications
                    if chatsViewSessionCreated {
                        self.batchSessionCreated = true
                    }
                    if chatsViewSessionDeleted {
                        self.batchSessionDeleted = true
                    }
                } else {
                    // Send ChatsView notifications immediately
                    if chatsViewSessionCreated {
                        self.chatsViewRelevantChangeSubject.send(.sessionCreated)
                    }
                    if chatsViewSessionDeleted {
                        self.chatsViewRelevantChangeSubject.send(.sessionDeleted)
                    }
                }

                if relevantChangeOccurred && !self.isBatchUpdating {
                    self.internalDataDidChangeSubject.send()
                }
            }
            .store(in: &cancellables)
    }

    private func isRelevantObject(_ object: NSManagedObject) -> Bool {
        return object is CDChatSession || object is CDMessage || object is CDChatSessionSetting || object is CDMessageAction
    }
    
    // The handler is now robust for deleted objects.
    // The `object` parameter is now Optional.
    private func handleChatViewRelevantChanges(for object: NSManagedObject?, with change: ObjectChange) {
        let key = change.changeType
        let changedValues = change.changedValues
        // Handle Chat Session changes
        if change.entityName == "CDChatSession" {
            if key == NSDeletedObjectsKey {
                // Safely use the pre-captured UUID.
                if let sessionId = change.sessionUUID {
                    self.chatViewRelevantChangeSubject.send((sessionId, .sessionDeleted))
                }
            } else if key == NSUpdatedObjectsKey, let chatSession = object as? CDChatSession {
                if let sessionId = chatSession.id {
                    if changedValues.keys.contains("title") {
                        self.chatViewRelevantChangeSubject.send((sessionId, .sessionTitleChanged(chatSession.title)))
                    }
                    if changedValues.keys.contains("settingsId") {
                        self.chatViewRelevantChangeSubject.send((sessionId, .sessionSettingsChanged(chatSession.settingsId)))
                    }
                }
            }
            // Add handling for insertions if needed
        }
        
        // Handle Chat Setting changes
        else if change.entityName == "CDChatSessionSetting" {
             if key == NSUpdatedObjectsKey, let setting = object as? CDChatSessionSetting {
                if let settingId = setting.id {
                    self.notifySessionsUsingSetting(settingId: settingId, changedValues: changedValues, setting: setting)
                }
            }
        }
        
        // Handle Message Action changes
        else if change.entityName == "CDMessageAction" {
            if key == NSUpdatedObjectsKey, let action = object as? CDMessageAction {
                self.notifySessionsUsingAction(action: action)
            }
        }
    }
    
    // Method to notify sessions that use a specific action
    private func notifySessionsUsingAction(action: CDMessageAction) {
        let fetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
        
        do {
            let allSettings = try self.context.fetch(fetchRequest)
            let affectedSettingIDs = allSettings.compactMap { setting -> UUID? in
                guard let messageActionSettingsData = setting.messageActionSettings,
                      let messageActionSettings = try? JSONDecoder().decode(MessageActionSettings.self, from: messageActionSettingsData) else {
                    return nil
                }
                
                let allActionIDs = messageActionSettings.uniqueActionIds

                if let actionID = action.id {
                    if allActionIDs.contains(actionID) {
                        return setting.id
                    }
                }
                
                return nil
            }
            
            if !affectedSettingIDs.isEmpty {
                let sessionFetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
                sessionFetchRequest.predicate = NSPredicate(format: "settingsId IN %@", affectedSettingIDs)
                
                let affectedSessions = try self.context.fetch(sessionFetchRequest)
                let domainAction = MessageActionMapper.toDomain(cdAction: action)
                
                for session in affectedSessions {
                    if let sessionId = session.id {
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingMessageActionContentChanged(domainAction)))
                    }
                }
            }
        } catch {
            print("Error fetching settings for action change notification: \(error)")
        }
    }
    
    // Method to notify sessions that use a specific setting
    private func notifySessionsUsingSetting(settingId: UUID, changedValues: [String: Any], setting: CDChatSessionSetting) {
        let fetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "settingsId == %@", settingId as CVarArg)
        
        do {
            let affectedSessions = try self.context.fetch(fetchRequest)
            for session in affectedSessions {
                if let sessionId = session.id {
                    if changedValues.keys.contains("uiThemeSettings") {
                        // Parse the data back to UIThemeSettings if needed
                        let themeSettings = try? setting.uiThemeSettings.flatMap { data in
                            try JSONDecoder().decode(UIThemeSettings.self, from: data)
                        }
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingUIThemeChanged(themeSettings)))
                    }
                    if changedValues.keys.contains("messageActionSettings") {
                        // Parse the data back to MessageActionSettings if needed
                        let actionSettings = try? setting.messageActionSettings.flatMap { data in
                            try JSONDecoder().decode(MessageActionSettings.self, from: data)
                        }
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingMessageActionsChanged(actionSettings)))
                    }
                    if changedValues.keys.contains("savedPromptSegments") {
                        // Parse the data back to SavedPromptSegment array if needed
                        let promptSegments = try? setting.savedPromptSegments.flatMap { data in
                            try JSONDecoder().decode([SavedPromptSegment].self, from: data)
                        }
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingPromptSegmentsChanged(promptSegments)))
                    }
                    if changedValues.keys.contains("shouldExpandThinking") {
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingShouldExpandThinkingChanged(setting.shouldExpandThinking)))
                    }
                    if changedValues.keys.contains("auxiliaryLLMInstanceId") {
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingAuxiliaryLLMInstanceIdChanged(setting.auxiliaryLLMInstanceId)))
                    }
                    if changedValues.keys.contains("shouldAutoGenerateTitle") {
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingShouldAutoGenerateTitleChanged(setting.shouldAutoGenerateTitle)))
                    }
                    if changedValues.keys.contains("contextMessageCount") {
                        self.chatViewRelevantChangeSubject.send((sessionId, .settingContextMessageCountChanged(setting.contextMessageCount)))
                    }
                }
            }
        } catch {
            print("Error fetching sessions for setting change notification: \(error)")
        }
    }

    // MARK: - Batch Operations Control
    
    func beginBatchUpdate() async {
        await context.perform {
            self.isBatchUpdating = true
            self.batchSessionCreated = false
            self.batchSessionDeleted = false
        }
    }
    
    func endBatchUpdateAndPublish() async throws {
        try await context.perform { // Ensuring context operations are on the correct queue
            self.isBatchUpdating = false
            
            // Send accumulated batch notifications for ChatsView
            if self.batchSessionCreated {
                DispatchQueue.main.async {
                    self.chatsViewRelevantChangeSubject.send(.sessionCreated)
                }
            }
            if self.batchSessionDeleted {
                DispatchQueue.main.async {
                    self.chatsViewRelevantChangeSubject.send(.sessionDeleted)
                }
            }
            
            // Reset batch flags
            self.batchSessionCreated = false
            self.batchSessionDeleted = false
            
            self.internalDataDidChangeSubject.send()
        }
    }
    
    // MARK: - ChatSession Operations
    
    func createChatSession(_ session: ChatSession) async throws {
        try await context.perform {
            let cdChatSession = ChatSessionMapper.toCoreData(chatSession: session, context: self.context)
            try self.context.save()
        }
    }
    
    func getChatSession(byId id: UUID) async throws -> ChatSession? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { ChatSessionMapper.toDomain(cdChatSession: $0) }
        }
    }
    
    func getAllChatSessions(for userId: UUID, sortBy sortDescriptors: [NSSortDescriptor]?) async throws -> [ChatSession] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "userId == %@", userId as CVarArg)
            
            // Apply sorting if provided, otherwise default to lastModifiedAt descending
            if let descriptors = sortDescriptors, !descriptors.isEmpty {
                fetchRequest.sortDescriptors = descriptors
            } else {
                // Default sort by lastModifiedAt descending
                fetchRequest.sortDescriptors = [NSSortDescriptor(key: "lastModifiedAt", ascending: false)]
            }
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { ChatSessionMapper.toDomain(cdChatSession: $0) }
        }
    }
    
    func updateChatSession(_ session: ChatSession) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", session.id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdChatSession = results.first {
                ChatSessionMapper.updateCoreData(cdChatSession: cdChatSession, with: session)
                try self.context.save()
            } else {
                throw NSError(domain: "ChatRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "Chat session not found."])
            }
        }
    }
    
    func deleteChatSession(byId id: UUID) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdChatSession = results.first {
                self.context.delete(cdChatSession)
                try self.context.save()
            }
        }
    }
    
    // MARK: - Message Operations
    
    func createMessage(_ message: Message) async throws {
        try await context.perform {
            let cdMessage = MessageMapper.toCoreData(message: message, context: self.context)
            
            // If this message has a parent, set up the relationship
            if let parentId = message.parentId {
                let parentFetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
                parentFetchRequest.predicate = NSPredicate(format: "id == %@", parentId as CVarArg)
                parentFetchRequest.fetchLimit = 1
                
                let parentResults = try self.context.fetch(parentFetchRequest)
                if let parentMessage = parentResults.first {
                    cdMessage.parent = parentMessage
                }
            }
            
            // Connect this message to its session
            let sessionFetchRequest: NSFetchRequest<CDChatSession> = CDChatSession.fetchRequest()
            sessionFetchRequest.predicate = NSPredicate(format: "id == %@", message.sessionId as CVarArg)
            sessionFetchRequest.fetchLimit = 1
            
            let sessionResults = try self.context.fetch(sessionFetchRequest)
            if let cdSession = sessionResults.first {
                cdMessage.session = cdSession
                
                // Update session's lastModifiedAt
                cdSession.lastModifiedAt = Date()
            }
            
            try self.context.save()
        }
    }
    
    func getMessage(byId id: UUID) async throws -> Message? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { MessageMapper.toDomain(cdMessage: $0) }
        }
    }
    
    func getMessages(for sessionId: UUID) async throws -> [Message] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "sessionId == %@", sessionId as CVarArg)
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageMapper.toDomain(cdMessage: $0) }
        }
    }
    
    func getMessages(for messageIds: [UUID]) async throws -> [Message] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id IN %@", messageIds as CVarArg)
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageMapper.toDomain(cdMessage: $0) }
        }
    }

    func updateMessage(_ message: Message) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", message.id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdMessage = results.first {
                MessageMapper.updateCoreData(cdMessage: cdMessage, with: message)
                try self.context.save()
            } else {
                throw NSError(domain: "ChatRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "Message not found."])
            }
        }
    }
    
    func deleteMessage(byId id: UUID) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            if let cdMessage = results.first {
                self.context.delete(cdMessage)
                try self.context.save()
            }
        }
    }
    
    func getMessages(parentId: UUID?) async throws -> [Message] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            
            if let parentId = parentId {
                fetchRequest.predicate = NSPredicate(format: "parentId == %@", parentId as CVarArg)
            } else {
                fetchRequest.predicate = NSPredicate(format: "parentId == NULL")
            }
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageMapper.toDomain(cdMessage: $0) }
        }
    }
    
    func getRootMessages(for sessionId: UUID) async throws -> [Message] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "sessionId == %@ AND parentId == NULL", sessionId as CVarArg)
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageMapper.toDomain(cdMessage: $0) }
        }
    }
    
    func getChildMessages(for parentMessageId: UUID) async throws -> [Message] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessage> = CDMessage.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "parentId == %@", parentMessageId as CVarArg)
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageMapper.toDomain(cdMessage: $0) }
        }
    }
    
    // MARK: - ChatSessionSetting Operations
    
    func createSetting(_ setting: ChatSessionSetting) async throws {
        try await context.perform {
            let cdSetting = ChatSessionSettingMapper.toCoreData(setting: setting, context: self.context)
            try self.context.save()
        }
    }
    
    func getSetting(byId id: UUID) async throws -> ChatSessionSetting? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { ChatSessionSettingMapper.toDomain(cdSetting: $0) }
        }
    }
    
    func getAllSettings() async throws -> [ChatSessionSetting] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
            
            fetchRequest.sortDescriptors = [
                NSSortDescriptor(key: "isSystemDefault", ascending: false),
                NSSortDescriptor(key: "name", ascending: true)
            ]
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { ChatSessionSettingMapper.toDomain(cdSetting: $0) }
        }
    }
    
    func getSystemDefaultSetting() async throws -> ChatSessionSetting? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "isSystemDefault == %@", NSNumber(value: true))
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { ChatSessionSettingMapper.toDomain(cdSetting: $0) }
        }
    }
    
    func updateSetting(_ setting: ChatSessionSetting) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", setting.id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            
            if let cdSetting = results.first {
                // Check if we're trying to modify a system default setting's isSystemDefault flag
                if cdSetting.isSystemDefault && !setting.isSystemDefault {
                    throw NSError(domain: "ChatRepository", code: 403, userInfo: [NSLocalizedDescriptionKey: "Cannot change system default setting to non-default."])
                }
                
                // Prevent making a non-default setting into a system default if one already exists
                if !cdSetting.isSystemDefault && setting.isSystemDefault {
                    let defaultFetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
                    defaultFetchRequest.predicate = NSPredicate(format: "isSystemDefault == %@", NSNumber(value: true))
                    defaultFetchRequest.fetchLimit = 1
                    
                    let defaultResults = try self.context.fetch(defaultFetchRequest)
                    if !defaultResults.isEmpty {
                        throw NSError(domain: "ChatRepository", code: 409, userInfo: [NSLocalizedDescriptionKey: "A system default setting already exists."])
                    }
                }
                
                ChatSessionSettingMapper.updateCoreData(cdSetting: cdSetting, with: setting)
                try self.context.save()
            } else {
                throw NSError(domain: "ChatRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "Chat session setting not found."])
            }
        }
    }
    
    func deleteSetting(byId id: UUID) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            
            if let cdSetting = results.first {
                // Prevent deleting the system default setting
                if cdSetting.isSystemDefault {
                    throw NSError(domain: "ChatRepository", code: 403, userInfo: [NSLocalizedDescriptionKey: "Cannot delete system default setting."])
                }
                
                // Check if this setting is used by any chat sessions
                if let chatSessions = cdSetting.chatSessions, chatSessions.count > 0 {
                    throw NSError(domain: "ChatRepository", code: 409, userInfo: [NSLocalizedDescriptionKey: "Cannot delete setting that is used by chat sessions."])
                }
                
                // Check if this setting is set as default for any users
                if let defaultForUser = cdSetting.defaultForUser {
                    // Update the user to use the system default setting instead
                    let systemDefaultFetchRequest: NSFetchRequest<CDChatSessionSetting> = CDChatSessionSetting.fetchRequest()
                    systemDefaultFetchRequest.predicate = NSPredicate(format: "isSystemDefault == %@", NSNumber(value: true))
                    systemDefaultFetchRequest.fetchLimit = 1
                    
                    let systemDefaultResults = try self.context.fetch(systemDefaultFetchRequest)
                    if let systemDefaultSetting = systemDefaultResults.first {
                        defaultForUser.defaultChatSettings = systemDefaultSetting
                    } else {
                        defaultForUser.defaultChatSettings = nil
                    }
                }
                
                self.context.delete(cdSetting)
                try self.context.save()
            }
        }
    }

    // MARK: - ChatView Relevant Changes
    
    func observeChatViewRelevantChanges(for sessionId: UUID) -> AnyPublisher<ChatViewRelevantChange, Never> {
        return chatViewRelevantChangeSubject
            .filter { $0.0 == sessionId }
            .map { $0.1 }
            .eraseToAnyPublisher()
    }
    
    func observeChatsViewRelevantChanges() -> AnyPublisher<ChatsViewRelevantChange, Never> {
        return chatsViewRelevantChangeSubject.eraseToAnyPublisher()
    }

    // MARK: - MessageAction Operations
    
    func createMessageAction(_ action: MessageAction) async throws {
        try await context.perform {
            _ = MessageActionMapper.toCoreData(action: action, context: self.context)
            try self.context.save()
        }
    }
    
    func getMessageAction(byId id: UUID) async throws -> MessageAction? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessageAction> = CDMessageAction.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            let results = try self.context.fetch(fetchRequest)
            return results.first.map { MessageActionMapper.toDomain(cdAction: $0) }
        }
    }
    
    func getMessageActions(for actionIds: [UUID]) async throws -> [MessageAction] {
        guard !actionIds.isEmpty else { return [] }
        return try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessageAction> = CDMessageAction.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id IN %@", actionIds as CVarArg)
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageActionMapper.toDomain(cdAction: $0) }
        }
    }
    
    func getAllMessageActions() async throws -> [MessageAction] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessageAction> = CDMessageAction.fetchRequest()
            fetchRequest.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
            
            let results = try self.context.fetch(fetchRequest)
            return results.map { MessageActionMapper.toDomain(cdAction: $0) }
        }
    }
    
    func updateMessageAction(_ action: MessageAction) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessageAction> = CDMessageAction.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", action.id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            guard let cdAction = try self.context.fetch(fetchRequest).first else {
                throw NSError(domain: "ChatRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "MessageAction not found."])
            }
            
            MessageActionMapper.updateCoreData(cdAction: cdAction, with: action)
            try self.context.save()
        }
    }
    
    func deleteMessageAction(byId id: UUID) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDMessageAction> = CDMessageAction.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1
            
            guard let cdAction = try self.context.fetch(fetchRequest).first else {
                // Not finding an action to delete is not necessarily an error.
                return
            }
            
            self.context.delete(cdAction)
            try self.context.save()
        }
    }
} 
