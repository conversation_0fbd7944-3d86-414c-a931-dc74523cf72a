import Foundation
import CoreData

/// Core Data implementation of the UserSettingsRepository
class CoreDataUserSettingsRepository: UserSettingsRepository {
    private let context: NSManagedObjectContext
    private let appSettingsRepository: AppSettingsRepository

    init(context: NSManagedObjectContext, appSettingsRepository: AppSettingsRepository) {
        self.context = context
        self.appSettingsRepository = appSettingsRepository
    }

    // MARK: - User CRUD Operations

    func createUser(_ user: User) async throws -> User {
        try await context.perform {
            // Check if user with this ID already exists
            let fetchRequest: NSFetchRequest<CDUser> = CDUser.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", user.id as CVarArg)
            fetchRequest.fetchLimit = 1

            let existingUsers = try self.context.fetch(fetchRequest)
            if !existingUsers.isEmpty {
                throw NSError(domain: "UserRepository", code: 409, userInfo: [NSLocalizedDescriptionKey: "User with ID \(user.id) already exists"])
            }

            // Create new user
            let cdUser = UserMapper.toCoreData(user: user, context: self.context)
            try self.context.save()

            return UserMapper.toDomain(cdUser: cdUser)
        }
    }

    func getUser(byId id: UUID) async throws -> User? {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDUser> = CDUser.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1

            let results = try self.context.fetch(fetchRequest)
            return results.first.map { UserMapper.toDomain(cdUser: $0) }
        }
    }

    func getAllUsers() async throws -> [User] {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDUser> = CDUser.fetchRequest()
            fetchRequest.sortDescriptors = [NSSortDescriptor(key: "registrationDate", ascending: true)]

            let results = try self.context.fetch(fetchRequest)
            return results.map { UserMapper.toDomain(cdUser: $0) }
        }
    }

    func updateUser(_ user: User) async throws -> User {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDUser> = CDUser.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", user.id as CVarArg)
            fetchRequest.fetchLimit = 1

            let results = try self.context.fetch(fetchRequest)
            guard let existingUser = results.first else {
                throw NSError(domain: "UserRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "User with ID \(user.id) not found"])
            }

            UserMapper.updateCoreData(cdUser: existingUser, with: user)
            try self.context.save()

            return UserMapper.toDomain(cdUser: existingUser)
        }
    }

    func deleteUser(byId id: UUID) async throws {
        try await context.perform {
            let fetchRequest: NSFetchRequest<CDUser> = CDUser.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            fetchRequest.fetchLimit = 1

            let results = try self.context.fetch(fetchRequest)
            guard let userToDelete = results.first else {
                throw NSError(domain: "UserRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "User with ID \(id) not found"])
            }

            // Clear current user if this is the current user
            if let currentUserId = self.appSettingsRepository.getCurrentUserId(),
               currentUserId == id {
                self.appSettingsRepository.setCurrentUserId(nil)
            }

            self.context.delete(userToDelete)
            try self.context.save()
        }
    }

    func saveUser(_ user: User) async throws {
        try await context.perform {
            // Check if user already exists
            let fetchRequest: NSFetchRequest<CDUser> = CDUser.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", user.id as CVarArg)
            fetchRequest.fetchLimit = 1

            let results = try self.context.fetch(fetchRequest)

            if let existingUser = results.first {
                // Update existing user
                UserMapper.updateCoreData(cdUser: existingUser, with: user)
            } else {
                // Create new user
                let _ = UserMapper.toCoreData(user: user, context: self.context)
            }

            try self.context.save()
        }
    }

    // MARK: - Current User Management

    func getCurrentUser() async throws -> User? {
        guard let currentUserId = appSettingsRepository.getCurrentUserId() else {
            return nil
        }

        return try await getUser(byId: currentUserId)
    }

    func setCurrentUser(userId: UUID) async throws {
        // Verify the user exists
        guard let _ = try await getUser(byId: userId) else {
            throw NSError(domain: "UserRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "User with ID \(userId) not found"])
        }

        appSettingsRepository.setCurrentUserId(userId)
    }

    // MARK: - Subscription Management

    func getSubscriptionStatus(for userId: UUID) async throws -> SubscriptionStatus? {
        // In Core Data, SubscriptionStatus is not directly stored as an entity.
        // This would be implemented differently in a full implementation, possibly using a separate service or repository.
        // For MVP, we might return a placeholder or fetch from a different source.
        throw NSError(domain: "UserSettingsRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription status not implemented in MVP."])
    }

    func saveSubscriptionStatus(_ status: SubscriptionStatus) async throws {
        // In Core Data, SubscriptionStatus is not directly stored as an entity.
        // This would be implemented differently in a full implementation, possibly using a separate service or repository.
        throw NSError(domain: "UserSettingsRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription status not implemented in MVP."])
    }

    func getAllSubscriptionPlans() async throws -> [SubscriptionPlan] {
        // In Core Data, SubscriptionPlan is not directly stored as an entity.
        // This would be implemented differently in a full implementation, possibly using a separate service or repository.
        // For MVP, we might return a placeholder or fetch from a different source.
        throw NSError(domain: "UserSettingsRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription plans not implemented in MVP."])
    }

    func getSubscriptionPlan(by productId: String) async throws -> SubscriptionPlan? {
        // In Core Data, SubscriptionPlan is not directly stored as an entity.
        // This would be implemented differently in a full implementation, possibly using a separate service or repository.
        throw NSError(domain: "UserSettingsRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription plans not implemented in MVP."])
    }
}