import Foundation

/// UserDefaults-based implementation of AppSettingsRepository
/// Manages app-level settings and configuration using UserDefaults
final class UserDefaultsAppSettingsRepository: AppSettingsRepository {
    
    // MARK: - Dependencies
    
    private let userDefaults: UserDefaults
    
    // MARK: - Keys
    
    private let currentUserKey = "CurrentUserId"
    private let builtinChatVersionKey = "BuiltinChatVersion"
    private let builtinLLMVersionKey = "BuiltinLLMVersion"
    private let builtinUserVersionKey = "BuiltinUserVersion"
    
    // MARK: - Initialization
    
    init(userDefaults: UserDefaults = .standard) {
        self.userDefaults = userDefaults
    }
    
    // MARK: - Current User Management
    
    func getCurrentUserId() -> UUID? {
        guard let currentUserIdString = userDefaults.string(forKey: currentUserKey),
              let currentUserId = UUID(uuidString: currentUserIdString) else {
            return nil
        }
        return currentUserId
    }
    
    func setCurrentUserId(_ userId: UUID?) {
        if let userId = userId {
            userDefaults.set(userId.uuidString, forKey: currentUserKey)
        } else {
            userDefaults.removeObject(forKey: currentUserKey)
        }
    }
    
    // MARK: - Version Management
    
    func getBuiltinChatVersion() -> Int {
        return userDefaults.integer(forKey: builtinChatVersionKey)
    }
    
    func setBuiltinChatVersion(_ version: Int) {
        userDefaults.set(version, forKey: builtinChatVersionKey)
    }
    
    func getBuiltinLLMVersion() -> Int {
        return userDefaults.integer(forKey: builtinLLMVersionKey)
    }
    
    func setBuiltinLLMVersion(_ version: Int) {
        userDefaults.set(version, forKey: builtinLLMVersionKey)
    }

    func getBuiltinUserVersion() -> Int {
        return userDefaults.integer(forKey: builtinUserVersionKey)
    }

    func setBuiltinUserVersion(_ version: Int) {
        userDefaults.set(version, forKey: builtinUserVersionKey)
    }
}
