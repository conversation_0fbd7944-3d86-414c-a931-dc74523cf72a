import Foundation

/// Initializer for user data and current user management
/// Ensures that the app always has a current user set
final class BuiltinUserInitializer {
    
    // MARK: - Constants
    
    /// Current version of user initialization data
    static let currentBuiltinUserVersion = 1
    
    // MARK: - Initialization Logic
    
    /// Initializes user data if needed, ensuring there's always a current user
    /// - Parameters:
    ///   - userSettingsRepository: Repository to use for creating and managing users
    ///   - appSettingsRepository: Repository to use for version tracking and current user management
    static func initializeIfNeeded(
        userSettingsRepository: UserSettingsRepository,
        appSettingsRepository: AppSettingsRepository
    ) async {
        // Get stored version or default to 0 if not found
        let storedVersion = appSettingsRepository.getBuiltinUserVersion()
        
        // Check if initialization or update is needed
        guard storedVersion < currentBuiltinUserVersion else {
            print("User initialization is already at version \(storedVersion), no update needed")
            return
        }
        
        print("Updating user initialization from version \(storedVersion) to \(currentBuiltinUserVersion)")
        
        do {
            // Check if there's already a current user
            let currentUser = try await userSettingsRepository.getCurrentUser()
            
            if currentUser == nil {
                // No current user exists, create a default user
                let defaultUser = createDefaultUser()
                
                // Save the user to the repository
                let createdUser = try await userSettingsRepository.createUser(defaultUser)
                print("Created default user with ID: \(createdUser.id)")
                
                // Set as current user
                try await userSettingsRepository.setCurrentUser(userId: createdUser.id)
                print("Set user \(createdUser.id) as current user")
            } else {
                print("Current user already exists: \(currentUser!.id)")
            }
            
            // Update the stored version number
            appSettingsRepository.setBuiltinUserVersion(currentBuiltinUserVersion)
            print("Successfully updated user initialization to version \(currentBuiltinUserVersion)")
            
        } catch {
            print("Error initializing user data: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Helper Methods
    
    /// Creates a default user with basic settings
    /// - Returns: A new User entity with default values
    private static func createDefaultUser() -> User {
        // Define default shouldRestoreLastChat value as single source of truth
        let defaultShouldRestoreLastChat = true

        // Set AppStorageManager value to ensure consistency between Domain and Presentation layers
        Task { @MainActor in
            AppStorageManager.shared.shouldRestoreLastChat = defaultShouldRestoreLastChat
        }

        return User(
            id: UUID(),
            nickname: nil, // User can set this later
            avatarIdentifier: nil, // User can set this later
            registrationDate: Date(),
            lastSeenAt: Date(),
            appSettings: AppSettings(shouldRestoreLastChat: defaultShouldRestoreLastChat), // Use consistent default value
            defaultChatSettingsId: BuiltinConstants.systemDefaultChatSessionSettingId, // Use predefined system default setting ID
            systemUtilityLLMInstanceId: nil, // Will be set when LLM instances are available
            syncStatus: nil, // Not syncing initially
            lastSyncTimestamp: nil,
            memoryFileBookmarkData: nil,
            metadata: ["source": "default_initialization"]
        )
    }
}
