import Foundation

/// Initializer for built-in Chat Session Settings and example Sessions
struct BuiltinChatInitializer {
    
    // MARK: - Constants
    
    /// Current version of built-in chat data
    static let currentBuiltinChatVersion = 1
    
    /// Get default LLM instance ID based on model ID
    private static let deepseekR1ModelId = UUID(uuidString: "A9A9A9A9-B8B8-7890-A9A9-A9A9B8B87890")! // DeepSeek R1
    private static let geminiModelId = UUID(uuidString: "C2C3C4C5-D6D7-7890-C2C3-C4C5D6D77890")! // Gemini 2.5 Flash
    private static let qwenModelId = UUID(uuidString: "F1F2F3F4-A5A6-7890-F1F2-F3F4A5A67890")! // Qwen3 235B A22B
    
    private static let deepseekR1InstanceId = BuiltinLLMInitializer.getBuiltinDefaultLLMInstance(forModelId: deepseekR1ModelId)?.id ?? UUID()
    private static let geminiInstanceId = BuiltinLLMInitializer.getBuiltinDefaultLLMInstance(forModelId: geminiModelId)?.id ?? UUID()
    private static let qwenInstanceId = BuiltinLLMInitializer.getBuiltinDefaultLLMInstance(forModelId: qwenModelId)?.id ?? UUID()
    
    // MARK: - Built-in Prompt Segments
    
    private static let builtinPromptSegments: [SavedPromptSegment] = [
        SavedPromptSegment(
            id: UUID(uuidString: "F1F1F1F1-F1F1-F1F1-F1F1-F1F1F1F1F1F1")!,
            content: "Summarize this in 3 key points",
            usageCount: 50,
            createdAt: Date().addingTimeInterval(-30 * 24 * 3600), // 30 days ago
            lastUsedAt: Date().addingTimeInterval(-1 * 24 * 3600) // 1 day ago
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F2F2F2F2-F2F2-F2F2-F2F2-F2F2F2F2F2F2")!,
            content: "Explain this like I'm 5 years old",
            usageCount: 45,
            createdAt: Date().addingTimeInterval(-25 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-2 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F3F3F3F3-F3F3-F3F3-F3F3-F3F3F3F3F3F3")!,
            content: "What are the pros and cons?",
            usageCount: 40,
            createdAt: Date().addingTimeInterval(-20 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-3 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F4F4F4F4-F4F4-F4F4-F4F4-F4F4F4F4F4F4")!,
            content: "Provide a step-by-step guide",
            usageCount: 35,
            createdAt: Date().addingTimeInterval(-18 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-4 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F5F5F5F5-F5F5-F5F5-F5F5-F5F5F5F5F5F5")!,
            content: "Find potential issues or problems",
            usageCount: 30,
            createdAt: Date().addingTimeInterval(-15 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-5 * 24 * 3600)
        )
    ]

    // MARK: - Built-in Message Actions
    private static let builtinMessageActions: [MessageAction] = [
        // Assistant Regenerate Actions
        MessageAction(
            id: UUID(uuidString: "AC710000-C001-0001-AC71-0000C0010001")!,
            name: "Pyramid Principle",
            icon: "pyramid",
            actionType: .assistantRegenerate,
            prompts: ["Act as an expert management consultant trained in the Minto Pyramid Principle. Rewrite your previous response to present it to a busy executive. The goal is to create a top-down, logically structured response that is easy to understand and act upon.\n\n**Your output must be only the rewritten text, starting immediately with the core argument and containing no conversational preamble.**\n\n**Language Consistency:** MUST detect the language of the your previous response and ensure the rewritten prompt is in the same language. If the previous response is in Chinese, rewrite in Chinese. If in English, rewrite in English. If in Japanese, rewrite in Japanese, etc."]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-C002-0002-AC71-0000C0020002")!,
            name: "Nonviolent Reply",
            icon: "heart.text.square",
            actionType: .assistantRegenerate,
            prompts: ["Please rephrase your last response using the four components of Nonviolent Communication (NVC). The goal is to present the information in a way that fosters connection and understanding.\n\n**Language Consistency:** MUST detect the language of the your previous response and ensure the rewritten prompt is in the same language. If the previous response is in Chinese, rewrite in Chinese. If in English, rewrite in English. If in Japanese, rewrite in Japanese, etc."]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-C003-0003-AC71-0000C0030003")!,
            name: "Translate",
            icon: "translate",
            actionType: .assistantRegenerate,
            prompts: [
                "Translate last response to English",
                "Translate last response to 简体中文",
                "Translate last response to 繁體中文",
                "Translate last response to 日本語",
                "Translate last response to 한국어",
                "Translate last response to Français",
                "Translate last response to Español"
            ]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-C004-0004-AC71-0000C0040004")!,
            name: "Explain Like I'm 5",
            icon: "face.smiling",
            actionType: .assistantRegenerate,
            prompts: ["Rewrite your previous response to 'Explain Like I'm 5'. Use very simple terms and a relatable analogy. Avoid all technical jargon.\n\n**Language Consistency:** MUST detect the language of the your previous response and ensure the rewritten prompt is in the same language. If the previous response is in Chinese, rewrite in Chinese. If in English, rewrite in English. If in Japanese, rewrite in Japanese, etc."]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-C005-0005-AC71-0000C0050005")!,
            name: "Socratic Method",
            icon: "questionmark.bubble",
            actionType: .assistantRegenerate,
            prompts: ["Instead of a direct statement, use the Socratic method to rephrase your last response. Guide me to the key insights with a series of thoughtful questions.\n\n**Language Consistency:** MUST detect the language of the your previous response and ensure the rewritten prompt is in the same language. If the previous response is in Chinese, rewrite in Chinese. If in English, rewrite in English. If in Japanese, rewrite in Japanese, etc."]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-C006-0006-AC71-0000C0060006")!,
            name: "Persuasive Pitch",
            icon: "target",
            actionType: .assistantRegenerate,
            prompts: ["Rewrite your previous response as a persuasive pitch using Monroe's Motivated Sequence. Structure it to grab attention, establish a need, provide a solution, visualize the benefits, and end with a clear call to action.\n\n**Language Consistency:** MUST detect the language of the your previous response and ensure the rewritten prompt is in the same language. If the previous response is in Chinese, rewrite in Chinese. If in English, rewrite in English. If in Japanese, rewrite in Japanese, etc."]
        ),
        // Action Panel Prompt Suggestions
        MessageAction(
            id: UUID(uuidString: "AC710000-9001-0001-AC71-000090010001")!,
            name: "Insert",
            icon: "text.insert",
            actionType: .actionPanelPromptInsert,
            prompts: [
                "Think step by step.",
                "Explain like I'm 5.",
                BuiltinPrompt.builtinActionPanelPromptInsert["RIPER-5"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["CHAT RECAP"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["EXPERT PANEL"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["IDEA-FACTORY"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["SPICE PROTOCOL"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["FIRST PRINCIPLES"]!,
            ]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-9002-0002-AC71-000090020002")!,
            name: "Optimize",
            icon: "wand.and.sparkles",
            actionType: .actionPanelPromptRewrite,
            prompts: [
                BuiltinPrompt.builtinActionPanelPromptRewrite["Prompt Optimizer"]!
            ]
        ),
        MessageAction(
            id: UUID(uuidString: "AC710000-9003-0003-AC71-000090030003")!,
            name: "Enhance",
            icon: "wand.and.sparkles.inverse",
            actionType: .actionPanelPromptRewrite,
            prompts: [
                BuiltinPrompt.builtinActionPanelPromptRewrite["Prompt Optimizer"]!,
                BuiltinPrompt.builtinActionPanelPromptRewrite["Prompt Clarifier"]!,
                BuiltinPrompt.builtinActionPanelPromptRewrite["Analytical Prompt Enhancer"]!,
                BuiltinPrompt.builtinActionPanelPromptRewrite["Creative Prompt Catalyst"]!
            ]
        ),
    ]

    // MARK: - Built-in Settings
    
    /// Default system chat session setting
    private static let systemDefaultChatSessionSetting = ChatSessionSetting(
        id: BuiltinConstants.systemDefaultChatSessionSettingId,
        name: "System Default",
        isSystemDefault: true,
        createdAt: Date(),
        lastModifiedAt: Date(),
        llmParameterOverrides: nil,
        defaultContextServerIds: nil,
        shouldExpandThinking: false,
        uiThemeSettings: UIThemeSettings(
            themeIdentifier: "system",
            customColors: nil,
            backgroundImage: nil,
            fontSize: .medium
        ),
        messageActionSettings: MessageActionSettings(
            actionPanelActions: [
                SystemMessageActions.camera.id,
                SystemMessageActions.photo.id,
                SystemMessageActions.file.id,
                builtinMessageActions[6].id, // Insert action
                builtinMessageActions[8].id, // Enhance action
            ],
            userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.edit.id],
            assistantMessageCardActions: [
                SystemMessageActions.copy.id,
                SystemMessageActions.regenerate.id,
                SystemMessageActions.like.id,
                SystemMessageActions.dislike.id,
                builtinMessageActions[0].id,
                builtinMessageActions[2].id,
            ],
            assistantMessageMenuActions: [
                SystemMessageActions.copy.id,
                SystemMessageActions.regenerate.id,
                SystemMessageActions.like.id,
                SystemMessageActions.dislike.id,
                SystemMessageActions.select.id,
                builtinMessageActions[0].id,
                builtinMessageActions[2].id,
            ]
        ),
        savedPromptSegments: builtinPromptSegments,
        auxiliaryLLMInstanceId: nil,
        shouldAutoGenerateTitle: false,
        contextMessageCount: Int64.max,
        metadata: nil
    )

    /// Minimal chat session setting for streamlined experience
    private static let minimalChatSessionSetting = ChatSessionSetting(
        id: BuiltinConstants.minimalChatSessionSettingId,
        name: "Minimal",
        isSystemDefault: false,
        createdAt: Date(),
        lastModifiedAt: Date(),
        llmParameterOverrides: nil,
        defaultContextServerIds: nil,
        shouldExpandThinking: true,
        uiThemeSettings: UIThemeSettings(
            themeIdentifier: "system",
            customColors: nil,
            backgroundImage: nil,
            fontSize: .medium
        ),
        messageActionSettings: MessageActionSettings(
            actionPanelActions: [
                SystemMessageActions.camera.id,
                SystemMessageActions.photo.id,
                SystemMessageActions.file.id
            ],
            userMessageActions: [
                SystemMessageActions.copy.id,
                SystemMessageActions.edit.id
            ],
            assistantMessageCardActions: [
                SystemMessageActions.copy.id,
                SystemMessageActions.regenerate.id,
                SystemMessageActions.like.id,
                SystemMessageActions.dislike.id
            ],
            assistantMessageMenuActions: [
                SystemMessageActions.copy.id,
                SystemMessageActions.regenerate.id,
                SystemMessageActions.like.id,
                SystemMessageActions.dislike.id,
                SystemMessageActions.select.id
            ]
        ),
        savedPromptSegments: [],
        auxiliaryLLMInstanceId: nil,
        shouldAutoGenerateTitle: false,
        contextMessageCount: 20,
        metadata: nil
    )

    // MARK: - Example Sessions and Messages

    private static let exampleSession1Id = UUID(uuidString: "C142A374-1001-1001-1001-000000001001")!
    private static let exampleSession2Id = UUID(uuidString: "C142A374-**************-000000002001")!
    private static let exampleSession3Id = UUID(uuidString: "C142A374-**************-000000003001")!

    /// Example session 1: Basic chat introduction
    private static func getExampleSession1(userId: UUID) -> ChatSession {
        return ChatSession(
            id: exampleSession1Id,
            title: "Welcome to LavaChat",
            createdAt: Date().addingTimeInterval(-2000),
            lastModifiedAt: Date().addingTimeInterval(-1650),
            activeMessageId: UUID(uuidString: "C142A374-1016-1016-1016-000000001016")!, // Set to the last message in the linear flow
            activeLLMInstanceIds: [deepseekR1InstanceId],
            usedLLMInstanceIds: [deepseekR1InstanceId], // Only DeepSeek used in this session
            activeContextServerIds: [],
            settingsId: systemDefaultChatSessionSetting.id,
            userId: userId,
            instanceSettings: [
                deepseekR1InstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false)
            ]
        )
    }
    
    /// Example session 2: Demonstrating complex interactions
    private static func getExampleSession2(userId: UUID) -> ChatSession {
        return ChatSession(
            id: exampleSession2Id,
            title: "Interaction with LavaChat",
            createdAt: Date().addingTimeInterval(-3 * 24 * 3600 - 2 * 3600),
            lastModifiedAt: Date().addingTimeInterval(-2 * 3600),
            activeMessageId: UUID(uuidString: "C142A374-**************-000000002012")!,
            activeLLMInstanceIds: [deepseekR1InstanceId, geminiInstanceId, qwenInstanceId],
            usedLLMInstanceIds: [deepseekR1InstanceId, geminiInstanceId, qwenInstanceId], // All three instances used
            activeContextServerIds: [],
            settingsId: systemDefaultChatSessionSetting.id,
            userId: userId,
            instanceSettings: [
                deepseekR1InstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false),
                geminiInstanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false),
                qwenInstanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false)
            ]
        )
    }
    
    /// Example session 3: File interaction placeholder
    private static func getExampleSession3(userId: UUID) -> ChatSession {
        return ChatSession(
            id: exampleSession3Id,
            title: "Placeholder",
            createdAt: Date().addingTimeInterval(-3 * 24 * 3600 - 2 * 3600),
            lastModifiedAt: Date().addingTimeInterval(-1 * 24 * 3600 - 2 * 3600),
            activeMessageId: UUID(uuidString: "C142A374-**************-000000003011")!, // Set to the system message itself
            activeLLMInstanceIds: [deepseekR1InstanceId],
            usedLLMInstanceIds: [deepseekR1InstanceId], // Only DeepSeek used
            activeContextServerIds: [],
            settingsId: systemDefaultChatSessionSetting.id,
            userId: userId,
            instanceSettings: [
                deepseekR1InstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false)
            ]
        )
    }

    /// Get all example sessions for a specific user
    private static func getAllExampleSessions(userId: UUID) -> [ChatSession] {
        return [
            getExampleSession1(userId: userId),
            getExampleSession2(userId: userId),
            getExampleSession3(userId: userId)
        ]
    }

    // MARK: - Example Messages
    
    /// Get messages for example session 1
    private static func getMessagesForExampleSession1(userId: UUID) -> [Message] {
        // Root user message (depth 0)
        let message1 = Message(
            id: UUID(uuidString: "C142A374-1011-1011-1011-000000001011")!,
            sessionId: exampleSession1Id,
            parentId: nil,
            timestamp: Date(),
            role: .user,
            content: [.text("Hello, who are you?")],
            depth: 0,
            userId: userId,
            status: .received
        )
        
        // Assistant reply (depth 1)
        let message2 = Message(
            id: UUID(uuidString: "C142A374-1012-1012-1012-000000001012")!,
            sessionId: exampleSession1Id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 1),
            role: .assistant,
            content: [.text("Hello! I am LavaChat🌋, your AI hub. You can interact with multiple large language models and leverage their unique capabilities.")],
            depth: 1,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 64,
            completionTokens: 256,
            status: .received
        )
        
        // Second user message (depth 2)
        let message3 = Message(
            id: UUID(uuidString: "C142A374-1013-1013-1013-000000001013")!,
            sessionId: exampleSession1Id,
            parentId: message2.id,
            timestamp: Date(timeIntervalSinceNow: 60),
            role: .user,
            content: [.text("How to setup LavaChat?")],
            depth: 2,
            userId: userId,
            status: .received
        )

        // Second assistant reply (depth 3)
        let message4 = Message(
            id: UUID(uuidString: "C142A374-1014-1014-1014-000000001014")!,
            sessionId: exampleSession1Id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 61),
            role: .assistant,
            content: [.text("Go to **Models** tab, tap a provider's header, then 'Edit' to add your `API key`. After setup, use the ➕ button in **Chats** tab to start chatting with any model.")],
            depth: 3,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 128,
            completionTokens: 352,
            status: .received
        )

        // Second user message (depth 4)
        let message5 = Message(
            id: UUID(uuidString: "C142A374-1015-1015-1015-000000001015")!,
            sessionId: exampleSession1Id,
            parentId: message4.id,
            timestamp: Date(timeIntervalSinceNow: 62),
            role: .user,
            content: [.text("Can you explain provider, model & instance?")],
            depth: 4,
            userId: userId,
            status: .received
        )

        // Second assistant reply (depth 5)
        let message6 = Message(
            id: UUID(uuidString: "C142A374-1016-1016-1016-000000001016")!,
            sessionId: exampleSession1Id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 63),
            role: .assistant,
            content: [.text("* **Provider**: AI companies like OpenAI\n* **Model**: The AI itself (GPT-4, etc.)\n* **Instance**: Customized Model with prompts & settings\n\nThese affect AI behavior - model capabilities and instance settings control the `Thinking` button behavior in chat. See **Interaction with LavaChat** for more guides! 🚀")],
            depth: 5,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 80,
            completionTokens: 250,
            status: .received
        )
        
        return [message1, message2, message3, message4, message5, message6]
    }
    
    /// Get messages for example session 2
    private static func getMessagesForExampleSession2(userId: UUID) -> [Message] {
        // First round user message (root - depth 0)
        let message1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002011")!,
            sessionId: exampleSession2Id,
            parentId: nil,
            timestamp: Date(),
            role: .user,
            content: [.text("How to interact with LavaChat?")],
            depth: 0,
            userId: userId,
            status: .received
        )

        // First round assistant replies (three parallel - depth 1)
        let message2_deepseekR1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002012")!,
            sessionId: exampleSession2Id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 1),
            role: .assistant,
            content: [.text("Using LavaChat🌋 is simple! Just type your message in the input box at the bottom. Tap the ➕ button to see available actions and model instances for this chat. Responses from different models appear as stacked cards - **swipe right** to like a message (it'll move to the bottom) and reveal the next one. Try swiping right now to see how it works!")],
            depth: 1,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 32,
            completionTokens: 128,
            status: .received,
            userFeedback: .none
        )
        
        let message2_gemini = Message(
            id: UUID(uuidString: "C142A374-**************-000000002013")!,
            sessionId: exampleSession2Id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 2),
            role: .assistant,
            content: [.text("When you tap that ➕ button, you'll see all the ways to customize your chat experience:\n\n- Add more models to the conversation\n- Swipe left on any model to remove it\n- Toggle options like thinking mode 💡 or web search 🌐 if the model supports them\n\nBack to swiping cards. Swipe left on one to dislike it and make it disappear. Go ahead, try swiping this message left to see what happens!")],
            depth: 1,
            llmInstanceId: geminiInstanceId,
            promptTokens: 32,
            completionTokens: 148,
            status: .received,
            userFeedback: .none
        )
        
        let message2_qwen = Message(
            id: UUID(uuidString: "C142A374-**************-000000002014")!,
            sessionId: exampleSession2Id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 3),
            role: .assistant,
            content: [.text("You can identify each model by its logo in the bottom right corner of cards. The order matches how you arranged models in the ➕ panel. Each model instance has its own unique system prompt and parameters - meaning you can even get totally different responses from multiple instances of the same model (and of course for different models too)!\n\nWant more tips? Tap the `>` button next to <1/2> below user message to see other guides.")],
            depth: 1,
            llmInstanceId: qwenInstanceId,
            promptTokens: 32,
            completionTokens: 168,
            status: .received,
            userFeedback: .none
        )
        
        // Second round user message (edited version - depth 0)
        let message3 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002015")!,
            sessionId: exampleSession2Id,
            parentId: nil,
            timestamp: Date(timeIntervalSinceNow: 60),
            role: .user,
            content: [.text("What is LavaChat and what are its features?")],
            depth: 0,
            userId: userId,
            status: .received,
            metadata: [Message.MetadataKeys.isEditOf: "C142A374-**************-000000002011"]
        )

        // Second round assistant replies (three parallel - depth 1)
        let message4_deepseekR1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002016")!,
            sessionId: exampleSession2Id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 61),
            role: .assistant,
            content: [.text("`Like` a message does not mean you choose it as the next message to continue the conversation. Only the message card shown in the top of card stack is send to the AI for response. All the model will receive the same message history based on the message card shown in the top of card stack. With this feature, you can compare the response from different models and choose the best one to continue the conversation.\n\nNot only swiping, click 👍 at bottom of the message will also trigger the card moving to the bottom. Try it now!")],
            depth: 1,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 48,
            completionTokens: 192,
            status: .received,
            userFeedback: .liked,
            isReplied: true
        )
        
        let message4_gemini = Message(
            id: UUID(uuidString: "C142A374-**************-000000002017")!,
            sessionId: exampleSession2Id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 62),
            role: .assistant,
            content: [.text("You also have all the normal actions for each message card like copy, select, edit, regenerate, etc. Either at the bottom of the message card or long press on the message card. \n\nClick 👎 at bottom of the message will also dismiss the message. Try it now!")],
            depth: 1,
            llmInstanceId: geminiInstanceId,
            promptTokens: 48,
            completionTokens: 212,
            status: .received,
            userFeedback: .liked
        )
        
        let message4_qwen = Message(
            id: UUID(uuidString: "C142A374-**************-000000002018")!,
            sessionId: exampleSession2Id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 63),
            role: .assistant,
            content: [.text("Swipe right to see more tips under the first message card. Try it now!")],
            depth: 1,
            llmInstanceId: qwenInstanceId,
            promptTokens: 48,
            completionTokens: 156,
            status: .received,
            userFeedback: .disliked
        )
        
        // Third round user message (depth 2)
        let message5 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002019")!,
            sessionId: exampleSession2Id,
            parentId: message4_deepseekR1.id, // Parent is the selected assistant message
            timestamp: Date(timeIntervalSinceNow: 120),
            role: .user,
            content: [.text("Explain the multi-assistant feature in detail.")],
            depth: 2,
            userId: userId,
            status: .received
        )

        // Third round assistant replies (original - depth 3)
        let message6_deepseekR1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002020")!,
            sessionId: exampleSession2Id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 121),
            role: .assistant,
            content: [.text("to be updated")],
            depth: 3,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 64,
            completionTokens: 256,
            status: .received
        )
        
        let message6_gemini = Message(
            id: UUID(uuidString: "C142A374-**************-000000002021")!,
            sessionId: exampleSession2Id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 122),
            role: .assistant,
            content: [.text("to be updated")],
            depth: 3,
            llmInstanceId: geminiInstanceId,
            promptTokens: 64,
            completionTokens: 298,
            status: .received
        )
        
        let message6_qwen = Message(
            id: UUID(uuidString: "C142A374-**************-000000002022")!,
            sessionId: exampleSession2Id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 123),
            role: .assistant,
            content: [.text("to be updated")],
            depth: 3,
            llmInstanceId: qwenInstanceId,
            promptTokens: 64,
            completionTokens: 322,
            status: .received
        )
        
        // Third round assistant reply (regeneration - depth 3)
        let message7_deepseekR1_regen = Message(
            id: UUID(uuidString: "C142A374-**************-000000002023")!,
            sessionId: exampleSession2Id,
            parentId: message5.id, // Same parent as the original
            timestamp: Date(timeIntervalSinceNow: 180),
            role: .assistant,
            content: [.text("to be updated")],
            depth: 3,
            llmInstanceId: deepseekR1InstanceId,
            promptTokens: 64,
            completionTokens: 312,
            status: .received,
            metadata: [Message.MetadataKeys.isRegenerationOf: "C142A374-**************-000000002020"]
        )
        
        return [
            message1, 
            message2_deepseekR1, message2_gemini, message2_qwen, 
            message3, 
            message4_deepseekR1, message4_gemini, message4_qwen,
            message5, 
            message6_deepseekR1, message6_gemini, message6_qwen,
            message7_deepseekR1_regen
        ]
    }
    
    /// Get messages for example session 3
    private static func getMessagesForExampleSession3(userId: UUID) -> [Message] {
        // System message explaining the file session (depth 0)
        let message1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000003011")!,
            sessionId: exampleSession3Id,
            parentId: nil,
            timestamp: Date(),
            role: .user,
            content: [.text("Hello, please introduce yourself.")],
            depth: 0,
            userId: userId,
            status: .received
        )

        let message2 = Message(
            id: UUID(uuidString: "C142A374-**************-000000003012")!,
            sessionId: exampleSession3Id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 1),
            role: .assistant,
            content: [.text("To be updated")],
            depth: 1,
            llmInstanceId: deepseekR1InstanceId,
            status: .received
        )
        
        return [message1, message2]
    }
    

    
    /// Get all messages for a given session ID
    private static func getMessagesForSession(sessionId: UUID, userId: UUID) -> [Message] {
        if sessionId == exampleSession1Id {
            return getMessagesForExampleSession1(userId: userId)
        } else if sessionId == exampleSession2Id {
            return getMessagesForExampleSession2(userId: userId)
        } else if sessionId == exampleSession3Id {
            return getMessagesForExampleSession3(userId: userId)
        }
        return []
    }
    
    // MARK: - Initialization Logic
    
    /// Initializes built-in chat session settings and example sessions if needed
    /// - Parameters:
    ///   - chatRepository: Repository to use for creating and updating chat sessions and messages
    ///   - appSettingsRepository: Repository to use for version tracking
    static func initializeIfNeeded(
        chatRepository: ChatRepository,
        appSettingsRepository: AppSettingsRepository
    ) async {
        // Get stored version or default to 0 if not found
        let storedVersion = appSettingsRepository.getBuiltinChatVersion()
        
        // Check if initialization or update is needed
        guard storedVersion < currentBuiltinChatVersion else {
            print("Built-in chat data is already at version \(storedVersion), no update needed")
            return
        }
        
        print("Updating built-in chat data from version \(storedVersion) to \(currentBuiltinChatVersion)")

        // Get current user ID from app settings
        guard let currentUserId = appSettingsRepository.getCurrentUserId() else {
            print("No current user found, skipping built-in chat initialization")
            return
        }

        print("Initializing built-in chat data for user: \(currentUserId)")

        do {
            // Begin batch update for chat repository
            await chatRepository.beginBatchUpdate()
            
            // STEP 1: Get existing built-in chat sessions
            let existingSessions = try await chatRepository.getAllChatSessions(
                for: currentUserId,
                sortBy: [NSSortDescriptor(key: "createdAt", ascending: true)]
            )
            
            // Convert to dictionary for easy lookup
            var existingSessionsDict = [UUID: ChatSession]()
            for session in existingSessions {
                existingSessionsDict[session.id] = session
            }
            
            // STEP 2: Handle system default chat session setting
            let systemDefaultSettingId = systemDefaultChatSessionSetting.id
            let existingSystemSetting = try await chatRepository.getSetting(byId: systemDefaultSettingId)

            if let existingSystemSetting = existingSystemSetting {
                // Check if the setting needs update (not modified by user)
                if existingSystemSetting.createdAt == existingSystemSetting.lastModifiedAt {
                    if !existingSystemSetting.isSystemDefault || existingSystemSetting.name != systemDefaultChatSessionSetting.name {
                        // Update it
                        try await chatRepository.updateSetting(systemDefaultChatSessionSetting)
                        print("Updated system default chat session setting")
                    }
                } else {
                    // User modified, don't update
                    print("System default chat session setting was modified by user, skipping update")
                }
            } else {
                // Create new system default setting
                try await chatRepository.createSetting(systemDefaultChatSessionSetting)
                print("Created system default chat session setting")
            }

            // STEP 2.1: Handle minimal chat session setting
            let minimalSettingId = minimalChatSessionSetting.id
            let existingMinimalSetting = try await chatRepository.getSetting(byId: minimalSettingId)

            if let existingMinimalSetting = existingMinimalSetting {
                // Check if the setting needs update (not modified by user)
                if existingMinimalSetting.createdAt == existingMinimalSetting.lastModifiedAt {
                    if existingMinimalSetting.name != minimalChatSessionSetting.name {
                        // Update it
                        try await chatRepository.updateSetting(minimalChatSessionSetting)
                        print("Updated minimal chat session setting")
                    }
                } else {
                    // User modified, don't update
                    print("Minimal chat session setting was modified by user, skipping update")
                }
            } else if storedVersion < 1 {
                // Create new minimal setting
                try await chatRepository.createSetting(minimalChatSessionSetting)
                print("Created minimal chat session setting")
            } else {
                // User modified or removed, don't update
                print("Minimal chat session setting was modified or removed, skipping update")
            }

            // STEP 2.5: Handle built-in message actions
            let builtinActionIDs = builtinMessageActions.map { $0.id }
            let existingActions = try await chatRepository.getMessageActions(for: builtinActionIDs)
            let existingActionsDict = Dictionary(uniqueKeysWithValues: existingActions.map { ($0.id, $0) })

            for builtinAction in builtinMessageActions {
                if let existingAction = existingActionsDict[builtinAction.id] {
                    // Action exists, do nothing
                    print("Built-in message action '\(existingAction.name)' already exists, skipping update.")
                } else {
                    // Action does not exist, create it
                    try await chatRepository.createMessageAction(builtinAction)
                    print("Created built-in message action: \(builtinAction.name)")
                }
            }
            
            // STEP 3: Process example sessions
            for exampleSession in getAllExampleSessions(userId: currentUserId) {
                if existingSessionsDict[exampleSession.id] == nil {
                    // Create new session
                    try await chatRepository.createChatSession(exampleSession)
                    print("Created example chat session: \(exampleSession.title ?? "Untitled")")
                    
                    // Create messages for this session
                    let messages = getMessagesForSession(sessionId: exampleSession.id, userId: currentUserId)
                    for message in messages {
                        try await chatRepository.createMessage(message)
                    }
                    print("Created \(messages.count) messages for session: \(exampleSession.title ?? "Untitled")")
                } else {
                    // Session already exists, skip creation
                    print("Example chat session already exists: \(exampleSession.title ?? "Untitled"), skipping creation")
                }
            }
            
            // Update the stored version number
            appSettingsRepository.setBuiltinChatVersion(currentBuiltinChatVersion)
            print("Successfully updated built-in chat data to version \(currentBuiltinChatVersion)")
            
        } catch {
            print("Error initializing built-in chat data: \(error.localizedDescription)")
        }
        
        do {
            try await chatRepository.endBatchUpdateAndPublish()
        } catch {
            print("Error during chatRepository.endBatchUpdateAndPublish: \(error.localizedDescription)")
        }
    }
}
