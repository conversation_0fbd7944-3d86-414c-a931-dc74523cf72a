import Foundation
import CryptoKit

/// Initializer for built-in LLM Providers, Models and Instance
struct BuiltinLLMInitializer {
    
    // MARK: - Constants
    
    /// Current version of built-in provider, model and instance data
    static let currentBuiltinLLMVersion = 1
    

    // MARK: - Built-in Providers, Models and Instance
    
    /// Built-in LLM providers - these should never change IDs between versions
    private static let builtinProviders: [LLMProvider] = [
        // OpenAI
        LLMProvider(
            id: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
            name: "OpenAI",
            logoImageName: "provider_logo_openai",
            websiteUrl: "https://openai.com",
            apiDocumentationUrl: "https://platform.openai.com/docs/api-reference",
            apiBaseUrl: "https://api.openai.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            metadata: nil
        ),
        
        // Anthropic
        LLMProvider(
            id: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
            name: "Anthropic",
            logoImageName: "provider_logo_anthropic",
            websiteUrl: "https://anthropic.com",
            apiDocumentationUrl: "https://docs.anthropic.com/claude/reference",
            apiBaseUrl: "https://api.anthropic.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .anthropic,
            apiEndpointPath: "/v1/messages",
            isUserCreated: false,
            metadata: nil
        ),
        
        // Google
        LLMProvider(
            id: UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!,
            name: "Google",
            logoImageName: "provider_logo_google",
            websiteUrl: "https://aistudio.google.com",
            apiDocumentationUrl: "https://ai.google.dev/docs/gemini_api_overview",
            apiBaseUrl: "https://generativelanguage.googleapis.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .google,
            apiEndpointPath: "/v1beta/models",
            isUserCreated: false,
            metadata: nil
        ),
        
        // DeepSeek
        LLMProvider(
            id: UUID(uuidString: "D4E5F6A1-B2C3-7890-D4E5-F6A1B2C37890")!,
            name: "DeepSeek",
            logoImageName: "provider_logo_deepseek",
            websiteUrl: "https://www.deepseek.com/",
            apiDocumentationUrl: "https://api-docs.deepseek.com/",
            apiBaseUrl: "https://api.deepseek.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/chat/completions",
            isUserCreated: false,
            metadata: nil
        ),
        
        // Xai (Grok)
        LLMProvider(
            id: UUID(uuidString: "E5F6A1B2-C3D4-7890-E5F6-A1B2C3D47890")!,
            name: "xAI",
            logoImageName: "provider_logo_xai",
            websiteUrl: "https://x.ai",
            apiDocumentationUrl: "https://docs.x.ai/",
            apiBaseUrl: "https://api.x.ai",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            metadata: nil
        ),
        
        // Qwen (Alibaba)
        LLMProvider(
            id: UUID(uuidString: "F6A1B2C3-D4E5-7890-F6A1-B2C3D4E57890")!,
            name: "Qwen",
            logoImageName: "provider_logo_qwen",
            websiteUrl: "https://www.alibabacloud.com/en/solutions/generative-ai/qwen",
            apiDocumentationUrl: "https://www.alibabacloud.com/help/en/model-studio/use-qwen-by-calling-api",
            apiBaseUrl: "https://dashscope.aliyuncs.com/compatible-mode",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            metadata: nil
        ),
        
        // OpenRouter
        LLMProvider(
            id: UUID(uuidString: "A2B3C4D5-E6F7-7890-A2B3-C4D5E6F77890")!,
            name: "OpenRouter",
            logoImageName: "provider_logo_openrouter",
            websiteUrl: "https://openrouter.ai",
            apiDocumentationUrl: "https://openrouter.ai/docs",
            apiBaseUrl: "https://openrouter.ai/api",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            metadata: nil
        ),

        // Nvidia
        LLMProvider(
            id: UUID(uuidString: "A3B4C5D6-E7F8-7890-A3B4-C5D6E7F87890")!,
            name: "Nvidia",
            logoImageName: "provider_logo_nvidia",
            websiteUrl: "https://build.nvidia.com/",
            apiDocumentationUrl: "https://docs.api.nvidia.com/nim/reference/llm-apis",
            apiBaseUrl: "https://integrate.api.nvidia.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            metadata: nil
        ),

        // SiliconFlow
        LLMProvider(
            id: UUID(uuidString: "A4B5C6D7-E8F9-7890-A4B5-C6D7E8F97890")!,
            name: "SiliconFlow",
            logoImageName: "provider_logo_siliconflow",
            websiteUrl: "https://siliconflow.cn/",
            apiDocumentationUrl: "https://docs.siliconflow.cn/",
            apiBaseUrl: "https://api.siliconflow.cn",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            metadata: nil
        )
    ]
    
    /// Get the built-in models for a specific provider
    private static func getBuiltinModelsForProvider(providerId: UUID) -> [LLMModel] {
        // Define models based on provider ID
        switch providerId.uuidString {
        case "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890": // OpenAI
            return [
                LLMModel(
                    id: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!,
                    providerId: providerId,
                    modelIdentifier: "gpt-4.1",
                    name: "GPT-4.1",
                    modelDescription: "Flagship GPT model for complex tasks.",
                    logoImageName: "provider_logo_openai",
                    contextWindowSize: 1047576,
                    inputModalities: [.text, .image],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 32768,
                    pricingInfo: "Input: $2/M tokens\nOutput: $8/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "A2A3A4A5-B6B7-7890-A2A3-A4A5B6B77890")!,
                    providerId: providerId,
                    modelIdentifier: "gpt-4o-mini",
                    name: "GPT-4o Mini",
                    modelDescription: "Fast, affordable small model for focused tasks.",
                    logoImageName: "provider_logo_openai",
                    contextWindowSize: 128000,
                    inputModalities: [.text, .image],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 16384,
                    pricingInfo: "Input: $0.15/M tokens\nOutput: $0.60/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "A3A4A5A6-B7B8-7890-A3A4-A5A6B7B87890")!,
                    providerId: providerId,
                    modelIdentifier: "o1-mini",
                    name: "o1-mini",
                    modelDescription: "A small model alternative to o1.",
                    logoImageName: "provider_logo_openai",
                    contextWindowSize: 128000,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 65536,
                    pricingInfo: "Input: $1.10/M tokens\nOutput: $4.40/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "A4A5A6A7-B8B9-7890-A4A5-A6A7B8B97890")!,
                    providerId: providerId,
                    modelIdentifier: "gpt-image-1",
                    name: "GPT Image 1",
                    modelDescription: "OpenAI State-of-the-art image generation model.",
                    logoImageName: "provider_logo_openai",
                    contextWindowSize: 0,
                    inputModalities: [.text, .image],
                    outputModalities: [.image],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 0,
                    pricingInfo: "Input: $5.00/M tokens\nOutput: $40.00/M tokens\n$0.011/0.042/0.167 per 1024x1024 low/medium/high quality image",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    apiConfigsOverride: [
                        ModelAPIConfig(apiStyle: .openaiImageGeneration, apiEndpointPath: "/v1/images/generations"),
                        ModelAPIConfig(apiStyle: .openaiImageEdit, apiEndpointPath: "/v1/images/edits")
                    ],
                    metadata: nil
                ),
            ]
        case "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890": // Anthropic
            return [
                LLMModel(
                    id: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!,
                    providerId: providerId,
                    modelIdentifier: "claude-sonnet-4-0",
                    name: "Claude Sonnet 4",
                    modelDescription: "Anthropic high-performance model.",
                    logoImageName: "model_logo_anthropic_claude",
                    contextWindowSize: 200000,
                    inputModalities: [.text, .image, .pdf],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "thinking"),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
                    maxOutputTokens: 64000,
                    pricingInfo: "Input: $3/M tokens\nOutput: $15/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "B2B3B4B5-C6C7-7890-B2B3-B4B5C6C77890")!,
                    providerId: providerId,
                    modelIdentifier: "claude-3-7-sonnet-latest",
                    name: "Claude Sonnet 3.7",
                    modelDescription: "Anthropic previous high-performance model.",
                    logoImageName: "model_logo_anthropic_claude",
                    contextWindowSize: 200000,
                    inputModalities: [.text, .image, .pdf],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "thinking"),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
                    maxOutputTokens: 64000,
                    pricingInfo: "Input: $3/M tokens\nOutput: $15/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "B3B4B5B6-C7C8-7890-B3B4-B5B6C7C87890")!,
                    providerId: providerId,
                    modelIdentifier: "claude-3-5-haiku-latest",
                    name: "Claude 3 Haiku",
                    modelDescription: "Fast and cost-effective model for simple tasks.",
                    logoImageName: "model_logo_anthropic_claude",
                    contextWindowSize: 200000,
                    inputModalities: [.text, .image, .pdf],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .promptBased, parameterName: "thinking_prompt"),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
                    maxOutputTokens: 8192,
                    pricingInfo: "Input: $0.80/M tokens\nOutput: $4.00/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890": // Google
            return [
                LLMModel(
                    id: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!,
                    providerId: providerId,
                    modelIdentifier: "gemini-2.5-pro",
                    name: "Gemini 2.5 Pro",
                    modelDescription: "Google's state-of-the-art thinking model for most tasks.",
                    logoImageName: "model_logo_google_gemini",
                    contextWindowSize: 1048576,
                    inputModalities: [.text, .image, .audio, .video],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .thinkingBudget, parameterName: "thinkingBudget", maxBudget: 24576),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
                    maxOutputTokens: 65536,
                    pricingInfo: "Input: $1.25 or $2.50/M tokens\nOutput: $10 or $15/M tokens, depends on prompts <= 200k or > 200k tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "C2C3C4C5-D6D7-7890-C2C3-C4C5D6D77890")!,
                    providerId: providerId,
                    modelIdentifier: "gemini-2.5-flash",
                    name: "Gemini 2.5 Flash",
                    modelDescription: "Google's latest flash model for most tasks.",
                    logoImageName: "model_logo_google_gemini",
                    contextWindowSize: 1048576,
                    inputModalities: [.text, .image, .audio, .video],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .thinkingBudget, parameterName: "thinkingBudget", maxBudget: 24576),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
                    maxOutputTokens: 65536,
                    pricingInfo: "Free daily quota. Otherwise: Input: $0.15/M tokens\nOutput: $0.60 (non-thinking) or $3.50 (thinking) /M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "D4E5F6A1-B2C3-7890-D4E5-F6A1B2C37890": // DeepSeek
            return [
                LLMModel(
                    id: UUID(uuidString: "D2D2D2D2-E5E6-7890-D1D2-D3D4E5E67890")!,
                    providerId: providerId,
                    modelIdentifier: "deepseek-chat",
                    name: "DeepSeek V3",
                    modelDescription: "DeepSeek's latest non-reasoning model.",
                    logoImageName: "provider_logo_deepseek",
                    contextWindowSize: 64000,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 8000,
                    pricingInfo: "Input: $0.27/M tokens\nOutput: $1.10/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "D2D2D2D2-E6E7-7890-D2D3-D4D5E6E77890")!,
                    providerId: providerId,
                    modelIdentifier: "deepseek-reasoner",
                    name: "DeepSeek R1",
                    modelDescription: "DeepSeek's latest reasoning model.",
                    logoImageName: "provider_logo_deepseek",
                    contextWindowSize: 64000,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 8000,
                    pricingInfo: "Input: $0.55/M tokens\nOutput: $2.19/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "E5F6A1B2-C3D4-7890-E5F6-A1B2C3D47890": // xAI
            return [
                LLMModel(
                    id: UUID(uuidString: "E1E2E3E4-F5F6-7890-E1E2-E3E4F5F67890")!,
                    providerId: providerId,
                    modelIdentifier: "grok-3-beta",
                    name: "Grok 3",
                    modelDescription: "xAI's latest model.",
                    logoImageName: "model_logo_xai_grok",
                    contextWindowSize: 131072,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "search_parameters"),
                    maxOutputTokens: 65536,
                    pricingInfo: "Input: $0.15/M tokens\nOutput: $0.60/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "E2E3E4E5-F6F7-7890-E2E3-E4E5F6F77890")!,
                    providerId: providerId,
                    modelIdentifier: "grok-3-mini-beta",
                    name: "Grok 3 Mini",
                    modelDescription: "xAI's latest mini model.",
                    logoImageName: "model_logo_xai_grok",
                    contextWindowSize: 131072,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .reasoningEffort, parameterName: "reasoning_effort", parameterOptions: ["low", "high"]),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "search_parameters"),
                    maxOutputTokens: 65536,
                    pricingInfo: "Input: $0.15/M tokens\nOutput: $0.60/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "F6A1B2C3-D4E5-7890-F6A1-B2C3D4E57890": // Qwen
            return [
                LLMModel(
                    id: UUID(uuidString: "F1F2F3F4-A5A6-7890-F1F2-F3F4A5A67890")!,
                    providerId: providerId,
                    modelIdentifier: "qwen3-235b-a22b",
                    name: "Qwen3 235B A22B",
                    modelDescription: "Qwen's latest SOTA model.",
                    logoImageName: "provider_logo_qwen",
                    contextWindowSize: 131072,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "enable_thinking"),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 129024,
                    pricingInfo: "Input: $0.7/M tokens\nOutput: $2.8/M tokens (non-thinking) or $8.4/M tokens (thinking)",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "A2B3C4D5-E6F7-7890-A2B3-C4D5E6F77890": // OpenRouter
            return [
                LLMModel(
                    id: UUID(uuidString: "A9A9A9A9-B9B9-7890-A9A9-A9A9B9B97890")!,
                    providerId: providerId,
                    modelIdentifier: "deepseek/deepseek-chat-v3-0324:free",
                    name: "DeepSeek V3 0324 free",
                    modelDescription: "DeepSeek's latest non-reasoning model.",
                    logoImageName: "provider_logo_deepseek",
                    contextWindowSize: 164000,
                    inputModalities: [.text, .image],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "plugins"),
                    maxOutputTokens: 164000,
                    pricingInfo: "Input: $0/M tokens\nOutput: $0/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "A9A9A9A9-B8B8-7890-A9A9-A9A9B8B87890")!,
                    providerId: providerId,
                    modelIdentifier: "deepseek/deepseek-r1-0528:free",
                    name: "DeepSeek R1 0528 free",
                    modelDescription: "DeepSeek's latest reasoning model.",
                    logoImageName: "provider_logo_deepseek",
                    contextWindowSize: 164000,
                    inputModalities: [.text, .image],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "plugins"),
                    maxOutputTokens: 164000,
                    pricingInfo: "Input: $0/M tokens\nOutput: $0/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                ),
                LLMModel(
                    id: UUID(uuidString: "A9A9A9A9-B7B7-7890-A9A9-A9A9B7B77890")!,
                    providerId: providerId,
                    modelIdentifier: "google/gemini-2.0-flash-exp:free",
                    name: "Gemini 2.0 Flash Exp free",
                    modelDescription: "Gemini 2.0 Flash Exp free",
                    logoImageName: "provider_logo_google",
                    contextWindowSize: 1048576,
                    inputModalities: [.text, .image],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                    searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "plugins"),
                    maxOutputTokens: 8192,
                    pricingInfo: "Input: $0/M tokens\nOutput: $0/M tokens",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "A3B4C5D6-E7F8-7890-A3B4-C5D6E7F87890": // Nvidia
            return [
                LLMModel(
                    id: UUID(uuidString: "A3A3A3A3-B4B4-7890-A3A3-A3A3B4B47890")!,
                    providerId: providerId,
                    modelIdentifier: "deepseek-ai/deepseek-r1-0528",
                    name: "DeepSeek R1 0528",
                    modelDescription: "DeepSeek's latest reasoning model.",
                    logoImageName: "provider_logo_deepseek",
                    contextWindowSize: 128000,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 128000,
                    pricingInfo: "Free Up to 40 RPM",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        case "A4B5C6D7-E8F9-7890-A4B5-C6D7E8F97890": // SiliconFlow
            return [
                LLMModel(
                    id: UUID(uuidString: "A4A4A4A4-B5B5-7890-A4A4-A4A4B5B57890")!,
                    providerId: providerId,
                    modelIdentifier: "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                    name: "DeepSeek R1 0528 Qwen3-8B",
                    modelDescription: "Qwen3-8B distilled from DeepSeek's latest reasoning model R1 0528.",
                    logoImageName: "provider_logo_deepseek",
                    contextWindowSize: 128000,
                    inputModalities: [.text],
                    outputModalities: [.text],
                    thinkingCapabilities: ThinkingCapabilities(controlType: .thinkingBudget, parameterName: "thinking_budget", maxBudget: 32768),
                    searchingCapabilities: SearchingCapabilities(controlType: .none),
                    maxOutputTokens: 128000,
                    pricingInfo: "Free Up to 1000 RPM and 50000 TPM",
                    availabilityStatus: .available,
                    isDefaultRecommendation: true,
                    isUserCreated: false,
                    metadata: nil
                )
            ]
        default:
            return []
        }
    }
    
    /// Get a built-in instance for a specific model
    private static func getBuiltinInstanceForModel(model: LLMModel, namespacePrefix: String = "lavachat.instance.from.model.") -> LLMInstance {
        // Get the provider
        let provider = builtinProviders.first(where: { $0.id == model.providerId })

        // Create a deterministic UUID based on the model ID
        let instanceId = deterministicUUID(from: model.id, namespacePrefix: namespacePrefix)

        // Use the shared factory method from LLMInstance
        return LLMInstance.createDefaultInstance(
            for: model,
            provider: provider,
            instanceId: instanceId,
            isUserModified: false
        )
    }

    /// Built-in special instances
    private static func getBuiltinSpecialInstances() -> [LLMInstance] {
        var instances: [LLMInstance] = []
        let geminiModels = getBuiltinModelsForProvider(providerId: UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!)

        // Gemini 2.5 Flash - RIPER-5
        let geminiFlashModel = geminiModels.first(where: { $0.name == "Gemini 2.5 Flash" })
        guard let geminiFlashModel = geminiFlashModel else {
            return []
        }
        var instance1 = getBuiltinInstanceForModel(model: geminiFlashModel, namespacePrefix: "lavachat.custom.instance.from.model.")
        instance1.name = "RIPER-5"
        instance1.systemPrompt = BuiltinPrompt.builtinCustomInstanceSystemPrompt["RIPER-5"]!
        instances.append(instance1)

        // Gemini 2.5 Pro - RIPER-5+
        let geminiProModel = geminiModels.first(where: { $0.name == "Gemini 2.5 Pro" })
        guard let geminiProModel = geminiProModel else {
            return []
        }
        var instance2 = getBuiltinInstanceForModel(model: geminiProModel, namespacePrefix: "lavachat.custom.instance.from.model.")
        instance2.name = "RIPER-5+"
        instance2.systemPrompt = BuiltinPrompt.builtinCustomInstanceSystemPrompt["RIPER-5+"]!
        instances.append(instance2)

        return instances
    }

    // MARK: - Method to get a specific built-in default instance
    
    /// Retrieves a built-in default LLMInstance for a given model ID.
    /// - Parameter modelId: The UUID of the `LLMModel`.
    /// - Returns: An optional `LLMInstance` if a corresponding built-in model and its default instance can be found; otherwise, `nil`.
    public static func getBuiltinDefaultLLMInstance(forModelId modelId: UUID) -> LLMInstance? {
        for provider in builtinProviders {
            let models = getBuiltinModelsForProvider(providerId: provider.id)
            if let model = models.first(where: { $0.id == modelId }) {
                return getBuiltinInstanceForModel(model: model)
            }
        }
        return nil
    }
    
    // MARK: - Initialization Logic
    
    /// Initializes built-in Providers, Models and Instance if needed
    /// - Parameters:
    ///   - repository: Repository to use for creating and updating data
    ///   - appSettingsRepository: Repository to use for version tracking
    static func initializeIfNeeded(repository: LLMInstanceRepository, appSettingsRepository: AppSettingsRepository) async {
        // Get stored version or default to 0 if not found
        let storedVersion = appSettingsRepository.getBuiltinLLMVersion()
        
        // Check if initialization or update is needed
        guard storedVersion < currentBuiltinLLMVersion else {
            print("Built-in provider/model data is already at version \(storedVersion), no update needed")
            return
        }
        
        print("Updating built-in provider/model data from version \(storedVersion) to \(currentBuiltinLLMVersion)")
        await repository.beginBatchUpdate()

        do {
            // STEP 1: Get existing built-in providers from the database
            let existingProviders = try await repository.getProviders(isUserCreated: false)
            
            // Convert to dictionary for easy lookup
            var existingProvidersDict = [UUID: LLMProvider]()
            for provider in existingProviders {
                existingProvidersDict[provider.id] = provider
            }
            
            // STEP 2: Process providers - create new ones or update existing ones
            for provider in builtinProviders {
                if let existingProvider = existingProvidersDict[provider.id] {
                    // Update if need update and user did not modified
                    if !existingProvider.isUserModified && needsUpdate(existingProvider: existingProvider, newProvider: provider) {
                        // Keep isUserModified as false for system updates
                        var updatedProvider = provider
                        updatedProvider.isUserModified = false
                        try await repository.updateProvider(updatedProvider)
                        print("Updated provider: \(provider.name)")
                    } else if existingProvider.isUserModified {
                        // For user-modified providers, only update critical fields
                        var updatedProvider = existingProvider
                        // Preserve user modifications but update essential system fields
                        updatedProvider.apiBaseUrl = provider.apiBaseUrl
                        updatedProvider.apiEndpointPath = provider.apiEndpointPath
                        updatedProvider.providerType = provider.providerType
                        // Keep isUserModified as true
                        try await repository.updateProvider(updatedProvider)
                        print("Partially updated user-modified provider: \(existingProvider.name)")
                    }
                } else {
                    // Create new provider
                    // Ensure isUserModified is false for new system providers
                    var newProvider = provider
                    newProvider.isUserModified = false
                    try await repository.createProvider(newProvider)
                    print("Created new provider: \(provider.name)")
                }
            }
            
            // STEP 3: Process models for each provider
            for provider in builtinProviders {
                // Get models for this provider
                let builtinModels = getBuiltinModelsForProvider(providerId: provider.id)
                
                // Skip if no models defined for this provider
                if builtinModels.isEmpty {
                    continue
                }
                
                // Get existing models for this provider
                let existingModels = try await repository.getAllModels(for: provider.id)
                
                // Convert to dictionary for easy lookup
                var existingModelsDict = [UUID: LLMModel]()
                for model in existingModels {
                    existingModelsDict[model.id] = model
                }
                
                // Create set of model IDs in the current version
                var currentModelIds = Set<UUID>()
                
                // Process each built-in model
                for model in builtinModels {
                    currentModelIds.insert(model.id)
                    
                    if let existingModel = existingModelsDict[model.id] {
                        // Update if need update and user did not modified
                        if !existingModel.isUserModified && needsUpdate(existingModel: existingModel, newModel: model) {
                            // Keep isUserModified as false for system updates
                            var updatedModel = model
                            updatedModel.isUserModified = false
                            try await repository.updateModel(updatedModel)
                            print("Updated model: \(model.name) for provider \(provider.name)")
                        // } else if existingModel.isUserModified {
                        //     // For user-modified models, only update critical fields
                        //     var updatedModel = existingModel
                        //     // Preserve user modifications but update essential system fields
                        //     updatedModel.contextWindowSize = model.contextWindowSize
                        //     updatedModel.inputModalities = model.inputModalities
                        //     updatedModel.outputModalities = model.outputModalities
                        //     updatedModel.availabilityStatus = model.availabilityStatus
                        //     updatedModel.thinkingCapabilities = model.thinkingCapabilities
                        //     updatedModel.maxOutputTokens = model.maxOutputTokens
                        //     // Keep isUserModified as true
                        //     try await repository.updateModel(updatedModel)
                        //     print("Partially updated user-modified model: \(existingModel.name)")
                        }
                    } else {
                        // Create new model
                        // Ensure isUserModified is false for new system models
                        var newModel = model
                        newModel.isUserModified = false
                        try await repository.createModel(newModel)
                        print("Created new model: \(model.name) for provider \(provider.name)")
                    }
                }
                
                // STEP 4: Handle unavailable models
                for existingModel in existingModels {
                    if !currentModelIds.contains(existingModel.id) {
                        // Model has been removed in new version, mark as unavailable
                        // but preserve user modifications
                        var modelToUpdate = existingModel
                        modelToUpdate.availabilityStatus = .unavailable
                        try await repository.updateModel(modelToUpdate)
                        print("Marked model as unavailable: \(existingModel.name)")
                    }
                }
            }
            
            // STEP 5: Create default instances for each model
            // Get all existing instances
            let existingInstances = try await repository.getAllInstances()
            var existingInstancesDict = [UUID: LLMInstance]()
            for instance in existingInstances {
                existingInstancesDict[instance.id] = instance
            }
            
            // Get all active models to create instances
            for provider in builtinProviders {
                let models = try await repository.getAllModels(for: provider.id)
                
                for model in models where model.availabilityStatus == .available {
                    // Create a default instance for each available model
                    let builtinInstance = getBuiltinInstanceForModel(model: model)
                    
                    if let existingInstance = existingInstancesDict[builtinInstance.id] {
                        // Only update if not user modified
                        if !existingInstance.isUserModified {
                            try await repository.updateInstance(builtinInstance)
                            print("Updated default instance for model: \(model.name)")
                        }
                    } else {
                        // Create new instance
                        try await repository.createInstance(builtinInstance)
                        print("Created default instance for model: \(model.name)")
                    }
                }
            }
            
            // STEP 6: Create special instances
            let specialInstances = getBuiltinSpecialInstances()
            for instance in specialInstances {
                if let existingInstance = existingInstancesDict[instance.id] {
                    // Only update if not user modified
                    if !existingInstance.isUserModified {
                        try await repository.updateInstance(instance)
                        print("Updated special instance: \(instance.name)")
                    }
                } else {
                    // Create new instance
                    try await repository.createInstance(instance)
                    print("Created special instance: \(instance.name)")
                }
            }
            
            // Update the stored version number
            appSettingsRepository.setBuiltinLLMVersion(currentBuiltinLLMVersion)
            print("Successfully updated built-in provider/model data to version \(currentBuiltinLLMVersion)")
            
        } catch {
            print("Error initializing built-in Providers, Models and Instance: \(error.localizedDescription)")
        }

        do {
            try await repository.endBatchUpdateAndPublish() // <<< End batch update and publish one signal
        } catch {
            // This catch is for errors specifically from endBatchUpdateAndPublish itself (e.g., if it tried a save that failed)
            print("Error during repository.endBatchUpdateAndPublish: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Helper Methods

    /// Creates a deterministic UUID from another UUID
    /// - Parameter sourceUUID: The source UUID to derive from
    /// - Parameter namespacePrefix: The namespace prefix to use for the input data
    /// - Returns: A new UUID that is deterministic based on the input UUID
    public static func deterministicUUID(from sourceUUID: UUID, namespacePrefix: String = "lavachat.instance.from.model.") -> UUID {
        // Combine the namespace with the UUID string to create our input data
        let inputString = namespacePrefix + sourceUUID.uuidString
        let inputData = Data(inputString.utf8)
        
        // Use SHA256 to create a deterministic hash
        let hashData = SHA256.hash(data: inputData)
        
        // Convert first 16 bytes of the hash to a uuid_t tuple
        var uuidBytes: uuid_t = (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        // Copy first 16 bytes of hash to the UUID bytes
        let hashBytes = Array(hashData)
        for i in 0..<min(16, hashBytes.count) {
            withUnsafeMutablePointer(to: &uuidBytes) { ptr in
                let tuplePtr = ptr.withMemoryRebound(to: UInt8.self, capacity: 16) { $0 }
                tuplePtr[i] = hashBytes[i]
            }
        }
        
        // Create UUID from the bytes
        return UUID(uuid: uuidBytes)
    }
    
    /// Check if a provider needs to be updated
    private static func needsUpdate(existingProvider: LLMProvider, newProvider: LLMProvider) -> Bool {
        return existingProvider.name != newProvider.name ||
               existingProvider.logoImageName != newProvider.logoImageName ||
               existingProvider.websiteUrl != newProvider.websiteUrl ||
               existingProvider.apiDocumentationUrl != newProvider.apiDocumentationUrl ||
               existingProvider.providerType != newProvider.providerType ||
               existingProvider.apiKeyStored != newProvider.apiKeyStored ||
               existingProvider.apiBaseUrl != newProvider.apiBaseUrl ||
               existingProvider.metadata != newProvider.metadata
    }
    
    /// Check if a model needs to be updated
    private static func needsUpdate(existingModel: LLMModel, newModel: LLMModel) -> Bool {
        return existingModel.name != newModel.name ||
               existingModel.modelIdentifier != newModel.modelIdentifier ||
               existingModel.modelDescription != newModel.modelDescription ||
               existingModel.contextWindowSize != newModel.contextWindowSize ||
               existingModel.inputModalities != newModel.inputModalities ||
               existingModel.outputModalities != newModel.outputModalities ||
               existingModel.thinkingCapabilities != newModel.thinkingCapabilities ||
               existingModel.searchingCapabilities != newModel.searchingCapabilities ||
               existingModel.maxOutputTokens != newModel.maxOutputTokens ||
               existingModel.pricingInfo != newModel.pricingInfo ||
               existingModel.group != newModel.group ||
               existingModel.availabilityStatus != newModel.availabilityStatus ||
               existingModel.isDefaultRecommendation != newModel.isDefaultRecommendation ||
               existingModel.apiConfigsOverride != newModel.apiConfigsOverride ||
               existingModel.metadata != newModel.metadata
    }
}
