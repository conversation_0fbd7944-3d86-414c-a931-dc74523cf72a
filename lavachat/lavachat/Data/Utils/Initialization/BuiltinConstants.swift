import Foundation

/// Shared constants for built-in data initialization
/// Contains predefined UUIDs and other constants used across different initializers
struct BuiltinConstants {
    
    // MARK: - Chat Session Settings

    /// UUID for the system default chat session setting
    /// This is used by both BuiltinUserInitializer and BuiltinChatInitializer
    /// to ensure consistency between user's defaultChatSettingsId and the actual setting
    static let systemDefaultChatSessionSettingId = UUID(uuidString: "C142A374-5536-7778-C142-A37455367778")!

    /// UUID for the minimal chat session setting
    /// Provides a streamlined chat experience with fewer UI elements and actions
    static let minimalChatSessionSettingId = UUID(uuidString: "C142A374-5537-7779-C142-A37455377779")!
    
    // MARK: - Future Constants
    
    // Additional shared constants can be added here as needed
    // For example:
    // - Default LLM instance IDs
    // - Built-in message action IDs
    // - System prompt IDs
}
