import Foundation
import Security

/// Helper class for secure Keychain operations
class KeychainHelper {
    
    /// Generic function to save a string to Keychain
    /// - Parameters:
    ///   - key: The key to save the string under
    ///   - string: The string to save
    ///   - service: The service identifier
    /// - Throws: KeychainError if operation fails
    static func saveString(_ string: String, for key: String, withService service: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidData
        }
        
        // Create a query dictionary for the keychain
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        // Delete any existing item first
        SecItemDelete(query as CFDictionary)
        
        // Add the item to keychain
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecDuplicateItem {
            throw KeychainError.duplicateItem
        } else if status == errSecInteractionNotAllowed {
            throw KeychainError.interactionNotAllowed
        } else if status == errSecNotAvailable {
            throw KeychainError.keychainNotAvailable
        } else if status != errSecSuccess {
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// Generic function to retrieve a string from Keychain
    /// - Parameters:
    ///   - key: The key to retrieve the string for
    ///   - service: The service identifier
    /// - Returns: The retrieved string or nil if not found
    /// - Throws: KeychainError if operation fails
    static func getString(for key: String, withService service: String) throws -> String? {
        // Create a query dictionary
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecMatchLimit as String: kSecMatchLimitOne,
            kSecReturnData as String: true
        ]
        
        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)
        
        guard status != errSecItemNotFound else {
            return nil
        }
        
        if status == errSecInteractionNotAllowed {
            throw KeychainError.interactionNotAllowed
        } else if status == errSecNotAvailable {
            throw KeychainError.keychainNotAvailable
        } else if status != errSecSuccess {
            throw KeychainError.unexpectedStatus(status)
        }
        
        guard let data = item as? Data, let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.invalidData
        }
        
        return string
    }
    
    /// Generic function to update a string in Keychain
    /// - Parameters:
    ///   - key: The key to update the string for
    ///   - string: The new string value
    ///   - service: The service identifier
    /// - Throws: KeychainError if operation fails
    static func updateString(_ string: String, for key: String, withService service: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidData
        }
        
        // Create query dictionary to find the item
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        // Create a dictionary with the attributes to update
        let attributes: [String: Any] = [
            kSecValueData as String: data
        ]
        
        // Update the item in keychain
        let status = SecItemUpdate(query as CFDictionary, attributes as CFDictionary)
        
        guard status != errSecItemNotFound else {
            // If item doesn't exist, save it instead
            try saveString(string, for: key, withService: service)
            return
        }
        
        if status == errSecInteractionNotAllowed {
            throw KeychainError.interactionNotAllowed
        } else if status == errSecNotAvailable {
            throw KeychainError.keychainNotAvailable
        } else if status != errSecSuccess {
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// Generic function to delete a string from Keychain
    /// - Parameters:
    ///   - key: The key to delete
    ///   - service: The service identifier
    /// - Throws: KeychainError if operation fails
    static func deleteString(for key: String, withService service: String) throws {
        // Create query dictionary to find the item
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        // Delete the item from keychain
        let status = SecItemDelete(query as CFDictionary)
        
        if status == errSecItemNotFound {
            // Item not found is not an error for deletion
            return
        }
        
        if status == errSecInteractionNotAllowed {
            throw KeychainError.interactionNotAllowed
        } else if status == errSecNotAvailable {
            throw KeychainError.keychainNotAvailable
        } else if status != errSecSuccess {
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// Check if a string exists in Keychain
    /// - Parameters:
    ///   - key: The key to check
    ///   - service: The service identifier
    /// - Returns: True if the key exists, false otherwise
    /// - Throws: KeychainError if operation fails
    static func stringExists(for key: String, withService service: String) throws -> Bool {
        // Create a query dictionary
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecMatchLimit as String: kSecMatchLimitOne,
            kSecReturnData as String: false
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        
        if status == errSecItemNotFound {
            return false
        }
        
        if status == errSecSuccess {
            return true
        }
        
        if status == errSecInteractionNotAllowed {
            throw KeychainError.interactionNotAllowed
        } else if status == errSecNotAvailable {
            throw KeychainError.keychainNotAvailable
        } else {
            throw KeychainError.unexpectedStatus(status)
        }
    }
} 