import Foundation
import CoreData

/// Utility class with static methods to help convert between Domain entities and Core Data entities
class EntityMapper {
    
    /// Encodes a Codable object to Data
    /// - Parameter value: The Codable object to encode
    /// - Returns: The encoded Data, or nil if encoding fails
    static func encode<T: Encodable>(_ value: T) -> Data? {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .sortedKeys
            return try encoder.encode(value)
        } catch {
            print("Error encoding \(T.self): \(error)")
            return nil
        }
    }
    
    /// Decodes Data into a Codable object
    /// - Parameter data: The Data to decode
    /// - Returns: The decoded object, or nil if decoding fails
    static func decode<T: Decodable>(_ data: Data?) -> T? {
        guard let data = data else { return nil }

        if data == Data("null".utf8) {
            return nil
        }
        
        do {
            return try JSONDecoder().decode(T.self, from: data)
        } catch {
            let dataString = String(data: data, encoding: .utf8) ?? "not a valid UTF-8 string or data is not decodable to String"
            print("Error decoding \(T.self): \(error). Data content (if UTF-8): '\(dataString)'. Data: \(data)")
            return nil
        }
    }
    
    /// Converts a UUID to a string
    static func uuidString(_ uuid: UUID?) -> String? {
        return uuid?.uuidString
    }
    
    /// Converts a string to a UUID
    static func uuid(from string: String?) -> UUID? {
        guard let string = string else { return nil }
        return UUID(uuidString: string)
    }
    
    /// Converts a list of UUIDs to Data
    static func encodeUUIDs(_ uuids: [UUID]?) -> Data? {
        guard let uuids = uuids else { return nil }
        return encode(uuids.map { $0.uuidString })
    }
    
    /// Converts Data to a list of UUIDs
    static func decodeUUIDs(_ data: Data?) -> [UUID]? {
        guard let data = data else { return nil }
        
        do {
            let strings = try JSONDecoder().decode([String].self, from: data)
            return strings.compactMap { UUID(uuidString: $0) }
        } catch {
            print("Error decoding UUIDs: \(error)")
            return nil
        }
    }
} 