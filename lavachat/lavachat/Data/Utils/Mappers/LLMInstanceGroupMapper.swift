import Foundation
import CoreData

/// Mapper for converting between LLMInstanceGroup domain entity and CDLLMInstanceGroup Core Data entity
struct LLMInstanceGroupMapper {
    
    /// Converts a CDLLMInstanceGroup entity to a domain LLMInstanceGroup entity
    /// - Parameter cdGroup: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdGroup: CDLLMInstanceGroup) -> LLMInstanceGroup {
        let metadata: [String: String] = EntityMapper.decode(cdGroup.metadata) ?? [:]
        let instanceIds: [UUID] = EntityMapper.decodeUUIDs(cdGroup.instanceIds) ?? []
        
        return LLMInstanceGroup(
            id: cdGroup.id ?? UUID(),
            name: cdGroup.name ?? "",
            description: cdGroup.description_,
            isFavorited: cdGroup.isFavorited,
            customLogoData: cdGroup.customLogoData,
            instanceIds: instanceIds,
            createdAt: cdGroup.createdAt ?? Date(),
            lastModifiedAt: cdGroup.lastModifiedAt ?? Date(),
            metadata: metadata
        )
    }
    
    /// Converts a domain LLMInstanceGroup entity to a CDLLMInstanceGroup entity
    /// - Parameters:
    ///   - group: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(group: LLMInstanceGroup, context: NSManagedObjectContext) -> CDLLMInstanceGroup {
        let cdGroup = CDLLMInstanceGroup(context: context)
        
        cdGroup.id = group.id
        cdGroup.name = group.name
        cdGroup.description_ = group.description
        cdGroup.isFavorited = group.isFavorited
        cdGroup.customLogoData = group.customLogoData
        cdGroup.instanceIds = EntityMapper.encodeUUIDs(group.instanceIds)
        cdGroup.createdAt = group.createdAt
        cdGroup.lastModifiedAt = group.lastModifiedAt
        cdGroup.metadata = EntityMapper.encode(group.metadata)
        
        return cdGroup
    }
    
    /// Updates an existing CDLLMInstanceGroup entity with values from a domain LLMInstanceGroup entity
    /// - Parameters:
    ///   - cdGroup: The Core Data entity to update
    ///   - group: The domain entity with the new values
    static func updateCoreData(cdGroup: CDLLMInstanceGroup, with group: LLMInstanceGroup) {
        // Don't update the ID as that's the primary key
        cdGroup.name = group.name
        cdGroup.description_ = group.description
        cdGroup.isFavorited = group.isFavorited
        cdGroup.customLogoData = group.customLogoData
        cdGroup.instanceIds = EntityMapper.encodeUUIDs(group.instanceIds)
        cdGroup.lastModifiedAt = group.lastModifiedAt
        cdGroup.metadata = EntityMapper.encode(group.metadata)
    }
}