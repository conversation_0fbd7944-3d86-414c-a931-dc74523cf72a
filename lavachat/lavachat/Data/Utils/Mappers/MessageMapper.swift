import Foundation
import CoreData

/// Mapper for converting between Message domain entity and CDMessage Core Data entity
struct MessageMapper {
    
    /// Converts a CDMessage entity to a domain Message entity
    /// - Parameter cdMessage: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdMessage: CDMessage) -> Message {
        let content: [ContentBlock] = EntityMapper.decode(cdMessage.content) ?? []
        let metadata: [String: String] = EntityMapper.decode(cdMessage.metadata) ?? [:]
        let parsedFileOperations: [FileOperation] = EntityMapper.decode(cdMessage.parsedFileOperations) ?? []
        
        return Message(
            id: cdMessage.id ?? UUID(),
            sessionId: cdMessage.sessionId ?? UUID(),
            parentId: cdMessage.parentId,
            timestamp: cdMessage.timestamp ?? Date(),
            role: MessageRole(rawValue: cdMessage.role ?? "user") ?? .user,
            content: content,
            depth: cdMessage.depth,
            rawResponseText: cdMessage.rawResponseText,
            llmInstanceId: cdMessage.llmInstanceId,
            promptTokens: cdMessage.promptTokens,
            completionTokens: cdMessage.completionTokens,
            userId: cdMessage.userId,
            status: MessageStatus(rawValue: cdMessage.status ?? "received") ?? .received,
            userFeedback: MessageFeedback(rawValue: cdMessage.userFeedback ?? "none") ?? .none,
            isReplied: cdMessage.isReplied,
            isFavorited: cdMessage.isFavorited,
            parsedFileOperations: parsedFileOperations,
            metadata: metadata
        )
    }
    
    /// Converts a domain Message entity to a CDMessage entity
    /// - Parameters:
    ///   - message: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(message: Message, context: NSManagedObjectContext) -> CDMessage {
        let cdMessage = CDMessage(context: context)
        
        cdMessage.id = message.id
        cdMessage.sessionId = message.sessionId
        cdMessage.userId = message.userId
        cdMessage.parentId = message.parentId
        cdMessage.role = message.role.rawValue
        cdMessage.content = EntityMapper.encode(message.content)
        cdMessage.depth = message.depth
        cdMessage.rawResponseText = message.rawResponseText
        cdMessage.timestamp = message.timestamp
        cdMessage.status = message.status.rawValue
        cdMessage.llmInstanceId = message.llmInstanceId
        cdMessage.promptTokens = message.promptTokens ?? 0
        cdMessage.completionTokens = message.completionTokens ?? 0
        cdMessage.isReplied = message.isReplied
        cdMessage.isFavorited = message.isFavorited
        cdMessage.userFeedback = message.userFeedback.rawValue
        cdMessage.parsedFileOperations = EntityMapper.encode(message.parsedFileOperations)
        cdMessage.metadata = EntityMapper.encode(message.metadata)
        
        return cdMessage
    }
    
    /// Updates an existing CDMessage entity with values from a domain Message entity
    /// - Parameters:
    ///   - cdMessage: The Core Data entity to update
    ///   - message: The domain entity with the new values
    static func updateCoreData(cdMessage: CDMessage, with message: Message) {
        // Don't update the ID as that's the primary key
        cdMessage.userId = message.userId
        cdMessage.parentId = message.parentId
        cdMessage.role = message.role.rawValue
        cdMessage.content = EntityMapper.encode(message.content)
        cdMessage.depth = message.depth
        cdMessage.rawResponseText = message.rawResponseText
        cdMessage.timestamp = message.timestamp
        cdMessage.status = message.status.rawValue
        cdMessage.llmInstanceId = message.llmInstanceId
        cdMessage.promptTokens = message.promptTokens ?? cdMessage.promptTokens
        cdMessage.completionTokens = message.completionTokens ?? cdMessage.completionTokens
        cdMessage.isReplied = message.isReplied
        cdMessage.isFavorited = message.isFavorited
        cdMessage.userFeedback = message.userFeedback.rawValue
        cdMessage.parsedFileOperations = EntityMapper.encode(message.parsedFileOperations)
        cdMessage.metadata = EntityMapper.encode(message.metadata)
    }
} 
