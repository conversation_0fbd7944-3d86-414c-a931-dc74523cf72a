import Foundation
import CoreData

/// Mapper for converting between LLMInstance domain entity and CDLLMInstance Core Data entity
struct LLMInstanceMapper {
    
    /// Converts a CDLLMInstance entity to a domain LLMInstance entity
    /// - Parameter cdInstance: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdInstance: CDLLMInstance) -> LLMInstance {
        let metadata: [String: String] = EntityMapper.decode(cdInstance.metadata) ?? [:]
        let defaultParameters: [String: String] = EntityMapper.decode(cdInstance.defaultParameters) ?? [:]
        
        return LLMInstance(
            id: cdInstance.id ?? UUID(),
            modelId: cdInstance.modelId ?? UUID(),
            name: cdInstance.name ?? "",
            customLogoData: cdInstance.customLogoData,
            systemPrompt: cdInstance.systemPrompt,
            defaultParameters: defaultParameters,
            totalPromptTokensUsed: cdInstance.totalPromptTokensUsed,
            totalCompletionTokensUsed: cdInstance.totalCompletionTokensUsed,
            createdAt: cdInstance.createdAt ?? Date(),
            lastUsedAt: cdInstance.lastUsedAt,
            isFavorited: cdInstance.isFavorited,
            isUserModified: cdInstance.isUserModified,
            metadata: metadata
        )
    }
    
    /// Converts a domain LLMInstance entity to a CDLLMInstance entity
    /// - Parameters:
    ///   - instance: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(instance: LLMInstance, context: NSManagedObjectContext) -> CDLLMInstance {
        let cdInstance = CDLLMInstance(context: context)
        
        cdInstance.id = instance.id
        cdInstance.name = instance.name
        cdInstance.modelId = instance.modelId
        cdInstance.customLogoData = instance.customLogoData
        cdInstance.systemPrompt = instance.systemPrompt
        cdInstance.defaultParameters = EntityMapper.encode(instance.defaultParameters)
        cdInstance.createdAt = instance.createdAt
        cdInstance.lastUsedAt = instance.lastUsedAt
        cdInstance.totalPromptTokensUsed = instance.totalPromptTokensUsed
        cdInstance.totalCompletionTokensUsed = instance.totalCompletionTokensUsed
        cdInstance.isFavorited = instance.isFavorited
        cdInstance.isUserModified = instance.isUserModified
        cdInstance.metadata = EntityMapper.encode(instance.metadata)
        
        return cdInstance
    }
    
    /// Updates an existing CDLLMInstance entity with values from a domain LLMInstance entity
    /// - Parameters:
    ///   - cdInstance: The Core Data entity to update
    ///   - instance: The domain entity with the new values
    static func updateCoreData(cdInstance: CDLLMInstance, with instance: LLMInstance) {
        // Don't update the ID as that's the primary key
        cdInstance.name = instance.name
        cdInstance.modelId = instance.modelId
        cdInstance.customLogoData = instance.customLogoData
        cdInstance.systemPrompt = instance.systemPrompt
        cdInstance.defaultParameters = EntityMapper.encode(instance.defaultParameters)
        cdInstance.lastUsedAt = instance.lastUsedAt
        cdInstance.totalPromptTokensUsed = instance.totalPromptTokensUsed
        cdInstance.totalCompletionTokensUsed = instance.totalCompletionTokensUsed
        cdInstance.isFavorited = instance.isFavorited
        cdInstance.isUserModified = instance.isUserModified
        cdInstance.metadata = EntityMapper.encode(instance.metadata)
    }
}
