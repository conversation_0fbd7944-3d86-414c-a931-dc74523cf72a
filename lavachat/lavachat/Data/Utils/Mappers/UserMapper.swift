import Foundation
import CoreData

/// Mapper for converting between User domain entity and CDUser Core Data entity
struct UserMapper {
    
    /// Converts a CDUser entity to a domain User entity
    /// - Parameter cdUser: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdUser: CDUser) -> User {
        let metadata: [String: String] = EntityMapper.decode(cdUser.metadata) ?? [:]
        let appSettings: AppSettings? = EntityMapper.decode(cdUser.appSettings)

        return User(
            id: cdUser.id ?? UUID(),
            nickname: cdUser.nickname,
            avatarIdentifier: cdUser.avatarIdentifier,
            registrationDate: cdUser.registrationDate ?? Date(),
            lastSeenAt: cdUser.lastSeenAt,
            appSettings: appSettings,
            defaultChatSettingsId: cdUser.defaultChatSettingsId,
            systemUtilityLLMInstanceId: cdUser.systemUtilityLLMInstanceId,
            syncStatus: User.SyncStatus(rawValue: cdUser.syncStatus ?? "error") ?? .error,
            lastSyncTimestamp: cdUser.lastSyncTimestamp,
            memoryFileBookmarkData: cdUser.memoryFileBookmarkData,
            metadata: metadata
        )
    }
    
    /// Converts a domain User entity to a CDUser entity
    /// - Parameters:
    ///   - user: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(user: User, context: NSManagedObjectContext) -> CDUser {
        let cdUser = CDUser(context: context)
        
        cdUser.id = user.id
        cdUser.nickname = user.nickname
        cdUser.avatarIdentifier = user.avatarIdentifier
        cdUser.registrationDate = user.registrationDate
        cdUser.lastSeenAt = user.lastSeenAt
        cdUser.defaultChatSettingsId = user.defaultChatSettingsId
        cdUser.systemUtilityLLMInstanceId = user.systemUtilityLLMInstanceId
        cdUser.memoryFileBookmarkData = user.memoryFileBookmarkData
        cdUser.appSettings = EntityMapper.encode(user.appSettings)
        cdUser.lastSyncTimestamp = user.lastSyncTimestamp
        cdUser.syncStatus = user.syncStatus?.rawValue
        cdUser.metadata = EntityMapper.encode(user.metadata)
        
        return cdUser
    }
    
    /// Updates an existing CDUser entity with values from a domain User entity
    /// - Parameters:
    ///   - cdUser: The Core Data entity to update
    ///   - user: The domain entity with the new values
    static func updateCoreData(cdUser: CDUser, with user: User) {
        // Don't update the ID as that's the primary key
        cdUser.nickname = user.nickname
        cdUser.avatarIdentifier = user.avatarIdentifier
        cdUser.lastSeenAt = user.lastSeenAt
        cdUser.defaultChatSettingsId = user.defaultChatSettingsId
        cdUser.systemUtilityLLMInstanceId = user.systemUtilityLLMInstanceId
        cdUser.memoryFileBookmarkData = user.memoryFileBookmarkData
        cdUser.appSettings = EntityMapper.encode(user.appSettings)
        cdUser.lastSyncTimestamp = user.lastSyncTimestamp
        cdUser.syncStatus = user.syncStatus?.rawValue
        cdUser.metadata = EntityMapper.encode(user.metadata)
    }
} 
