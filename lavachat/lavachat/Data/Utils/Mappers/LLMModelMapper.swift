import Foundation
import CoreData

/// Mapper for converting between LLMModel domain entity and CDLLMModel Core Data entity
struct LLMModelMapper {
    
    /// Converts a CDLLMModel entity to a domain LLMModel entity
    /// - Parameter cdModel: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdModel: CDLLMModel) -> LLMModel {
        let metadata: [String: String] = EntityMapper.decode(cdModel.metadata) ?? [:]
        let inputModalities: [ModelModality] = EntityMapper.decode(cdModel.inputModalities) ?? [.text]
        let outputModalities: [ModelModality] = EntityMapper.decode(cdModel.outputModalities) ?? [.text]
        let availabilityStatus = cdModel.availabilityStatus.flatMap { LLMModel.ModelAvailabilityStatus(rawValue: $0) } ?? .available
        let thinkingCapabilities: ThinkingCapabilities? = EntityMapper.decode(cdModel.thinkingCapabilities)
        let searchingCapabilities: SearchingCapabilities? = EntityMapper.decode(cdModel.searchingCapabilities)
        let apiConfigsOverride: [ModelAPIConfig] = EntityMapper.decode(cdModel.apiConfigsOverride) ?? []
        
        return LLMModel(
            id: cdModel.id ?? UUID(),
            providerId: cdModel.providerId ?? UUID(),
            modelIdentifier: cdModel.modelIdentifier ?? "",
            name: cdModel.name ?? "",
            modelDescription: cdModel.modelDescription,
            logoImageName: cdModel.logoImageName,
            customLogoData: cdModel.customLogoData,
            contextWindowSize: cdModel.contextWindowSize,
            inputModalities: inputModalities,
            outputModalities: outputModalities,
            thinkingCapabilities: thinkingCapabilities,
            searchingCapabilities: searchingCapabilities,
            maxOutputTokens: cdModel.maxOutputTokens,
            pricingInfo: cdModel.pricingInfo,
            group: cdModel.group,
            availabilityStatus: availabilityStatus,
            isDefaultRecommendation: cdModel.isDefaultRecommendation,
            isUserCreated: cdModel.isUserCreated,
            isUserModified: cdModel.isUserModified,
            apiConfigsOverride: apiConfigsOverride,
            metadata: metadata
        )
    }
    
    /// Converts a domain LLMModel entity to a CDLLMModel entity
    /// - Parameters:
    ///   - model: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(model: LLMModel, context: NSManagedObjectContext) -> CDLLMModel {
        let cdModel = CDLLMModel(context: context)
        
        cdModel.id = model.id
        cdModel.providerId = model.providerId
        cdModel.name = model.name
        cdModel.modelIdentifier = model.modelIdentifier
        cdModel.modelDescription = model.modelDescription
        cdModel.logoImageName = model.logoImageName
        cdModel.customLogoData = model.customLogoData
        cdModel.contextWindowSize = model.contextWindowSize ?? 0
        cdModel.inputModalities = EntityMapper.encode(model.inputModalities)
        cdModel.outputModalities = EntityMapper.encode(model.outputModalities)
        cdModel.thinkingCapabilities = EntityMapper.encode(model.thinkingCapabilities)
        cdModel.searchingCapabilities = EntityMapper.encode(model.searchingCapabilities)
        cdModel.maxOutputTokens = model.maxOutputTokens ?? 0
        cdModel.isDefaultRecommendation = model.isDefaultRecommendation
        cdModel.isUserCreated = model.isUserCreated
        cdModel.isUserModified = model.isUserModified
        cdModel.availabilityStatus = model.availabilityStatus?.rawValue
        cdModel.pricingInfo = model.pricingInfo
        cdModel.group = model.group
        cdModel.apiConfigsOverride = EntityMapper.encode(model.apiConfigsOverride)
        cdModel.metadata = EntityMapper.encode(model.metadata)
        
        return cdModel
    }
    
    /// Updates an existing CDLLMModel entity with values from a domain LLMModel entity
    /// - Parameters:
    ///   - cdModel: The Core Data entity to update
    ///   - model: The domain entity with the new values
    static func updateCoreData(cdModel: CDLLMModel, with model: LLMModel) {
        // Don't update the ID as that's the primary key
        cdModel.providerId = model.providerId
        cdModel.name = model.name
        cdModel.modelIdentifier = model.modelIdentifier
        cdModel.modelDescription = model.modelDescription
        cdModel.logoImageName = model.logoImageName
        cdModel.customLogoData = model.customLogoData
        cdModel.contextWindowSize = model.contextWindowSize ?? cdModel.contextWindowSize
        cdModel.inputModalities = EntityMapper.encode(model.inputModalities)
        cdModel.outputModalities = EntityMapper.encode(model.outputModalities)
        cdModel.thinkingCapabilities = EntityMapper.encode(model.thinkingCapabilities)
        cdModel.searchingCapabilities = EntityMapper.encode(model.searchingCapabilities)
        cdModel.maxOutputTokens = model.maxOutputTokens ?? cdModel.maxOutputTokens
        cdModel.isDefaultRecommendation = model.isDefaultRecommendation
        cdModel.isUserCreated = model.isUserCreated
        cdModel.isUserModified = model.isUserModified
        cdModel.availabilityStatus = model.availabilityStatus?.rawValue
        cdModel.pricingInfo = model.pricingInfo
        cdModel.group = model.group
        cdModel.apiConfigsOverride = EntityMapper.encode(model.apiConfigsOverride)
        cdModel.metadata = EntityMapper.encode(model.metadata)
    }
}
