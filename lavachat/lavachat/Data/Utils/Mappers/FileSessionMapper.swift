import Foundation
import CoreData

/// Mapper for converting between FileSession domain entity and CDFileSession Core Data entity
struct FileSessionMapper {
    
    /// Converts a CDFileSession entity to a domain FileSession entity
    /// - Parameter cdFileSession: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdFileSession: CDFileSession) -> FileSession {
        let metadata: [String: String] = EntityMapper.decode(cdFileSession.metadata) ?? [:]
        let originalSourceInfo: OriginalSourceInfo? = EntityMapper.decode(cdFileSession.originalSourceInfo)
        
        return FileSession(
            id: cdFileSession.id ?? UUID(),
            userId: cdFileSession.userId ?? UUID(),
            originalSourceInfo: originalSourceInfo,
            title: cdFileSession.title ?? "",
            createdAt: cdFileSession.createdAt ?? Date(),
            lastModifiedAt: cdFileSession.lastModifiedAt ?? Date(),
            currentFileCheckpointId: cdFileSession.currentFileCheckpointId,
            rootMessageId: cdFileSession.rootMessageId,
            activeLLMInstanceId: cdFileSession.activeLLMInstanceId,
            settingsId: cdFileSession.settingsId,
            bookmarkData: cdFileSession.bookmarkData,
            metadata: metadata
        )
    }
    
    /// Converts a domain FileSession entity to a CDFileSession entity
    /// - Parameters:
    ///   - fileSession: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(fileSession: FileSession, context: NSManagedObjectContext) -> CDFileSession {
        let cdFileSession = CDFileSession(context: context)
        
        cdFileSession.id = fileSession.id
        cdFileSession.userId = fileSession.userId
        cdFileSession.title = fileSession.title
        cdFileSession.bookmarkData = fileSession.bookmarkData
        cdFileSession.originalSourceInfo = EntityMapper.encode(fileSession.originalSourceInfo)
        cdFileSession.createdAt = fileSession.createdAt
        cdFileSession.lastModifiedAt = fileSession.lastModifiedAt
        cdFileSession.currentFileCheckpointId = fileSession.currentFileCheckpointId
        cdFileSession.rootMessageId = fileSession.rootMessageId
        cdFileSession.activeLLMInstanceId = fileSession.activeLLMInstanceId
        cdFileSession.settingsId = fileSession.settingsId
        cdFileSession.metadata = EntityMapper.encode(fileSession.metadata)
        
        return cdFileSession
    }
    
    /// Updates an existing CDFileSession entity with values from a domain FileSession entity
    /// - Parameters:
    ///   - cdFileSession: The Core Data entity to update
    ///   - fileSession: The domain entity with the new values
    static func updateCoreData(cdFileSession: CDFileSession, with fileSession: FileSession) {
        // Don't update the ID as that's the primary key
        cdFileSession.userId = fileSession.userId
        cdFileSession.title = fileSession.title
        cdFileSession.bookmarkData = fileSession.bookmarkData
        cdFileSession.originalSourceInfo = EntityMapper.encode(fileSession.originalSourceInfo)
        cdFileSession.lastModifiedAt = fileSession.lastModifiedAt
        cdFileSession.currentFileCheckpointId = fileSession.currentFileCheckpointId
        cdFileSession.rootMessageId = fileSession.rootMessageId
        cdFileSession.activeLLMInstanceId = fileSession.activeLLMInstanceId
        cdFileSession.settingsId = fileSession.settingsId
        cdFileSession.metadata = EntityMapper.encode(fileSession.metadata)
    }
}
