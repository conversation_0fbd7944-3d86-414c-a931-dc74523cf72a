import Foundation
import CoreData

/// Mapper for converting between ChatSessionSetting domain entity and CDChatSessionSetting Core Data entity
struct ChatSessionSettingMapper {
    
    /// Converts a CDChatSessionSetting entity to a domain ChatSessionSetting entity
    /// - Parameter cdSetting: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdSetting: CDChatSessionSetting) -> ChatSessionSetting {
        let metadata: [String: String] = EntityMapper.decode(cdSetting.metadata) ?? [:]
        let llmParameterOverrides: [UUID: [String: String]] = EntityMapper.decode(cdSetting.llmParameterOverrides) ?? [:]
        let uiThemeSettings: UIThemeSettings = EntityMapper.decode(cdSetting.uiThemeSettings) ?? UIThemeSettings()
        let messageActionSettings: MessageActionSettings = EntityMapper.decode(cdSetting.messageActionSettings) ?? MessageActionSettings()
        let defaultContextServerIds: [UUID] = EntityMapper.decodeUUIDs(cdSetting.defaultContextServerIds) ?? []
        let savedPromptSegments: [SavedPromptSegment] = EntityMapper.decode(cdSetting.savedPromptSegments) ?? []
        
        return ChatSessionSetting(
            id: cdSetting.id ?? UUID(),
            name: cdSetting.name ?? "",
            isSystemDefault: cdSetting.isSystemDefault,
            createdAt: cdSetting.createdAt ?? Date(),
            lastModifiedAt: cdSetting.lastModifiedAt ?? Date(),
            llmParameterOverrides: llmParameterOverrides,
            defaultContextServerIds: defaultContextServerIds,
            shouldExpandThinking: cdSetting.shouldExpandThinking,
            uiThemeSettings: uiThemeSettings,
            messageActionSettings: messageActionSettings,
            savedPromptSegments: savedPromptSegments,
            auxiliaryLLMInstanceId: cdSetting.auxiliaryLLMInstanceId,
            shouldAutoGenerateTitle: cdSetting.shouldAutoGenerateTitle,
            contextMessageCount: cdSetting.contextMessageCount,
            metadata: metadata
        )
    }
    
    /// Converts a domain ChatSessionSetting entity to a CDChatSessionSetting entity
    /// - Parameters:
    ///   - setting: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(setting: ChatSessionSetting, context: NSManagedObjectContext) -> CDChatSessionSetting {
        let cdSetting = CDChatSessionSetting(context: context)
        cdSetting.id = setting.id
        cdSetting.createdAt = setting.createdAt
        updateCoreData(cdSetting: cdSetting, with: setting)
        return cdSetting
    }
    
    /// Updates an existing CDChatSessionSetting entity with values from a domain ChatSessionSetting entity
    /// - Parameters:
    ///   - cdSetting: The Core Data entity to update
    ///   - setting: The domain entity with the new values
    static func updateCoreData(cdSetting: CDChatSessionSetting, with setting: ChatSessionSetting) {
        // Don't update the ID as that's the primary key
        cdSetting.name = setting.name
        cdSetting.isSystemDefault = setting.isSystemDefault
        cdSetting.llmParameterOverrides = EntityMapper.encode(setting.llmParameterOverrides)
        cdSetting.shouldExpandThinking = setting.shouldExpandThinking
        cdSetting.uiThemeSettings = EntityMapper.encode(setting.uiThemeSettings)
        cdSetting.messageActionSettings = EntityMapper.encode(setting.messageActionSettings)
        cdSetting.defaultContextServerIds = EntityMapper.encodeUUIDs(setting.defaultContextServerIds)
        cdSetting.savedPromptSegments = EntityMapper.encode(setting.savedPromptSegments)
        cdSetting.auxiliaryLLMInstanceId = setting.auxiliaryLLMInstanceId
        cdSetting.shouldAutoGenerateTitle = setting.shouldAutoGenerateTitle
        cdSetting.contextMessageCount = setting.contextMessageCount
        cdSetting.lastModifiedAt = setting.lastModifiedAt
        cdSetting.metadata = EntityMapper.encode(setting.metadata)
    }
} 
