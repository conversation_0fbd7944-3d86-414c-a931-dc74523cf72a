import Foundation
import CoreData

/// Mapper for converting between FileCheckpoint domain entity and CDFileCheckpoint Core Data entity
struct FileCheckpointMapper {
    
    /// Converts a CDFileCheckpoint entity to a domain FileCheckpoint entity
    /// - Parameter cdCheckpoint: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdCheckpoint: CDFileCheckpoint) -> FileCheckpoint {
        let metadata: [String: String] = EntityMapper.decode(cdCheckpoint.metadata) ?? [:]
        let pendingEditStates: [PendingEditInfo] = EntityMapper.decode(cdCheckpoint.pendingEditStates) ?? []
        
        return FileCheckpoint(
            id: cdCheckpoint.id ?? UUID(),
            fileSessionId: cdCheckpoint.fileSessionId ?? UUID(),
            timestamp: cdCheckpoint.timestamp ?? Date(),
            basedOnCheckpointId: cdCheckpoint.basedOnCheckpointId,
            triggeringMessageId: cdCheckpoint.triggeringMessageId,
            fileContent: cdCheckpoint.fileContent ?? "",
            pendingEditStates: pendingEditStates,
            metadata: metadata
        )
    }
    
    /// Converts a domain FileCheckpoint entity to a CDFileCheckpoint entity
    /// - Parameters:
    ///   - checkpoint: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(checkpoint: FileCheckpoint, context: NSManagedObjectContext) -> CDFileCheckpoint {
        let cdCheckpoint = CDFileCheckpoint(context: context)
        
        cdCheckpoint.id = checkpoint.id
        cdCheckpoint.fileSessionId = checkpoint.fileSessionId
        cdCheckpoint.timestamp = checkpoint.timestamp
        cdCheckpoint.fileContent = checkpoint.fileContent
        cdCheckpoint.basedOnCheckpointId = checkpoint.basedOnCheckpointId
        cdCheckpoint.triggeringMessageId = checkpoint.triggeringMessageId
        cdCheckpoint.pendingEditStates = EntityMapper.encode(checkpoint.pendingEditStates)
        cdCheckpoint.metadata = EntityMapper.encode(checkpoint.metadata)
        
        // Link to file session
        let sessionFetchRequest: NSFetchRequest<CDFileSession> = CDFileSession.fetchRequest()
        sessionFetchRequest.predicate = NSPredicate(format: "id == %@", checkpoint.fileSessionId as CVarArg)
        sessionFetchRequest.fetchLimit = 1
        
        do {
            let sessions = try context.fetch(sessionFetchRequest)
            if let fileSession = sessions.first {
                cdCheckpoint.fileSession = fileSession
            }
        } catch {
            print("Error fetching file session: \(error)")
        }
        
        return cdCheckpoint
    }
    
    /// Updates an existing CDFileCheckpoint entity with values from a domain FileCheckpoint entity
    /// - Parameters:
    ///   - cdCheckpoint: The Core Data entity to update
    ///   - checkpoint: The domain entity with the new values
    static func updateCoreData(cdCheckpoint: CDFileCheckpoint, with checkpoint: FileCheckpoint) {
        // Don't update the ID as that's the primary key
        cdCheckpoint.fileSessionId = checkpoint.fileSessionId
        cdCheckpoint.timestamp = checkpoint.timestamp
        cdCheckpoint.fileContent = checkpoint.fileContent
        cdCheckpoint.basedOnCheckpointId = checkpoint.basedOnCheckpointId
        cdCheckpoint.triggeringMessageId = checkpoint.triggeringMessageId
        cdCheckpoint.pendingEditStates = EntityMapper.encode(checkpoint.pendingEditStates)
        cdCheckpoint.metadata = EntityMapper.encode(checkpoint.metadata)
    }
}