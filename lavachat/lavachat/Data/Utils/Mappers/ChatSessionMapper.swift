import Foundation
import CoreData

/// Mapper for converting between ChatSession domain entity and CDChatSession Core Data entity
struct ChatSessionMapper {
    
    /// Converts a CDChatSession entity to a domain ChatSession entity
    /// - Parameter cdChatSession: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdChatSession: CDChatSession) -> ChatSession {
        let metadata: [String: String] = EntityMapper.decode(cdChatSession.metadata) ?? [:]
        let activeLLMInstanceIds: [UUID] = EntityMapper.decodeUUIDs(cdChatSession.activeLLMInstanceIds) ?? []
        let usedLLMInstanceIds: [UUID] = EntityMapper.decodeUUIDs(cdChatSession.usedLLMInstanceIds) ?? []
        let activeContextServerIds: [UUID] = EntityMapper.decodeUUIDs(cdChatSession.activeContextServerIds) ?? []
        let instanceSettings: [UUID: SessionInstanceSetting]? = EntityMapper.decode(cdChatSession.instanceSettings)
        
        return ChatSession(
            id: cdChatSession.id ?? UUID(),
            title: cdChatSession.title ?? "",
            createdAt: cdChatSession.createdAt ?? Date(),
            lastModifiedAt: cdChatSession.lastModifiedAt ?? Date(),
            activeMessageId: cdChatSession.activeMessageId,
            activeLLMInstanceIds: activeLLMInstanceIds,
            usedLLMInstanceIds: usedLLMInstanceIds,
            activeContextServerIds: activeContextServerIds,
            settingsId: cdChatSession.settingsId,
            userId: cdChatSession.userId ?? UUID(),
            instanceSettings: instanceSettings,
            metadata: metadata
        )
    }
    
    /// Converts a domain ChatSession entity to a CDChatSession entity
    /// - Parameters:
    ///   - chatSession: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(chatSession: ChatSession, context: NSManagedObjectContext) -> CDChatSession {
        let cdChatSession = CDChatSession(context: context)
        
        cdChatSession.id = chatSession.id
        cdChatSession.userId = chatSession.userId
        cdChatSession.title = chatSession.title
        cdChatSession.createdAt = chatSession.createdAt
        cdChatSession.lastModifiedAt = chatSession.lastModifiedAt
        cdChatSession.settingsId = chatSession.settingsId
        cdChatSession.activeMessageId = chatSession.activeMessageId
        cdChatSession.activeLLMInstanceIds = EntityMapper.encodeUUIDs(chatSession.activeLLMInstanceIds)
        cdChatSession.usedLLMInstanceIds = EntityMapper.encodeUUIDs(chatSession.usedLLMInstanceIds)
        cdChatSession.activeContextServerIds = EntityMapper.encodeUUIDs(chatSession.activeContextServerIds)
        cdChatSession.instanceSettings = EntityMapper.encode(chatSession.instanceSettings)
        cdChatSession.metadata = EntityMapper.encode(chatSession.metadata)
        
        return cdChatSession
    }
    
    /// Updates an existing CDChatSession entity with values from a domain ChatSession entity
    /// - Parameters:
    ///   - cdChatSession: The Core Data entity to update
    ///   - chatSession: The domain entity with the new values
    static func updateCoreData(cdChatSession: CDChatSession, with chatSession: ChatSession) {
        // Don't update the ID as that's the primary key
        cdChatSession.userId = chatSession.userId
        cdChatSession.title = chatSession.title
        cdChatSession.lastModifiedAt = chatSession.lastModifiedAt
        cdChatSession.settingsId = chatSession.settingsId
        cdChatSession.activeMessageId = chatSession.activeMessageId
        cdChatSession.activeLLMInstanceIds = EntityMapper.encodeUUIDs(chatSession.activeLLMInstanceIds)
        cdChatSession.usedLLMInstanceIds = EntityMapper.encodeUUIDs(chatSession.usedLLMInstanceIds)
        cdChatSession.activeContextServerIds = EntityMapper.encodeUUIDs(chatSession.activeContextServerIds)
        cdChatSession.instanceSettings = EntityMapper.encode(chatSession.instanceSettings)
        cdChatSession.metadata = EntityMapper.encode(chatSession.metadata)
    }
} 
