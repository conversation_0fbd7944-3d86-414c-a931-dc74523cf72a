import Foundation
import CoreData

/// Mapper for converting between LLMProvider domain entity and CDLLMProvider Core Data entity
struct LLMProviderMapper {
    
    /// Converts a CDLLMProvider entity to a domain LLMProvider entity
    /// - Parameter cdProvider: The Core Data entity
    /// - Returns: The domain entity
    static func toDomain(cdProvider: CDLLMProvider) -> LLMProvider {
        let metadata: [String: String] = EntityMapper.decode(cdProvider.metadata) ?? [:]
        
        return LLMProvider(
            id: cdProvider.id ?? UUID(),
            name: cdProvider.name ?? "",
            logoImageName: cdProvider.logoImageName,
            customLogoData: cdProvider.customLogoData,
            websiteUrl: cdProvider.websiteUrl,
            apiDocumentationUrl: cdProvider.apiDocumentationUrl,
            apiBaseUrl: cdProvider.apiBaseUrl,
            providerType: LLMProvider.LLMProviderType(rawValue: cdProvider.providerType ?? "user_api_key") ?? .userApiKey,
            apiKeyStored: cdProvider.apiKeyStored,
            apiStyle: APIStyle(rawValue: cdProvider.apiStyle) ?? .openaiCompatible,
            apiEndpointPath: cdProvider.apiEndpointPath,
            isUserCreated: cdProvider.isUserCreated,
            isUserModified: cdProvider.isUserModified,
            metadata: metadata
        )
    }
    
    /// Converts a domain LLMProvider entity to a CDLLMProvider entity
    /// - Parameters:
    ///   - provider: The domain entity
    ///   - context: The managed object context
    /// - Returns: The Core Data entity
    static func toCoreData(provider: LLMProvider, context: NSManagedObjectContext) -> CDLLMProvider {
        let cdProvider = CDLLMProvider(context: context)
        
        cdProvider.id = provider.id
        cdProvider.name = provider.name
        cdProvider.logoImageName = provider.logoImageName
        cdProvider.customLogoData = provider.customLogoData
        cdProvider.websiteUrl = provider.websiteUrl
        cdProvider.apiDocumentationUrl = provider.apiDocumentationUrl
        cdProvider.apiBaseUrl = provider.apiBaseUrl
        cdProvider.providerType = provider.providerType.rawValue
        cdProvider.apiKeyStored = provider.apiKeyStored
        cdProvider.apiStyle = provider.apiStyle.rawValue
        cdProvider.apiEndpointPath = provider.apiEndpointPath
        cdProvider.isUserCreated = provider.isUserCreated
        cdProvider.isUserModified = provider.isUserModified
        cdProvider.metadata = EntityMapper.encode(provider.metadata)
        
        return cdProvider
    }
    
    /// Updates an existing CDLLMProvider entity with values from a domain LLMProvider entity
    /// - Parameters:
    ///   - cdProvider: The Core Data entity to update
    ///   - provider: The domain entity with the new values
    static func updateCoreData(cdProvider: CDLLMProvider, with provider: LLMProvider) {
        // Don't update the ID as that's the primary key
        cdProvider.name = provider.name
        cdProvider.logoImageName = provider.logoImageName
        cdProvider.customLogoData = provider.customLogoData
        cdProvider.websiteUrl = provider.websiteUrl
        cdProvider.apiDocumentationUrl = provider.apiDocumentationUrl
        cdProvider.apiBaseUrl = provider.apiBaseUrl
        cdProvider.providerType = provider.providerType.rawValue
        cdProvider.apiKeyStored = provider.apiKeyStored
        cdProvider.apiStyle = provider.apiStyle.rawValue
        cdProvider.apiEndpointPath = provider.apiEndpointPath
        cdProvider.isUserCreated = provider.isUserCreated
        cdProvider.isUserModified = provider.isUserModified
        cdProvider.metadata = EntityMapper.encode(provider.metadata)
    }
}
