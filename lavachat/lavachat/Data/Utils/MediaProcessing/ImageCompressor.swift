import UIKit
import CoreGraphics

/// <PERSON>les image compression for multimodal content
struct ImageCompressor {
    
    // MARK: - Compression Options
    
    struct CompressionOptions {
        let targetSizeBytes: Int64
        let maxDimension: CGFloat
        let qualitySteps: [CGFloat]
        let preserveAspectRatio: Bool
        
        static let `default` = CompressionOptions(
            targetSizeBytes: 5 * 1024 * 1024, // 5MB
            maxDimension: 2048,
            qualitySteps: [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3],
            preserveAspectRatio: true
        )
        
        static func forAPIStyle(_ apiStyle: APIStyle) -> CompressionOptions {
            return CompressionOptions(
                targetSizeBytes: FileSizeValidator.targetCompressionSize(for: apiStyle),
                maxDimension: 2048,
                qualitySteps: [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3],
                preserveAspectRatio: true
            )
        }
    }
    
    // MARK: - Main Compression Methods
    
    /// Compresses image data to meet size requirements
    /// - Parameters:
    ///   - imageData: The original image data
    ///   - options: Compression options
    /// - Returns: Compressed image data
    /// - Throws: FileProcessingError if compression fails
    static func compressImage(_ imageData: Data, options: CompressionOptions = .default) throws -> Data {
        guard let image = UIImage(data: imageData) else {
            throw FileProcessingError.invalidFileData(reason: "Cannot create UIImage from data")
        }
        
        // If already within target size, return original
        if Int64(imageData.count) <= options.targetSizeBytes {
            return imageData
        }
        
        // Try resizing first if image is too large
        let resizedImage = try resizeImageIfNeeded(image, maxDimension: options.maxDimension)
        
        // Try different quality levels
        for quality in options.qualitySteps {
            if let compressedData = resizedImage.jpegData(compressionQuality: quality),
               Int64(compressedData.count) <= options.targetSizeBytes {
                return compressedData
            }
        }
        
        // If still too large, try more aggressive resizing
        let aggressivelyResized = try resizeImageAggressively(resizedImage, targetSize: options.targetSizeBytes)
        
        guard let finalData = aggressivelyResized.jpegData(compressionQuality: 0.3) else {
            throw FileProcessingError.compressionFailed(reason: "Failed to generate JPEG data")
        }
        
        // Final check
        if Int64(finalData.count) > options.targetSizeBytes {
            throw FileProcessingError.compressionFailed(
                reason: "Unable to compress image below \(FileSizeValidator.humanReadableSize(options.targetSizeBytes))"
            )
        }
        
        return finalData
    }
    
    /// Compresses image for specific API style
    /// - Parameters:
    ///   - imageData: The original image data
    ///   - apiStyle: The target API style
    /// - Returns: Compressed image data
    /// - Throws: FileProcessingError if compression fails
    static func compressImageForAPI(_ imageData: Data, apiStyle: APIStyle) throws -> Data {
        let options = CompressionOptions.forAPIStyle(apiStyle)
        return try compressImage(imageData, options: options)
    }
    
    // MARK: - Resizing Methods
    
    /// Resizes image if it exceeds maximum dimension
    /// - Parameters:
    ///   - image: The image to resize
    ///   - maxDimension: Maximum allowed dimension
    /// - Returns: Resized image or original if within limits
    /// - Throws: FileProcessingError if resizing fails
    static func resizeImageIfNeeded(_ image: UIImage, maxDimension: CGFloat) throws -> UIImage {
        let size = image.size
        let maxCurrentDimension = max(size.width, size.height)
        
        // If image is within limits, return original
        if maxCurrentDimension <= maxDimension {
            return image
        }
        
        // Calculate new size maintaining aspect ratio
        let scale = maxDimension / maxCurrentDimension
        let newSize = CGSize(
            width: size.width * scale,
            height: size.height * scale
        )
        
        return try resizeImage(image, to: newSize)
    }
    
    /// Aggressively resizes image to meet target file size
    /// - Parameters:
    ///   - image: The image to resize
    ///   - targetSize: Target file size in bytes
    /// - Returns: Resized image
    /// - Throws: FileProcessingError if resizing fails
    static func resizeImageAggressively(_ image: UIImage, targetSize: Int64) throws -> UIImage {
        let originalSize = image.size
        var currentImage = image
        
        // Start with 80% of original size and reduce until target is met
        let reductionSteps: [CGFloat] = [0.8, 0.6, 0.4, 0.3, 0.2]
        
        for reduction in reductionSteps {
            let newSize = CGSize(
                width: originalSize.width * reduction,
                height: originalSize.height * reduction
            )
            
            currentImage = try resizeImage(image, to: newSize)
            
            // Test compression at medium quality
            if let testData = currentImage.jpegData(compressionQuality: 0.5),
               Int64(testData.count) <= targetSize {
                return currentImage
            }
        }
        
        return currentImage
    }
    
    /// Resizes image to specific dimensions
    /// - Parameters:
    ///   - image: The image to resize
    ///   - size: Target size
    /// - Returns: Resized image
    /// - Throws: FileProcessingError if resizing fails
    static func resizeImage(_ image: UIImage, to size: CGSize) throws -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        let resizedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
        
        guard resizedImage.size.width > 0 && resizedImage.size.height > 0 else {
            throw FileProcessingError.compressionFailed(reason: "Resized image has invalid dimensions")
        }
        
        return resizedImage
    }
    
    // MARK: - Format Conversion
    
    /// Converts image to JPEG format with specified quality
    /// - Parameters:
    ///   - image: The image to convert
    ///   - quality: JPEG compression quality (0.0 to 1.0)
    /// - Returns: JPEG data
    /// - Throws: FileProcessingError if conversion fails
    static func convertToJPEG(_ image: UIImage, quality: CGFloat = 0.8) throws -> Data {
        guard let jpegData = image.jpegData(compressionQuality: quality) else {
            throw FileProcessingError.compressionFailed(reason: "Failed to convert image to JPEG")
        }
        return jpegData
    }
    
    /// Converts image to PNG format
    /// - Parameter image: The image to convert
    /// - Returns: PNG data
    /// - Throws: FileProcessingError if conversion fails
    static func convertToPNG(_ image: UIImage) throws -> Data {
        guard let pngData = image.pngData() else {
            throw FileProcessingError.compressionFailed(reason: "Failed to convert image to PNG")
        }
        return pngData
    }
    
    // MARK: - Utility Methods
    
    /// Estimates compressed size for given image and quality
    /// - Parameters:
    ///   - image: The image to estimate for
    ///   - quality: JPEG compression quality
    /// - Returns: Estimated size in bytes
    static func estimateCompressedSize(_ image: UIImage, quality: CGFloat) -> Int64 {
        guard let data = image.jpegData(compressionQuality: quality) else {
            return 0
        }
        return Int64(data.count)
    }
    
    /// Gets optimal compression quality for target size
    /// - Parameters:
    ///   - image: The image to compress
    ///   - targetSize: Target size in bytes
    /// - Returns: Optimal quality value (0.0 to 1.0) or nil if impossible
    static func optimalQuality(for image: UIImage, targetSize: Int64) -> CGFloat? {
        let qualitySteps: [CGFloat] = [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
        
        for quality in qualitySteps {
            if estimateCompressedSize(image, quality: quality) <= targetSize {
                return quality
            }
        }
        
        return nil
    }
    
    /// Validates if image data can be processed
    /// - Parameter imageData: The image data to validate
    /// - Returns: True if valid, false otherwise
    static func isValidImageData(_ imageData: Data) -> Bool {
        return UIImage(data: imageData) != nil
    }
}
