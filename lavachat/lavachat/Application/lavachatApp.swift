//
//  lavachatApp.swift
//  lavachat
//
//  Created by ht on 2025/4/16.
//

import SwiftUI

@main
struct lavachatApp: App {
    let persistenceController = PersistenceController.shared

    // Create DIContainer as a StateObject for app-wide availability
    @StateObject private var container: DIContainer

    // Import result handling - moved to MainTabView for better lifecycle management
    @StateObject private var importCoordinator = ImportCoordinator()
    
    init() {
        let context = persistenceController.container.viewContext
        let diContainer = DIContainer(context: context)
        _container = StateObject(wrappedValue: diContainer)
        
        // Initialize built-in provider and model data
        initializeBuiltinData(diContainer: diContainer)
    }
    
    var body: some Scene {
        WindowGroup {
            MainTabView() // Use MainTabView as the root view
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(container) // Inject DIContainer into the environment
                .environmentObject(importCoordinator) // Inject ImportCoordinator
                .onAppear {
                    // Ensure initialization has completed on first app launch
                    // This is a backup in case the init() method didn't complete
                    // initializeBuiltinData()
                }
                .onOpenURL { url in
                    print("🔄 lavachatApp: onOpenURL: \(url)")
                    handleIncomingURL(url)
                }
        }
    }
    
    /// Initialize built-in providers and models
    private func initializeBuiltinData(diContainer: DIContainer) {
        Task {
            // 1. First ensure there's a current user
            await BuiltinUserInitializer.initializeIfNeeded(
                userSettingsRepository: diContainer.getSharedUserSettingsRepository(),
                appSettingsRepository: diContainer.getSharedAppSettingsRepository()
            )

            // 2. Initialize built-in providers and models
            await BuiltinLLMInitializer.initializeIfNeeded(
                repository: diContainer.getSharedLLMInstanceRepository(),
                appSettingsRepository: diContainer.getSharedAppSettingsRepository()
            )

            // 3. Initialize built-in chat session settings (requires current user)
            await BuiltinChatInitializer.initializeIfNeeded(
                chatRepository: diContainer.getSharedChatRepository(),
                appSettingsRepository: diContainer.getSharedAppSettingsRepository()
            )
        }
    }

    // MARK: - URL Handling

    /// Handles incoming URLs from external sources
    /// - Parameter url: The URL to handle
    private func handleIncomingURL(_ url: URL) {
        Task {
            let useCase = container.makeHandleIncomingURLUseCase()
            let result = await useCase.handleURL(url)
            print("Import result: \(result)")

            await MainActor.run {
                importCoordinator.showImportResult(result)
            }
        }
    }
}
