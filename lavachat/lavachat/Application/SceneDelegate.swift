//
//  SceneDelegate.swift
//  lavachat
//
//  Created by ht on 2025/7/21.
//

import UIKit
import SwiftUI
import CloudKit
import os.log

class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    
    var window: UIWindow?
    private let logger = Logger(subsystem: "io.github.alwayskeepcoding.lavachat", category: "SceneDelegate")
    
    // MARK: - Scene Lifecycle
    
    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
        // If using a storyboard, the `window` property will automatically be initialized and attached to the scene.
        // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).

        logger.info("🔄 SceneDelegate: Scene will connect to session")
        logger.info("🔄 SceneDelegate: User activities count: \(connectionOptions.userActivities.count)")
        logger.info("🔄 SceneDelegate: URL contexts count: \(connectionOptions.urlContexts.count)")

        // Handle CloudKit share if app was launched with one
        if let userActivity = connectionOptions.userActivities.first {
            logger.info("🔄 SceneDelegate: Found user activity: \(userActivity.activityType)")
            handleUserActivity(userActivity)
        }

        // Handle CloudKit share URLs if app was launched with one
        for urlContext in connectionOptions.urlContexts {
            logger.info("🔄 SceneDelegate: Found URL context: \(urlContext.url)")
            handleURL(urlContext.url)
        }
    }
    
    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
        logger.info("Scene did disconnect")
    }
    
    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
        logger.info("Scene did become active")
    }
    
    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
        logger.info("Scene will resign active")
    }
    
    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
        logger.info("Scene will enter foreground")
    }
    
    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
        logger.info("Scene did enter background")
    }
    
    // MARK: - CloudKit Share Handling
    
    func windowScene(_ windowScene: UIWindowScene, userDidAcceptCloudKitShareWith shareMetadata: CKShare.Metadata) {
        logger.info("🔄 SceneDelegate: User accepted CloudKit share: \(shareMetadata.share.url?.absoluteString ?? "unknown")")

        guard let shareURL = shareMetadata.share.url else {
            logger.error("🔄 SceneDelegate: CloudKit share metadata missing URL")
            return
        }

        logger.info("🔄 SceneDelegate: Processing CloudKit share URL: \(shareURL)")

        // Handle the CloudKit share URL using our existing import system
        handleCloudKitShareURL(shareURL)
    }
    
    // MARK: - URL Handling
    
    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        logger.info("🔄 SceneDelegate: Scene received URL contexts: \(URLContexts.count)")

        for urlContext in URLContexts {
            logger.info("🔄 SceneDelegate: Processing URL: \(urlContext.url)")
            handleURL(urlContext.url)
        }
    }

    func scene(_ scene: UIScene, continue userActivity: NSUserActivity) {
        logger.info("🔄 SceneDelegate: Scene continue user activity: \(userActivity.activityType)")
        if let url = userActivity.webpageURL {
            logger.info("🔄 SceneDelegate: User activity webpage URL: \(url)")
        }
        handleUserActivity(userActivity)
    }
    
    // MARK: - Private Methods
    
    private func handleURL(_ url: URL) {
        logger.info("Handling URL: \(url.absoluteString)")

        // Handle the URL using our existing system
        handleIncomingURL(url)
    }
    
    private func handleUserActivity(_ userActivity: NSUserActivity) {
        logger.info("Handling user activity: \(userActivity.activityType)")
        
        // Handle CloudKit sharing user activities
        if userActivity.activityType == NSUserActivityTypeBrowsingWeb,
           let url = userActivity.webpageURL {
            handleURL(url)
        }
    }
    
    private func handleCloudKitShareURL(_ shareURL: URL) {
        logger.info("Handling CloudKit share URL: \(shareURL.absoluteString)")
        handleIncomingURL(shareURL)
    }
    
    private func handleIncomingURL(_ url: URL) {
        logger.info("Processing incoming URL: \(url.absoluteString)")

        // Post a notification that can be observed by the SwiftUI app
        // The SwiftUI app will handle the actual import logic using existing infrastructure
        NotificationCenter.default.post(
            name: NSNotification.Name("SceneDelegateURLReceived"),
            object: url
        )
    }
}
