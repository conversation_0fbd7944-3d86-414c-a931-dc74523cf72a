import Foundation
import CoreData

/// Factory responsible for creating repository instances
final class RepositoriesFactory {
    private let context: NSManagedObjectContext
    
    /// Initialize the factory with a Core Data context
    /// - Parameter context: The managed object context for Core Data operations
    init(context: NSManagedObjectContext) {
        self.context = context
    }

    /// Creates a shared instance of LLMInstanceRepository
    private lazy var sharedLLMInstanceRepository: LLMInstanceRepository = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockLLMInstanceRepository() 
        }
        #endif
        return CoreDataLLMInstanceRepository(context: context)
    }()
    
    /// Creates an instance of LLMInstanceRepository
    /// - Returns: An implementation of LLMInstanceRepository
    func makeLLMInstanceRepository() -> LLMInstanceRepository {
        return sharedLLMInstanceRepository
    }

    /// Creates a shared instance of KeychainRepository
    private lazy var sharedKeychainRepository: KeychainRepository = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockKeychainRepository()
        }
        #endif
        return KeychainRepositoryImpl()
    }()
    
    /// Creates an instance of KeychainRepository
    /// - Returns: An implementation of KeychainRepository
    func makeKeychainRepository() -> KeychainRepository {
        return sharedKeychainRepository
    }

    /// Creates a shared instance of ChatRepository
    private lazy var sharedChatRepository: ChatRepository = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockChatRepository()
        }
        #endif
        return CoreDataChatRepository(context: context)
    }()
    
    /// Creates an instance of ChatRepository
    /// - Returns: An implementation of ChatRepository
    func makeChatRepository() -> ChatRepository {
        return sharedChatRepository
    }

    /// Creates a shared instance of UserSettingsRepository
    private lazy var sharedUserSettingsRepository: UserSettingsRepository = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockUserSettingsRepository()
        }
        #endif
        return CoreDataUserSettingsRepository(context: context, appSettingsRepository: sharedAppSettingsRepository)
    }()

    /// Creates an instance of UserSettingsRepository
    /// - Returns: An implementation of UserSettingsRepository
    func makeUserSettingsRepository() -> UserSettingsRepository {
        return sharedUserSettingsRepository
    }

    /// Creates a shared instance of AppSettingsRepository
    private lazy var sharedAppSettingsRepository: AppSettingsRepository = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return UserDefaultsAppSettingsRepository(userDefaults: UserDefaults(suiteName: "preview")!)
        }
        #endif
        return UserDefaultsAppSettingsRepository()
    }()

    /// Creates an instance of AppSettingsRepository
    /// - Returns: An implementation of AppSettingsRepository
    func makeAppSettingsRepository() -> AppSettingsRepository {
        return sharedAppSettingsRepository
    }

    // MARK: Services

    /// Creates a shared instance of NetworkMonitor
    private lazy var sharedNetworkMonitor: NetworkMonitor = {
        return NetworkMonitor.shared
    }()

    /// Creates an instance of NetworkMonitor
    /// - Returns: An implementation of NetworkMonitor
    func makeNetworkMonitor() -> NetworkMonitor {
        return sharedNetworkMonitor
    }

    /// Creates a shared instance of LLMClientFactory
    private lazy var sharedLLMClientFactory: LLMClientFactory = {
        return LLMClientFactory(keychainRepository: makeKeychainRepository())
    }()

    /// Creates an instance of LLMClientFactory
    /// - Returns: An implementation of LLMClientFactory
    func makeLLMClientFactory() -> LLMClientFactory {
        return sharedLLMClientFactory
    }

    /// Creates a shared instance of LLMAPIService
    private lazy var sharedLLMAPIService: LLMAPIServiceProtocol = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockLLMAPIService(configuration: .default)
        }
        #endif
        return LLMAPIServiceImpl(
            llmInstanceRepository: makeLLMInstanceRepository(),
            keychainRepository: makeKeychainRepository(),
            clientFactory: sharedLLMClientFactory,
            networkMonitor: sharedNetworkMonitor
        )
    }()

    /// Creates an instance of LLMAPIService
    /// - Returns: An implementation of LLMAPIService
    func makeLLMAPIService() -> LLMAPIServiceProtocol {
        return sharedLLMAPIService
    }

    /// Creates a shared instance of ShareService
    private lazy var sharedShareService: ShareServiceProtocol = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockShareService()
        }
        #endif
        return ShareServiceImpl(
            fileShareHandler: try! FileShareHandler(),
            cloudKitShareHandler: CloudKitShareHandler(),
            qrCodeShareHandler: QRCodeShareHandler(),
            llmRepository: makeLLMInstanceRepository(),
            chatRepository: makeChatRepository()
        )
    }()

    /// Creates an instance of ShareService
    /// - Returns: An implementation of ShareServiceProtocol
    func makeShareService() -> ShareServiceProtocol {
        return sharedShareService
    }

    /// Creates a shared instance of ImportService
    private lazy var sharedImportService: ImportServiceProtocol = {
        #if DEBUG
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return MockImportService()
        }
        #endif
        return ImportServiceImpl(
            fileShareHandler: try! FileShareHandler(),
            cloudKitShareHandler: CloudKitShareHandler(),
            qrCodeShareHandler: QRCodeShareHandler(),
            llmRepository: makeLLMInstanceRepository(),
            chatRepository: makeChatRepository()
        )
    }()

    /// Creates an instance of ImportService
    /// - Returns: An implementation of ImportServiceProtocol
    func makeImportService() -> ImportServiceProtocol {
        return sharedImportService
    }
}
