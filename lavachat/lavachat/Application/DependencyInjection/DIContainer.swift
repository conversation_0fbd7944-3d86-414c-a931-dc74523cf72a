import Foundation
import CoreData
import SwiftUI

/// Central dependency injection container that provides access to ViewModels
final class DIContainer: ObservableObject {
    // Factories
    private let repositoriesFactory: RepositoriesFactory
    private let useCasesFactory: UseCasesFactory
    private let viewModelsFactory: ViewModelsFactory
    
    /// Initialize the container with a Core Data context
    /// - Parameter context: The managed object context for Core Data operations
    init(context: NSManagedObjectContext) {
        self.repositoriesFactory = RepositoriesFactory(context: context)
        self.useCasesFactory = UseCasesFactory(repositoriesFactory: repositoriesFactory)
        self.viewModelsFactory = ViewModelsFactory(useCasesFactory: useCasesFactory)
    }

    // MARK: - Tab ViewModels

    /// Creates a ModelsViewModel
    /// - Returns: A configured ModelsViewModel
    func makeModelsViewModel() -> ModelsViewModel {
        return viewModelsFactory.makeModelsViewModel()
    }
    
    /// Creates a ChatsViewModel
    /// - Returns: A configured ChatsViewModel
    func makeChatsViewModel() -> ChatsViewModel {
        return viewModelsFactory.makeChatsViewModel(container: self)
    }
    
    /// Creates a ChatViewModel for a specific chat session
    /// - Parameters:
    ///   - chatSessionId: UUID of the chat session to load
    ///   - onDuplicateChatSession: Optional callback for when a chat is duplicated
    /// - Returns: A configured ChatViewModel
    func makeChatViewModel(chatSessionId: UUID, onDuplicateChatSession: ChatDuplicationAction? = nil) -> ChatViewModel {
        return viewModelsFactory.makeChatViewModel(chatSessionId: chatSessionId, onDuplicateChatSession: onDuplicateChatSession)
    }

    /// Creates a ChatSessionDetailViewModel for a specific chat session
    /// - Parameter chatSessionId: UUID of the chat session to manage
    /// - Returns: A configured ChatSessionDetailViewModel
    func makeChatSessionDetailViewModel(chatSessionId: UUID) -> ChatSessionDetailViewModel {
        return viewModelsFactory.makeChatSessionDetailViewModel(chatSessionId: chatSessionId)
    }
    
    // MARK: - Provider ViewModels
    
    /// Creates a ProviderDetailViewModel
    /// - Parameter providerId: Optional UUID of the provider to display/edit, nil for creation mode
    /// - Returns: A configured ProviderDetailViewModel
    func makeProviderDetailViewModel(providerId: UUID?) -> ProviderDetailViewModel {
        return viewModelsFactory.makeProviderDetailViewModel(providerId: providerId)
    }
    
    /// Creates a ProviderListViewModel
    /// - Returns: A configured ProviderListViewModel
    func makeProviderListViewModel() -> Any {
        return viewModelsFactory.makeProviderListViewModel()
    }
    
    // MARK: - Model ViewModels
    
    /// Creates a ModelDetailViewModel
    /// - Parameters:
    ///   - modelId: Optional UUID of the model to display/edit, nil for creation mode
    ///   - providerId: Optional UUID of the provider when creating a new model, nil for existing model
    /// - Returns: A configured ModelDetailViewModel
    func makeModelDetailViewModel(modelId: UUID?, providerId: UUID?) -> ModelDetailViewModel {
        return viewModelsFactory.makeModelDetailViewModel(modelId: modelId, providerId: providerId)
    }
    
    /// Creates a ModelListViewModel
    /// - Parameter providerId: UUID of the provider to list models for
    /// - Returns: A configured ModelListViewModel
    func makeModelListViewModel(providerId: UUID) -> Any {
        return viewModelsFactory.makeModelListViewModel(providerId: providerId)
    }
    
    // MARK: - Instance ViewModels
    
    /// Creates an InstanceDetailViewModel
    /// - Parameters:
    ///   - instanceId: Optional UUID of the instance to display/edit, nil for creation mode
    ///   - modelId: UUID of the model when creating a new instance
    /// - Returns: A configured InstanceDetailViewModel
    func makeInstanceDetailViewModel(instanceId: UUID?, modelId: UUID?) -> InstanceDetailViewModel {
        return viewModelsFactory.makeInstanceDetailViewModel(instanceId: instanceId, modelId: modelId)
    }
    
    /// Creates an InstanceListViewModel
    /// - Returns: A configured InstanceListViewModel
    func makeInstanceListViewModel() -> Any {
        return viewModelsFactory.makeInstanceListViewModel()
    }
    
    // MARK: - Group ViewModels
    
    /// Creates an InstanceGroupViewModel
    /// - Parameter groupId: Optional UUID of the group to display/edit, nil for creation mode
    /// - Returns: A configured InstanceGroupViewModel
    func makeInstanceGroupViewModel(groupId: UUID?) -> Any {
        return viewModelsFactory.makeInstanceGroupViewModel(groupId: groupId)
    }
    
    /// Creates an InstanceGroupListViewModel
    /// - Returns: A configured InstanceGroupListViewModel
    func makeInstanceGroupListViewModel() -> Any {
        return viewModelsFactory.makeInstanceGroupListViewModel()
    }
    
    // MARK: - Chat ViewModels
    
    /// Creates a NewChatSheetViewModel for chat creation
    /// - Parameter onCreateNewChat: Callback for new chat creation
    /// - Returns: A configured NewChatSheetViewModel
    func makeNewChatSheetViewModel(onCreateNewChat: @escaping NewChatCreationAction) -> NewChatSheetViewModel {
        return viewModelsFactory.makeNewChatSheetViewModel(onCreateNewChat: onCreateNewChat)
    }
    
    /// Creates a NewChatSheetViewModel for instance selection
    /// - Parameters:
    ///   - mode: The sheet mode (chatCreation or instanceSelection)
    ///   - onSelectInstance: Callback for instance selection
    /// - Returns: A configured NewChatSheetViewModel
    func makeNewChatSheetViewModel(
        mode: SheetMode,
        onSelectInstance: @escaping InstanceSelectionAction
    ) -> NewChatSheetViewModel {
        return viewModelsFactory.makeNewChatSheetViewModel(
            mode: mode,
            onSelectInstance: onSelectInstance
        )
    }

    /// Creates a NewChatSheetViewModel for auxiliary instance selection
    /// - Parameters:
    ///   - mode: The sheet mode (auxiliaryInstanceSelection)
    ///   - onSelectAuxiliaryInstance: Callback for auxiliary instance selection
    /// - Returns: A configured NewChatSheetViewModel
    func makeNewChatSheetViewModel(
        mode: SheetMode,
        onSelectAuxiliaryInstance: @escaping AuxiliaryInstanceSelectionAction
    ) -> NewChatSheetViewModel {
        return viewModelsFactory.makeNewChatSheetViewModel(
            mode: mode,
            onSelectAuxiliaryInstance: onSelectAuxiliaryInstance
        )
    }
    
    // MARK: - Model Management ViewModels

    /// Creates a NewModelsSheetViewModel
    /// - Returns: A configured NewModelsSheetViewModel
    func makeNewModelsSheetViewModel() -> NewModelsSheetViewModel {
        return viewModelsFactory.makeNewModelsSheetViewModel()
    }

    // MARK: - Settings ViewModels

    /// Creates a SettingsViewModel
    /// - Returns: A configured SettingsViewModel
    func makeSettingsViewModel() -> SettingsViewModel {
        return viewModelsFactory.makeSettingsViewModel()
    }

    /// Creates a GeneralSettingsViewModel
    /// - Returns: A configured GeneralSettingsViewModel
    func makeGeneralSettingsViewModel() -> GeneralSettingsViewModel {
        return viewModelsFactory.makeGeneralSettingsViewModel()
    }

    /// Creates a MyActionsViewModel
    /// - Returns: A configured MyActionsViewModel
    func makeMyActionsViewModel() -> MyActionsViewModel {
        return viewModelsFactory.makeMyActionsViewModel()
    }

    /// Creates an ActionManagementViewModel
    /// - Parameter category: The MessageActionCategory to manage
    /// - Returns: A configured ActionManagementViewModel
    func makeActionManagementViewModel(category: MessageActionCategory) -> ActionManagementViewModel {
        return viewModelsFactory.makeActionManagementViewModel(category: category)
    }

    /// Creates an ActionEditorViewModel
    /// - Parameters:
    ///   - category: The MessageActionCategory for the action
    ///   - editingAction: Optional action to edit, nil for creating new action
    /// - Returns: A configured ActionEditorViewModel
    func makeActionEditorViewModel(category: MessageActionCategory, editingAction: MessageAction?) -> ActionEditorViewModel {
        return viewModelsFactory.makeActionEditorViewModel(category: category, editingAction: editingAction)
    }

    /// Creates a MyChatSettingsViewModel
    /// - Returns: A configured MyChatSettingsViewModel
    func makeMyChatSettingsViewModel() -> MyChatSettingsViewModel {
        return viewModelsFactory.makeMyChatSettingsViewModel()
    }

    /// Creates a ChatSettingEditorViewModel
    /// - Parameters:
    ///   - editingSetting: Optional setting to edit, nil for creating new setting
    /// - Returns: A configured ChatSettingEditorViewModel
    func makeChatSettingEditorViewModel(editingSetting: ChatSessionSetting?) -> ChatSettingEditorViewModel {
        return viewModelsFactory.makeChatSettingEditorViewModel(editingSetting: editingSetting)
    }
    
    // MARK: - Repository Access (if needed by external services like initializers)

    /// Provides access to the shared LLMInstanceRepository.
    /// This might be used by app-level initializers or specific services
    /// that need direct repository access outside the ViewModel context.
    func getSharedLLMInstanceRepository() -> LLMInstanceRepository {
        return repositoriesFactory.makeLLMInstanceRepository()
    }

    /// Provides access to the shared ChatRepository.
    /// This might be used by app-level initializers or specific services
    /// that need direct repository access outside the ViewModel context.
    func getSharedChatRepository() -> ChatRepository {
        return repositoriesFactory.makeChatRepository()
    }

    /// Provides access to the shared AppSettingsRepository.
    /// This might be used by app-level initializers or specific services
    /// that need direct repository access outside the ViewModel context.
    func getSharedAppSettingsRepository() -> AppSettingsRepository {
        return repositoriesFactory.makeAppSettingsRepository()
    }

    /// Provides access to the shared UserSettingsRepository.
    /// This might be used by app-level initializers or specific services
    /// that need direct repository access outside the ViewModel context.
    func getSharedUserSettingsRepository() -> UserSettingsRepository {
        return repositoriesFactory.makeUserSettingsRepository()
    }



    /// Provides access to the shared LLMAPIService.
    /// This might be used by app-level initializers or specific services
    /// that need direct service access outside the ViewModel context.
    func getSharedLLMAPIService() -> LLMAPIServiceProtocol {
        return repositoriesFactory.makeLLMAPIService()
    }

    // MARK: - App-Level Use Cases

    /// Creates a HandleIncomingURLUseCase for processing incoming URLs
    /// - Returns: A configured HandleIncomingURLUseCase
    func makeHandleIncomingURLUseCase() -> HandleIncomingURLUseCaseProtocol {
        return useCasesFactory.makeHandleIncomingURLUseCase()
    }

    // MARK: - Temporary Testing Methods

    /// Provides access to the shared MessageTreeManagerUseCase for testing purposes.
    /// TODO: Remove this method once proper ViewModel integration is complete.
    func getSharedMessageTreeManagerUseCase() -> MessageTreeManagerUseCaseProtocol {
        return useCasesFactory.makeMessageTreeManagerUseCase()
    }
    
    /// Provides access to PrepareMessagesUseCase for testing purposes.
    /// TODO: Remove this method once proper ViewModel integration is complete.
    func makePrepareMessagesUseCase() -> PrepareMessagesUseCaseProtocol {
        return useCasesFactory.makePrepareMessagesUseCase()
    }

    /// Provides access to the shared SendMessageUseCase for testing purposes.
    /// TODO: Remove this method once proper ViewModel integration is complete.
    func getSharedSendMessageUseCase() -> SendMessageUseCaseProtocol {
        return useCasesFactory.makeSendMessageUseCase()
    }

    /// Creates a GetAllMessageActionsUseCase
    /// - Returns: A configured GetAllMessageActionsUseCase
    func makeGetAllMessageActionsUseCase() -> GetAllMessageActionsUseCaseProtocol {
        return useCasesFactory.makeGetAllMessageActionsUseCase()
    }

    // MARK: - Share ViewModels

    /// Creates a ShareViewModel for LLMInstance
    /// - Parameter instance: The LLMInstance to share
    /// - Returns: A configured ShareViewModel for LLMInstance
    func makeInstanceShareViewModel(instance: LLMInstance) -> ShareViewModel<LLMInstance> {
        return ShareViewModel(
            item: instance,
            shareInstanceUseCase: useCasesFactory.makeShareInstanceUseCase()
        )
    }

    /// Creates a ShareViewModel for ChatSession
    /// - Parameter session: The ChatSession to share
    /// - Returns: A configured ShareViewModel for ChatSession
    func makeChatSessionShareViewModel(session: ChatSession) -> ShareViewModel<ChatSession> {
        return ShareViewModel(
            item: session,
            shareChatSessionUseCase: useCasesFactory.makeShareChatSessionUseCase()
        )
    }

    /// Creates a ShareViewModel for MessageAction
    /// - Parameter action: The MessageAction to share
    /// - Returns: A configured ShareViewModel for MessageAction
    func makeMessageActionShareViewModel(action: MessageAction) -> ShareViewModel<MessageAction> {
        return ShareViewModel(
            item: action,
            shareMessageActionUseCase: useCasesFactory.makeShareMessageActionUseCase()
        )
    }

    /// Creates a ShareViewModel for ChatSessionSetting
    /// - Parameter setting: The ChatSessionSetting to share
    /// - Returns: A configured ShareViewModel for ChatSessionSetting
    func makeChatSessionSettingShareViewModel(setting: ChatSessionSetting) -> ShareViewModel<ChatSessionSetting> {
        return ShareViewModel(
            item: setting,
            shareChatSessionSettingUseCase: useCasesFactory.makeShareChatSessionSettingUseCase()
        )
    }
}
