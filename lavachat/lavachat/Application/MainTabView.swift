import SwiftUI

struct MainTabView: View {
    @EnvironmentObject private var container: DIContainer
    @EnvironmentObject private var importCoordinator: ImportCoordinator
    @StateObject private var appStorageManager = AppStorageManager.shared
    @State private var chatsViewModel: ChatsViewModel?

    var body: some View {
        TabView {
            // Chats Tab
            if let chatsVM = chatsViewModel {
                ChatsView(viewModel: chatsVM)
                    .tabItem {
                        Label("Chats", systemImage: "message")
                    }
            } else {
                // Loading placeholder
                ProgressView("Loading...")
                    .tabItem {
                        Label("Chats", systemImage: "message")
                    }
            }

            // Model Management Tab
            ModelsView(viewModel: container.makeModelsViewModel())
                .tabItem {
                    Label("Models", systemImage: "square.grid.3x3.middle.filled")
                }

            // Placeholder for the third tab (Hub - MVP Placeholder)
            Text("Hub Tab")
                .tabItem {
                    Label("Hub", systemImage: "square.stack.3d.up")
                }

            // Settings Tab
            SettingsView(viewModel: container.makeSettingsViewModel())
                .tabItem {
                    Label("Settings", systemImage: "gear")
                }

            // #if DEBUG
            //     DebugView()
            //         .tabItem {
            //             Label("Debug", systemImage: "ladybug")
            //         }
            // #endif
        }
        .onAppear {
            // Initialize chatsViewModel if not already done
            if chatsViewModel == nil {
                chatsViewModel = container.makeChatsViewModel()
            }
            handleAppLaunchNavigation()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
            appStorageManager.setAppBackgroundState(true)
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            appStorageManager.setAppBackgroundState(false)
        }
        .sheet(isPresented: $importCoordinator.showImportResult) {
            if let result = importCoordinator.importResult {
                ImportResultView(result: result) {
                    importCoordinator.dismissImportResult()
                }
            }
        }
    }

    // MARK: - Helper Methods

    private func handleAppLaunchNavigation() {
        guard let sessionUUID = appStorageManager.lastOpenChatSessionUUID else {
            return
        }

        // Ensure we have the chatsViewModel
        guard let chatsVM = chatsViewModel else {
            print("🔄 MainTabView: ChatsViewModel not initialized yet")
            return
        }

        // Delay to ensure the view is fully loaded and ChatsViewModel has loaded sessions
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            Task {
                // Wait for sessions to load if they haven't loaded yet
                if chatsVM.sessions.isEmpty && !chatsVM.isLoading {
                    print("🔄 MainTabView: Sessions not loaded yet, loading...")
                    await chatsVM.loadSessions()
                }

                print("🔄 MainTabView: Looking for session in \(chatsVM.sessions.count) loaded sessions")

                if let session = chatsVM.sessions.first(where: { $0.id == sessionUUID }) {
                    print("🔄 MainTabView: Found session to restore: \(session.title ?? "Untitled")")
                    await MainActor.run {
                        chatsVM.navigateToChatSession(session)
                    }
                } else {
                    print("🔄 MainTabView: Session not found, clearing last session")
                    appStorageManager.clearLastChatSession()
                }
            }
        }
    }
}

#Preview {
    MainTabView()
        .environmentObject(DIContainer(context: PersistenceController.preview.container.viewContext))
} 
