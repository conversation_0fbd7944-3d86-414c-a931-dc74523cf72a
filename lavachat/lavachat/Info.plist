<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to take photos for chat messages.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select images for chat messages.</string>
	<key>CKSharingSupported</key>
	<true/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>io.github.alwayskeepcoding.lavachat</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>lavachat</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>iCloud Share</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
		</dict>
	</array>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>LavaChat Share File</string>
			<key>CFBundleTypeDescription</key>
			<string>LavaChat shared configuration and data</string>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>io.github.alwayskeepcoding.lavachat.share</string>
			</array>
		</dict>
	</array>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<false/>
	<key>UISupportsDocumentBrowser</key>
	<false/>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>io.github.alwayskeepcoding.lavachat.share</string>
			<key>UTTypeDescription</key>
			<string>LavaChat Share File</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.content</string>
			</array>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>lavachat</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>application/x-lavachat</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
