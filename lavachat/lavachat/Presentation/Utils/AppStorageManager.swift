import Foundation
import SwiftUI

/// Manager for UI-level settings using @AppStorage
/// Handles navigation state and user interface preferences that require immediate UI updates
final class AppStorageManager: ObservableObject {

    // MARK: - UI Settings

    /// Whether to restore the last opened chat when app launches
    @AppStorage("shouldRestoreLastChat") var shouldRestoreLastChat: Bool = true

    // MARK: - Navigation State

    /// ID of the last opened chat session
    @AppStorage("lastOpenChatSessionId") var lastOpenChatSessionId: String = ""

    // MARK: - App State Tracking

    /// Track if app is currently in background to avoid clearing session on app lifecycle events
    private var isAppInBackground = false

    // MARK: - Computed Properties

    /// Get the last chat session ID as UUID if valid
    var lastOpenChatSessionUUID: UUID? {
        guard shouldRestoreLastChat else { return nil }
        guard !lastOpenChatSessionId.isEmpty else { return nil }
        return UUID(uuidString: lastOpenChatSessionId)
    }

    // MARK: - Methods

    /// Record that a chat session was opened
    func recordChatSessionOpened(_ sessionId: UUID) {
        lastOpenChatSessionId = sessionId.uuidString
        print("🔄 AppStorageManager: Recorded chat session opened: \(sessionId)")
    }

    /// Clear the last opened chat session
    func clearLastChatSession() {
        lastOpenChatSessionId = ""
        print("🔄 AppStorageManager: Cleared last chat session")
    }

    /// Clear the last opened chat session only if not caused by app backgrounding
    func clearLastChatSessionIfUserInitiated() {
        if !isAppInBackground {
            clearLastChatSession()
        } else {
            print("🔄 AppStorageManager: Skipping clear - app is in background")
        }
    }

    /// Set app background state
    func setAppBackgroundState(_ inBackground: Bool) {
        isAppInBackground = inBackground
        print("🔄 AppStorageManager: App background state: \(inBackground)")
    }

    /// Sync current @AppStorage values to domain AppSettings
    func toDomainAppSettings() -> AppSettings {
        return AppSettings(shouldRestoreLastChat: shouldRestoreLastChat)
    }

    /// Update @AppStorage values from domain AppSettings
    func syncFromDomain(_ appSettings: AppSettings) {
        shouldRestoreLastChat = appSettings.shouldRestoreLastChat
    }
}

// MARK: - Singleton Access

extension AppStorageManager {
    /// Shared instance for app-wide access
    static let shared = AppStorageManager()
}
