import Foundation
import SwiftUI

/// Coordinator for managing import result display across the app
final class ImportCoordinator: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var importResult: URLHandlingResult?
    @Published var showImportResult = false
    
    // MARK: - Public Methods
    
    /// Shows the import result in a sheet
    /// - Parameter result: The result to display
    func showImportResult(_ result: URLHandlingResult) {
        importResult = result
        showImportResult = true
    }
    
    /// Dismisses the import result sheet
    func dismissImportResult() {
        showImportResult = false
        importResult = nil
    }
}
