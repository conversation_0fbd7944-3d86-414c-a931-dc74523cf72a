import Foundation
import Combine
import UIKit

// MARK: - ChatViewModel MessageInteraction Extension

extension ChatViewModel {
    
    // MARK: - MessageInteractionHandler Implementation
    
    func copyMessage(_ message: Message) {
        UIPasteboard.general.string = message.textContent
        showCopyToast = true
    }
    
    @MainActor
    func regenerateSingleMessage(_ message: Message) {
        print("Regenerating message: \(message.id)")
        
        // Check: Ensure message.role is .assistant or .mergedAssistant
        guard message.role == .assistant || message.role == .mergedAssistant else {
            print("❌ Error: Cannot regenerate non-assistant message")
            handleError(ChatError.regenerationFailed)
            return
        }
        
        Task { @MainActor in
            do {
                // Locate UI component: Find the corresponding MessageRowViewModel based on message.id
                guard let messageRowViewModel = messageRowViewModels.first(where: { rowViewModel in
                    rowViewModel.messageViewModels.contains { $0.message.id == message.id }
                }) else {
                    print("❌ Error: Could not find MessageRowViewModel for message \(message.id)")
                    handleError(ChatError.regenerationFailed)
                    return
                }
                
                // Find the AssistantReplyGroupViewModel containing this message in the MessageRowViewModel
                guard messageRowViewModel.assistantReplyGroupViewModels.contains(where: { groupViewModel in
                    groupViewModel.messageViewModels.contains { $0.message.id == message.id }
                }) else {
                    print("❌ Error: Could not find AssistantReplyGroupViewModel for message \(message.id)")
                    handleError(ChatError.regenerationFailed)
                    return
                }

                guard let currentChatSession = self.chatSession else {
                    print("❌ Error: Chat session not found")
                    handleError(ChatError.regenerationFailed)
                    return
                }

                guard let chatSessionSetting = currentChatSessionSetting else {
                    print("❌ Error: Chat session setting not found")
                    handleError(ChatError.regenerationFailed)
                    return
                }

                // Call regenerateSingleMessageUseCase.execute
                let (stream, placeholderMessage) = try await regenerateSingleMessageUseCase.execute(message: message, chatSession: currentChatSession, chatSessionSetting: chatSessionSetting)
                
                // Create a new MessageViewModel using placeholderMessage
                var instanceContext: LLMInstanceContext? = nil
                
                // Get LLMInstanceContext from the original message and assign it to the new ViewModel
                if let instanceId = message.llmInstanceId {
                    instanceContext = await getInstanceContext(for: instanceId)
                }
                
                let newMessageViewModel = MessageViewModel(
                    message: placeholderMessage,
                    LLMInstanceContext: instanceContext,
                    interactionHandler: self,
                    currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                    resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
                )
                
                // Add the new MessageViewModel to the MessageRowViewModel and set it as active
                messageRowViewModel.addAndActivateRegeneratedAssistantMessageViewModel(newMessageViewModel)
                
                // Handle streaming response
                await handleStreamingResponse(stream, messageViewModels: [newMessageViewModel])
                
            } catch {
                print("❌ Error regenerating message: \(error)")
                handleError(ChatError.from(error))
            }
        }
    }

    @MainActor
    func regenerateSingleMessageWithAction(_ message: Message, _ action: MessageAction) {
        print("Regenerating message: \(message.id) with action: \(action.name)")

        guard !action.prompts.isEmpty else {
            print("❌ Error: No prompts provided for assistant regenerate action")
            return
        }

        if action.prompts.count == 1 {
            // Single prompt: execute directly
            executeRegenerateWithPrompt(message: message, prompt: action.prompts[0])
        } else {
            // Multiple prompts: show selection sheet
            promptSelectionMessage = message
            promptSelectionAction = action
            showingPromptSelection = true
        }
    }

    /// Execute regenerate with a specific prompt
    @MainActor
    private func executeRegenerateWithPrompt(message: Message, prompt: String) {
        print("Executing regenerate with prompt: '\(prompt)' for message: \(message.id)")

        // Check: Ensure message.role is .assistant or .mergedAssistant
        guard message.role == .assistant || message.role == .mergedAssistant else {
            print("❌ Error: Cannot regenerate non-assistant message")
            handleError(ChatError.regenerationFailed)
            return
        }

        // Check: Ensure we have a current chat session
        guard let currentChatSession = chatSession else {
            print("❌ Error: Chat session not found")
            handleError(ChatError.sessionNotFound)
            return
        }

        // Find the MessageRowViewModel containing this message
        guard let messageRowViewModel = messageRowViewModels.first(where: { rowViewModel in
            rowViewModel.messageViewModels.contains { $0.message.id == message.id }
        }) else {
            print("❌ Error: Could not find MessageRowViewModel for message")
            handleError(ChatError.regenerationFailed)
            return
        }

        Task {
            do {
                print("🔄 Starting regeneration with prompt for message: \(message.id)")

                guard let chatSessionSetting = currentChatSessionSetting else {
                    print("❌ Error: Chat session setting not found")
                    await MainActor.run {
                        handleError(ChatError.regenerationFailed)
                    }
                    return
                }

                // Call regenerateSingleMessageWithPromptUseCase.execute
                let (stream, placeholderMessage) = try await regenerateSingleMessageWithPromptUseCase.execute(
                    message: message,
                    prompt: prompt,
                    chatSession: currentChatSession,
                    chatSessionSetting: chatSessionSetting
                )

                // Create a new MessageViewModel using placeholderMessage
                var instanceContext: LLMInstanceContext? = nil

                // Get LLMInstanceContext from the original message and assign it to the new ViewModel
                if let instanceId = message.llmInstanceId {
                    instanceContext = await getInstanceContext(for: instanceId)
                }

                let newMessageViewModel = MessageViewModel(
                    message: placeholderMessage,
                    LLMInstanceContext: instanceContext,
                    interactionHandler: self,
                    currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                    resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
                )

                // Add the new MessageViewModel to the MessageRowViewModel and set it as active
                messageRowViewModel.addAndActivateRegeneratedAssistantMessageViewModel(newMessageViewModel)

                // Handle streaming response
                await handleStreamingResponse(stream, messageViewModels: [newMessageViewModel])

            } catch {
                print("❌ Error regenerating message with prompt: \(error)")
                handleError(ChatError.from(error))
            }
        }
    }

    /// Handle prompt selection from the sheet
    @MainActor
    func handlePromptSelection(_ selectedPrompt: String) {
        guard let action = promptSelectionAction else {
            print("❌ Error: No action available for prompt selection")
            return
        }

        // Determine the action type and handle accordingly
        switch action.actionType {
        case .assistantRegenerate:
            // For assistant regenerate, we need a message
            guard let message = promptSelectionMessage else {
                print("❌ Error: No message available for assistant regenerate prompt selection")
                showingPromptSelection = false
                promptSelectionAction = nil
                promptSelectionMessage = nil
                return
            }

            // Clear the selection state
            showingPromptSelection = false
            promptSelectionAction = nil
            promptSelectionMessage = nil

            // Execute regenerate with the selected prompt
            executeRegenerateWithPrompt(message: message, prompt: selectedPrompt)

        case .actionPanelPromptInsert:
            // Clear the selection state
            showingPromptSelection = false
            promptSelectionAction = nil
            promptSelectionMessage = nil

            // Execute prompt insert
            Task { @MainActor in
                executePromptInsert(prompt: selectedPrompt)
            }

        case .actionPanelPromptRewrite:
            // Clear the selection state
            showingPromptSelection = false
            promptSelectionAction = nil
            promptSelectionMessage = nil

            // Execute prompt rewrite
            Task { @MainActor in
                executePromptRewrite(prompt: selectedPrompt)
            }

        default:
            print("❌ Error: Unsupported action type for prompt selection: \(action.actionType)")
            showingPromptSelection = false
            promptSelectionAction = nil
            promptSelectionMessage = nil
        }
    }

    @MainActor
    func regenerateAllMessages(fromRowViewModel rowViewModelId: UUID) {
        print("Regenerating all messages from row: \(rowViewModelId)")
        
        Task { @MainActor in
            do {
                // Find the assistant MessageRowViewModel by ID
                guard let assistantRowViewModel = messageRowViewModels.first(where: { $0.id == rowViewModelId }) else {
                    print("❌ Error: Could not find MessageRowViewModel with ID \(rowViewModelId)")
                    handleError(ChatError.regenerationFailed)
                    return
                }
                
                // Find the index of the assistant row
                guard let assistantRowIndex = messageRowViewModels.firstIndex(of: assistantRowViewModel) else {
                    print("❌ Error: Could not find index of assistant MessageRowViewModel")
                    handleError(ChatError.regenerationFailed)
                    return
                }
                
                // Find the preceding user message row by looking backwards from the assistant row
                var userRowViewModel: MessageRowViewModel?
                for i in (0..<assistantRowIndex).reversed() {
                    let rowViewModel = messageRowViewModels[i]
                    if rowViewModel.containsUserMessages {
                        userRowViewModel = rowViewModel
                        break
                    }
                }
                
                guard let userRowViewModel = userRowViewModel else {
                    print("❌ Error: Could not find preceding user message row for regeneration")
                    handleError(ChatError.regenerationFailed)
                    return
                }
                
                // Get the active user message from the user row
                guard let userMessage = userRowViewModel.activeUserMessageViewModel?.message else {
                    print("❌ Error: Could not find active user message for regeneration")
                    handleError(ChatError.regenerationFailed)
                    return
                }
                
                guard let currentChatSession = self.chatSession else {
                    print("❌ Error: Chat session not found")
                    handleError(ChatError.regenerationFailed)
                    return
                }

                guard let chatSessionSetting = currentChatSessionSetting else {
                    print("❌ Error: Chat session setting not found")
                    handleError(ChatError.regenerationFailed)
                    return
                }

                // Call regenerateAllMessagesUseCase.execute
                let (stream, newAssistantMessageLayer) = try await regenerateAllMessagesUseCase.execute(userMessage: userMessage, chatSession: currentChatSession, chatSessionSetting: chatSessionSetting)
                
                // Create MessageViewModels for the new assistant message layer
                let newAssistantRowViewModel = await createMessageRowViewModel(from: newAssistantMessageLayer)
                
                // Find the user row index
                guard let userRowIndex = messageRowViewModels.firstIndex(of: userRowViewModel) else {
                    print("❌ Error: Could not find user row index")
                    handleError(ChatError.regenerationFailed)
                    return
                }
                
                // Remove all assistant message rows after the user message row and add the new one
                messageRowViewModels = Array(messageRowViewModels.prefix(upTo: userRowIndex + 1))
                messageRowViewModels.append(newAssistantRowViewModel)
                
                // Handle streaming response
                await handleStreamingResponse(stream, messageViewModels: newAssistantRowViewModel.messageViewModels)
                
            } catch {
                print("❌ Error regenerating all messages: \(error)")
                handleError(ChatError.from(error))
            }
        }
    }
    
    func likeMessage(_ message: Message) {
        print("Liking message: \(message.id)")
        
        Task { @MainActor in
            do {
                var updatedMessage = message
                updatedMessage.userFeedback = .liked
                try await messageTreeManagerUseCase.updateMessageInCacheAndRepository(sessionId: chatSessionId, message: updatedMessage)
            } catch {
                handleError(ChatError.from(error))
            }
        }
    }
    
    func dislikeMessage(_ message: Message) {
        print("Disliking message: \(message.id)")
        
        Task { @MainActor in
            do {
                var updatedMessage = message
                updatedMessage.userFeedback = .disliked
                try await messageTreeManagerUseCase.updateMessageInCacheAndRepository(sessionId: chatSessionId, message: updatedMessage)
            } catch {
                handleError(ChatError.from(error))
            }
        }
    }
    
    func editMessage(_ message: Message, fromRowViewModel rowViewModelId: UUID) {
        guard message.role == .user else { return }
        
        print("Editing message: \(message.id) from row: \(rowViewModelId)")
        
        // Start editing mode
        Task { @MainActor in
            startEditingMessage(message, fromRowViewModel: rowViewModelId)
        }
    }
    
    @MainActor
    func userMessageDidChange(_ message: Message, fromRowViewModel rowViewModelId: UUID) {
        print("User message changed to: \(message.id) from row: \(rowViewModelId)")
        
        Task {
            do {
                // For now, we'll use switchActiveMessage with the same message ID
                // In a complete implementation, we'd need a dedicated updateActiveMessage method
                let updatedLayers = try await messageTreeManagerUseCase.switchActiveMessage(
                    sessionId: chatSessionId,
                    fromMessageId: message.id, 
                    newActiveMessageId: message.id
                )
                
                // Rebuild view models from updated tree
                await rebuildViewModelsFromUpdatedLayers(updatedLayers)
            } catch {
                handleError(ChatError.from(error))
            }
        }
    }
    
    @MainActor
    func assistantMessageDidChange(_ message: Message, fromRowViewModel rowViewModelId: UUID) {
        print("Assistant message changed to: \(message.id) from row: \(rowViewModelId)")
        
        Task {
            do {
                // For now, we'll use switchActiveMessage with the same message ID
                // In a complete implementation, we'd need a dedicated updateActiveMessage method
                let updatedLayers = try await messageTreeManagerUseCase.switchActiveMessage(
                    sessionId: chatSessionId, 
                    fromMessageId: message.id, 
                    newActiveMessageId: message.id
                )
                
                // Rebuild view models from updated tree
                await rebuildViewModelsFromUpdatedLayers(updatedLayers)
            } catch {
                handleError(ChatError.from(error))
            }
        }
    }
    
    // MARK: - Editing State Management
    
    /// Start editing a message
    @MainActor
    func startEditingMessage(_ message: Message, fromRowViewModel rowViewModelId: UUID) {
        guard message.role == .user else {
            print("Warning: Attempting to edit non-user message")
            return
        }

        editingState = EditingState(message: message, sourceRowViewModelId: rowViewModelId)
        newMessageText = editingState?.originalText ?? ""

        // Load images and files from the original message
        loadImagesAndFilesFromMessage(message)

        print("Started editing message: \(message.id) from row: \(rowViewModelId)")
    }

    /// Load images and files from a message into selectedImages and selectedFiles arrays
    @MainActor
    private func loadImagesAndFilesFromMessage(_ message: Message) {
        // Clear existing selections and clean up any previous temporary files
        cleanupTemporaryEditingFiles()
        selectedImages.removeAll()
        selectedFiles.removeAll()

        // Extract images and files from message content
        for contentBlock in message.content {
            switch contentBlock {
            case .image(let imageInfo):
                // Convert ImageInfo back to UIImage
                if let imageData = imageInfo.data,
                   let uiImage = UIImage(data: imageData) {
                    selectedImages.append(uiImage)
                    print("✅ Loaded image for editing: \(imageInfo.fileSizeBytes ?? 0) bytes")
                } else if let base64Data = imageInfo.base64Data,
                          let data = Data(base64Encoded: base64Data),
                          let uiImage = UIImage(data: data) {
                    selectedImages.append(uiImage)
                    print("✅ Loaded image from base64 for editing: \(base64Data.count) chars")
                } else {
                    print("⚠️ Failed to load image data for editing")
                }

            case .file(let fileInfo):
                // For files, we need to create a temporary URL since we only have the base64 data
                // We'll create a temporary file in the app's temporary directory
                if !fileInfo.base64Data.isEmpty,
                   let data = Data(base64Encoded: fileInfo.base64Data) {
                    let tempURL = createTemporaryFileURL(fileName: fileInfo.fileName, data: data)
                    selectedFiles.append(tempURL)
                    print("✅ Created temporary file from base64 for editing: \(fileInfo.fileName)")
                } else {
                    print("⚠️ Failed to load file data for editing: \(fileInfo.fileName)")
                }

            default:
                continue
            }
        }

        print("📝 Loaded \(selectedImages.count) images and \(selectedFiles.count) files for editing")
    }

    /// Create a temporary file URL for editing purposes
    private func createTemporaryFileURL(fileName: String, data: Data) -> URL {
        let tempDirectory = FileManager.default.temporaryDirectory

        // Extract file name and extension
        let fileURL = URL(fileURLWithPath: fileName)
        let nameWithoutExtension = fileURL.deletingPathExtension().lastPathComponent
        let fileExtension = fileURL.pathExtension

        // Create new filename: originalName_editing_UUID.extension
        let uniqueIdentifier = UUID().uuidString.prefix(8) // Use shorter UUID for readability
        let newFileName: String
        if fileExtension.isEmpty {
            newFileName = "\(nameWithoutExtension)_editing_\(uniqueIdentifier)"
        } else {
            newFileName = "\(nameWithoutExtension)_editing_\(uniqueIdentifier).\(fileExtension)"
        }

        let tempFileURL = tempDirectory.appendingPathComponent(newFileName)

        do {
            try data.write(to: tempFileURL)
            return tempFileURL
        } catch {
            print("⚠️ Failed to create temporary file: \(error)")
            // Fallback: create a URL with just the filename in temp directory
            return tempDirectory.appendingPathComponent(fileName)
        }
    }

    
    /// Cancel editing mode and clear input
    @MainActor
    func cancelEditing() {
        editingState = nil
        newMessageText = ""

        // Clear selected images and files, and clean up temporary files
        selectedImages.removeAll()
        selectedFiles.removeAll()
        cleanupTemporaryEditingFiles()

        print("Cancelled editing mode")
    }
} 
