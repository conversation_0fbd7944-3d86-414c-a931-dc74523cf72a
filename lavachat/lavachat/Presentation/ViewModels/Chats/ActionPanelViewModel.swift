import Foundation
import Combine

// MARK: - Data Transfer Structures

/// Data passed from ChatViewModel to ActionPanel
struct ActionPanelData {
    let instances: [LLMInstanceContext]
    let instanceSettings: [UUID: SessionInstanceSetting]
    let savedPromptSegments: [SavedPromptSegment]
    let resolvedMessageActionSettings: ResolvedMessageActionSettings?
}

/// Callbacks provided by ChatViewModel for ActionPanel interactions
struct ActionPanelCallbacks {
    let toggleInstanceThinking: (UUID) async -> Bool
    let toggleInstanceNetwork: (UUID) async -> Bool
    let navigateToInstanceDetail: (UUID) -> Void
    let removeInstanceFromSession: (UUID) async -> Bool
    let addInstanceToSession: (UUID) async -> (LLMInstanceContext, SessionInstanceSetting)?
    let handleActionPanelPromptInsert: (MessageAction) -> Void
    let handleActionPanelPromptRewrite: (MessageAction) -> Void
    let handleCameraAction: () -> Void
    let handlePhotoAction: () -> Void
    let handleFileAction: () -> Void
}

// MARK: - Thinking State Enum

enum ThinkingState {
    case notSupported       // gray lightbulb.slash
    case alwaysOn          // blue lightbulb.max.fill (cannot be toggled)
    case enabled           // blue lightbulb.max.fill (can be toggled)
    case disabled          // gray lightbulb (can be toggled)
    
    var iconName: String {
        switch self {
        case .notSupported:
            return "lightbulb.slash"
        case .alwaysOn, .enabled:
            return "lightbulb.max.fill"
        case .disabled:
            return "lightbulb"
        }
    }
    
    var iconColor: String {
        switch self {
        case .notSupported, .disabled:
            return "gray"
        case .alwaysOn, .enabled:
            return "blue"
        }
    }
    
    var canToggle: Bool {
        switch self {
        case .notSupported, .alwaysOn:
            return false
        case .enabled, .disabled:
            return true
        }
    }
    
    var toastMessage: String? {
        switch self {
        case .notSupported:
            return "This model does not support thinking."
        case .alwaysOn:
            return "This model is always on thinking."
        case .enabled, .disabled:
            return nil
        }
    }
}

// MARK: - Network State Enum

enum NetworkState {
    case notSupported
    case enabled
    case disabled
    
    var iconName: String {
        switch self {
        case .notSupported:
            return "network.slash"
        case .enabled, .disabled:
            return "network"
        }
    }
    
    var iconColor: String {
        switch self {
        case .notSupported, .disabled:
            return "gray"
        case .enabled:
            return "blue"
        }
    }
    
    var canToggle: Bool {
        switch self {
        case .notSupported:
            return false
        case .enabled, .disabled:
            return true
        }
    }
    
    var toastMessage: String? {
        switch self {
        case .notSupported:
            return "This model does not support searching."
        case .enabled, .disabled:
            return nil
        }
    }
}

// MARK: - ActionPanelViewModel

final class ActionPanelViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var instances: [LLMInstanceContext]
    @Published var instanceSettings: [UUID: SessionInstanceSetting]
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    @Published var toastIcon: String = "info.circle.fill"
    @Published var showInstanceSelectionSheet: Bool = false
    
    // MARK: - Private Properties

    private let savedPromptSegments: [SavedPromptSegment]
    private let callbacks: ActionPanelCallbacks
    private let resolvedMessageActionSettings: ResolvedMessageActionSettings?
    
    // MARK: - Computed Properties

    /// Get valid action panel actions from resolved settings or fallback to system defaults
    var actionPanelActions: [MessageAction] {
        return resolvedMessageActionSettings?.validActionPanelActions ?? SystemMessageActions.defaultActionPanelActions
    }

    // MARK: - Initialization

    init(data: ActionPanelData, callbacks: ActionPanelCallbacks) {
        self.instances = data.instances
        self.instanceSettings = data.instanceSettings
        self.savedPromptSegments = data.savedPromptSegments
        self.callbacks = callbacks
        self.resolvedMessageActionSettings = data.resolvedMessageActionSettings
    }
    
    // MARK: - Public Methods
    
    /// Add instance to the current list
    func addInstance(context: LLMInstanceContext, settings: SessionInstanceSetting) {
        // Check if instance already exists
        if !instances.contains(where: { $0.instance.id == context.instance.id }) {
            instances.append(context)
            instanceSettings[context.instance.id] = settings
        } else {
            // Update existing settings
            instanceSettings[context.instance.id] = settings
        }
    }
    
    /// Remove instance from the current list
    func removeInstance(instanceId: UUID) {
        instances.removeAll { $0.instance.id == instanceId }
        instanceSettings.removeValue(forKey: instanceId)
    }
    
    /// Get thinking state for a specific instance
    func getThinkingState(for instanceContext: LLMInstanceContext) -> ThinkingState {
        let model = instanceContext.model
        let instanceId = instanceContext.instance.id
        
        // Check model's thinking capabilities
        guard let thinkingCapabilities = model.thinkingCapabilities else {
            return .notSupported
        }
        
        switch thinkingCapabilities.controlType {
        case .some(.none):
            return .notSupported
        case .some(.defaultOn):
            return .alwaysOn
        case .some(.reasoningEffort), .some(.thinkingBudget), .some(.promptBased), .some(.parameterBased):
            // Check session settings for this instance
            let sessionSetting = instanceSettings[instanceId]
            let isEnabled = sessionSetting?.thinkingEnabled ?? false
            return isEnabled ? .enabled : .disabled
        default:
            return .notSupported
        }
    }
    
    /// Get network state for a specific instance
    func getNetworkState(for instanceContext: LLMInstanceContext) -> NetworkState {
        let model = instanceContext.model
        let instanceId = instanceContext.instance.id
        
        // Check model's searching capabilities
        guard let searchingCapabilities = model.searchingCapabilities, searchingCapabilities.controlType != SearchingCapabilities.ControlType.none else {
            return .notSupported
        }
        
        // Check session settings for this instance
        let sessionSetting = instanceSettings[instanceId]
        let isEnabled = sessionSetting?.networkEnabled ?? false
        return isEnabled ? .enabled : .disabled
    }

    // MARK: - Action Panel Callbacks
    
    /// Handle thinking icon tap
    func handleThinkingTap(for instanceContext: LLMInstanceContext) {
        let thinkingState = getThinkingState(for: instanceContext)
        
        if thinkingState.canToggle {
            Task { @MainActor in
                let newThinkingState = await callbacks.toggleInstanceThinking(instanceContext.instance.id)
                updateInstanceThinkingSetting(instanceId: instanceContext.instance.id, enabled: newThinkingState)
            }
        } else if let message = thinkingState.toastMessage {
            showToastMessage(message, icon: "lightbulb")
        }
    }
    
    /// Handle network icon tap
    func handleNetworkTap(for instanceContext: LLMInstanceContext) {
        let networkState = getNetworkState(for: instanceContext)
        
        if networkState.canToggle {
            Task { @MainActor in
                let newNetworkState = await callbacks.toggleInstanceNetwork(instanceContext.instance.id)
                updateInstanceNetworkSetting(instanceId: instanceContext.instance.id, enabled: newNetworkState)
            }
        } else if let message = networkState.toastMessage {
            showToastMessage(message, icon: "network")
        }
    }
    
    /// Handle instance row tap (excluding icon areas)
    func handleInstanceRowTap(for instanceContext: LLMInstanceContext) {
        callbacks.navigateToInstanceDetail(instanceContext.instance.id)
    }
    
    /// Handle instance deletion
    func handleInstanceDeletion(for instanceContext: LLMInstanceContext) {
        let instanceId = instanceContext.instance.id

        // Store the current state for potential rollback
        let originalInstances = instances
        let originalSettings = instanceSettings

        // Immediately remove from UI to prevent SwiftUI collection view inconsistency
        removeInstance(instanceId: instanceId)

        // Handle the backend operation asynchronously
        Task { @MainActor in
            let wasRemoved = await callbacks.removeInstanceFromSession(instanceId)
            if !wasRemoved {
                // Rollback the UI changes if backend operation failed
                instances = originalInstances
                instanceSettings = originalSettings

                // Show error toast
                showToastMessage("Failed to remove instance", icon: "exclamationmark.triangle.fill")
            }
        }
    }
    
    /// Handle add new instance
    func handleAddNewInstance() {
        showInstanceSelectionSheet = true
    }
    
    /// Handle instance selection from sheet
    func handleInstanceSelected(instanceId: UUID) {
        // This will be handled by the callback system
        // The actual logic is in ChatViewModel+ActionPanel
        showInstanceSelectionSheet = false
        print("ActionPanelViewModel: Instance selected: \(instanceId)")
    }
    
    /// Handle action panel prompt insert
    func handleActionPanelPromptInsert(_ action: MessageAction) {
        callbacks.handleActionPanelPromptInsert(action)
    }

    /// Handle action panel prompt rewrite
    func handleActionPanelPromptRewrite(_ action: MessageAction) {
        callbacks.handleActionPanelPromptRewrite(action)
    }

    /// Handle camera action
    func handleCameraAction() {
        callbacks.handleCameraAction()
    }

    /// Handle photo action
    func handlePhotoAction() {
        callbacks.handlePhotoAction()
    }

    /// Handle file action
    func handleFileAction() {
        callbacks.handleFileAction()
    }
    
    /// Add instance to session using the callback
    func addInstanceToSession(instanceId: UUID) async -> (LLMInstanceContext, SessionInstanceSetting)? {
        return await callbacks.addInstanceToSession(instanceId)
    }

    /// Handle instance selection from the selection sheet
    @MainActor
    func handleInstanceSelection(instanceId: UUID) async {
        // Call the new callback which adds the instance to the session
        // and returns the context and settings
        if let (instanceContext, instanceSettings) = await addInstanceToSession(instanceId: instanceId) {
            // Add the instance to the ActionPanelViewModel's local state
            addInstance(context: instanceContext, settings: instanceSettings)
            print("✅ ActionPanelView: Successfully added instance: \(instanceId)")
        } else {
            print("❌ ActionPanelView: Failed to add instance: \(instanceId)")
            // Could show an error toast here if needed
        }
        
        // Dismiss the selection sheet
        showInstanceSelectionSheet = false
    }
    
    // MARK: - Private Methods
    
    /// Update instance thinking setting in local state
    private func updateInstanceThinkingSetting(instanceId: UUID, enabled: Bool) {
        var settings = instanceSettings[instanceId] ?? SessionInstanceSetting()
        settings.thinkingEnabled = enabled
        instanceSettings[instanceId] = settings
        print("ActionPanelViewModel: Updated instance thinking setting \(instanceSettings[instanceId])")
    }
    
    /// Update instance network setting in local state
    private func updateInstanceNetworkSetting(instanceId: UUID, enabled: Bool) {
        var settings = instanceSettings[instanceId] ?? SessionInstanceSetting()
        settings.networkEnabled = enabled
        instanceSettings[instanceId] = settings
        print("ActionPanelViewModel: Updated instance network setting \(instanceSettings[instanceId])")
    }
    
    /// Show toast message
    private func showToastMessage(_ message: String, icon: String) {
        toastMessage = message
        toastIcon = icon
        showToast = true
        
        // Auto hide after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.showToast = false
        }
    }
} 
