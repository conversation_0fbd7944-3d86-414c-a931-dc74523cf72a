import Foundation
import SwiftUI
import Combine

/// ViewModel for managing individual message UI state and display logic
final class MessageViewModel: ObservableObject, Identifiable {
    // MARK: - Published Properties
    
    /// The underlying message data
    @Published var message: Message
    
    /// Display text for streaming updates (initialized from message.content, updated during streaming)
    @Published var displayContentText: String = ""

    /// Thinking text for streaming updates (initialized from message.content, updated during streaming)
    @Published var displayThinkingText: String = ""
    
    /// Instance logo information for this message (if applicable)
    @Published var LLMInstanceContext: LLMInstanceContext?
    
    /// Loading state for content (e.g., images, attachments)
    @Published var isLoadingContent: Bool = false
    
    /// Whether this message is currently selected for continuation
    @Published var isReplied: Bool = false
    
    /// User feedback state
    @Published var userFeedback: MessageFeedback = .none
    
    /// Whether this message is favorited
    @Published var isFavorited: Bool = false

    /// Current chat session setting (subscribed from ChatViewModel)
    @Published var currentChatSessionSetting: ChatSessionSetting?

    /// Resolved message action settings (subscribed from ChatViewModel)
    @Published var resolvedMessageActionSettings: ResolvedMessageActionSettings?
    
    // MARK: - Computed Properties
    
    var id: UUID { message.id }
    
    var role: MessageRole { message.role }
    
    var timestamp: Date { message.timestamp }
    
    var status: MessageStatus { message.status }
    
    var llmInstanceId: UUID? { message.llmInstanceId }
    
    /// Formatted timestamp for display
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: message.timestamp)
    }
    
    /// Whether this is a user message
    var isUserMessage: Bool {
        message.role == .user
    }
    
    /// Whether this is an assistant message
    var isAssistantMessage: Bool {
        message.role == .assistant || message.role == .mergedAssistant
    }
    
    /// Whether this message has content blocks other than text
    var hasNonTextContent: Bool {
        message.content.contains { contentBlock in
            switch contentBlock {
            case .text:
                return false
            default:
                return true
            }
        }
    }
    
    /// Whether this message can be regenerated
    var canRegenerate: Bool {
        isAssistantMessage && status == .received
    }
    
    /// Whether this message can be edited
    var canEdit: Bool {
        isUserMessage && status == .received
    }
    
    // MARK: - Private Properties
    
    private weak var interactionHandler: MessageInteractionHandler?
    private var cancellables = Set<AnyCancellable>()
    
    /// Parent row view model ID for interaction callbacks
    var parentRowViewModelId: UUID?
    
    /// Callback for triggering programmatic swipe animations
    var onProgrammaticSwipeRequested: ((SwipeDirection) -> Void)?
    
    // MARK: - Initialization

    init(
        message: Message,
        LLMInstanceContext: LLMInstanceContext? = nil,
        interactionHandler: MessageInteractionHandler? = nil,
        currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never>? = nil,
        resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never>? = nil
    ) {
        self.message = message
        self.LLMInstanceContext = LLMInstanceContext
        self.interactionHandler = interactionHandler

        // Initialize published properties from message
        self.isReplied = message.isReplied
        self.userFeedback = message.userFeedback
        self.isFavorited = message.isFavorited

        // Initialize display text from message content
        updateDisplayContentText()

        // Subscribe to settings publishers if provided
        subscribeToSettings(
            currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
            resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
        )
    }
    
    // MARK: - Public Methods
    
    /// Update the underlying message data
    func updateMessage(_ newMessage: Message) {
        guard newMessage.id == message.id else {
            print("Warning: Attempting to update MessageViewModel with different message ID")
            return
        }
        
        message = newMessage
        
        // Update published properties
        isReplied = newMessage.isReplied
        userFeedback = newMessage.userFeedback
        isFavorited = newMessage.isFavorited
        
        // Update display text if content changed
        updateDisplayContentText()
    }
    
    /// Append text to display text for streaming
    func appendToDisplayContentTextAndUpdateMessage(_ additionalText: String) {
        displayContentText += additionalText
        var updatedMessage = message
        if case .text(let text) = updatedMessage.content.last {
            updatedMessage.content[updatedMessage.content.count - 1] = .text(text + additionalText)
        } else {
            updatedMessage.content.append(.text(additionalText))
        }
        message = updatedMessage
    }

    /// Append text to thinking text for streaming
    func appendToDisplayThinkingTextAndUpdateMessage(_ additionalText: String) {
        displayThinkingText += additionalText
        var updatedMessage = message
        if case .thinking(let text) = updatedMessage.content.last {
            updatedMessage.content[updatedMessage.content.count - 1] = .thinking(text + additionalText)
        } else {
            updatedMessage.content.append(.thinking(additionalText))
        }
        message = updatedMessage
    }
    
    /// Update message with error status and append error description to display text
    func updateMessageWithError(_ error: APIError) {
        let errorDescription = error.localizedDescription
        let errorText = "Error: \(errorDescription)"
        
        // Update display text immediately for UI
        displayContentText += errorText
        
        // Update underlying message
        var updatedMessage = message
        updatedMessage.status = .error
        
        // Append error content to message content
        updatedMessage.content.append(ContentBlock.text(errorText))
        
        // Update the message
        updateMessage(updatedMessage)
        
        print("📝 Updated message \(id) with error: \(errorDescription)")
    }
    
    /// Update instance logo information
    func updateLLMInstanceContext(_ instanceContext: LLMInstanceContext?) {
        LLMInstanceContext = instanceContext
    }
    
    /// Get all content blocks of a specific type
    func getContentBlocks<T>(ofType type: T.Type) -> [T] {
        return message.content.compactMap { contentBlock in
            switch contentBlock {
            case .text(let text) where T.self == String.self:
                return text as? T
            case .image(let imageInfo) where T.self == ImageInfo.self:
                return imageInfo as? T
            case .attachedFileContent(let fileName, let content) where T.self == (String, String).self:
                return (fileName, content) as? T
            case .diffBlock(let diffBlock) where T.self == DiffBlock.self:
                return diffBlock as? T
            default:
                return nil
            }
        }
    }
    
    /// Check if message has content of specific type
    func hasContentOfType<T>(_ type: T.Type) -> Bool {
        return !getContentBlocks(ofType: type).isEmpty
    }

    /// Whether this message has multimodal content (images or files)
    var hasMultimodalContent: Bool {
        message.content.contains { $0.isMultimodal }
    }

    /// Get all image content blocks
    func getImageBlocks() -> [ImageInfo] {
        message.content.compactMap { block in
            if case .image(let imageInfo) = block {
                return imageInfo
            }
            return nil
        }
    }

    /// Get all file content blocks
    func getFileBlocks() -> [FileInfo] {
        message.content.compactMap { block in
            if case .file(let fileInfo) = block {
                return fileInfo
            }
            return nil
        }
    }

    /// Get raw text content of the message
    func getRawTextContent() -> String {
        let content = message.textContent
        return content.isEmpty ? (displayContentText.isEmpty ? "No text content available" : displayContentText) : content
    }

    func likeMessageFromButton() {
        userFeedback = .liked
        var updatedMessage = message
        updatedMessage.userFeedback = .liked
        updateMessage(updatedMessage)
        onProgrammaticSwipeRequested?(.right)
        interactionHandler?.likeMessage(message)
    }

    func dislikeMessageFromButton() {
        userFeedback = .disliked
        var updatedMessage = message
        updatedMessage.userFeedback = .disliked
        updateMessage(updatedMessage)
        onProgrammaticSwipeRequested?(.left)
        interactionHandler?.dislikeMessage(message)
    }
    
    // MARK: - Interaction Methods
    
    func copyMessage() {
        interactionHandler?.copyMessage(message)
    }
    
    func regenerateSingleMessage() {
        interactionHandler?.regenerateSingleMessage(message)
    }

    func regenerateSingleMessageWithAction(_ action: MessageAction) {
        interactionHandler?.regenerateSingleMessageWithAction(message, action)
    }
    
    func likeMessage() {
        userFeedback = .liked
        var updatedMessage = message
        updatedMessage.userFeedback = .liked
        updateMessage(updatedMessage)
        interactionHandler?.likeMessage(message)
    }
    
    func dislikeMessage() {
        userFeedback = .disliked
        var updatedMessage = message
        updatedMessage.userFeedback = .disliked
        updateMessage(updatedMessage)
        interactionHandler?.dislikeMessage(message)
    }
    
    func editMessage() {
        guard let parentRowViewModelId = parentRowViewModelId else {
            print("Warning: Cannot edit message - parentRowViewModelId is nil")
            return
        }
        interactionHandler?.editMessage(message, fromRowViewModel: parentRowViewModelId)
    }
    
    func toggleFavorite() {
        isFavorited.toggle()
        var updatedMessage = message
        updatedMessage.isFavorited = isFavorited
        updateMessage(updatedMessage)
    }
    
    // MARK: - Private Methods

    /// Subscribe to settings publishers from ChatViewModel
    private func subscribeToSettings(
        currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never>?,
        resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never>?
    ) {
        // Subscribe to current chat session setting changes
        if let settingPublisher = currentChatSessionSettingPublisher {
            settingPublisher
                .sink { [weak self] setting in
                    self?.currentChatSessionSetting = setting
                }
                .store(in: &cancellables)
        }

        // Subscribe to resolved message action settings changes
        if let actionSettingsPublisher = resolvedMessageActionSettingsPublisher {
            actionSettingsPublisher
                .sink { [weak self] actionSettings in
                    self?.resolvedMessageActionSettings = actionSettings
                }
                .store(in: &cancellables)
        }
    }

    private func updateDisplayContentText() {
        // Extract thinking text from message.content
        let thinkingTextBlocks = message.content.compactMap { contentBlock in
            switch contentBlock {
            case .thinking(let text):
                return text
            default:
                return nil
            }
        }
        displayThinkingText = thinkingTextBlocks.joined(separator: "\n\n")

        // Extract text content from message.content
        let textBlocks = message.content.compactMap { contentBlock in
            switch contentBlock {
            case .text(let text):
                return text
            default:
                return nil
            }
        }
        displayContentText = textBlocks.joined(separator: "\n\n")
    }
}

// MARK: - Equatable

extension MessageViewModel: Equatable {
    static func == (lhs: MessageViewModel, rhs: MessageViewModel) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Hashable

extension MessageViewModel: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
