import Foundation
import Combine

// MARK: - Types

enum SheetMode {
    case chatCreation
    case instanceSelection
    case auxiliaryInstanceSelection
}

typealias NewChatCreationAction = (UUID) -> Void
typealias InstanceSelectionAction = (UUID) -> Void
typealias AuxiliaryInstanceSelectionAction = (UUID?) -> Void

final class NewChatSheetViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var allProviders: [LLMProvider] = []
    @Published var instancesByProviderId: [UUID: [LLMInstance]] = [:]
    
    // Dictionaries for quick lookups
    @Published var allProvidersById: [UUID: LLMProvider] = [:]
    @Published var allModelsById: [UUID: LLMModel] = [:]
    @Published var allInstancesById: [UUID: LLMInstance] = [:]

    @Published var searchText: String = ""
    @Published var filteredProviders: [LLMProvider] = []
    @Published var filteredInstancesByProviderId: [UUID: [LLMInstance]] = [:]
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    // MARK: - Properties
    let mode: SheetMode

    // Callbacks for different modes
    private let onCreateNewChat: NewChatCreationAction?
    private let onSelectInstance: InstanceSelectionAction?
    private let onSelectAuxiliaryInstance: AuxiliaryInstanceSelectionAction?

    // Public accessor for auxiliary instance selection
    var onInstanceSelection: AuxiliaryInstanceSelectionAction? {
        return onSelectAuxiliaryInstance
    }

    // MARK: - Private Properties
    private let getAllProvidersUseCase: GetAllProvidersUseCaseProtocol
    private let getAllModelsUseCase: GetAllModelsUseCaseProtocol
    private let getAllInstancesUseCase: GetAllInstancesUseCaseProtocol
    private let getModelUseCase: GetModelUseCaseProtocol
    private let observeModelManagementChangesUseCase: ObserveModelManagementChangesUseCaseProtocol
    private let createChatSessionUseCase: CreateChatSessionUseCaseProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init(
        mode: SheetMode = .chatCreation,
        getAllProvidersUseCase: GetAllProvidersUseCaseProtocol,
        getAllModelsUseCase: GetAllModelsUseCaseProtocol,
        getAllInstancesUseCase: GetAllInstancesUseCaseProtocol,
        getModelUseCase: GetModelUseCaseProtocol,
        observeModelManagementChangesUseCase: ObserveModelManagementChangesUseCaseProtocol,
        createChatSessionUseCase: CreateChatSessionUseCaseProtocol,
        onCreateNewChat: NewChatCreationAction? = nil,
        onSelectInstance: InstanceSelectionAction? = nil,
        onSelectAuxiliaryInstance: AuxiliaryInstanceSelectionAction? = nil
    ) {
        self.mode = mode
        self.getAllProvidersUseCase = getAllProvidersUseCase
        self.getAllModelsUseCase = getAllModelsUseCase
        self.getAllInstancesUseCase = getAllInstancesUseCase
        self.getModelUseCase = getModelUseCase
        self.observeModelManagementChangesUseCase = observeModelManagementChangesUseCase
        self.createChatSessionUseCase = createChatSessionUseCase
        self.onCreateNewChat = onCreateNewChat
        self.onSelectInstance = onSelectInstance
        self.onSelectAuxiliaryInstance = onSelectAuxiliaryInstance

        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                Task { @MainActor [weak self] in
                    self?.applyFilters()
                }
            }
            .store(in: &cancellables)
        
        self.observeModelManagementChangesUseCase.execute()
            .debounce(for: .milliseconds(200), scheduler: DispatchQueue.main)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] in
                guard let self = self else { return }
                if !self.isLoading {
                    Task {
                        await self.loadData()
                    }
                }
            }
            .store(in: &cancellables)

        Task {
            await loadData()
        }
    }

    // MARK: - Public Methods
    
    /// Loads all providers, models and instances data
    @MainActor
    func loadData() async {
        isLoading = true
        errorMessage = nil

        do {
            // Concurrently fetch data
            async let providersResult = getAllProvidersUseCase.execute(onlyUserCreated: nil)
            async let modelsResult = getAllModelsUseCase.execute()
            async let instancesResult = getAllInstancesUseCase.execute()
            
            let (fetchedProviders, fetchedModels, allFetchedInstances) = 
                try await (providersResult, modelsResult, instancesResult)

            self.allProviders = fetchedProviders.sorted { 
                if $0.apiKeyStored != $1.apiKeyStored {
                    return $0.apiKeyStored && !$1.apiKeyStored
                }
                return $0.name < $1.name
            }
            
            // Populate lookup dictionaries
            self.allProvidersById = Dictionary(uniqueKeysWithValues: fetchedProviders.map { ($0.id, $0) })
            self.allModelsById = Dictionary(uniqueKeysWithValues: fetchedModels.map { ($0.id, $0) })
            self.allInstancesById = Dictionary(uniqueKeysWithValues: allFetchedInstances.map { ($0.id, $0) })

            // Process instances to group them by providerId
            var tempInstancesByProviderId: [UUID: [LLMInstance]] = [:]
            for instance in allFetchedInstances {
                if let model = self.allModelsById[instance.modelId] {
                    let providerId = model.providerId
                    tempInstancesByProviderId[providerId, default: []].append(instance)
                } else {
                    print("Error: Could not find model with ID \(instance.modelId) for instance \(instance.id)")
                }
            }
            
            // Sort instances within each provider group
            for (providerId, instances) in tempInstancesByProviderId {
                tempInstancesByProviderId[providerId] = instances.sorted { $0.name < $1.name }
            }
            self.instancesByProviderId = tempInstancesByProviderId
            
            applyFilters()
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }

    /// Applies search filter to providers and instances
    @MainActor
    func applyFilters() {
        let lowercasedSearchText = searchText.lowercased()

        if searchText.isEmpty {
            filteredProviders = allProviders
            filteredInstancesByProviderId = instancesByProviderId
            return
        }

        var tempFilteredInstancesByProviderId: [UUID: [LLMInstance]] = [:]
        var providersWithMatchingInstances = Set<UUID>()

        for (providerId, instances) in instancesByProviderId {
            let matchingInstances = instances.filter { instance in
                instance.name.lowercased().contains(lowercasedSearchText)
            }
            if !matchingInstances.isEmpty {
                tempFilteredInstancesByProviderId[providerId] = matchingInstances
                providersWithMatchingInstances.insert(providerId)
            }
        }
        self.filteredInstancesByProviderId = tempFilteredInstancesByProviderId

        self.filteredProviders = allProviders.filter { provider in
            provider.name.lowercased().contains(lowercasedSearchText) || providersWithMatchingInstances.contains(provider.id)
        }.sorted { 
            if $0.apiKeyStored != $1.apiKeyStored {
                return $0.apiKeyStored && !$1.apiKeyStored
            }
            return $0.name < $1.name
        }
    }
    
    /// Refreshes all data
    @MainActor
    func refreshData() async {
        await loadData()
    }
    
    /// Creates a new chat with the selected instance or handles instance selection
    func handleInstanceSelection(with instanceId: UUID) {
        switch mode {
        case .chatCreation:
            createNewChat(with: instanceId)
        case .instanceSelection:
            onSelectInstance?(instanceId)
        case .auxiliaryInstanceSelection:
            onSelectAuxiliaryInstance?(instanceId)
        }
    }
    
    /// Creates a new chat with the selected instance
    private func createNewChat(with instanceId: UUID) {
        Task {
            do {
                // Build instance context to get default settings
                let instanceSettings: [UUID: SessionInstanceSetting]
                if let context = buildInstanceContext(instanceId: instanceId) {
                    instanceSettings = [instanceId: context.defaultSessionInstanceSetting()]
                } else {
                    // Fallback to default (all false) if context cannot be built
                    instanceSettings = [instanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false)]
                }
                
                let newSession = try await createChatSessionUseCase.execute(
                    title: ChatSession.defaultSessionName,
                    llmInstanceId: instanceId,
                    instanceSettings: instanceSettings
                )
                await MainActor.run {
                    onCreateNewChat?(newSession.id)
                }
            } catch {
                await MainActor.run {
                    let chatError = error as? ChatError ?? ChatError.messageSendFailed(underlying: error)
                    handleError(chatError)
                }
            }
        }
    }

    // MARK: - Private Methods
    
    /// Build LLMInstanceContext from cached data
    private func buildInstanceContext(instanceId: UUID) -> LLMInstanceContext? {
        guard let instance = allInstancesById[instanceId],
              let model = allModelsById[instance.modelId],
              let provider = allProvidersById[model.providerId] else {
            return nil
        }
        return LLMInstanceContext(instance: instance, model: model, provider: provider)
    }
    
    /// Handles errors and updates the error message
    @MainActor
    private func handleError(_ error: ChatError) {
        switch error {
        case .sessionNotFound:
            errorMessage = "Error: Chat session not found."
        case .messageSendFailed(let underlying):
            if let underlying = underlying {
                errorMessage = "Error creating chat: \(underlying.localizedDescription)"
            } else {
                errorMessage = "Error: Failed to create new chat."
            }
        case .apiError(let apiError, _):
            errorMessage = "API Error: \(apiError.localizedDescription)"
        default:
            errorMessage = error.errorDescription ?? "An unexpected error occurred: \(error.localizedDescription)"
        }
        print("NewChatSheetViewModel error: \(String(describing: errorMessage))")
    }
    
    /// Handles model management errors and updates the error message
    @MainActor
    private func handleError(_ error: Error) {
        if let chatError = error as? ChatError {
            handleError(chatError)
            return
        }
        
        if let modelManagementError = error as? ModelManagementError {
            switch modelManagementError {
            case .providerNotFound:
                errorMessage = "Error: Provider not found."
            case .modelNotFound:
                errorMessage = "Error: Model not found. This may affect some items' display."
            case .instanceNotFound:
                errorMessage = "Error: Instance not found."
            case .groupNotFound:
                errorMessage = "Error: Model group not found."
            case .persistenceError(let underlyingError):
                errorMessage = "Database error: \(underlyingError.localizedDescription)"
            default:
                errorMessage = "An error occurred in model management: \(modelManagementError.localizedDescription)"
            }
        } else {
            errorMessage = "An unexpected error occurred: \(error.localizedDescription)"
        }
        print("NewChatSheetViewModel error: \(String(describing: errorMessage))")
    }
}
