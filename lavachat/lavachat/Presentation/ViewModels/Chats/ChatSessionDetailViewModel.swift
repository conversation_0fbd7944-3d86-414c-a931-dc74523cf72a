import Foundation
import Combine

final class ChatSessionDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    
    @Published var chatSession: ChatSession?
    @Published var currentChatSessionSetting: ChatSessionSetting?
    @Published var resolvedMessageActionSettings: ResolvedMessageActionSettings?
    @Published var allChatSessionSettings: [ChatSessionSetting] = []
    @Published var auxiliaryLLMInstance: LLMInstance? = nil
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil

    // MARK: - Private Properties
    
    private let chatSessionId: UUID
    private let getChatSessionUseCase: GetChatSessionUseCaseProtocol
    private let updateChatSessionUseCase: UpdateChatSessionUseCaseProtocol
    private let getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol
    private let getAllChatSessionSettingsUseCase: GetAllChatSessionSettingsUseCaseProtocol
    private let updateChatSessionSettingUseCase: UpdateChatSessionSettingUseCaseProtocol
    private let getInstanceUseCase: GetInstanceUseCaseProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(
        chatSessionId: UUID,
        getChatSessionUseCase: GetChatSessionUseCaseProtocol,
        updateChatSessionUseCase: UpdateChatSessionUseCaseProtocol,
        getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol,
        getAllChatSessionSettingsUseCase: GetAllChatSessionSettingsUseCaseProtocol,
        updateChatSessionSettingUseCase: UpdateChatSessionSettingUseCaseProtocol,
        getInstanceUseCase: GetInstanceUseCaseProtocol
    ) {
        self.chatSessionId = chatSessionId
        self.getChatSessionUseCase = getChatSessionUseCase
        self.updateChatSessionUseCase = updateChatSessionUseCase
        self.getChatSessionSettingUseCase = getChatSessionSettingUseCase
        self.getAllChatSessionSettingsUseCase = getAllChatSessionSettingsUseCase
        self.updateChatSessionSettingUseCase = updateChatSessionSettingUseCase
        self.getInstanceUseCase = getInstanceUseCase
        
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Data Loading
    
    @MainActor
    func loadInitialData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load chat session
            if let session = try await getChatSessionUseCase.execute(sessionId: chatSessionId) {
                self.chatSession = session
                
                // Load current setting
                if let settingsId = session.settingsId {
                    if let (setting, resolvedActions) = try await getChatSessionSettingUseCase.executeAndResolveActions(settingId: settingsId) {
                        self.currentChatSessionSetting = setting
                        self.resolvedMessageActionSettings = resolvedActions
                    }
                }
            }
            
            // Load all available settings
            self.allChatSessionSettings = try await getAllChatSessionSettingsUseCase.execute()

            // Load auxiliary LLM instance if set
            if let auxiliaryId = currentChatSessionSetting?.auxiliaryLLMInstanceId {
                self.auxiliaryLLMInstance = try await getInstanceUseCase.execute(id: auxiliaryId)
            }

        } catch {
            self.errorMessage = "Failed to load session details: \(error.localizedDescription)"
        }
        
        isLoading = false
    }

    // MARK: - Computed Properties

    var displayTitle: String {
        return chatSession?.title ?? "Untitled Chat"
    }

    var currentSettingName: String {
        return currentChatSessionSetting?.name ?? "Unknown Setting"
    }

    var isCurrentSettingSystemDefault: Bool {
        return currentChatSessionSetting?.isSystemDefault ?? false
    }

    var contextMessageCountDisplayText: String {
        guard let setting = currentChatSessionSetting else { return "Unlimited" }
        let count = setting.contextMessageCount

        if count == Int64.max {
            return "Unlimited"
        } else {
            return "\(count)"
        }
    }

    var auxiliaryInstanceDisplayText: String {
        if let instance = auxiliaryLLMInstance {
            return instance.name
        } else {
            return "None"
        }
    }

    var shouldAutoGenerateTitleEnabled: Bool {
        return currentChatSessionSetting?.auxiliaryLLMInstanceId != nil
    }

    // MARK: - Title Management
    
    @MainActor
    func updateTitle(_ newTitle: String) async {
        guard var session = chatSession else { return }
        
        session.title = newTitle.isEmpty ? nil : newTitle
        
        do {
            let updatedSession = try await updateChatSessionUseCase.execute(session)
            self.chatSession = updatedSession
        } catch {
            self.errorMessage = "Failed to update title: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Setting Management
    
    @MainActor
    func switchToSetting(_ settingId: UUID) async {
        guard var session = chatSession else { return }
        
        session.settingsId = settingId
        
        do {
            let updatedSession = try await updateChatSessionUseCase.execute(session)
            self.chatSession = updatedSession
            
            // Reload the new setting
            if let (setting, resolvedActions) = try await getChatSessionSettingUseCase.executeAndResolveActions(settingId: settingId) {
                self.currentChatSessionSetting = setting
                self.resolvedMessageActionSettings = resolvedActions
            }
        } catch {
            self.errorMessage = "Failed to switch setting: \(error.localizedDescription)"
        }
    }
    
    @MainActor
    func updateShouldExpandThinking(_ shouldExpand: Bool) async {
        guard var setting = currentChatSessionSetting else { return }
        
        setting.shouldExpandThinking = shouldExpand
        setting.lastModifiedAt = Date()
        
        do {
            let updatedSetting = try await updateChatSessionSettingUseCase.execute(setting)
            self.currentChatSessionSetting = updatedSetting
        } catch {
            self.errorMessage = "Failed to update thinking expansion setting: \(error.localizedDescription)"
        }
    }
    
    @MainActor
    func updateMessageActionSettings(_ newSettings: MessageActionSettings) async {
        guard var setting = currentChatSessionSetting else { return }
        
        setting.messageActionSettings = newSettings
        setting.lastModifiedAt = Date()
        
        do {
            let updatedSetting = try await updateChatSessionSettingUseCase.execute(setting)
            self.currentChatSessionSetting = updatedSetting
            
            // Reload resolved actions
            if let (_, resolvedActions) = try await getChatSessionSettingUseCase.executeAndResolveActions(settingId: setting.id) {
                self.resolvedMessageActionSettings = resolvedActions
            }
        } catch {
            self.errorMessage = "Failed to update message action settings: \(error.localizedDescription)"
        }
    }

    // MARK: - Auxiliary Settings Management

    @MainActor
    func updateAuxiliaryLLMInstanceId(_ instanceId: UUID?) async {
        guard var setting = currentChatSessionSetting else { return }

        setting.auxiliaryLLMInstanceId = instanceId
        setting.lastModifiedAt = Date()

        do {
            let updatedSetting = try await updateChatSessionSettingUseCase.execute(setting)
            self.currentChatSessionSetting = updatedSetting

            // Load the new auxiliary instance
            if let instanceId = instanceId {
                self.auxiliaryLLMInstance = try await getInstanceUseCase.execute(id: instanceId)
            } else {
                self.auxiliaryLLMInstance = nil
            }
        } catch {
            self.errorMessage = "Failed to update auxiliary LLM instance: \(error.localizedDescription)"
        }
    }

    @MainActor
    func updateShouldAutoGenerateTitle(_ shouldAutoGenerate: Bool) async {
        guard var setting = currentChatSessionSetting else { return }

        setting.shouldAutoGenerateTitle = shouldAutoGenerate
        setting.lastModifiedAt = Date()

        do {
            let updatedSetting = try await updateChatSessionSettingUseCase.execute(setting)
            self.currentChatSessionSetting = updatedSetting
        } catch {
            self.errorMessage = "Failed to update auto-generate title setting: \(error.localizedDescription)"
        }
    }

    @MainActor
    func updateContextMessageCount(_ count: Int64) async {
        guard var setting = currentChatSessionSetting else { return }

        setting.contextMessageCount = count
        setting.lastModifiedAt = Date()

        do {
            let updatedSetting = try await updateChatSessionSettingUseCase.execute(setting)
            self.currentChatSessionSetting = updatedSetting
        } catch {
            self.errorMessage = "Failed to update context message count: \(error.localizedDescription)"
        }
    }

}
