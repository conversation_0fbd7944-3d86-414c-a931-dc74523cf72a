import Foundation
import Combine
import UIKit

// MARK: - ChatViewModel ActionPanel Extension

extension ChatViewModel {
    
    // MARK: - Data Provider Methods
    
    /// Prepare data for ActionPanel
    func getActionPanelData() -> ActionPanelData {
        // Get active instances from session
        let activeInstanceIds = chatSession?.activeLLMInstanceIds ?? []
        let activeInstances = activeInstanceIds.compactMap { instanceId in
            instanceContextCache[instanceId]
        }

        // Get current session settings
        let instanceSettings = chatSession?.instanceSettings ?? [:]

        // Get saved prompt segments from current session setting
        let promptSegments = currentChatSessionSetting?.savedPromptSegments ?? []

        return ActionPanelData(
            instances: activeInstances,
            instanceSettings: instanceSettings,
            savedPromptSegments: promptSegments,
            resolvedMessageActionSettings: resolvedMessageActionSettings
        )
    }
    
    /// Create callbacks for ActionPanel
    func getActionPanelCallbacks() -> ActionPanelCallbacks {
        return ActionPanelCallbacks(
            toggleInstanceThinking: { [weak self] instanceId in
                return await self?.toggleInstanceThinking(instanceId: instanceId) ?? false
            },
            toggleInstanceNetwork: { [weak self] instanceId in
                return await self?.toggleInstanceNetwork(instanceId: instanceId) ?? false
            },
            navigateToInstanceDetail: { [weak self] instanceId in
                self?.navigateToInstanceDetail(instanceId: instanceId)
            },
            removeInstanceFromSession: { [weak self] instanceId in
                return await self?.removeInstanceFromSession(instanceId: instanceId) ?? false
            },
            addInstanceToSession: { [weak self] instanceId in
                await self?.addInstanceToSession(instanceId: instanceId)
            },
            handleActionPanelPromptInsert: { [weak self] action in
                self?.handleActionPanelPromptInsert(action)
            },
            handleActionPanelPromptRewrite: { [weak self] action in
                self?.handleActionPanelPromptRewrite(action)
            },
            handleCameraAction: { [weak self] in
                Task { @MainActor in
                    self?.handleCameraAction()
                }
            },
            handlePhotoAction: { [weak self] in
                Task { @MainActor in
                    self?.handlePhotoAction()
                }
            },
            handleFileAction: { [weak self] in
                Task { @MainActor in
                    self?.handleFileAction()
                }
            }
        )
    }
    
    // MARK: - Action Panel Callback Implementations
    
    /// Toggle thinking capability for an instance
    @MainActor
    func toggleInstanceThinking(instanceId: UUID) async -> Bool {
        guard var session = chatSession else { return false }
        
        // Get current setting for this instance
        var instanceSettings = session.instanceSettings ?? [:]
        var instanceSetting = instanceSettings[instanceId] ?? SessionInstanceSetting()
        
        // Toggle thinking enabled state
        let currentState = instanceSetting.thinkingEnabled ?? false
        let newState = !currentState
        instanceSetting.thinkingEnabled = newState
        
        // Update the settings
        instanceSettings[instanceId] = instanceSetting
        session.instanceSettings = instanceSettings
        
        // Update local chat session
        self.chatSession = session

        // Update the chat session in the database
        do {
            try await messageTreeManagerUseCase.updateChatSessionInCacheAndRepository(chatSession: session)
            print("✅ Toggled thinking for instance \(instanceId): \(newState)")
            return newState
        } catch {
            print("❌ Failed to update chat session: \(error)")
            return currentState
        }
    }
    
    /// Toggle network capability for an instance
    @MainActor
    func toggleInstanceNetwork(instanceId: UUID) async -> Bool {
        guard var session = chatSession else { return false }
        
        // Get current setting for this instance
        var instanceSettings = session.instanceSettings ?? [:]
        var instanceSetting = instanceSettings[instanceId] ?? SessionInstanceSetting()
        
        // Toggle network enabled state
        let currentState = instanceSetting.networkEnabled ?? false
        let newState = !currentState
        instanceSetting.networkEnabled = newState
        
        // Update the settings
        instanceSettings[instanceId] = instanceSetting
        session.instanceSettings = instanceSettings
        
        // Update local chat session
        self.chatSession = session
        
        // Update the chat session in the database
        do {
            try await messageTreeManagerUseCase.updateChatSessionInCacheAndRepository(chatSession: session)
            print("✅ Toggled network for instance \(instanceId): \(newState)")
            return newState
        } catch {
            print("❌ Failed to update chat session: \(error)")
            return currentState
        }
    }
    
    /// Navigate to instance detail view
    func navigateToInstanceDetail(instanceId: UUID) {
        // This will be implemented when we integrate with navigation
        print("Navigate to instance detail for: \(instanceId)")
        
        // In real implementation, this would:
        // 1. Set a navigation state in ChatViewModel
        // 2. Or trigger a navigation event through a coordinator
        // 3. Pass the instanceId to the destination view
    }
    
    /// Remove instance from current session
    @MainActor
    func removeInstanceFromSession(instanceId: UUID) async -> Bool {
        guard var session = chatSession else { return false }
        
        // Check if instance exists in session
        guard session.activeLLMInstanceIds.contains(instanceId) else {
            print("⚠️ Instance \(instanceId) not found in session")
            return false
        }
        
        // Remove from active instance list
        session.activeLLMInstanceIds.removeAll { $0 == instanceId }
        
        // Remove from instance settings
        session.instanceSettings?.removeValue(forKey: instanceId)
        
        // Update local chat session
        self.chatSession = session

        // Update the chat session in the database
        do {
            try await messageTreeManagerUseCase.updateChatSessionInCacheAndRepository(chatSession: session)
            print("✅ Removed instance \(instanceId) from session")
            return true
        } catch {
            print("❌ Failed to update chat session: \(error)")
            return false
        }
    }
    
    /// Add instance to session and return the context and settings
    @MainActor
    func addInstanceToSession(instanceId: UUID) async -> (LLMInstanceContext, SessionInstanceSetting)? {
        guard var session = chatSession else { 
            print("❌ No chat session available")
            return nil 
        }
        
        // Check if instance is already in the session
        if session.activeLLMInstanceIds.contains(instanceId) {
            print("⚠️ Instance \(instanceId) is already in the session")
            return nil
        }
        
        // Get instance context (which will also save the context to the cache if it's not already there)
        guard let instanceContext = await getInstanceContext(for: instanceId) else {
            print("❌ Failed to get instance context for \(instanceId)")
            return nil
        }
        
        // Add to active instance list
        session.activeLLMInstanceIds.append(instanceId)
        
        // Create default instance session settings
        let defaultSettings = instanceContext.defaultSessionInstanceSetting()
        
        // Add to instance settings
        if session.instanceSettings == nil {
            session.instanceSettings = [:]
        }
        session.instanceSettings![instanceId] = defaultSettings
        
        // Update local chat session
        self.chatSession = session
        
        print("✅ Added instance \(instanceId) to session")

        // Update the chat session in the database
        do {
            try await messageTreeManagerUseCase.updateChatSessionInCacheAndRepository(chatSession: session)
        } catch {
            print("❌ Failed to update chat session: \(error)")
        }
        
        return (instanceContext, defaultSettings)
    }
    
    /// Handle action panel prompt insert
    func handleActionPanelPromptInsert(_ action: MessageAction) {
        print("📝 [ChatViewModel] Handle action panel prompt insert: \(action.name)")

        guard !action.prompts.isEmpty else {
            print("❌ Error: No prompts provided for action panel prompt insert")
            return
        }
        
        Task { @MainActor in
            if action.prompts.count == 1 {
                // Single prompt: insert directly
                executePromptInsert(prompt: action.prompts[0])
            } else {
                // Multiple prompts: show selection sheet
                promptSelectionAction = action
                promptSelectionMessage = nil // Not needed for action panel actions
                showingPromptSelection = true
            }
        }
    }

    /// Handle action panel prompt rewrite
    func handleActionPanelPromptRewrite(_ action: MessageAction) {
        print("✨ [ChatViewModel] Handle action panel prompt rewrite: \(action.name)")

        guard !action.prompts.isEmpty else {
            print("❌ Error: No prompts provided for action panel prompt rewrite")
            return
        }
        
        Task { @MainActor in
            if action.prompts.count == 1 {
                // Single prompt: replace directly
                executePromptRewrite(prompt: action.prompts[0])
            } else {
                // Multiple prompts: show selection sheet
                promptSelectionAction = action
                promptSelectionMessage = nil // Not needed for action panel actions
                showingPromptSelection = true
            }
        }
    }

    // MARK: - Private Helper Methods

    /// Execute prompt insert with a specific prompt
    @MainActor
    internal func executePromptInsert(prompt: String) {
        print("📝 [ChatViewModel] Executing prompt insert: '\(prompt)'")

        // Insert prompt into the input field
        // Since iOS TextEditor doesn't provide easy cursor position access,
        // we'll use a smart insertion strategy:
        // 1. If input is empty, insert the prompt directly
        // 2. If input exists, append with proper spacing

        if newMessageText.isEmpty {
            newMessageText = prompt
        } else {
            // Add appropriate spacing based on existing content
            let trimmedText = newMessageText.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmedText.isEmpty {
                newMessageText = prompt
            } else {
                // Add double newline for clear separation
                newMessageText = trimmedText + "\n\n" + prompt
            }
        }

        print("✅ [ChatViewModel] Prompt inserted successfully")
    }

    /// Execute prompt rewrite with a specific prompt
    @MainActor
    internal func executePromptRewrite(prompt: String) {
        print("✨ [ChatViewModel] Executing prompt rewrite: '\(prompt)'")

        // Store the original input
        let originalInput = newMessageText.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check if input field is empty
        if originalInput.isEmpty {
            print("⚠️ [ChatViewModel] Input field is empty, showing toast reminder")
            showEmptyInputToast = true
            return
        }

        // Check if we have a current chat session
        guard let currentChatSession = chatSession else {
            print("❌ Error: Chat session not found")
            handleError(ChatError.sessionNotFound)
            return
        }

        Task {
            do {
                print("🔄 Starting prompt rewrite with RewritePromptWithPromptUseCase")

                guard let chatSessionSetting = currentChatSessionSetting else {
                    throw ChatError.sessionNotFound
                }

                // Get the streaming response
                let stream = try await rewritePromptWithPromptUseCase.execute(
                    originalInput: originalInput,
                    prompt: prompt,
                    chatSession: currentChatSession,
                    chatSessionSetting: chatSessionSetting
                )

                // Handle the streaming response
                await handlePromptRewriteStreaming(stream: stream, originalInput: originalInput)

            } catch {
                await MainActor.run {
                    // Restore original input on error
                    newMessageText = originalInput
                    print("❌ [ChatViewModel] Prompt Rewrite failed: \(error)")
                    handleError(ChatError.from(error))
                }
            }
        }
    }

    // MARK: - Prompt Rewrite Streaming

    /// Handle streaming response for prompt rewrite
    @MainActor
    private func handlePromptRewriteStreaming(stream: AsyncThrowingStream<LLMStreamingResponse, Error>, originalInput: String) async {
        // Set sending state to enable pause functionality
        isSendingMessage = true

        // Initialize state for pattern detection
        var fullResponse = ""
        var isInsideRewrittenPrompt = false
        var extractedContent = ""
        var spinnerIndex = 0
        let spinnerChars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧"]

        // Start with loading indicator
        self.newMessageText = "\(spinnerChars[0]) Processing"

        // Timer for spinner animation when not inside rewritten_prompt tags
        let spinnerTimer = Timer.scheduledTimer(withTimeInterval: 0.25, repeats: true) { [weak self] _ in
            Task { @MainActor in
                guard let self = self else { return }
                if !isInsideRewrittenPrompt {
                    spinnerIndex = (spinnerIndex + 1) % spinnerChars.count
                    self.newMessageText = "\(spinnerChars[spinnerIndex]) Processing"
                }
            }
        }

        do {
            for try await response in stream {
                switch response.responseType {
                case .contentDelta(let content):
                    fullResponse += content

                    // Check if we've entered the rewritten_prompt section
                    if !isInsideRewrittenPrompt && fullResponse.contains("<rewritten_prompt>") {
                        isInsideRewrittenPrompt = true
                        spinnerTimer.invalidate()

                        // Extract content after <rewritten_prompt> tag
                        if let startRange = fullResponse.range(of: "<rewritten_prompt>") {
                            let afterTag = String(fullResponse[startRange.upperBound...])
                            extractedContent = afterTag
                            self.newMessageText = extractedContent
                        }
                    } else if isInsideRewrittenPrompt {
                        // We're inside the rewritten_prompt section, append new content
                        extractedContent += content
                        self.newMessageText = extractedContent
                    }

                case .thinkingDelta(_):
                    // Ignore thinking content for prompt rewrite
                    break

                case .error(let error):
                    spinnerTimer.invalidate()
                    print("❌ [ChatViewModel] Prompt Rewrite streaming error: \(error)")
                    throw ChatError.apiError(error)

                case .completionMarker(_):
                    spinnerTimer.invalidate()
                    print("✅ [ChatViewModel] Prompt Rewrite streaming completed")

                    // Final processing: extract content from <rewritten_prompt> tags
                    let finalPrompt = extractRewrittenPrompt(from: fullResponse)
                    if !finalPrompt.isEmpty {
                        self.newMessageText = finalPrompt
                        print("🎯 Final extracted prompt: '\(finalPrompt)'")
                    } else {
                        // No valid content found, show error toast and revert
                        self.newMessageText = originalInput
                        self.showRewriteFailedToast = true
                    }
                    break

                case .statusUpdate(_):
                    // Ignore status updates
                    break
                }
            }
        } catch {
            spinnerTimer.invalidate()
            // Show error toast and revert to original
            self.newMessageText = originalInput
            self.showRewriteFailedToast = true
            print("❌ [ChatViewModel] Prompt Rewrite streaming failed: \(error)")
            handleError(ChatError.from(error))
        }

        // Reset sending state
        isSendingMessage = false
    }

    /// Extract the rewritten prompt from LLM response
    private func extractRewrittenPrompt(from response: String) -> String {
        // Look for content within <rewritten_prompt> tags
        let pattern = "<rewritten_prompt>(.*?)</rewritten_prompt>"

        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators])
            let range = NSRange(location: 0, length: response.utf16.count)

            if let match = regex.firstMatch(in: response, options: [], range: range) {
                let matchRange = Range(match.range(at: 1), in: response)
                if let matchRange = matchRange {
                    let extractedContent = String(response[matchRange])
                    return extractedContent.trimmingCharacters(in: .whitespacesAndNewlines)
                }
            }
        } catch {
            print("❌ [ChatViewModel] Regex error: \(error)")
        }

        // Fallback: if no <rewritten_prompt> tags found, return empty string to indicate failure
        print("⚠️ [ChatViewModel] No <rewritten_prompt> tags found in response")
        return ""
    }
}

// MARK: - File Selection Extension

extension ChatViewModel {

    // MARK: - File Selection Actions

    /// Handle camera action from ActionPanel
    @MainActor
    func handleCameraAction() {
        imageSourceType = .camera
        isImagePickerPresented = true
    }

    /// Handle photo action from ActionPanel
    @MainActor
    func handlePhotoAction() {
        isPhotosPickerPresented = true
    }

    /// Handle file action from ActionPanel
    @MainActor
    func handleFileAction() {
        isDocumentPickerPresented = true
    }

    /// Handle selected files after document picker dismisses
    @MainActor
    func handleSelectedFiles() {
        guard !selectedFiles.isEmpty else { return }

        let totalBytes = selectedFiles.reduce(0) { result, url in
            guard let fileSize = try? url.resourceValues(forKeys: [.fileSizeKey]).fileSize else { return result }
            return result + fileSize
        }

        // Very rough token estimation (1 byte ≈ 0.25 tokens for English text is a common approximation)
        estimatedTokenCount = Int(Double(totalBytes) * 0.25)

        // Show warning if estimated token count is large
        if estimatedTokenCount > 4000 {
            isShowingTokenWarningAlert = true
        } else {
            Task {
                await sendFileContents()
            }
        }
    }

    /// Remove image at index
    @MainActor
    func removeImage(at index: Int) {
        guard index < selectedImages.count else { return }
        selectedImages.remove(at: index)
    }

    /// Remove file at index
    @MainActor
    func removeFile(at index: Int) {
        guard index < selectedFiles.count else { return }
        selectedFiles.remove(at: index)
    }

    /// Clear all selected media
    @MainActor
    func clearSelectedMedia() {
        selectedImages = []
        selectedFiles = []
        estimatedTokenCount = 0

        // Clean up any temporary editing files
        cleanupTemporaryEditingFiles()
    }

    /// Send file contents as message using FileProcessor
    @MainActor
    func sendFileContents() async {
        var contentBlocks: [ContentBlock] = []
        var hasErrors = false

        // 1. Add text content if present
        let textContent = newMessageText.trimmingCharacters(in: .whitespacesAndNewlines)
        if !textContent.isEmpty {
            contentBlocks.append(.text(textContent))
        }

        // 2. Process images (Following UI order)
        for image in selectedImages {
            do {
                guard let imageData = image.jpegData(compressionQuality: 0.8) else {
                    print("Failed to convert image to JPEG data")
                    hasErrors = true
                    continue
                }

                // Get API style from current LLM instances
                let apiStyle = getCurrentAPIStyle()

                let result = try FileProcessor.processImage(
                    data: imageData,
                    mimeType: "image/jpeg",
                    for: apiStyle
                )
                contentBlocks.append(result.contentBlock)

                print("✅ Processed image: \(result.originalSize) -> \(result.processedSize) bytes, compressed: \(result.wasCompressed)")

            } catch {
                let errorDescription = (error as? ChatError)?.errorDescription ?? error.localizedDescription
                print("❌ Failed to process image: \(errorDescription) with error: \(error)")
                fileProcessingErrorMessage = "Failed to process image: \(errorDescription)"
                isShowingFileProcessingErrorAlert = true
                hasErrors = true
            }
        }

        // 3. Process files (Following UI order)
        for fileUrl in selectedFiles {
            do {
                let fileData = try Data(contentsOf: fileUrl)
                let mimeType = fileUrl.mimeType()

                // Get API style from current LLM instances
                let apiStyle = getCurrentAPIStyle()

                let result = try FileProcessor.processFile(
                    data: fileData,
                    fileName: fileUrl.lastPathComponent,
                    mimeType: mimeType,
                    for: apiStyle
                )
                contentBlocks.append(result.contentBlock)

                print("✅ Processed file: \(fileUrl.lastPathComponent), \(result.originalSize) -> \(result.processedSize) bytes, compressed: \(result.wasCompressed)")

            } catch {
                let errorDescription = (error as? ChatError)?.errorDescription ?? error.localizedDescription
                print("❌ Failed to process file \(fileUrl.lastPathComponent): \(errorDescription) with error: \(error)")
                fileProcessingErrorMessage = "Failed to process file \(fileUrl.lastPathComponent): \(errorDescription)"
                isShowingFileProcessingErrorAlert = true
                hasErrors = true
            }
        }

        // 4. Send message if we have content
        if !contentBlocks.isEmpty {
            await sendMessage(content: contentBlocks)
        } else if hasErrors {
            // Show error toast if no content was processed due to errors
            // TODO: Add error toast handling
            print("⚠️ No content could be processed for sending")
        }
    }

    /// Get current API style from active LLM instances
    private func getCurrentAPIStyle() -> APIStyle {
        // Get the first active instance's API style, default to OpenAI Compatible
        guard let firstInstanceId = chatSession?.activeLLMInstanceIds.first,
              let instanceContext = instanceContextCache[firstInstanceId] else {
            return .openaiCompatible
        }

        return instanceContext.provider.apiStyle
    }
}
