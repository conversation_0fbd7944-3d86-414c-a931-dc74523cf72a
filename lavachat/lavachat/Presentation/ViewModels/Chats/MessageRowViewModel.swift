import Foundation
import SwiftUI
import Combine
import UIKit

/// ViewModel for managing a row of messages
final class MessageRowViewModel: ObservableObject, Identifiable {
    // MARK: - Published Properties
    
    /// Array of message view models for this row
    @Published var messageViewModels: [MessageViewModel]
    
    /// Instance logos for messages in this row
    @Published var instanceLogos: [UUID: LLMInstanceContext]
    
    /// UI state for user messages - active edit version
    @Published var activeUserMessageId: UUID?

    /// UI state for assistant messages - active assistant message id
    @Published var activeAssistantMessageId: UUID?

    /// Reply groups for assistant messages (organized by original message + regenerations)
    @Published var assistantReplyGroupViewModels: [AssistantReplyGroupViewModel] = []
    
    /// UI state for assistant messages - current visible group index
    @Published var currentAssistantReplyGroupViewModelIndex = 0
    
    /// UI state for assistant messages - dismissed reply groups
    @Published var dislikedAssistantReplyGroupViewModelIDs: Set<UUID> = []
    
    /// UI state for assistant messages - display order of reply groups
    @Published var assistantReplyGroupViewModelDisplayOrder: [UUID] = []
    
    /// Animation state
    @Published var isSwipeAnimationInProgress = false
    
    /// Programmatic swipe animation state
    @Published var shouldTriggerProgrammaticSwipe = false
    @Published var programmaticSwipeDirection: SwipeDirection?
    @Published var programmaticSwipeGroupId: UUID?
    
    /// Toast state for single card interactions
    @Published var showToast = false
    @Published var toastMessage = ""
    @Published var toastIcon = ""

    /// Current chat session setting (subscribed from ChatViewModel)
    @Published var currentChatSessionSetting: ChatSessionSetting?

    /// Resolved message action settings (subscribed from ChatViewModel)
    @Published var resolvedMessageActionSettings: ResolvedMessageActionSettings?
    
    // MARK: - Computed Properties
    
    let id = UUID()
    
    /// Whether this row contains user messages
    var containsUserMessages: Bool {
        messageViewModels.contains { $0.isUserMessage }
    }
    
    /// Whether this row contains assistant messages
    var containsAssistantMessages: Bool {
        messageViewModels.contains { $0.isAssistantMessage }
    }
    
    /// Primary message role for this row
    var primaryMessageRole: MessageRole? {
        messageViewModels.first?.role
    }
    
    /// User messages in this row
    var userMessageViewModels: [MessageViewModel] {
        messageViewModels.filter { $0.isUserMessage }
    }
    
    /// Assistant messages in this row
    var assistantMessageViewModels: [MessageViewModel] {
        messageViewModels.filter { $0.isAssistantMessage }
    }
    
    /// Currently active user message view model
    var activeUserMessageViewModel: MessageViewModel? {
        guard let activeId = activeUserMessageId else {
            return userMessageViewModels.last // Default to last
        }
        return userMessageViewModels.first { $0.id == activeId }
    }
    
    /// Visible reply group view models (excluding disliked ones) in display order
    var visibleAssistantReplyGroupViewModels: [AssistantReplyGroupViewModel] {
        let allGroups = assistantReplyGroupViewModels
        return assistantReplyGroupViewModelDisplayOrder
            .filter { !dislikedAssistantReplyGroupViewModelIDs.contains($0) }
            .compactMap { id in allGroups.first { $0.id == id } }
    }
    
    /// Whether this row has only a single visible card
    var isSingleCard: Bool {
        visibleAssistantReplyGroupViewModels.count == 1
    }
    
    // MARK: - Private Properties
    
    private weak var interactionHandler: MessageInteractionHandler?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(
        messageViewModels: [MessageViewModel],
        instanceLogos: [UUID: LLMInstanceContext] = [:],
        interactionHandler: MessageInteractionHandler? = nil,
        activeUserMessageId: UUID? = nil,
        activeAssistantMessageId: UUID? = nil,
        currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never>? = nil,
        resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never>? = nil
    ) {
        self.messageViewModels = messageViewModels
        self.instanceLogos = instanceLogos
        self.interactionHandler = interactionHandler

        // Set active message IDs before initializing UI state
        self.activeUserMessageId = activeUserMessageId
        self.activeAssistantMessageId = activeAssistantMessageId

        // Set parent row view model ID for all message view models
        for messageViewModel in self.messageViewModels {
            messageViewModel.parentRowViewModelId = self.id
        }

        // Subscribe to settings publishers first to get initial values
        subscribeToSettings(
            currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
            resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
        )

        // Initialize UI state based on message types (after settings subscription)
        initializeUIState()

        // Set up observers
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /// Update the message view models
    func updateMessageViewModels(_ newViewModels: [MessageViewModel]) {
        messageViewModels = newViewModels
        initializeUIState()
    }
    
    /// Update instance logos
    func updateInstanceLogos(_ newLogos: [UUID: LLMInstanceContext]) {
        instanceLogos = newLogos
        
        // Update logo info in message view models
        for messageViewModel in messageViewModels {
            if let instanceId = messageViewModel.llmInstanceId,
               let instanceContext = newLogos[instanceId] {
                messageViewModel.updateLLMInstanceContext(instanceContext)
            }
        }
    }
    
    /// Add a new regenerated assistant message view model to the appropriate group and set it as active
    func addAndActivateRegeneratedAssistantMessageViewModel(_ messageViewModel: MessageViewModel) {
        messageViewModels.append(messageViewModel)

        // Set parent row view model ID
        messageViewModel.parentRowViewModelId = self.id
        
        // Update instance logo if needed
        if let instanceId = messageViewModel.llmInstanceId,
           let instanceContext = instanceLogos[instanceId] {
            messageViewModel.updateLLMInstanceContext(instanceContext)
        }
        
        // Find the appropriate AssistantReplyGroupViewModel for this regeneration
        // The regeneration should be added to the group of its original message
        guard let originalMessageId = messageViewModel.message.isRegenerationOf else {
            print("❌ Error: Regenerated message must have isRegenerationOf set")
            return
        }
        
        // Find the group that contains the original message
        if let targetGroupViewModel = assistantReplyGroupViewModels.first(where: { groupViewModel in
            groupViewModel.messageViewModels.contains { $0.message.id == originalMessageId } ||
            groupViewModel.messageViewModels.contains { $0.message.isRegenerationOf == originalMessageId }
        }) {
            targetGroupViewModel.addAndActivateNewRegeneration(messageViewModel)
        } else {
            print("❌ Error: Could not find target group for regeneration")
        }
    }

    /// Add a new edited user message view model to the row and set it as active
    func addAndActivateNewEditedUserMessageViewModel(_ messageViewModel: MessageViewModel) {
        messageViewModels.append(messageViewModel)

        // Set parent row view model ID
        messageViewModel.parentRowViewModelId = self.id

        // Set as the active message
        activeUserMessageId = messageViewModel.id

        // Do not need to notify interaction handler of the change
        // Edit will create new assistant message row view models after this layer at ChatViewModel
    }
    
    /// Remove a message view model from this row
    func removeMessageViewModel(withId messageId: UUID) {
        messageViewModels.removeAll { $0.id == messageId }
        initializeUIState()
    }
    
    /// Handle swipe gesture on assistant reply groups
    func handleAssistantReplySwipe(direction: SwipeDirection, groupId: UUID) {
        guard let index = assistantReplyGroupViewModelDisplayOrder.firstIndex(of: groupId) else { return }
        let previousActiveMessageId = visibleAssistantReplyGroupViewModels.first?.activeMessageViewModel?.id
        
        switch direction {
        case .left:
            // Dislike the group
            dislikedAssistantReplyGroupViewModelIDs.insert(groupId)
            
            // Find the group and dislike all messages in it
            if let group = assistantReplyGroupViewModels.first(where: { $0.id == groupId }) {
                for messageViewModel in group.messageViewModels {
                    messageViewModel.dislikeMessage()
                }
            }
            
            // Remove from display order
            assistantReplyGroupViewModelDisplayOrder.remove(at: index)
            
        case .right:
            // Like the group
            dislikedAssistantReplyGroupViewModelIDs.remove(groupId)
            
            // Find the group and like the current message in it
            if let group = assistantReplyGroupViewModels.first(where: { $0.id == groupId }),
               let activeMessageViewModel = group.activeMessageViewModel {
                activeMessageViewModel.likeMessage()
            }
            
            // Move to the end of display order (unless it's already at the end)
            if index < assistantReplyGroupViewModelDisplayOrder.count - 1 {
                assistantReplyGroupViewModelDisplayOrder.remove(at: index)
                assistantReplyGroupViewModelDisplayOrder.append(groupId)
            }
        }
        
        // Reset current index if needed
        if currentAssistantReplyGroupViewModelIndex >= visibleAssistantReplyGroupViewModels.count {
            currentAssistantReplyGroupViewModelIndex = 0
        }
        
        // Update activeAssistantMessageId to reflect the new current active message
        if let currentVisibleGroupViewModel = visibleAssistantReplyGroupViewModels.first,
           let activeMessage = currentVisibleGroupViewModel.activeMessageViewModel {
            activeAssistantMessageId = activeMessage.id
            
            // Notify interaction handler of the change
            if previousActiveMessageId == nil || activeMessage.id != previousActiveMessageId {
                interactionHandler?.assistantMessageDidChange(activeMessage.message, fromRowViewModel: id)
            }
        }
    }
    
    /// Navigate to previous user message edit
    func showPreviousUserEdit() {
        guard let currentIndex = userMessageViewModels.firstIndex(where: { $0.id == activeUserMessageId }) else {
            return
        }
        
        if currentIndex > 0 {
            activeUserMessageId = userMessageViewModels[currentIndex - 1].id
            notifyUserMessageChanged()
        }
    }
    
    /// Navigate to next user message edit
    func showNextUserEdit() {
        guard let currentIndex = userMessageViewModels.firstIndex(where: { $0.id == activeUserMessageId }) else {
            return
        }
        
        if currentIndex < userMessageViewModels.count - 1 {
            activeUserMessageId = userMessageViewModels[currentIndex + 1].id
            notifyUserMessageChanged()
        }
    }
    
    /// Handle single card like operation with toast feedback
    func handleSingleCardLike(groupId: UUID) {
        // Find the group and like the current message in it
        if let groupViewModel = assistantReplyGroupViewModels.first(where: { $0.id == groupId }),
           let activeMessageViewModel = groupViewModel.activeMessageViewModel {
            activeMessageViewModel.likeMessage()
            
            // Show toast feedback
            toastMessage = "Liked. End of replies."
            toastIcon = "hand.thumbsup.fill"
            showToast = true
        }
    }
    
    /// Trigger programmatic swipe animation for like/dislike button interactions
    func triggerProgrammaticSwipe(direction: SwipeDirection, groupId: UUID) {
        // Prevent multiple animations
        guard !isSwipeAnimationInProgress && !shouldTriggerProgrammaticSwipe else { return }
        
        // Check if this is a single card scenario
        if isSingleCard && direction == .right {
            // For single card like, just trigger the toast without animation
            handleSingleCardLike(groupId: groupId)
            return
        }
        
        // Set up programmatic swipe state
        programmaticSwipeDirection = direction
        programmaticSwipeGroupId = groupId
        shouldTriggerProgrammaticSwipe = true
        isSwipeAnimationInProgress = true
    }
    
    /// Complete the programmatic swipe operation (called after animation finishes)
    func completeProgrammaticSwipe() {
        guard let direction = programmaticSwipeDirection,
              let groupId = programmaticSwipeGroupId else { return }
        
        // Perform the actual swipe logic
        handleAssistantReplySwipe(direction: direction, groupId: groupId)
        
        // Reset programmatic swipe state
        shouldTriggerProgrammaticSwipe = false
        programmaticSwipeDirection = nil
        programmaticSwipeGroupId = nil
        isSwipeAnimationInProgress = false
    }

    /// Regenerate all messages in this row
    func regenerateAllMessages() {
        interactionHandler?.regenerateAllMessages(fromRowViewModel: id)
    }
    
    // MARK: - Private Methods
    
    private func initializeUIState() {
        if containsUserMessages {
            // Use the activeUserMessageId set during initialization, or fall back to latest
            if activeUserMessageId == nil {
                activeUserMessageId = userMessageViewModels.last?.id
            }
        }
        
        if containsAssistantMessages {
            // Initialize assistant message state
            currentAssistantReplyGroupViewModelIndex = 0
            
            // Organize assistant reply groups
            assistantReplyGroupViewModels = organizeAssistantReplyGroupViewModels()
            
            // Initialize display order if empty or doesn't match current groups
            let currentGroupIds = Set(assistantReplyGroupViewModels.map { $0.id })
            if assistantReplyGroupViewModelDisplayOrder.isEmpty || !Set(assistantReplyGroupViewModelDisplayOrder).isSubset(of: currentGroupIds) {
                assistantReplyGroupViewModelDisplayOrder = assistantReplyGroupViewModels.map { $0.id }
            }
            
            // Initialize disliked groups based on message feedback
            dislikedAssistantReplyGroupViewModelIDs = Set(
                assistantReplyGroupViewModels.filter { groupViewModel in
                    groupViewModel.messageViewModels.contains { $0.userFeedback == .disliked }
                }.map { $0.id }
            )
        }
    }
    
    private func setupObservers() {
        // Set up observers for message view model changes if needed
        // For now, we'll handle updates through direct method calls
    }

    /// Subscribe to settings publishers from ChatViewModel
    private func subscribeToSettings(
        currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never>?,
        resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never>?
    ) {
        // Subscribe to current chat session setting changes
        if let settingPublisher = currentChatSessionSettingPublisher {
            settingPublisher
                .sink { [weak self] setting in
                    self?.currentChatSessionSetting = setting
                }
                .store(in: &cancellables)
        }

        // Subscribe to resolved message action settings changes
        if let actionSettingsPublisher = resolvedMessageActionSettingsPublisher {
            actionSettingsPublisher
                .sink { [weak self] actionSettings in
                    self?.resolvedMessageActionSettings = actionSettings
                }
                .store(in: &cancellables)
        }
    }
    
    private func organizeAssistantReplyGroupViewModels() -> [AssistantReplyGroupViewModel] {
        let assistantMessages = assistantMessageViewModels
        
        // Edge case protection - empty assistant messages
        guard !assistantMessages.isEmpty else {
            return []
        }
        
        // Build regeneration groups using lookup table approach
        let regenerationGroups = buildRegenerationGroups(from: assistantMessages)
        
        // Convert to AssistantReplyGroupViewModel objects
        var groups: [AssistantReplyGroupViewModel] = []
        var groupPreferenceIndexMap: [UUID: Int] = [:]
        var activeGroupId: UUID? = nil
        
        for (rootMessageId, groupMessages) in regenerationGroups {
            // Sort messages by timestamp within each group
            let sortedGroupMessages = groupMessages.sorted { $0.timestamp < $1.timestamp }
            
            // Calculate group preference index (based on the earliest message in the group)
            let groupMessageIds = Set(sortedGroupMessages.map { $0.id })
            let minIndex = assistantMessages.enumerated().compactMap { (index, msgVM) in
                groupMessageIds.contains(msgVM.id) ? index : nil
            }.min() ?? assistantMessages.count
            groupPreferenceIndexMap[rootMessageId] = minIndex
            
            // Determine active message for this group
            var activeMessageViewModel: MessageViewModel?
            if let activeAssistantMessageId = activeAssistantMessageId {
                activeMessageViewModel = sortedGroupMessages.first { $0.id == activeAssistantMessageId }
                
                // Identify active group
                if activeMessageViewModel != nil {
                    activeGroupId = rootMessageId
                }
            }
            if activeMessageViewModel == nil {
                activeMessageViewModel = sortedGroupMessages.last
            }
            
            // Set up programmatic swipe callback for all message view models in this group
            for messageVM in sortedGroupMessages {
                messageVM.onProgrammaticSwipeRequested = { [weak self] direction in
                    self?.triggerProgrammaticSwipe(direction: direction, groupId: rootMessageId)
                }
            }
            
            // Create the group
            let group = AssistantReplyGroupViewModel(
                id: rootMessageId, // Use root message ID as group ID
                messageViewModels: sortedGroupMessages,
                activeMessageViewModel: activeMessageViewModel,
                interactionHandler: interactionHandler,
                parentRowViewModel: self,
                currentChatSessionSettingPublisher: $currentChatSessionSetting.eraseToAnyPublisher(),
                resolvedMessageActionSettingsPublisher: $resolvedMessageActionSettings.eraseToAnyPublisher()
            )
            groups.append(group)
        }
        
        // Sort groups by preference index
        groups.sort { group1, group2 in
            let index1 = groupPreferenceIndexMap[group1.id] ?? Int.max
            let index2 = groupPreferenceIndexMap[group2.id] ?? Int.max
            return index1 < index2
        }
        
        // Implement shift logic for active group
        if let activeGroupId = activeGroupId,
           let activeGroupIndex = groups.firstIndex(where: { $0.id == activeGroupId }) {
            // Move active group and subsequent groups to front
            let beforeActive = Array(groups[0..<activeGroupIndex])
            let fromActive = Array(groups[activeGroupIndex...])
            groups = fromActive + beforeActive
        }
        
        return groups
    }
    
    /// Builds regeneration groups using a lookup table approach with O(N) complexity
    /// Handles multi-level regenerations (e.g., A -> B -> C)
    private func buildRegenerationGroups(from assistantMessages: [MessageViewModel]) -> [UUID: [MessageViewModel]] {
        // Step 1: Build lookup table to find root message for each message
        var messageToRootMap: [UUID: UUID] = [:]
        
        // Initialize: each message maps to itself initially
        for messageVM in assistantMessages {
            messageToRootMap[messageVM.id] = messageVM.id
        }
        
        // Build parent lookup table for efficient traversal
        var parentLookup: [UUID: UUID] = [:]
        for messageVM in assistantMessages {
            if let parentId = messageVM.message.isRegenerationOf {
                parentLookup[messageVM.id] = parentId
            }
        }
        
        // Step 2: Find root message for each message using path compression
        func findRoot(messageId: UUID) -> UUID {
            var current = messageId
            var path: [UUID] = []
            
            // Traverse up to find root
            while let parent = parentLookup[current] {
                path.append(current)
                current = parent
            }
            
            // Path compression: update all messages in path to point directly to root
            let root = current
            for messageInPath in path {
                messageToRootMap[messageInPath] = root
            }
            
            return root
        }
        
        // Update all mappings to point to their actual roots
        for messageVM in assistantMessages {
            messageToRootMap[messageVM.id] = findRoot(messageId: messageVM.id)
        }
        
        // Step 3: Group messages by their root message
        var rootToMessagesMap: [UUID: [MessageViewModel]] = [:]
        
        for messageVM in assistantMessages {
            guard let rootId = messageToRootMap[messageVM.id] else { continue }
            
            if rootToMessagesMap[rootId] == nil {
                rootToMessagesMap[rootId] = []
            }
            rootToMessagesMap[rootId]?.append(messageVM)
        }
        
        return rootToMessagesMap
    }
    
    private func notifyUserMessageChanged() {
        guard let activeMessageViewModel = activeUserMessageViewModel else { return }
        interactionHandler?.userMessageDidChange(activeMessageViewModel.message, fromRowViewModel: id)
    }
}

// MARK: - Equatable

extension MessageRowViewModel: Equatable {
    static func == (lhs: MessageRowViewModel, rhs: MessageRowViewModel) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Hashable

extension MessageRowViewModel: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

// MARK: - AssistantReplyGroupViewModel

/// Model to represent a group of related assistant messages (original + regenerations)
final class AssistantReplyGroupViewModel: ObservableObject, Identifiable, Hashable {
    let id: UUID // original message id in the group
    @Published var messageViewModels: [MessageViewModel]
    @Published var activeMessageViewModel: MessageViewModel? {
        didSet {
            cancellables.removeAll() // Clean up old subscriptions
            setupObservers()
        }
    }
    
    /// Whether to show the raw text sheet for text selection
    @Published var showingRawTextSheet: Bool = false

    /// Current chat session setting (subscribed from ChatViewModel)
    @Published var currentChatSessionSetting: ChatSessionSetting?

    /// Resolved message action settings (subscribed from ChatViewModel)
    @Published var resolvedMessageActionSettings: ResolvedMessageActionSettings?

    // MARK: - Private Properties
    
    private weak var interactionHandler: MessageInteractionHandler?
    private var cancellables = Set<AnyCancellable>()
    
    /// Index of the currently active message
    var activeMessageIndex: Int {
        guard let activeViewModel = activeMessageViewModel,
              let index = messageViewModels.firstIndex(where: { $0.id == activeViewModel.id }) else {
            return messageViewModels.count - 1 // Default to last
        }
        return index
    }
    
    private weak var parentRowViewModel: MessageRowViewModel?
    
    init(
        id: UUID,
        messageViewModels: [MessageViewModel],
        activeMessageViewModel: MessageViewModel? = nil,
        interactionHandler: MessageInteractionHandler? = nil,
        parentRowViewModel: MessageRowViewModel? = nil,
        currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never>? = nil,
        resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never>? = nil
    ) {
        self.id = id
        self.messageViewModels = messageViewModels
        self.interactionHandler = interactionHandler
        self.parentRowViewModel = parentRowViewModel
        self.activeMessageViewModel = activeMessageViewModel ?? messageViewModels.last

        // Subscribe to settings publishers if provided
        subscribeToSettings(
            currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
            resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
        )
    }
    
    /// Navigate to previous regeneration
    func showPreviousRegeneration() {
        let currentIndex = activeMessageIndex
        if currentIndex > 0 {
            activeMessageViewModel = messageViewModels[currentIndex - 1]
            
            // Update parent row's activeAssistantMessageId
            if let activeMessage = activeMessageViewModel {
                parentRowViewModel?.activeAssistantMessageId = activeMessage.id
                
                // Notify interaction handler of the change
                if let parentRowId = parentRowViewModel?.id {
                    interactionHandler?.assistantMessageDidChange(activeMessage.message, fromRowViewModel: parentRowId)
                }
            }
        }
    }
    
    /// Navigate to next regeneration
    func showNextRegeneration() {
        let currentIndex = activeMessageIndex
        if currentIndex < messageViewModels.count - 1 {
            activeMessageViewModel = messageViewModels[currentIndex + 1]
            
            // Update parent row's activeAssistantMessageId
            if let activeMessage = activeMessageViewModel {
                parentRowViewModel?.activeAssistantMessageId = activeMessage.id
                
                // Notify interaction handler of the change
                if let parentRowId = parentRowViewModel?.id {
                    interactionHandler?.assistantMessageDidChange(activeMessage.message, fromRowViewModel: parentRowId)
                }
            }
        }
    }
    
    /// Show raw text sheet for the currently active message
    func showRawTextSheetForActiveMessage() {
        showingRawTextSheet = true
    }
    
    /// Add a new regeneration to this group and activate it
    func addAndActivateNewRegeneration(_ messageViewModel: MessageViewModel) {
        // Set up programmatic swipe callback for the new message
        messageViewModel.onProgrammaticSwipeRequested = { [weak self] direction in
            self?.parentRowViewModel?.triggerProgrammaticSwipe(direction: direction, groupId: self?.id ?? messageViewModel.id)
        }
        
        // Add to the end of the message list (newest regeneration)
        messageViewModels.append(messageViewModel)
        
        // Set as the active message
        activeMessageViewModel = messageViewModel
        
        // Update parent row's activeAssistantMessageId
        parentRowViewModel?.activeAssistantMessageId = messageViewModel.message.id
        
        // Notify interaction handler of the change
        if let parentRowId = parentRowViewModel?.id {
            interactionHandler?.assistantMessageDidChange(messageViewModel.message, fromRowViewModel: parentRowId)
        }
    }

    /// Subscribe to settings publishers from ChatViewModel
    private func subscribeToSettings(
        currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never>?,
        resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never>?
    ) {
        // Subscribe to current chat session setting changes
        if let settingPublisher = currentChatSessionSettingPublisher {
            settingPublisher
                .sink { [weak self] setting in
                    self?.currentChatSessionSetting = setting
                }
                .store(in: &cancellables)
        }

        // Subscribe to resolved message action settings changes
        if let actionSettingsPublisher = resolvedMessageActionSettingsPublisher {
            actionSettingsPublisher
                .sink { [weak self] actionSettings in
                    self?.resolvedMessageActionSettings = actionSettings
                }
                .store(in: &cancellables)
        }
    }

    /// Setup observers for the active message view model
    private func setupObservers() {
        guard let messageViewModel = activeMessageViewModel else { return }

        // Only listen to UI-related properties to avoid excessive refreshes
        Publishers.Merge4(
            messageViewModel.$userFeedback.map { _ in () },
            messageViewModel.$isFavorited.map { _ in () },
            messageViewModel.$displayContentText.map { _ in () },
            messageViewModel.$displayThinkingText.map { _ in () }
        )
        .sink { [weak self] _ in
            self?.objectWillChange.send()
        }
        .store(in: &cancellables)
    }
    
    static func == (lhs: AssistantReplyGroupViewModel, rhs: AssistantReplyGroupViewModel) -> Bool {
        lhs.id == rhs.id
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

// MARK: - Message Interaction Handler

/// Protocol for handling message interactions
protocol MessageInteractionHandler: AnyObject {
    func copyMessage(_ message: Message)
    func regenerateSingleMessage(_ message: Message)
    func regenerateSingleMessageWithAction(_ message: Message, _ action: MessageAction)
    func regenerateAllMessages(fromRowViewModel rowViewModelId: UUID)
    func likeMessage(_ message: Message)
    func dislikeMessage(_ message: Message)
    func editMessage(_ message: Message, fromRowViewModel rowViewModelId: UUID)
    func userMessageDidChange(_ message: Message, fromRowViewModel rowViewModelId: UUID)
    func assistantMessageDidChange(_ message: Message, fromRowViewModel rowViewModelId: UUID)
}

// MARK: - Stack Measured Height PreferenceKey

struct StackMeasuredHeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0 // Or 100 if you prefer a non-zero default

    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        let new = nextValue()
        if new > value { // Only update if new value is positive
            value = new
        }
    }
}
