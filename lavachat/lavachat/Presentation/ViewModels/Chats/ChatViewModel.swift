import Foundation
import Combine
import UIKit
import SwiftUI
import PhotosUI

// MARK: - Types

// Add callback type alias after the existing imports
typealias ChatDuplicationAction = (UUID) -> Void

final class ChatViewModel: ObservableObject, MessageInteractionHandler {
    // MARK: - Published Properties
    
    @Published var chatSession: ChatSession?

    @Published var messageRowViewModels: [MessageRowViewModel] = []
    @Published var newMessageText: String = ""
    @Published var isLoadingSession: Bool = false
    @Published var isLoadingMessages: Bool = false
    @Published var isSendingMessage: Bool = false
    @Published var errorMessage: String? = nil
    @Published var currentChatSessionSetting: ChatSessionSetting?
    @Published var resolvedMessageActionSettings: ResolvedMessageActionSettings?
    @Published var editingState: EditingState? = nil

    // UI related properties
    @Published var showingActionPanel: Bool = false
    @Published var showCopyToast: Bool = false
    @Published var showEmptyInputToast: Bool = false
    @Published var showRewriteFailedToast: Bool = false
    @Published var scrollToBottom: Bool = false
    @Published var scrollToBottomWithAnimation: Bool = false

    // Prompt selection properties
    @Published var showingPromptSelection: Bool = false
    @Published var promptSelectionAction: MessageAction?
    @Published var promptSelectionMessage: Message?
    
    // Scroll position properties
    @Published var scrollDistanceToTop: CGFloat = 0
    @Published var scrollDistanceToBottom: CGFloat = 0
    @Published var scrollContentHeight: CGFloat = 0
    @Published var scrollViewportHeight: CGFloat = 0
    @Published var isNearBottom: Bool = false
    @Published var scrollToBottomIfAtBottom: Bool = false
    @Published var showScrollToBottomButton: Bool = false

    // File selection properties
    @Published var selectedImages: [UIImage] = []
    @Published var selectedFiles: [URL] = []
    @Published var photosPickerItems: [PhotosPickerItem] = []
    @Published var isDocumentPickerPresented: Bool = false
    @Published var isImagePickerPresented: Bool = false
    @Published var isPhotosPickerPresented: Bool = false
    @Published var imageSourceType: UIImagePickerController.SourceType = .photoLibrary
    @Published var isShowingTokenWarningAlert: Bool = false
    @Published var estimatedTokenCount: Int = 0
    @Published var isShowingFileProcessingErrorAlert: Bool = false
    @Published var fileProcessingErrorMessage: String = ""

    /// Whether to generate chat title after current message completes
    private var shouldGenerateTitle: Bool = false

    /// Whether the chat is currently in editing mode
    var isEditingMode: Bool {
        editingState != nil
    }

    // MARK: - Publishers for Settings

    /// Publisher for current chat session setting changes
    var currentChatSessionSettingPublisher: AnyPublisher<ChatSessionSetting?, Never> {
        return $currentChatSessionSetting
            .prepend(currentChatSessionSetting)  // Send current value immediately on subscription
            .eraseToAnyPublisher()
    }

    /// Publisher for resolved message action settings changes
    var resolvedMessageActionSettingsPublisher: AnyPublisher<ResolvedMessageActionSettings?, Never> {
        $resolvedMessageActionSettings
            .prepend(resolvedMessageActionSettings)  // Send current value immediately on subscription
            .eraseToAnyPublisher()
    }
    
    // MARK: - Private Properties
    
    internal let chatSessionId: UUID
    private var cancellables = Set<AnyCancellable>()
    internal var instanceContextCache: [UUID: LLMInstanceContext] = [:]
    
    // MARK: - Dependencies
    
    internal let getChatSessionUseCase: GetChatSessionUseCaseProtocol
    internal let prepareMessagesUseCase: PrepareMessagesUseCaseProtocol
    internal let sendMessageUseCase: SendMessageUseCaseProtocol
    internal let messageTreeManagerUseCase: MessageTreeManagerUseCaseProtocol
    internal let observeChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol
    internal let getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol
    internal let getInstancesWithRelatedEntitiesUseCase: GetInstancesWithRelatedEntitiesUseCaseProtocol
    internal let regenerateSingleMessageUseCase: RegenerateSingleMessageUseCaseProtocol
    internal let regenerateSingleMessageWithPromptUseCase: RegenerateSingleMessageWithPromptUseCaseProtocol
    internal let regenerateAllMessagesUseCase: RegenerateAllMessagesUseCaseProtocol
    internal let rewritePromptWithPromptUseCase: RewritePromptWithPromptUseCaseProtocol
    internal let generateChatTitleUseCase: GenerateChatTitleUseCaseProtocol
    internal let duplicateChatSessionUseCase: DuplicateChatSessionUseCaseProtocol
    
    // Navigation callback
    private let onDuplicateChatSession: ChatDuplicationAction?
    
    // MARK: - Initialization
    
    init(
        chatSessionId: UUID,
        getChatSessionUseCase: GetChatSessionUseCaseProtocol,
        prepareMessagesUseCase: PrepareMessagesUseCaseProtocol,
        sendMessageUseCase: SendMessageUseCaseProtocol,
        messageTreeManagerUseCase: MessageTreeManagerUseCaseProtocol,
        observeChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol,
        getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol,
        getInstancesWithRelatedEntitiesUseCase: GetInstancesWithRelatedEntitiesUseCaseProtocol,
        regenerateSingleMessageUseCase: RegenerateSingleMessageUseCaseProtocol,
        regenerateSingleMessageWithPromptUseCase: RegenerateSingleMessageWithPromptUseCaseProtocol,
        regenerateAllMessagesUseCase: RegenerateAllMessagesUseCaseProtocol,
        rewritePromptWithPromptUseCase: RewritePromptWithPromptUseCaseProtocol,
        generateChatTitleUseCase: GenerateChatTitleUseCaseProtocol,
        duplicateChatSessionUseCase: DuplicateChatSessionUseCaseProtocol,
        onDuplicateChatSession: ChatDuplicationAction? = nil
    ) {
        print("🔄 ChatViewModel init")
        self.chatSessionId = chatSessionId
        self.getChatSessionUseCase = getChatSessionUseCase
        self.prepareMessagesUseCase = prepareMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
        self.messageTreeManagerUseCase = messageTreeManagerUseCase
        self.observeChatsChangesUseCase = observeChatsChangesUseCase
        self.getChatSessionSettingUseCase = getChatSessionSettingUseCase
        self.getInstancesWithRelatedEntitiesUseCase = getInstancesWithRelatedEntitiesUseCase
        self.regenerateSingleMessageUseCase = regenerateSingleMessageUseCase
        self.regenerateSingleMessageWithPromptUseCase = regenerateSingleMessageWithPromptUseCase
        self.regenerateAllMessagesUseCase = regenerateAllMessagesUseCase
        self.rewritePromptWithPromptUseCase = rewritePromptWithPromptUseCase
        self.generateChatTitleUseCase = generateChatTitleUseCase
        self.duplicateChatSessionUseCase = duplicateChatSessionUseCase
        self.onDuplicateChatSession = onDuplicateChatSession
        
        subscribeToRelevantChanges()
        
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Public Methods
    
    @MainActor
    func loadInitialData() async {
        isLoadingSession = true
        isLoadingMessages = true
        errorMessage = nil
        
        do {
            print("🔄 loadInitialData")
            // Load chat session
            guard let session = try await getChatSessionUseCase.execute(sessionId: chatSessionId) else {
                throw ChatError.sessionNotFound
            }
            self.chatSession = session
            
            // Load chat session setting
            if let settingsId = session.settingsId,
               let (setting, resolvedActions) = try await getChatSessionSettingUseCase.executeAndResolveActions(settingId: settingsId) {
                self.currentChatSessionSetting = setting
                self.resolvedMessageActionSettings = resolvedActions
            }
            
            // Initialize message tree from repository
            try await messageTreeManagerUseCase.initializeTreeFromRepository(sessionId: chatSessionId)
            
            // Get message layers for display
            let messageLayers = await messageTreeManagerUseCase.getMessageLayersForDisplay(sessionId: chatSessionId)
            
            // Compute message row view models from layers
            self.messageRowViewModels = await computeMessageRowViewModelsFromLayers(messageLayers)
            
            // Collect all unique instance IDs from the computed view models
            var instanceIds = Set(messageRowViewModels.flatMap { rowViewModel in
                rowViewModel.messageViewModels.compactMap { $0.llmInstanceId }
            })

            // append session active instance ids to instanceIds
            instanceIds.formUnion(session.activeLLMInstanceIds)
            
            // Batch load instance contexts
            if !instanceIds.isEmpty {
                await loadInstanceContexts(for: Array(instanceIds))
            }
            
        } catch {
            handleError(ChatError.from(error))
        }
        
        isLoadingSession = false
        isLoadingMessages = false
    }
    
    @MainActor
    func sendMessage(content: [ContentBlock] = []) async {
        // Use text from newMessageText if no content provided
        let messageContent = content.isEmpty ? [ContentBlock.text(newMessageText)] : content
        
        guard !messageContent.isEmpty, 
              case .text(let text) = messageContent[0], !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        guard let chatSession = chatSession else {
            handleError(ChatError.sessionNotFound)
            return
        }

        guard let chatSessionSetting = currentChatSessionSetting else {
            handleError(ChatError.sessionNotFound)
            return
        }

        isSendingMessage = true
        errorMessage = nil
        
        do {
            // Determine operation type and related parameters
            let operationType: MessageOperationType
            let parentMessageId: UUID?
            let userMessageDepth: Int64
            let instanceIds: [UUID]
            let originalMessageId: UUID?

            if let editingState = editingState {
                // Editing mode
                operationType = .editMessage
                // For editing, we need the original message details
                guard let originalMessage = messageRowViewModels
                    .flatMap({ $0.messageViewModels })
                    .first(where: { $0.id == editingState.messageId })?.message else {
                    handleError(ChatError.messageEditFailed)
                    return
                }
                parentMessageId = originalMessage.parentId
                userMessageDepth = originalMessage.depth
                instanceIds = chatSession.activeLLMInstanceIds
                originalMessageId = editingState.messageId

                // Clear editing state immediately
                self.editingState = nil
            } else {
                // Normal message sending
                operationType = .newMessage
                // Get the last message in the active path for parent ID and depth calculation
                let activePath = messageTreeManagerUseCase.getActivePathMessage(for: chatSessionId)
                parentMessageId = activePath.last?.id
                userMessageDepth = (activePath.last?.depth ?? -1) + 1
                instanceIds = chatSession.activeLLMInstanceIds
                originalMessageId = nil

                // Check if we should generate title after completion
                // Only for first user message (no existing messages) with auto-generate enabled
                shouldGenerateTitle = activePath.isEmpty &&
                    currentChatSessionSetting?.auxiliaryLLMInstanceId != nil &&
                    currentChatSessionSetting?.shouldAutoGenerateTitle == true &&
                    (chatSession.title == nil || chatSession.title == ChatSession.defaultSessionName)
                
                // Update parent message's isReplied status if it's an assistant message
                if let parentMessage = activePath.last,
                   parentMessage.role == .assistant,
                   !parentMessage.isReplied {
                    do {
                        var updatedParentMessage = parentMessage
                        updatedParentMessage.isReplied = true
                        
                        // Update cache and repository
                        try await messageTreeManagerUseCase.updateMessageInCacheAndRepository(
                            sessionId: chatSessionId,
                            message: updatedParentMessage
                        )
                        
                        // Update UI immediately
                        await updateMessageInUI(updatedParentMessage)
                        
                        print("✅ Updated parent message isReplied status: \(parentMessage.id)")
                    } catch {
                        print("❌ Failed to update parent message isReplied status: \(error)")
                        // Continue with message sending even if parent update fails
                    }
                }
            }

            // Clear the input field and selected media immediately after sending message
            newMessageText = ""
            clearSelectedMedia()
            
            // Step 1: Prepare messages for immediate UI display
            let preparedMessages = try await prepareMessagesUseCase.execute(
                chatSession: chatSession,
                chatSessionSetting: chatSessionSetting,
                content: messageContent,
                instanceIds: instanceIds,
                operationType: operationType,
                parentMessageId: parentMessageId,
                originalMessageId: originalMessageId,
                userMessageDepth: userMessageDepth
            )
            
            // Step 2: Immediately update UI with user message and placeholder assistant messages
            if operationType == .newMessage {
                // Add user message layer
                let userMessageRowViewModel = MessageRowViewModel(
                    messageViewModels: [MessageViewModel(
                        message: preparedMessages.userMessage,
                        LLMInstanceContext: nil,
                        interactionHandler: self,
                        currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                        resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
                    )],
                    instanceLogos: [:],
                    interactionHandler: self,
                    activeUserMessageId: preparedMessages.userMessage.id,
                    currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                    resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
                )
                messageRowViewModels.append(userMessageRowViewModel)
            } else if operationType == .editMessage {
                // Update the existing user message
                if let userMessageRowViewModel = messageRowViewModels.first(where: { $0.messageViewModels.first?.message.depth == preparedMessages.userMessage.depth }) {
                    userMessageRowViewModel.addAndActivateNewEditedUserMessageViewModel(MessageViewModel(
                        message: preparedMessages.userMessage,
                        LLMInstanceContext: nil,
                        interactionHandler: self,
                        currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                        resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher)
                    )

                    // remove all messageRowViewModels after the user message row
                    messageRowViewModels = Array(
                        messageRowViewModels.prefix(upTo: messageRowViewModels.firstIndex(where: { $0.messageViewModels.first?.message.depth == preparedMessages.userMessage.depth })! + 1)
                    )
                }
            }
            
            // Add placeholder assistant messages layer if provided (regeneration do not use this method)
            var streamingMessageViewModels: [MessageViewModel] = []
            if let newAssistantMessageLayer = preparedMessages.newAssistantMessageLayer {
                let assistantMessageRowViewModel = await createMessageRowViewModel(from: newAssistantMessageLayer)
                messageRowViewModels.append(assistantMessageRowViewModel)
                streamingMessageViewModels = assistantMessageRowViewModel.messageViewModels
                var updatedSession = chatSession
                updatedSession.activeMessageId = newAssistantMessageLayer.activeMessageId
                self.chatSession = updatedSession
            }
            
            // Step 3: Start streaming response processing
            let responseStream = sendMessageUseCase.execute(preparedMessages: preparedMessages)

            // Step 4: Set scrollToBottom to true
            scrollToBottom = true
            
            // Handle streaming response
            await handleStreamingResponse(responseStream, messageViewModels: streamingMessageViewModels)

            // Generate title if needed (after streaming completes)
            if shouldGenerateTitle {
                await generateChatTitleIfNeeded(userInput: messageContent)
                shouldGenerateTitle = false // Reset flag
            }

        } catch {
            handleError(ChatError.from(error))
        }
    }

    func getInstanceContext(for instanceId: UUID) async -> LLMInstanceContext? {
        // Check cache first
        if let cachedInfo = instanceContextCache[instanceId] {
            print("🔄 Instance context found in cache for ID: \(instanceId)")
            return cachedInfo
        }

        print("🔄 Instance context not found in cache for ID: \(instanceId) with cache: \(instanceContextCache.keys)")
        
        // Use the use case to get instance context
        do {
            print("🔄 Loading instance context for ID: \(instanceId)")
            let instanceContexts = try await getInstancesWithRelatedEntitiesUseCase.execute(instanceIds: [instanceId])
            
            if let instanceContext = instanceContexts.first {
                // Cache the result
                instanceContextCache[instanceId] = instanceContext
                return instanceContext
            } else {
                print("Instance context not found for ID: \(instanceId)")
                return nil
            }
        } catch {
            print("Failed to get instance context for ID: \(instanceId), error: \(error)")
            return nil
        }
    }
    
    /// Batch load instance contexts for multiple instance IDs
    @MainActor
    private func loadInstanceContexts(for instanceIds: [UUID]) async {
        guard !instanceIds.isEmpty else { return }
        
        do {
            print("🔄 Loading instance contexts for \(instanceIds)")
            let instanceContexts = try await getInstancesWithRelatedEntitiesUseCase.execute(instanceIds: instanceIds)
            print("✅ Loaded \(instanceContexts.count) instance contexts using use case")
            
            // Cache all loaded contexts
            for context in instanceContexts {
                instanceContextCache[context.instance.id] = context
            }
            
        } catch {
            print("❌ Failed to load instance contexts: \(error)")
            // Continue with empty cache - UI will handle fallback
        }
    }
    
    @MainActor
    func startNewSessionBasedOnCurrent() async -> ChatSession? {
        print("Creating new chat based on current session")
        
        // Check if current chatSession exists
        guard let currentSession = chatSession else {
            handleError(ChatError.sessionNotFound)
            return nil
        }
        
        do {
            // Create duplicate session using the use case
            let duplicatedSession = try await duplicateChatSessionUseCase.execute(sourceSession: currentSession)
            
            // Call the callback with the new session ID
            onDuplicateChatSession?(duplicatedSession.id)
            
            return duplicatedSession
        } catch {
            handleError(ChatError.from(error))
            return nil
        }
    }
    
    @MainActor
    func stopSendingMessage() async {
        print("🛑 stopSendingMessage called")
        
        await sendMessageUseCase.stopSending(sessionId: chatSessionId)
        isSendingMessage = false
        print("✅ Successfully stopped sending message")
        
    }
    
    /// Create a MessageRowViewModel from a MessageLayer
    @MainActor
    internal func createMessageRowViewModel(from layer: MessageLayer) async -> MessageRowViewModel {
        // Create MessageViewModels for each message
        var messageViewModels: [MessageViewModel] = []
        var rowInstanceLogos: [UUID: LLMInstanceContext] = [:]
        
        for message in layer.siblingMessages {
            // Get instance context if available
            var instanceContext: LLMInstanceContext? = nil
            if let instanceId = message.llmInstanceId {
                instanceContext = await getInstanceContext(for: instanceId)
                if let instanceContext = instanceContext {
                    rowInstanceLogos[instanceId] = instanceContext
                }
            }
            
            // Create MessageViewModel
            let messageViewModel = MessageViewModel(
                message: message,
                LLMInstanceContext: instanceContext,
                interactionHandler: self,
                currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
            )
            messageViewModels.append(messageViewModel)
        }
        
        // Determine active message IDs based on layer information
        let activeMessageId = layer.activeMessageId
        let activeMessage = layer.siblingMessages.first { $0.id == activeMessageId }

        let activeMessageTextContent = activeMessage?.content.compactMap { content in
            if case .text(let text) = content { return text.prefix(50) } else { return nil }
        }
        print("🎯 createMessageRowViewModel with activeMessage: \(activeMessageTextContent ?? [])")
        
        var activeUserMessageId: UUID? = nil
        var activeAssistantMessageId: UUID? = nil
        
        if activeMessage?.role == .user {
            activeUserMessageId = activeMessageId
        } else if activeMessage?.role == .assistant || activeMessage?.role == .mergedAssistant {
            activeAssistantMessageId = activeMessageId
        }
        
        // Create MessageRowViewModel with active message IDs
        let rowViewModel = MessageRowViewModel(
            messageViewModels: messageViewModels,
            instanceLogos: rowInstanceLogos,
            interactionHandler: self,
            activeUserMessageId: activeUserMessageId,
            activeAssistantMessageId: activeAssistantMessageId,
            currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
            resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
        )
        
        return rowViewModel
    }
    
    /// Update an existing MessageRowViewModel with new layer data (incremental update)
    @MainActor
    private func updateMessageRowViewModel(_ existingRowViewModel: MessageRowViewModel, from layer: MessageLayer) async -> MessageRowViewModel {
        // Error handling: Check for empty layer
        guard !layer.siblingMessages.isEmpty else {
            print("⚠️ Warning: Attempting to update MessageRowViewModel with empty layer")
            return existingRowViewModel
        }
        
        // Get existing message IDs for quick lookup
        let existingMessageIds = Set(existingRowViewModel.messageViewModels.map { $0.message.id })
        
        // Track which existing MessageViewModels to keep
        var updatedMessageViewModels: [MessageViewModel] = []
        var updatedInstanceLogos = existingRowViewModel.instanceLogos
        
        // Process each message in the new layer
        for message in layer.siblingMessages {
            if existingMessageIds.contains(message.id) {
                // Reuse existing MessageViewModel
                if let existingViewModel = existingRowViewModel.messageViewModels.first(where: { $0.message.id == message.id }) {
                    updatedMessageViewModels.append(existingViewModel)
                }
            } else {
                // Create new MessageViewModel for new message
                var instanceContext: LLMInstanceContext? = nil
                if let instanceId = message.llmInstanceId {
                    instanceContext = await getInstanceContext(for: instanceId)
                    // Only add to instanceLogos if not already present (avoid overwriting)
                    if let instanceContext = instanceContext, updatedInstanceLogos[instanceId] == nil {
                        updatedInstanceLogos[instanceId] = instanceContext
                    }
                }
                
                let messageViewModel = MessageViewModel(
                    message: message,
                    LLMInstanceContext: instanceContext,
                    interactionHandler: self,
                    currentChatSessionSettingPublisher: currentChatSessionSettingPublisher,
                    resolvedMessageActionSettingsPublisher: resolvedMessageActionSettingsPublisher
                )
                updatedMessageViewModels.append(messageViewModel)
            }
        }
        
        // Error handling: Ensure we have at least one message after processing
        guard !updatedMessageViewModels.isEmpty else {
            print("⚠️ Warning: No valid messages found after updating MessageRowViewModel")
            return existingRowViewModel
        }
        
        // Use the proper update methods from MessageRowViewModel
        existingRowViewModel.updateMessageViewModels(updatedMessageViewModels)
        existingRowViewModel.updateInstanceLogos(updatedInstanceLogos)
        
        // Update active message IDs based on layer information
        let activeMessageId = layer.activeMessageId
        let activeMessage = layer.siblingMessages.first { $0.id == activeMessageId }

        let activeMessageTextContent = activeMessage?.content.compactMap { content in
            if case .text(let text) = content { return text.prefix(50) } else { return nil }
        }
        print("🎯 updateMessageRowViewModel with activeMessage: \(activeMessageTextContent ?? [])")
        
        if let activeMessage = activeMessage {
            if activeMessage.role == .user {
                existingRowViewModel.activeUserMessageId = activeMessageId
                existingRowViewModel.activeAssistantMessageId = nil
            } else if activeMessage.role == .assistant || activeMessage.role == .mergedAssistant {
                existingRowViewModel.activeAssistantMessageId = activeMessageId
                // Don't clear user message ID as rows can contain both user and assistant messages
            }
        }
        
        return existingRowViewModel
    }
    
    // MARK: - Observer Pattern Implementation
    
    /// Subscribe to relevant changes for this chat session
    private func subscribeToRelevantChanges() {
        print("🔄 subscribeToRelevantChanges")
        observeChatsChangesUseCase.execute(forSpecificSessionId: chatSessionId)
            .sink { [weak self] change in
                Task { @MainActor [weak self] in
                    await self?.handleChatViewRelevantChange(change)
                }
            }
            .store(in: &cancellables)

        // Subscribe to PhotosPicker items changes
        $photosPickerItems
            .sink { [weak self] items in
                Task { @MainActor [weak self] in
                    await self?.handlePhotosPickerItems(items)
                }
            }
            .store(in: &cancellables)

    }
    
    /// Handle specific chat view relevant changes
    @MainActor
    private func handleChatViewRelevantChange(_ change: ChatViewRelevantChange) async {
        switch change {
        case .sessionTitleChanged(let title):
            // Update session title
            if var session = chatSession {
                session.title = title
                self.chatSession = session
            }
            
        case .sessionSettingsChanged(let settingsId):
            // Reload settings if they changed
            if let settingsId = settingsId {
                do {
                    if let (setting, resolvedActions) = try await getChatSessionSettingUseCase.executeAndResolveActions(settingId: settingsId) {
                        self.currentChatSessionSetting = setting
                        self.resolvedMessageActionSettings = resolvedActions
                    }
                } catch {
                    handleError(ChatError.from(error))
                }
            }
            
        case .sessionDeleted:
            // Handle session deletion - for now just clear the data
            self.chatSession = nil
            self.messageRowViewModels = []
            
        case .settingUIThemeChanged(let themeSettings):
            // Update UI theme if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.uiThemeSettings = themeSettings
                self.currentChatSessionSetting = setting
            }
            
        case .settingMessageActionsChanged(let actionSettings):
            // Update message actions if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.messageActionSettings = actionSettings
                self.currentChatSessionSetting = setting

                // Also reload resolved message action settings
                do {
                    if let (_, resolvedActions) = try await getChatSessionSettingUseCase.executeAndResolveActions(settingId: setting.id) {
                        self.resolvedMessageActionSettings = resolvedActions
                    }
                } catch {
                    print("⚠️ Failed to reload resolved message action settings: \(error)")
                }
            }
            
        case .settingPromptSegmentsChanged(let promptSegments):
            // Update prompt segments if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.savedPromptSegments = promptSegments ?? []
                self.currentChatSessionSetting = setting
            }

        case .settingMessageActionContentChanged(let action):
            // Check if this action exists in our resolved message action settings
            guard let resolvedSettings = resolvedMessageActionSettings else {
                print("⚠️ No resolved message action settings available")
                return
            }

            // Use the new method to update action content
            let updatedSettings = resolvedSettings.updatingActionContent(with: action)

            // Check if the settings actually changed (action was found and updated)
            if resolvedSettings.containsAction(with: action.id) {
                self.resolvedMessageActionSettings = updatedSettings
                print("✅ Action \(action.name) found and updated in resolved settings")
            } else {
                print("ℹ️ Action \(action.name) not found in current resolved settings, no update needed")
            }

        case .settingShouldExpandThinkingChanged(let shouldExpandThinking):
            // Update should expand thinking if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.shouldExpandThinking = shouldExpandThinking
                self.currentChatSessionSetting = setting
            }

        case .settingAuxiliaryLLMInstanceIdChanged(let auxiliaryLLMInstanceId):
            // Update auxiliary LLM instance ID if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.auxiliaryLLMInstanceId = auxiliaryLLMInstanceId
                self.currentChatSessionSetting = setting
            }

        case .settingShouldAutoGenerateTitleChanged(let shouldAutoGenerateTitle):
            // Update should auto generate title if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.shouldAutoGenerateTitle = shouldAutoGenerateTitle
                self.currentChatSessionSetting = setting
            }

        case .settingContextMessageCountChanged(let contextMessageCount):
            // Update context message count if current session setting changed
            if var setting = currentChatSessionSetting {
                setting.contextMessageCount = contextMessageCount
                self.currentChatSessionSetting = setting
            }
        }
    }

    /// Handle PhotosPicker items selection
    @MainActor
    private func handlePhotosPickerItems(_ items: [PhotosPickerItem]) async {
        guard !items.isEmpty else { return }

        for item in items {
            do {
                if let data = try await item.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    selectedImages.append(image)
                }
            } catch {
                print("⚠️ Failed to load image from PhotosPicker: \(error)")
            }
        }

        // Clear the picker items after processing
        photosPickerItems = []
    }


    // MARK: - Streaming Response Handling
    
    /// Handle streaming response from SendMessageUseCase
    internal func handleStreamingResponse(_ stream: AsyncThrowingStream<LLMStreamingResponse, Error>, messageViewModels: [MessageViewModel]) async {
        await MainActor.run {
            isSendingMessage = true
        }

        do {
            for try await response in stream {
                guard let targetMessageViewModel = messageViewModels.first(where: { $0.llmInstanceId == response.instanceId }) else {
                    print("⚠️ Warning: Could not find MessageViewModel for instanceId: \(response.instanceId)")
                    continue
                }
                await MainActor.run {
                    switch response.responseType {
                    case .contentDelta(let content):
                        // Update UI with incremental content
                        appendToMessageDisplayContentText(targetMessageViewModel, additionalText: content)
                    case .thinkingDelta(let content):
                        // Handle thinking content if needed
                        appendToMessageDisplayThinkingText(targetMessageViewModel, additionalText: content)
                    case .completionMarker(let info):
                        // Handle completion - token info is already saved by the UseCase
                        print("Message completed for instance \(response.instanceId): \(info.finishReason ?? "unknown")")
                    case .error(let error):
                        // Handle individual instance errors - show on message instead of global error
                        handleStreamingErrorForMessageViewModel(targetMessageViewModel, error: error)
                    case .statusUpdate(let status):
                        // Handle status updates
                        print("Status update for instance \(response.instanceId): \(status)")
                    }
                }
            }
        } catch {
            let error = ChatError.from(error)
            await MainActor.run {
                handleError(error)
            }
        }
        
        await MainActor.run {
            isSendingMessage = false
        }
    }
    
    // MARK: - New Architecture Methods
    
    /// Public method to access message row view models for the view
    func getMessageRowViewModelsForView() -> [MessageRowViewModel] {
        return messageRowViewModels
    }
    
    /// Compute message row view models from layers with intelligent caching and reuse optimization
    /// This method uses a 3-phase approach to maximize performance:
    /// 1. Reuse existing MessageRowViewModels that match layers
    /// 2. Early return if all layers are processed
    /// 3. Create new rows for remaining layers
    @MainActor
    private func computeMessageRowViewModelsFromLayers(_ layers: [MessageLayer]) async -> [MessageRowViewModel] {
        var rowViewModels: [MessageRowViewModel] = []
        var layerIndex = -1

        print("🔄 Optimization: messageRowViewModels.count: \(self.messageRowViewModels.count) layers.count: \(layers.count)")
        
        // Phase 1: Reuse existing MessageRowViewModels that match layers
        if self.messageRowViewModels.count > 0 && !layers.isEmpty {
            // Find matching existing rows
            let maxIterations = min(layers.count, self.messageRowViewModels.count)
            print("🔄 Optimization: maxIterations: \(maxIterations)")
            for index in 0..<maxIterations {
                let layer = layers[index]
                let messageRowViewModel = self.messageRowViewModels[index]
                
                // Skip empty layers
                guard !layer.siblingMessages.isEmpty else { continue }
                
                // Check if the active message ID matches
                let layerActiveId = layer.activeMessageId
                let rowActiveUserId = messageRowViewModel.activeUserMessageId
                let rowActiveAssistantId = messageRowViewModel.activeAssistantMessageId
                
                if (rowActiveUserId != nil && rowActiveUserId == layerActiveId) ||
                   (rowActiveAssistantId != nil && rowActiveAssistantId == layerActiveId) {
                    rowViewModels.append(messageRowViewModel)
                    layerIndex = index
                } else {
                    break
                }
                print("🔄 Optimization: Reused row at index \(index) maxIterations: \(maxIterations)")
            }
        }
        
        // Phase 2: Check if we've processed all layers (early return optimization)
        if layerIndex + 1 >= layers.count {
            print("🔄 Optimization: Reused all \(rowViewModels.count) existing rows, no new layers to process")
            return rowViewModels
        }
        
        // Track counts for Phase 3
        var phase3CreatedCount = 0
        
        // Phase 3: Create new rows for remaining layers
        let remainingStartIndex = layerIndex + 1
        if remainingStartIndex < layers.count {
            let remainingLayers = Array(layers[remainingStartIndex...])
            print("🔄 Optimization: Creating \(remainingLayers.count) new rows from index \(remainingStartIndex)")
            
            for layer in remainingLayers {
                // Skip if no messages in this layer
                guard !layer.siblingMessages.isEmpty else { continue }
                
                // Create new MessageRowViewModel
                let rowViewModel = await createMessageRowViewModel(from: layer)
                rowViewModels.append(rowViewModel)
                phase3CreatedCount += 1
            }
        }
        
        let reusedCount = layerIndex + 1
        let totalCount = rowViewModels.count
        print("🔄 Optimization complete: Reused \(reusedCount) rows, created \(phase3CreatedCount) new rows, total \(totalCount) rows")
        return rowViewModels
    }
    
    /// Rebuild view models from updated tree
    @MainActor
    internal func rebuildViewModelsFromUpdatedLayers(_ updatedLayers: [MessageLayer]) async {
        print("🔄 rebuildViewModelsFromUpdatedLayers")
        do {
            // Intelligently update view models by comparing with current state
            let newRowViewModels = await computeMessageRowViewModelsFromLayers(updatedLayers)
            
            // For MVP, we'll do a simple replacement. In the future, we can implement intelligent diffing
            self.messageRowViewModels = newRowViewModels
            
            // Update instance contexts cache for any new instances
            let instanceIds = Set(messageRowViewModels.flatMap { rowViewModel in
                rowViewModel.messageViewModels.compactMap { $0.llmInstanceId }
            })
            
            let missingInstanceIds = instanceIds.filter { instanceContextCache[$0] == nil }
            if !missingInstanceIds.isEmpty {
                await loadInstanceContexts(for: Array(missingInstanceIds))
            }
            
        } catch {
            handleError(ChatError.from(error))
        }
    }
    
    // MARK: - Scroll Position Methods
    
    /// Update scroll positions based on geometry information
    @MainActor
    func updateScrollPositions(contentHeight: CGFloat, viewportHeight: CGFloat, offset: CGFloat) {
        self.scrollContentHeight = contentHeight
        self.scrollViewportHeight = viewportHeight
        self.scrollDistanceToTop = offset
        self.scrollDistanceToBottom = max(0, contentHeight - (offset + viewportHeight))
        self.isNearBottom = scrollDistanceToBottom <= 100

        // Show scroll to bottom button when not near bottom and has content
        self.showScrollToBottomButton = !isNearBottom && contentHeight > viewportHeight

        print("🔍 Scroll positions - Content: \(contentHeight), Viewport: \(viewportHeight), Offset: \(offset)")
        print("🔍 Distances - Top: \(scrollDistanceToTop), Bottom: \(scrollDistanceToBottom), Near bottom: \(isNearBottom)")
    }

    /// Handle scroll to bottom button tap
    @MainActor
    func scrollToBottomButtonTapped() {
        scrollToBottomWithAnimation = true
    }
    
    // MARK: - Private Methods
    
    /// Update a message in the UI by finding the corresponding MessageViewModel
    @MainActor
    private func updateMessageInUI(_ message: Message) async {
        // Find the MessageRowViewModel that contains this message
        for rowViewModel in messageRowViewModels {
            // Look for the message in this row's messageViewModels
            for messageViewModel in rowViewModel.messageViewModels {
                if messageViewModel.message.id == message.id {
                    // Update the message using the existing updateMessage method
                    messageViewModel.updateMessage(message)
                    print("✅ Updated UI for message: \(message.id)")
                    return
                }
            }
        }
        print("⚠️ Warning: Could not find MessageViewModel for message: \(message.id)")
    }
    
    /// Append text to message for streaming
    @MainActor
    private func appendToMessageDisplayContentText(_ messageViewModel: MessageViewModel, additionalText: String) {
        messageViewModel.appendToDisplayContentTextAndUpdateMessage(additionalText)
    }
    
    /// Append text to message for streaming
    @MainActor
    private func appendToMessageDisplayThinkingText(_ messageViewModel: MessageViewModel, additionalText: String) {
        messageViewModel.appendToDisplayThinkingTextAndUpdateMessage(additionalText)
    }

    /// Handle streaming error for specific message instance
    @MainActor
    private func handleStreamingErrorForMessageViewModel(_ messageViewModel: MessageViewModel, error: APIError) {
        messageViewModel.updateMessageWithError(error)
    }
    
    @MainActor
    internal func handleError(_ error: ChatError) {
        errorMessage = error.errorDescription ?? error.localizedDescription
        print("ChatViewModel error: \(error)")
    }

    // MARK: - Chat Title Generation

    /// Generate chat title if conditions are met
    @MainActor
    private func generateChatTitleIfNeeded(userInput: [ContentBlock]) async {
        guard let chatSession = chatSession,
              let currentChatSessionSetting = currentChatSessionSetting,
              let auxiliaryInstanceId = currentChatSessionSetting.auxiliaryLLMInstanceId,
              currentChatSessionSetting.shouldAutoGenerateTitle else {
            print("🏷️ Skipping title generation: conditions not met")
            return
        }

        // Extract text content from user input
        let userInputText = userInput.compactMap { block in
            if case .text(let text) = block {
                return text
            }
            return nil
        }.joined(separator: " ")

        guard !userInputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("🏷️ Skipping title generation: empty user input")
            return
        }

        print("🏷️ Starting automatic title generation for session: \(chatSession.id)")

        do {
            // Generate title using the use case
            let generatedTitle = try await generateChatTitleUseCase.execute(
                userInput: userInputText,
                chatSession: chatSession,
                chatSessionSetting: currentChatSessionSetting
            )

            // Update the chat session with the new title
            var updatedSession = chatSession
            updatedSession.title = generatedTitle

            print("🏷️ Updated session: \(updatedSession)")

            try await messageTreeManagerUseCase.updateChatSessionInCacheAndRepository(chatSession: updatedSession)
            self.chatSession = updatedSession

            print("✅ Successfully generated and updated chat title: '\(generatedTitle)'")

        } catch {
            print("❌ Failed to generate chat title: \(error)")
            // Don't show error to user as this is a background operation
        }
    }

    // MARK: - Temporary File Management

    /// Clean up temporary files created for editing
    func cleanupTemporaryEditingFiles() {
        let tempDirectory = FileManager.default.temporaryDirectory

        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(at: tempDirectory,
                                                                       includingPropertiesForKeys: nil)

            for fileURL in tempFiles {
                let fileName = fileURL.lastPathComponent
                // Only delete files that match our editing pattern: *_editing_*
                if fileName.contains("_editing_") {
                    do {
                        try FileManager.default.removeItem(at: fileURL)
                        print("🗑️ Cleaned up temporary editing file: \(fileName)")
                    } catch {
                        print("⚠️ Failed to delete temporary file \(fileName): \(error)")
                    }
                }
            }
        } catch {
            print("⚠️ Failed to enumerate temporary directory: \(error)")
        }
    }
}
