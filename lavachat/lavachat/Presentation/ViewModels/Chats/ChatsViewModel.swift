import Foundation
import Combine
import SwiftUI

final class ChatsViewModel: ObservableObject {
    // MARK: - Published Properties
    
    @Published var sessions: [ChatSession] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var searchText: String = ""
    @Published var filteredSessions: [ChatSession] = []
    @Published var isNavigatingToChatSession: Bool = false
    @Published var sessionActiveMessageMap: [UUID: Message] = [:]
    @Published var selectedSession: ChatSession? = nil
    @Published var selectedChatView: AnyView? = nil
    @Published var toolbarVisibility: Visibility = .visible
    @Published var instanceContextsMap: [UUID: LLMInstanceContext] = [:]
    @Published var sessionActiveInstanceContextsMap: [UUID: [LLMInstanceContext]] = [:]
    @Published var chatViewId: UUID = UUID() // Force SwiftUI to recreate ChatView when needed
    
    // MARK: - Private Properties
    
    private var isPerformingDirectNavigation: Bool = false
    private var isInSubNavigation: Bool = false
    
    private let getAllSessionsUseCase: GetAllChatSessionsUseCaseProtocol
    private let deleteSessionUseCase: DeleteChatSessionUseCaseProtocol
    private let getMessagesUseCase: GetMessagesUseCaseProtocol
    private let observeChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol
    private let getInstancesWithRelatedEntitiesUseCase: GetInstancesWithRelatedEntitiesUseCaseProtocol
    private weak var container: DIContainer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(
        getAllSessionsUseCase: GetAllChatSessionsUseCaseProtocol,
        deleteSessionUseCase: DeleteChatSessionUseCaseProtocol,
        getMessagesUseCase: GetMessagesUseCaseProtocol,
        observeChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol,
        getInstancesWithRelatedEntitiesUseCase: GetInstancesWithRelatedEntitiesUseCaseProtocol,
        container: DIContainer
    ) {
        self.getAllSessionsUseCase = getAllSessionsUseCase
        self.deleteSessionUseCase = deleteSessionUseCase
        self.getMessagesUseCase = getMessagesUseCase
        self.observeChatsChangesUseCase = observeChatsChangesUseCase
        self.getInstancesWithRelatedEntitiesUseCase = getInstancesWithRelatedEntitiesUseCase
        self.container = container
        
        // Setup search filtering
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                self?.applyFilters()
            }
            .store(in: &cancellables)
        
        // Subscribe to data changes
        subscribeToSessionChanges()
        
        // Load initial data
        Task {
            await loadSessions()
        }
    }
    
    deinit {
        print("🗑️ ChatsViewModel deinit")
        selectedChatView = nil
    }
    
    // MARK: - Public Methods
    
    @MainActor
    func loadSessions() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let fetchedSessions = try await getAllSessionsUseCase.execute()
            self.sessions = fetchedSessions
            applyFilters()
        } catch {
            let chatError = error as? ChatError ?? ChatError.sessionNotFound
            handleError(chatError)
        }

        // Get active message for each session
        let sessionActiveMessageIdMap = Dictionary(uniqueKeysWithValues: sessions.compactMap { session in
            session.activeMessageId.map { (session.id, $0) }
        })
        
        let missingMessageIds = sessionActiveMessageIdMap.values.filter { sessionActiveMessageMap[$0] == nil }
        
        if !missingMessageIds.isEmpty {
            do {
                let activeMessages = try await getMessagesUseCase.execute(messageIds: Array(missingMessageIds))
                for message in activeMessages {
                    sessionActiveMessageMap[message.sessionId] = message
                }
            } catch {
                let chatError = error as? ChatError ?? ChatError.messageNotFound
                handleError(chatError)
            }
        }

        // Collect all instance IDs from all sessions that are not already in instanceContextsMap
        let allInstanceIds = Array(Set(sessions.flatMap { $0.activeLLMInstanceIds }))
        let missingInstanceIds = allInstanceIds.filter { instanceContextsMap[$0] == nil }

        if !missingInstanceIds.isEmpty {
            do {
                print("ChatsViewModel: Loading instance contexts for \(missingInstanceIds.count) instances")
                let instanceContexts = try await getInstancesWithRelatedEntitiesUseCase.execute(instanceIds: missingInstanceIds)
                for context in instanceContexts {
                    instanceContextsMap[context.instance.id] = context
                }
            } catch {
                print("ChatsViewModel: Failed to load instance contexts - \(error)")
                // Keep existing instanceContextsMap if error occurs
            }
        }
        
        // Always rebuild sessionActiveInstanceContextsMap to ensure it's up-to-date
        var sessionInstanceContextsMap: [UUID: [LLMInstanceContext]] = [:]
        for session in sessions {
            let orderedContexts = session.activeLLMInstanceIds.compactMap { instanceId in
                instanceContextsMap[instanceId]
            }
            sessionInstanceContextsMap[session.id] = orderedContexts
        }
        sessionActiveInstanceContextsMap = sessionInstanceContextsMap
                
        isLoading = false
    }
    
    @MainActor
    func refreshSessions() async {
        await loadSessions()
    }
    
    func deleteSession(at offsets: IndexSet) {
        Task {
            do {
                for index in offsets {
                    let sessionToDelete = filteredSessions[index]
                    
                    // Check if we're currently navigated to this session
                    if selectedSession?.id == sessionToDelete.id {
                        await MainActor.run {
                            resetNavigation()
                        }
                    }
                    
                    try await deleteSessionUseCase.execute(sessionId: sessionToDelete.id)

                    await loadSessions()
                }
            } catch {
                let chatError = error as? ChatError ?? ChatError.messageSendFailed(underlying: error)
                await MainActor.run {
                    handleError(chatError)
                }
            }
        }
    }

    func navigateToChatSession(_ session: ChatSession) {
        print("🔄 ChatsViewModel navigateToChatSession: \(session.id)")
        guard let container = container else {
            print("❌ ChatsViewModel: Container is nil, cannot create ChatView")
            return
        }
        
        // Avoid redundant navigation to the same session
        if selectedSession?.id == session.id && isNavigatingToChatSession {
            print("🔄 Already navigated to session: \(session.id)")
            return
        }
        
        selectedSession = session
        toolbarVisibility = .hidden
        
        // Create ChatView directly in ViewModel with duplication callback
        let chatView = ChatView(viewModel: container.makeChatViewModel(
            chatSessionId: session.id,
            onDuplicateChatSession: { [weak self] newSessionId in
                Task { @MainActor [weak self] in
                    await self?.handleChatDuplication(newSessionId: newSessionId)
                }
            }
        ))
        .environmentObject(container)
        selectedChatView = AnyView(chatView)
        
        // Update chatViewId to ensure SwiftUI creates a new view
        chatViewId = UUID()

        // Record the opened chat session for app restoration
        AppStorageManager.shared.recordChatSessionOpened(session.id)

        isNavigatingToChatSession = true
    }
    
    /// Directly navigate to a chat session without resetting navigation state
    /// Used for seamless transitions like chat duplication
    /// - Parameter session: The session to navigate to
    @MainActor
    private func directNavigateToChatSession(_ session: ChatSession) {
        print("🔄 ChatsViewModel directNavigateToChatSession: \(session.id)")
        guard let container = container else {
            print("❌ ChatsViewModel: Container is nil, cannot create ChatView")
            return
        }
        
        // Set flag to prevent resetNavigation during transition
        isPerformingDirectNavigation = true
        
        // Update session and view
        selectedSession = session
        toolbarVisibility = .hidden
        
        // Create new ChatView with duplication callback
        let chatView = ChatView(viewModel: container.makeChatViewModel(
            chatSessionId: session.id,
            onDuplicateChatSession: { [weak self] newSessionId in
                Task { @MainActor [weak self] in
                    await self?.handleChatDuplication(newSessionId: newSessionId)
                }
            }
        ))
        .environmentObject(container)
        selectedChatView = AnyView(chatView)
        
        // Force SwiftUI to recreate the view by updating the ID
        chatViewId = UUID()

        // Record the opened chat session for app restoration
        AppStorageManager.shared.recordChatSessionOpened(session.id)

        // Reset flag after a brief delay to allow UI to stabilize
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.isPerformingDirectNavigation = false
        }
    }
    
    func resetNavigation() {
        print("🔄 ChatsViewModel resetNavigation")

        // Skip reset if we're performing direct navigation or in sub-navigation
        if isPerformingDirectNavigation || isInSubNavigation {
            print("🔄 ChatsViewModel resetNavigation skipped - performing direct navigation or in sub-navigation")
            return
        }

        // Only clear the last opened chat session when explicitly requested (user actively exits)
        AppStorageManager.shared.clearLastChatSessionIfUserInitiated()

        Task {
            await loadSessions()
        }
        isNavigatingToChatSession = false
        selectedChatView = nil
        selectedSession = nil
        chatViewId = UUID() // Reset the view ID
        withAnimation {
            toolbarVisibility = .visible
        }
    }

    /// Mark that we're entering sub-navigation within ChatView
    func enterSubNavigation() {
        print("🔄 ChatsViewModel enterSubNavigation")
        isInSubNavigation = true
    }

    /// Mark that we're leaving sub-navigation within ChatView
    func exitSubNavigation() {
        print("🔄 ChatsViewModel exitSubNavigation")
        isInSubNavigation = false
    }
    
    /// Handles the duplication callback from ChatViewModel
    /// - Parameter newSessionId: ID of the newly created duplicate session
    @MainActor
    private func handleChatDuplication(newSessionId: UUID) async {
        print("🔄 ChatsViewModel handleChatDuplication: \(newSessionId)")
        
        // Refresh sessions list to include the new session
        await refreshSessions()
        
        // Directly navigate to the newly created session without resetting navigation
        if let newSession = sessions.first(where: { $0.id == newSessionId }) {
            directNavigateToChatSession(newSession)
        } else {
            print("⚠️ Warning: Could not find newly created session: \(newSessionId)")
            // Fallback to normal navigation if session not found
            resetNavigation()
        }
    }
    
    // MARK: - Private Methods
    
    private func subscribeToSessionChanges() {
        observeChatsChangesUseCase.executeForChatsView()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] change in
                guard let self = self else { return }
                if !self.isLoading {
                    Task {
                        await self.loadSessions()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func applyFilters() {
        if searchText.isEmpty {
            filteredSessions = sessions.sorted { $0.lastModifiedAt > $1.lastModifiedAt }
            return
        }
        
        let lowercasedSearchText = searchText.lowercased()
        
        // Filter sessions where title contains the search text
        filteredSessions = sessions.filter { session in
            let titleMatch = session.title?.lowercased().contains(lowercasedSearchText) ?? false
            return titleMatch
        }.sorted { $0.lastModifiedAt > $1.lastModifiedAt }
    }
    
    @MainActor
    private func handleError(_ error: ChatError) {
        errorMessage = error.errorDescription ?? error.localizedDescription
        print("ChatsViewModel error: \(error)")
    }
}
