import Foundation
import SwiftUI

/// ViewModel for the ModelDetailView that handles loading, editing, and saving model data
final class ModelDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    
    /// The model being viewed or edited, nil when creating a new model
    @Published var model: LLMModel?
    
    /// Editable copy of the model used during editing or creation
    @Published var editableModel: LLMModel
    
    /// The provider associated with this model
    @Published var associatedProvider: LLMProvider?
    
    /// List of instances based on this model
    @Published var associatedInstances: [LLMInstance] = []
    
    /// Loading state used for UI feedback
    @Published var isLoading = false
    
    /// Error message to display to the user
    @Published var errorMessage: String? = nil
    
    /// Whether the view is in editing mode
    @Published var isEditing = false

    /// Whether to auto-create a default instance when creating new models
    @Published var shouldAutoCreateDefaultInstance = true
    
    // MARK: - Private Properties
    
    /// ID of the model being viewed or edited, nil when creating a new model
    private let modelId: UUID?
    
    /// ID of the provider for new model creation, only used when modelId is nil
    private let providerIdForNewModel: UUID?
    
    // Use case dependencies
    private let getModelUseCase: GetModelUseCaseProtocol
    private let getProviderUseCase: GetProviderUseCaseProtocol
    private let getInstancesForModelUseCase: GetInstancesForModelUseCaseProtocol
    private let createModelUseCase: CreateModelUseCaseProtocol
    private let updateModelUseCase: UpdateModelUseCaseProtocol
    private let deleteModelUseCase: DeleteModelUseCaseProtocol
    private let createInstanceUseCase: CreateInstanceUseCaseProtocol
    
    // MARK: - Computed Properties
    
    /// Returns true if creating a new model
    var isCreatingNewModel: Bool { modelId == nil }
    
    // MARK: - Initialization
    
    /// Initialize with the model ID and provider ID (for creation), and all required use cases
    /// - Parameters:
    ///   - modelId: ID of the model to view/edit, or nil to create a new model
    ///   - providerId: ID of the provider when creating a new model
    ///   - getModelUseCase: Use case for retrieving a model
    ///   - getProviderUseCase: Use case for retrieving a provider
    ///   - getInstancesForModelUseCase: Use case for retrieving instances for a model
    ///   - createModelUseCase: Use case for creating a new model
    ///   - updateModelUseCase: Use case for updating an existing model
    ///   - deleteModelUseCase: Use case for deleting a model
    ///   - createInstanceUseCase: Use case for creating a new instance
    init(
        modelId: UUID?,
        providerId: UUID?,
        getModelUseCase: GetModelUseCaseProtocol,
        getProviderUseCase: GetProviderUseCaseProtocol,
        getInstancesForModelUseCase: GetInstancesForModelUseCaseProtocol,
        createModelUseCase: CreateModelUseCaseProtocol,
        updateModelUseCase: UpdateModelUseCaseProtocol,
        deleteModelUseCase: DeleteModelUseCaseProtocol,
        createInstanceUseCase: CreateInstanceUseCaseProtocol
    ) {
        self.modelId = modelId
        self.providerIdForNewModel = providerId
        self.getModelUseCase = getModelUseCase
        self.getProviderUseCase = getProviderUseCase
        self.getInstancesForModelUseCase = getInstancesForModelUseCase
        self.createModelUseCase = createModelUseCase
        self.updateModelUseCase = updateModelUseCase
        self.deleteModelUseCase = deleteModelUseCase
        self.createInstanceUseCase = createInstanceUseCase
        
        // Initialize with empty model for creation mode or a placeholder for edit/view mode
        if modelId == nil {
            // Create mode - start with a blank model and editing mode
            let idForEmptyModelProvider = providerId ?? UUID() // loadInitialData will check for nil and handle error
            let emptyModel = LLMModel(
                providerId: idForEmptyModelProvider,
                modelIdentifier: "",
                name: "",
                modelDescription: nil,
                contextWindowSize: 64000,
                inputModalities: [.text],
                outputModalities: [.text],
                thinkingCapabilities: ThinkingCapabilities(controlType: .none),
                searchingCapabilities: SearchingCapabilities(controlType: .none),
                maxOutputTokens: 64000,
                availabilityStatus: .available,
                isUserCreated: true,
                isUserModified: true
            )
            self.editableModel = emptyModel
            self.isEditing = true
        } else {
            // Edit/View mode - start with a placeholder that will be replaced when loaded
            self.editableModel = LLMModel(
                id: modelId!,
                providerId: providerId ?? UUID(),
                modelIdentifier: "",
                name: "",
                isUserCreated: false,
                isUserModified: false
            )
            self.isEditing = false
        }
        
        // Load data asynchronously
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Public Methods
    
    /// Loads initial data based on mode (create vs edit)
    @MainActor
    private func loadInitialData() async {
        if isCreatingNewModel {
            guard let strongInitialProviderId = providerIdForNewModel else {
                isLoading = false
                handleError(ModelManagementError.missingProviderIdForNewModel)
                return
            }
            await loadAssociatedProviderDetails(providerId: strongInitialProviderId)
        } else {
            await loadModelDetails()
        }
    }
    
    /// Loads the model details and associated data
    @MainActor
    func loadModelDetails() async {
        guard !isCreatingNewModel else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Fetch model
            if let modelId = modelId {
                let model = try await getModelUseCase.execute(modelId: modelId)
                
                self.model = model
                self.editableModel = model
                
                // Load associated provider and instances
                await loadAssociatedProviderDetails(providerId: model.providerId)
                await loadAssociatedInstances(modelId: model.id)
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Loads the provider associated with this model
    @MainActor
    func loadAssociatedProviderDetails(providerId: UUID) async {
        do {
            self.associatedProvider = try await getProviderUseCase.execute(providerId: providerId)
        } catch {
            handleError(error)
        }
    }
    
    /// Loads the instances based on this model
    @MainActor
    func loadAssociatedInstances(modelId: UUID) async {
        do {
            self.associatedInstances = try await getInstancesForModelUseCase.execute(modelId: modelId)
        } catch {
            handleError(error)
        }
    }
    
    /// Saves changes to the model
    @MainActor
    func saveChanges() async {
        // Validate input
        guard validateInput() else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            if isCreatingNewModel {
                // Create new model
                let newModel = try await createModelUseCase.execute(model: editableModel)
                self.model = newModel
                self.editableModel = newModel

                // Auto-create default instance if enabled
                if shouldAutoCreateDefaultInstance {
                    try await createDefaultInstance(for: newModel)
                }

            } else if let modelId = modelId {
                // Update existing model
                let updatedModel = try await updateModelUseCase.execute(model: editableModel)
                self.model = updatedModel
                self.editableModel = updatedModel
                
                // Reload instances after update to ensure we have the latest data
                await loadAssociatedInstances(modelId: modelId)
            }
            
            // Reset the UI state
            isEditing = false
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Deletes the current model
    @MainActor
    func deleteModel() async {
        guard let modelId = modelId, model?.isUserCreated == true else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try await deleteModelUseCase.execute(modelId: modelId)
            // Successful deletion - the view should handle navigation
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Prepares the view for editing
    @MainActor
    func prepareForEditing() {
        if let model = model {
            // Ensure we have the latest copy for editing
            editableModel = model
        }
        isEditing = true
    }
    
    /// Cancels editing and resets the editable model
    @MainActor
    func cancelEditing() {
        if let model = model {
            // Reset to the original model
            editableModel = model
        }
        
        // Reset state
        errorMessage = nil
        isEditing = false
    }
    
    // MARK: - Private Methods

    /// Creates a default instance for the given model using the shared factory method
    @MainActor
    private func createDefaultInstance(for model: LLMModel) async throws {
        // Use the shared factory method to create a default instance
        let defaultInstance = LLMInstance.createDefaultInstance(
            for: model,
            provider: associatedProvider,
            instanceId: UUID(),
            isUserModified: true
        )

        // Create the instance using the use case
        _ = try await createInstanceUseCase.execute(
            modelId: model.id,
            name: defaultInstance.name,
            systemPrompt: defaultInstance.systemPrompt,
            defaultParameters: defaultInstance.defaultParameters,
            customLogoData: nil
        )

        // Reload instances to show the newly created one
        await loadAssociatedInstances(modelId: model.id)
    }
    
    /// Validates input before saving
    @MainActor
    private func validateInput() -> Bool {
        // Name must not be empty
        if editableModel.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errorMessage = "Model name cannot be empty"
            return false
        }
        
        // Model Identifier must not be empty
        if editableModel.modelIdentifier.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errorMessage = "Model identifier cannot be empty"
            return false
        }
        
        // Validate context window size if provided
        if let contextSize = editableModel.contextWindowSize, contextSize <= 0 {
            errorMessage = "Context window size must be a positive number"
            return false
        }
        
        // Validate max output tokens if provided
        if let maxTokens = editableModel.maxOutputTokens, maxTokens <= 0 {
            errorMessage = "Maximum output tokens must be a positive number"
            return false
        }
        
        // Make sure input and output modalities are not empty
        if editableModel.inputModalities.isEmpty {
            errorMessage = "Model must support at least one input modality"
            return false
        }
        
        if editableModel.outputModalities.isEmpty {
            errorMessage = "Model must support at least one output modality"
            return false
        }
        
        return true
    }
    
    /// Handles errors and updates the errorMessage
    @MainActor
    private func handleError(_ error: Error) {
        if let modelError = error as? ModelManagementError {
            switch modelError {
            case .modelNotFound:
                errorMessage = "Model not found"
            case .cannotDeleteBuiltInModel:
                errorMessage = "Cannot delete a built-in model"
            case .cannotModifyBuiltInModelCore:
                errorMessage = "Cannot modify core properties of a built-in model"
            case .cannotDeleteModelUsedByInstances:
                errorMessage = "Cannot delete model that is used by instances"
            case .duplicateModelIdentifier:
                errorMessage = "A model with this identifier already exists for this provider"
            case .providerNotFound:
                errorMessage = "Provider not found"
            case .missingProviderIdForNewModel:
                errorMessage = "Provider ID is missing for new model creation"
            case .persistenceError(let underlyingError):
                errorMessage = "Database error: \(underlyingError.localizedDescription)"
            default:
                errorMessage = "An unexpected error occurred: \(modelError)"
            }
        } else {
            errorMessage = "An error occurred: \(error.localizedDescription)"
        }
        
        print("ModelDetailViewModel error: \(error)")
    }
}
