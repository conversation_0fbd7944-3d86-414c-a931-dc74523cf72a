import Foundation
import Combine

final class NewModelsSheetViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var allProviders: [LLMProvider] = []
    @Published var modelsByProviderId: [UUID: [LLMModel]] = [:]
    
    // Dictionaries for quick lookups
    @Published var allModelsById: [UUID: LLMModel] = [:]
    
    @Published var searchText: String = ""
    @Published var filteredProviders: [LLMProvider] = []
    @Published var filteredModelsByProviderId: [UUID: [LLMModel]] = [:]
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    // MARK: - Private Properties
    private let getAllProvidersUseCase: GetAllProvidersUseCaseProtocol
    private let getAllModelsUseCase: GetAllModelsUseCaseProtocol
    private let observeModelManagementChangesUseCase: ObserveModelManagementChangesUseCaseProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        getAllProvidersUseCase: GetAllProvidersUseCaseProtocol,
        getAllModelsUseCase: GetAllModelsUseCaseProtocol,
        observeModelManagementChangesUseCase: ObserveModelManagementChangesUseCaseProtocol
    ) {
        self.getAllProvidersUseCase = getAllProvidersUseCase
        self.getAllModelsUseCase = getAllModelsUseCase
        self.observeModelManagementChangesUseCase = observeModelManagementChangesUseCase
        
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                Task { @MainActor [weak self] in
                    self?.applyFilters()
                }
            }
            .store(in: &cancellables)
        
        self.observeModelManagementChangesUseCase.execute()
            .debounce(for: .milliseconds(200), scheduler: DispatchQueue.main)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] in
                guard let self = self else { return }
                if !self.isLoading {
                    Task {
                        await self.loadData()
                    }
                }
            }
            .store(in: &cancellables)
        
        Task {
            await loadData()
        }
    }
    
    // MARK: - Public Methods
    
    /// Loads all providers and models data
    @MainActor
    func loadData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Concurrently fetch data
            async let providersResult = getAllProvidersUseCase.execute(onlyUserCreated: nil)
            async let modelsResult = getAllModelsUseCase.execute()
            
            let (fetchedProviders, fetchedModels) = try await (providersResult, modelsResult)
            
            self.allProviders = fetchedProviders.sorted { 
                if $0.apiKeyStored != $1.apiKeyStored {
                    return $0.apiKeyStored && !$1.apiKeyStored
                }
                return $0.name < $1.name
            }
            
            // Populate lookup dictionaries
            self.allModelsById = Dictionary(uniqueKeysWithValues: fetchedModels.map { ($0.id, $0) })
            
            // Process models to group them by providerId
            var tempModelsByProviderId: [UUID: [LLMModel]] = [:]
            for model in fetchedModels {
                tempModelsByProviderId[model.providerId, default: []].append(model)
            }
            
            // Sort models within each provider group
            for (providerId, models) in tempModelsByProviderId {
                tempModelsByProviderId[providerId] = models.sorted { $0.name < $1.name }
            }
            self.modelsByProviderId = tempModelsByProviderId
            
            applyFilters()
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Applies search filter to providers and models
    @MainActor
    func applyFilters() {
        let lowercasedSearchText = searchText.lowercased()
        
        if searchText.isEmpty {
            filteredProviders = allProviders
            filteredModelsByProviderId = modelsByProviderId
            return
        }
        
        var tempFilteredModelsByProviderId: [UUID: [LLMModel]] = [:]
        var providersWithMatchingModels = Set<UUID>()
        
        for (providerId, models) in modelsByProviderId {
            let matchingModels = models.filter { model in
                model.name.lowercased().contains(lowercasedSearchText) ||
                model.modelDescription?.lowercased().contains(lowercasedSearchText) == true
            }
            if !matchingModels.isEmpty {
                tempFilteredModelsByProviderId[providerId] = matchingModels
                providersWithMatchingModels.insert(providerId)
            }
        }
        self.filteredModelsByProviderId = tempFilteredModelsByProviderId
        
        self.filteredProviders = allProviders.filter { provider in
            provider.name.lowercased().contains(lowercasedSearchText) || 
            providersWithMatchingModels.contains(provider.id)
        }.sorted { 
            if $0.apiKeyStored != $1.apiKeyStored {
                return $0.apiKeyStored && !$1.apiKeyStored
            }
            return $0.name < $1.name
        }
    }
    
    /// Refreshes all data
    @MainActor
    func refreshData() async {
        await loadData()
    }
    
    // MARK: - Private Methods
    
    /// Handles errors and updates the error message
    @MainActor
    private func handleError(_ error: Error) {
        if let modelManagementError = error as? ModelManagementError {
            switch modelManagementError {
            case .providerNotFound:
                errorMessage = "Error: Provider not found."
            case .modelNotFound:
                errorMessage = "Error: Model not found. This may affect some items' display."
            case .persistenceError(let underlyingError):
                errorMessage = "Database error: \(underlyingError.localizedDescription)"
            default:
                errorMessage = "An error occurred in model management: \(modelManagementError.localizedDescription)"
            }
        } else {
            errorMessage = "An unexpected error occurred: \(error.localizedDescription)"
        }
        print("NewModelsSheetViewModel error: \(String(describing: errorMessage))")
    }
} 