import Foundation
import SwiftUI

/// ViewModel for the InstanceDetailView that handles loading, editing, and saving instance data
final class InstanceDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    
    /// The instance being viewed or edited, nil when creating a new instance
    @Published var instance: LLMInstance?
    
    /// Editable copy of the instance used during editing or creation
    @Published var editableInstance: LLMInstance
    
    /// The model associated with this instance
    @Published var associatedModel: LLMModel?
    
    /// The provider associated with this model
    @Published var associatedProvider: LLMProvider?

    /// Available providers for selection
    @Published var availableProviders: [LLMProvider] = []

    /// Available models for the current provider
    @Published var availableModelsForCurrentProvider: [LLMModel] = []

    /// ID of the duplicated instance for navigation
    @Published var duplicatedInstanceId: UUID? = nil
    
    /// Loading state used for UI feedback
    @Published var isLoading = false
    
    /// Error message to display to the user
    @Published var errorMessage: String? = nil
    
    /// Whether the view is in editing mode
    @Published var isEditing = false
    
    // MARK: - Private Properties
    
    /// ID of the instance being viewed or edited, nil when creating a new instance
    private let instanceId: UUID?
    
    /// ID of the model for new instance creation, only used when instanceId is nil
    private let modelIdForNewInstance: UUID?
    
    // Use case dependencies
    private let getInstanceUseCase: GetInstanceUseCaseProtocol
    private let getModelUseCase: GetModelUseCaseProtocol
    private let getProviderUseCase: GetProviderUseCaseProtocol
    private let createInstanceUseCase: CreateInstanceUseCaseProtocol
    private let updateInstanceUseCase: UpdateInstanceUseCaseProtocol
    private let deleteInstanceUseCase: DeleteInstanceUseCaseProtocol
    private let duplicateInstanceUseCase: DuplicateInstanceUseCaseProtocol
    private let toggleInstanceFavoriteUseCase: ToggleInstanceFavoriteUseCaseProtocol
    private let getModelsForProviderUseCase: GetModelsForProviderUseCaseProtocol
    private let getAllProvidersUseCase: GetAllProvidersUseCaseProtocol

    // MARK: - Computed Properties
    
    /// Returns true if creating a new instance
    var isCreatingNewInstance: Bool { instanceId == nil }
    
    // MARK: - Initialization
    
    /// Initialize with the instance ID and model ID (for creation), and all required use cases
    /// - Parameters:
    ///   - instanceId: ID of the instance to view/edit, or nil to create a new instance
    ///   - modelIdForNewInstance: ID of the model when creating a new instance
    ///   - getInstanceUseCase: Use case for retrieving an instance
    ///   - getModelUseCase: Use case for retrieving a model
    ///   - getProviderUseCase: Use case for retrieving a provider
    ///   - createInstanceUseCase: Use case for creating a new instance
    ///   - updateInstanceUseCase: Use case for updating an existing instance
    ///   - deleteInstanceUseCase: Use case for deleting an instance
    ///   - duplicateInstanceUseCase: Use case for duplicating an instance
    ///   - toggleInstanceFavoriteUseCase: Use case for toggling instance favorite status
    init(
        instanceId: UUID?,
        modelIdForNewInstance: UUID?,
        getInstanceUseCase: GetInstanceUseCaseProtocol,
        getModelUseCase: GetModelUseCaseProtocol,
        getProviderUseCase: GetProviderUseCaseProtocol,
        createInstanceUseCase: CreateInstanceUseCaseProtocol,
        updateInstanceUseCase: UpdateInstanceUseCaseProtocol,
        deleteInstanceUseCase: DeleteInstanceUseCaseProtocol,
        duplicateInstanceUseCase: DuplicateInstanceUseCaseProtocol,
        toggleInstanceFavoriteUseCase: ToggleInstanceFavoriteUseCaseProtocol,
        getModelsForProviderUseCase: GetModelsForProviderUseCaseProtocol,
        getAllProvidersUseCase: GetAllProvidersUseCaseProtocol
    ) {
        self.instanceId = instanceId
        self.modelIdForNewInstance = modelIdForNewInstance
        self.getInstanceUseCase = getInstanceUseCase
        self.getModelUseCase = getModelUseCase
        self.getProviderUseCase = getProviderUseCase
        self.createInstanceUseCase = createInstanceUseCase
        self.updateInstanceUseCase = updateInstanceUseCase
        self.deleteInstanceUseCase = deleteInstanceUseCase
        self.duplicateInstanceUseCase = duplicateInstanceUseCase
        self.toggleInstanceFavoriteUseCase = toggleInstanceFavoriteUseCase
        self.getModelsForProviderUseCase = getModelsForProviderUseCase
        self.getAllProvidersUseCase = getAllProvidersUseCase
        
        // Initialize with empty instance for creation mode or a placeholder for edit/view mode
        if instanceId == nil {
            // Create mode - start with a blank instance and editing mode
            let idForNewModel = modelIdForNewInstance ?? UUID() // loadInitialData will check for nil and handle error
            let emptyInstance = LLMInstance(
                modelId: idForNewModel,
                name: "New Instance",
                systemPrompt: "You are a helpful assistant.",
                defaultParameters: nil, // Will be set in setDefaultParametersForNewInstance
                isUserModified: true
            )
            self.editableInstance = emptyInstance
            self.isEditing = true
        } else {
            // Edit/View mode - start with a placeholder that will be replaced when loaded
            self.editableInstance = LLMInstance(
                id: instanceId!,
                modelId: UUID(), // Will be updated when instance is loaded
                name: "",
                isUserModified: false
            )
            self.isEditing = false
        }
        
        // Load data asynchronously
        Task { @MainActor in
            await loadInitialData()
        }
    }
    
    // MARK: - Public Methods
    
    /// Loads initial data based on mode (create vs edit)
    @MainActor
    private func loadInitialData() async {
        if isCreatingNewInstance {
            guard let strongModelId = modelIdForNewInstance else {
                isLoading = false
                handleError(ModelManagementError.validationError)
                return
            }
            await loadAssociatedModelAndProviderDetails(modelId: strongModelId)
        } else {
            await loadInstanceDetails()
        }

        await loadAvailableProviders()
        // loadAssociatedModelAndProviderDetails will load the models for the current provider
    }
    
    /// Loads the instance details and associated data
    @MainActor
    func loadInstanceDetails() async {
        guard !isCreatingNewInstance else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Fetch instance
            if let instanceId = instanceId {
                let instance = try await getInstanceUseCase.execute(id: instanceId)
                
                if let instance = instance {
                    self.instance = instance
                    self.editableInstance = instance
                    
                    // Load associated model and provider
                    await loadAssociatedModelAndProviderDetails(modelId: instance.modelId)
                } else {
                    throw ModelManagementError.instanceNotFound
                }
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Loads the model and provider associated with this instance
    @MainActor
    func loadAssociatedModelAndProviderDetails(modelId: UUID) async {
        isLoading = true
        
        do {
            // Get the model
            let model = try await getModelUseCase.execute(modelId: modelId)
            self.associatedModel = model
            
            // Get the provider based on the model
            let providerId = model.providerId
            self.associatedProvider = try await getProviderUseCase.execute(providerId: providerId)

            // Load available models for this provider
            await loadAvailableModelsForCurrentProvider()
            
            // Set default parameters for new instances based on model capabilities
            if isCreatingNewInstance {
                setDefaultParametersForNewInstance(model: model)
            }
        
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }

    /// Loads all available providers
    @MainActor
    func loadAvailableProviders() async {
        isLoading = true
        do {
            availableProviders = try await getAllProvidersUseCase.execute(onlyUserCreated: nil)
        } catch {
            handleError(error)
        }
        isLoading = false
    }

    /// Loads all available models for the current provider
    @MainActor
    func loadAvailableModelsForCurrentProvider() async {
        guard let provider = associatedProvider else {
            availableModelsForCurrentProvider = []
            return
        }
        
        isLoading = true
        do {
            availableModelsForCurrentProvider = try await getModelsForProviderUseCase.execute(providerId: provider.id)
        } catch {
            handleError(error)
        }
        isLoading = false
    }
    
    /// Updates model when the user selects a different model
    @MainActor
    func instanceModelDidChange(newModelId: UUID) async {
        // Update the instance's model ID
        editableInstance.modelId = newModelId
        
        // Load the new model and provider details
        await loadAssociatedModelAndProviderDetails(modelId: newModelId)
    }
    
    /// Updates provider and available models when the user selects a different provider
    @MainActor
    func instanceProviderDidChange(newProviderId: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // First, check if there are any models available for this provider
            let availableModelsForProvider = try await getModelsForProviderUseCase.execute(providerId: newProviderId)
            
            if availableModelsForProvider.isEmpty {
                // No models available - show error without changing the provider
                throw ModelManagementError.cannotFindModelForProvider
            }
            
            // Models are available, so update the provider
            self.associatedProvider = try await getProviderUseCase.execute(providerId: newProviderId)
            
            // Try to find a model marked as default recommendation
            let defaultModel = availableModelsForProvider.first(where: { $0.isDefaultRecommendation }) ?? availableModelsForProvider.first
            
            if let selectedModel = defaultModel {
                // Update the instance's model ID to the selected model
                editableInstance.modelId = selectedModel.id
                
                // Load the associated model details
                await loadAssociatedModelAndProviderDetails(modelId: selectedModel.id)
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Saves changes to the instance
    @MainActor
    func saveChanges() async {
        // Validate input
        guard validateInput() else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Ensure the instance is marked as user-modified
            var instanceToSave = editableInstance
            instanceToSave.isUserModified = true

            if isCreatingNewInstance {
                // Create new instance
                let newInstance = try await createInstanceUseCase.execute(
                    modelId: instanceToSave.modelId,
                    name: instanceToSave.name,
                    systemPrompt: instanceToSave.systemPrompt,
                    defaultParameters: instanceToSave.defaultParameters,
                    customLogoData: instanceToSave.customLogoData
                )
                
                self.instance = newInstance
                self.editableInstance = newInstance
                
            } else {
                // Update existing instance
                try await updateInstanceUseCase.execute(instance: instanceToSave)
                
                // Refresh the instance
                let id = instanceToSave.id
                let updatedInstance = try await getInstanceUseCase.execute(id: id)
                if let updatedInstance = updatedInstance {
                    self.instance = updatedInstance
                    self.editableInstance = updatedInstance
                }
            }
            
            // Reset the UI state
            isEditing = false
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Deletes the current instance
    @MainActor
    func deleteInstance() async {
        guard let instanceId = instanceId ?? instance?.id else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try await deleteInstanceUseCase.execute(id: instanceId)
            // Successful deletion - the view should handle navigation
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Duplicates the current instance
    @MainActor
    func duplicateInstance() async {
        guard let instanceId = instanceId ?? instance?.id else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let duplicatedInstance = try await duplicateInstanceUseCase.execute(id: instanceId)
            // Store the duplicated instance ID for navigation
            self.duplicatedInstanceId = duplicatedInstance.id
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Toggles the favorite status of the instance
    @MainActor
    func toggleFavorite() async {
        guard let instanceId = instanceId ?? instance?.id else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let updatedInstance = try await toggleInstanceFavoriteUseCase.execute(id: instanceId)
            
            // Update both the view model's instance copies
            editableInstance.isFavorited = updatedInstance.isFavorited
            if instance != nil {
                instance?.isFavorited = updatedInstance.isFavorited
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Prepares the view for editing
    @MainActor
    func prepareForEditing() {
        if let instance = instance {
            // Ensure we have the latest copy for editing
            editableInstance = instance
        }
        isEditing = true
    }
    
    /// Cancels editing and resets the editable instance
    @MainActor
    func cancelEditing() {
        if let instance = instance {
            // Reset to the original instance
            editableInstance = instance
        }
        
        // Reset state
        errorMessage = nil
        isEditing = false
    }
    
    // MARK: - Private Methods
    
    /// Validates input before saving
    @MainActor
    private func validateInput() -> Bool {
        // Name must not be empty
        if editableInstance.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errorMessage = "Instance name cannot be empty"
            return false
        }
        
        // Model ID must be valid
        if editableInstance.modelId == UUID.init(uuid: UUID_NULL) {
            errorMessage = "A model must be selected"
            return false
        }
        
        // Clean up system prompt (remove leading/trailing whitespace)
        if let systemPrompt = editableInstance.systemPrompt {
            let trimmedPrompt = systemPrompt.trimmingCharacters(in: .whitespacesAndNewlines)
            editableInstance.systemPrompt = trimmedPrompt.isEmpty ? nil : trimmedPrompt
        }
        
        return true
    }
    
    /// Handles errors and updates the errorMessage
    @MainActor
    private func handleError(_ error: Error) {
        if let instanceError = error as? ModelManagementError {
            switch instanceError {
            case .instanceNotFound:
                errorMessage = "Instance not found"
            case .cannotModifyBuiltInInstance:
                errorMessage = "Cannot modify a built-in instance"
            case .cannotDuplicateInstance:
                errorMessage = "Cannot duplicate this instance"
            case .cannotUpdateInstanceModelAssociation:
                errorMessage = "Cannot change the model for this instance"
            case .invalidInstanceParameters:
                errorMessage = "One or more instance parameters are invalid"
            case .instanceNameAlreadyExists:
                errorMessage = "An instance with this name already exists"
            case .modelNotFound:
                errorMessage = "The selected model was not found"
            case .providerNotFound:
                errorMessage = "The provider for this model was not found"
            case .cannotFindModelForProvider:
                errorMessage = "No models are available for this provider"
            case .validationError:
                errorMessage = "Please check your input and try again"
            case .persistenceError(let underlyingError):
                errorMessage = "Database error: \(underlyingError.localizedDescription)"
            default:
                errorMessage = "An unexpected error occurred: \(instanceError)"
            }
        } else {
            errorMessage = "An error occurred: \(error.localizedDescription)"
        }
        
        print("InstanceDetailViewModel error: \(error)")
    }
    
    /// Sets default parameters for a new instance based on the model's thinking capabilities
    /// This method uses the shared logic from LLMInstance.createDefaultInstance
    @MainActor
    private func setDefaultParametersForNewInstance(model: LLMModel) {
        // Only set defaults if no parameters exist yet
        guard editableInstance.defaultParameters == nil || editableInstance.defaultParameters?.isEmpty == true else {
            return
        }

        // Use the shared factory method to get default parameters
        let defaultInstance = LLMInstance.createDefaultInstance(
            for: model,
            provider: associatedProvider,
            instanceId: editableInstance.id,
            isUserModified: true
        )

        // Update the editable instance with the default parameters
        editableInstance.defaultParameters = defaultInstance.defaultParameters
    }
}
