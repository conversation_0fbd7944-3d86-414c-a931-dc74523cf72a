import Foundation
import Combine
import SwiftUI

final class ModelsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var allProviders: [LLMProvider] = []
    @Published var allModels: [LLMModel] = [] // Added to store all models
    @Published var instancesByProviderId: [UUID: [LLMInstance]] = [:]
    @Published var favoritedInstances: [LLMInstance] = []
    @Published var instanceGroups: [LLMInstanceGroup] = []
    
    // Dictionaries for quick lookups
    @Published var allProvidersById: [UUID: LLMProvider] = [:]
    @Published var allModelsById: [UUID: LLMModel] = [:]

    @Published var searchText: String = ""
    @Published var filteredProviders: [LLMProvider] = []
    @Published var filteredInstancesByProviderId: [UUID: [LLMInstance]] = [:]
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var toolbarVisibility: Visibility = .visible

    // MARK: - Private Properties
    private let getAllProvidersUseCase: GetAllProvidersUseCaseProtocol
    private let getAllModelsUseCase: GetAllModelsUseCaseProtocol // Added use case
    private let getAllInstancesUseCase: GetAllInstancesUseCaseProtocol
    private let getFavoritedInstancesUseCase: GetFavoritedInstancesUseCaseProtocol
    private let getAllInstanceGroupsUseCase: GetAllInstanceGroupsUseCaseProtocol
    private let getModelUseCase: GetModelUseCaseProtocol
    private let observeModelManagementChangesUseCase: ObserveModelManagementChangesUseCaseProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init(
        getAllProvidersUseCase: GetAllProvidersUseCaseProtocol,
        getAllModelsUseCase: GetAllModelsUseCaseProtocol, // Added parameter
        getAllInstancesUseCase: GetAllInstancesUseCaseProtocol,
        getFavoritedInstancesUseCase: GetFavoritedInstancesUseCaseProtocol,
        getAllInstanceGroupsUseCase: GetAllInstanceGroupsUseCaseProtocol,
        getModelUseCase: GetModelUseCaseProtocol,
        observeModelManagementChangesUseCase: ObserveModelManagementChangesUseCaseProtocol
    ) {
        self.getAllProvidersUseCase = getAllProvidersUseCase
        self.getAllModelsUseCase = getAllModelsUseCase // Store use case
        self.getAllInstancesUseCase = getAllInstancesUseCase
        self.getFavoritedInstancesUseCase = getFavoritedInstancesUseCase
        self.getAllInstanceGroupsUseCase = getAllInstanceGroupsUseCase
        self.getModelUseCase = getModelUseCase
        self.observeModelManagementChangesUseCase = observeModelManagementChangesUseCase

        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                Task { @MainActor [weak self] in
                    self?.applyFilters()
                }
            }
            .store(in: &cancellables)
        
        self.observeModelManagementChangesUseCase.execute()
            .debounce(for: .milliseconds(200), scheduler: DispatchQueue.main)
            .receive(on: DispatchQueue.main) // Ensure updates on main thread
            .sink { [weak self] in
                guard let self = self else { return }
                if !self.isLoading { // Avoid refreshing if already loading
                    Task {
                        await self.refreshData()
                    }
                }
            }
            .store(in: &cancellables)

        Task {
            await loadAllData()
        }
    }

    // MARK: - Public Methods
    @MainActor
    func loadAllData() async {
        isLoading = true
        errorMessage = nil

        do {
            // Concurrently fetch all data
            async let providersResult = getAllProvidersUseCase.execute(onlyUserCreated: nil)
            async let modelsResult = getAllModelsUseCase.execute() // Fetch all models
            async let instancesResult = getAllInstancesUseCase.execute()
            async let favoritedInstancesResult = getFavoritedInstancesUseCase.execute()
            async let groupsResult = getAllInstanceGroupsUseCase.execute()
            
            let (fetchedProviders, fetchedModels, allFetchedInstances, fetchedFavoritedInstances, fetchedGroups) = try await (providersResult, modelsResult, instancesResult, favoritedInstancesResult, groupsResult)

            self.allProviders = fetchedProviders.sorted { 
                if $0.apiKeyStored != $1.apiKeyStored {
                    return $0.apiKeyStored && !$1.apiKeyStored
                }
                return $0.name < $1.name
            }
            self.allModels = fetchedModels.sorted { $0.name < $1.name }
            self.favoritedInstances = fetchedFavoritedInstances.sorted { $0.name < $1.name }
            self.instanceGroups = fetchedGroups.sorted { $0.name < $1.name }
            
            // Populate lookup dictionaries
            self.allProvidersById = Dictionary(uniqueKeysWithValues: fetchedProviders.map { ($0.id, $0) })
            self.allModelsById = Dictionary(uniqueKeysWithValues: fetchedModels.map { ($0.id, $0) })
            
            // Process instances to group them by providerId
            var tempInstancesByProviderId: [UUID: [LLMInstance]] = [:]
            for instance in allFetchedInstances {
                if let model = self.allModelsById[instance.modelId] {
                    let providerId = model.providerId
                    tempInstancesByProviderId[providerId, default: []].append(instance)
                } else {
                    print("Error: Could not find model with ID \(instance.modelId) for instance \(instance.id) in allModelsById lookup.")
                    // Optionally, handle instances whose models weren't found (e.g., log, or assign to a default/error provider group)
                }
            }
            // Sort instances within each provider group
            for (providerId, instances) in tempInstancesByProviderId {
                tempInstancesByProviderId[providerId] = instances.sorted { $0.name < $1.name }
            }
            self.instancesByProviderId = tempInstancesByProviderId
            
            applyFilters()

        } catch {
            handleError(error)
        }
        
        isLoading = false
    }

    @MainActor
    func applyFilters() {
        let lowercasedSearchText = searchText.lowercased()

        if searchText.isEmpty {
            filteredProviders = allProviders
            filteredInstancesByProviderId = instancesByProviderId
            return
        }

        var tempFilteredInstancesByProviderId: [UUID: [LLMInstance]] = [:]
        var providersWithMatchingInstances = Set<UUID>()

        for (providerId, instances) in instancesByProviderId {
            let matchingInstances = instances.filter { instance in
                instance.name.lowercased().contains(lowercasedSearchText)
            }
            if !matchingInstances.isEmpty {
                tempFilteredInstancesByProviderId[providerId] = matchingInstances
                providersWithMatchingInstances.insert(providerId)
            }
        }
        self.filteredInstancesByProviderId = tempFilteredInstancesByProviderId

        self.filteredProviders = allProviders.filter { provider in
            provider.name.lowercased().contains(lowercasedSearchText) || providersWithMatchingInstances.contains(provider.id)
        }.sorted { 
            if $0.apiKeyStored != $1.apiKeyStored {
                return $0.apiKeyStored && !$1.apiKeyStored
            }
            return $0.name < $1.name
        }
    }
    
    @MainActor
    func refreshData() async {
        await loadAllData()
    }
    
    // MARK: - Toolbar Visibility Methods
    @MainActor
    func hideToolbar() {
        toolbarVisibility = .hidden
    }
    
    @MainActor
    func showToolbar() {
        toolbarVisibility = .visible
    }
    
    @MainActor
    func showToolbarWithAnimation() {
        withAnimation {
            toolbarVisibility = .visible
        }
    }

    // MARK: - Private Methods
    @MainActor
    private func handleError(_ error: Error) {
        if let modelManagementError = error as? ModelManagementError {
            switch modelManagementError {
            case .providerNotFound:
                errorMessage = "Error: Provider not found."
            case .modelNotFound:
                errorMessage = "Error: Model not found. This may affect display of some items."
            case .instanceNotFound:
                errorMessage = "Error: Instance not found."
            case .groupNotFound:
                errorMessage = "Error: Group not found."
            case .persistenceError(let underlyingError):
                errorMessage = "Database error: \(underlyingError.localizedDescription)"
            default:
                errorMessage = "An error occurred in model management: \(modelManagementError.localizedDescription)"
            }
        } else {
            errorMessage = "An unexpected error occurred: \(error.localizedDescription)"
        }
        print("ModelsViewModel error: \(String(describing: errorMessage))")
    }
}
