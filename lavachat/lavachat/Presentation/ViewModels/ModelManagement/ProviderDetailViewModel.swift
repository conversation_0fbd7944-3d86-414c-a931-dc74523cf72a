import Foundation
import SwiftUI

/// ViewModel for the ProviderDetailView that handles loading, editing, and saving provider data
final class ProviderDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    
    /// The provider being viewed or edited, nil when creating a new provider
    @Published var provider: LLMProvider?
    
    /// Editable copy of the provider used during editing or creation
    @Published var editableProvider: LLMProvider
    
    /// List of models associated with this provider
    @Published var associatedModels: [LLMModel] = []
    
    /// Loading state used for UI feedback
    @Published var isLoading = false
    
    /// Error message to display to the user
    @Published var errorMessage: String? = nil
    
    /// Whether the view is in editing mode
    @Published var isEditing = false
    
    /// Input field for API key during editing mode
    @Published var apiKeyInput: String = ""
    
    /// Controls whether the full API key is displayed during non-editing mode
    @Published var showFullApiKey = false
    
    /// The retrieved API key after authentication during non-editing mode
    @Published var retrievedApiKey: String? = nil
    
    /// Whether the provider has an API key stored
    @Published var apiKeyStored = false
    
    // MARK: - Private Properties
    
    /// ID of the provider being viewed or edited, nil when creating a new provider
    private let providerId: UUID?
    
    // Use case dependencies
    private let getProviderUseCase: GetProviderUseCaseProtocol
    private let getModelsForProviderUseCase: GetModelsForProviderUseCaseProtocol
    private let createProviderUseCase: CreateProviderUseCaseProtocol
    private let updateProviderUseCase: UpdateProviderUseCaseProtocol
    private let deleteProviderUseCase: DeleteProviderUseCaseProtocol
    private let getProviderAPIKeyUseCase: GetProviderAPIKeyUseCaseProtocol
    private let saveProviderAPIKeyUseCase: SaveProviderAPIKeyUseCaseProtocol
    private let clearProviderAPIKeyUseCase: ClearProviderAPIKeyUseCaseProtocol
    
    // MARK: - Computed Properties
    
    /// Returns true if creating a new provider
    var isCreatingNewProvider: Bool { providerId == nil }
    
    // MARK: - Initialization
    
    /// Initialize with the provider ID and all required use cases
    /// - Parameters:
    ///   - providerId: ID of the provider to view/edit, or nil to create a new provider
    ///   - getProviderUseCase: Use case for retrieving a provider
    ///   - getModelsForProviderUseCase: Use case for retrieving models for a provider
    ///   - createProviderUseCase: Use case for creating a new provider
    ///   - updateProviderUseCase: Use case for updating an existing provider
    ///   - deleteProviderUseCase: Use case for deleting a provider
    ///   - getProviderAPIKeyUseCase: Use case for retrieving a provider's API key
    ///   - saveProviderAPIKeyUseCase: Use case for saving a provider's API key
    ///   - clearProviderAPIKeyUseCase: Use case for clearing a provider's API key
    init(
        providerId: UUID?,
        getProviderUseCase: GetProviderUseCaseProtocol,
        getModelsForProviderUseCase: GetModelsForProviderUseCaseProtocol,
        createProviderUseCase: CreateProviderUseCaseProtocol,
        updateProviderUseCase: UpdateProviderUseCaseProtocol,
        deleteProviderUseCase: DeleteProviderUseCaseProtocol,
        getProviderAPIKeyUseCase: GetProviderAPIKeyUseCaseProtocol,
        saveProviderAPIKeyUseCase: SaveProviderAPIKeyUseCaseProtocol,
        clearProviderAPIKeyUseCase: ClearProviderAPIKeyUseCaseProtocol
    ) {
        self.providerId = providerId
        self.getProviderUseCase = getProviderUseCase
        self.getModelsForProviderUseCase = getModelsForProviderUseCase
        self.createProviderUseCase = createProviderUseCase
        self.updateProviderUseCase = updateProviderUseCase
        self.deleteProviderUseCase = deleteProviderUseCase
        self.getProviderAPIKeyUseCase = getProviderAPIKeyUseCase
        self.saveProviderAPIKeyUseCase = saveProviderAPIKeyUseCase
        self.clearProviderAPIKeyUseCase = clearProviderAPIKeyUseCase
        
        // Initialize with empty provider for creation mode or a placeholder for edit/view mode
        if providerId == nil {
            // Create mode - start with a blank provider and editing mode
            let emptyProvider = LLMProvider(
                name: "",
                providerType: .userApiKey,
                apiKeyStored: false,
                apiStyle: .openaiCompatible,
                isUserCreated: true,
                isUserModified: true
            )
            self.editableProvider = emptyProvider
            self.isEditing = true
        } else {
            // Edit/View mode - start with a placeholder that will be replaced when loaded
            self.editableProvider = LLMProvider(
                id: providerId!,
                name: "",
                providerType: .userApiKey,
                apiKeyStored: false,
                apiStyle: .openaiCompatible,
                isUserCreated: false,
                isUserModified: false
            )
            self.isEditing = false
        }
        
        // Load data asynchronously
        Task {
            await loadProviderDetails()
        }
    }
    
    // MARK: - Public Methods
    
    /// Loads the provider details and associated models
    @MainActor
    func loadProviderDetails() async {
        guard !isCreatingNewProvider else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Fetch provider
            if let providerId = providerId {
                let provider = try await getProviderUseCase.execute(providerId: providerId)
                
                self.provider = provider
                self.editableProvider = provider
                self.apiKeyStored = provider.apiKeyStored
                
                // Load associated models
                await loadAssociatedModels()
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Loads the models available for this provider
    @MainActor
    func loadAssociatedModels() async {
        guard let providerId = providerId else { return }
        
        do {
            let models = try await getModelsForProviderUseCase.execute(providerId: providerId)
            self.associatedModels = models
        } catch {
            handleError(error)
        }
    }
    
    /// Saves changes to the provider
    @MainActor
    func saveChanges() async {
        // Validate input
        guard validateInput() else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            if isCreatingNewProvider {
                if editableProvider.providerType != .userApiKey {
                    throw ModelManagementError.cannotCreateProviderDoesNotRequireApiKey
                }

                // API key handling for new provider
                var updatedProvider = editableProvider
                do {
                    if !apiKeyInput.isEmpty {
                        try await saveProviderAPIKeyUseCase.execute(providerId: updatedProvider.id, apiKey: apiKeyInput)
                        updatedProvider.apiKeyStored = true
                        self.apiKeyStored = true
                    } else {
                        updatedProvider.apiKeyStored = false
                        self.apiKeyStored = false
                    }
                } catch {
                    self.apiKeyStored = false
                    throw error
                }

                // Create new provider
                let newProvider = try await createProviderUseCase.execute(provider: updatedProvider)
                self.provider = newProvider
                self.editableProvider = newProvider
                
            } else if let providerId = providerId {
                // API key handling for existing provider
                var updatedProvider = editableProvider
                if updatedProvider.providerType == .userApiKey {
                    if !apiKeyInput.isEmpty {
                        // Save/update API key
                        try await saveProviderAPIKeyUseCase.execute(providerId: providerId, apiKey: apiKeyInput)
                        
                        // Update provider to reflect that key is stored
                        if !updatedProvider.apiKeyStored {
                            updatedProvider.apiKeyStored = true                            
                            self.apiKeyStored = true
                        }
                    } else if apiKeyInput.isEmpty && apiKeyStored {
                        // Clear API key
                        try await clearProviderAPIKeyUseCase.execute(providerId: providerId)
                        
                        // Update provider to reflect that key is not stored
                        updatedProvider.apiKeyStored = false
                        self.apiKeyStored = false
                    }
                }

                // Update existing provider
                try await updateProviderUseCase.execute(provider: updatedProvider)
                self.provider = updatedProvider
                self.editableProvider = updatedProvider
                
                // Reload models after update to ensure we have the latest data
                await loadAssociatedModels()
            }
            
            // Reset the UI state
            isEditing = false
            apiKeyInput = ""
            retrievedApiKey = nil
            showFullApiKey = false
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Deletes the current provider
    @MainActor
    func deleteProvider() async {
        guard let providerId = providerId else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try await deleteProviderUseCase.execute(providerId: providerId)
            // Successful deletion - the view should handle navigation
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Requests access to view the API key (triggers biometric authentication)
    @MainActor
    func requestApiKeyAccess() async {
        guard let providerId = providerId, provider?.providerType == .userApiKey, apiKeyStored else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let apiKey = try await getProviderAPIKeyUseCase.execute(providerId: providerId)
            retrievedApiKey = apiKey
            showFullApiKey = true
        } catch {
            // Don't show an error if the user canceled authentication
            if case KeychainError.interactionNotAllowed = error {
                // This is expected when user cancels
            } else {
                handleError(error)
            }
        }
        
        isLoading = false
    }

    /// Prepares the view for editing
    @MainActor
    func prepareForEditing() async {
        if isCreatingNewProvider {
            self.apiKeyInput = ""
            self.showFullApiKey = false
            self.isEditing = true
            return
        }

        guard let providerId = self.providerId, editableProvider.providerType == .userApiKey else {
            self.isEditing = true // Allow editing other fields even if not userApiKey type
            return
        }

        self.isLoading = true
        self.errorMessage = nil
        self.showFullApiKey = false // Reset view mode flag

        if self.apiKeyStored {
            do {
                let fetchedApiKey = try await getProviderAPIKeyUseCase.execute(providerId: providerId)
                self.apiKeyInput = fetchedApiKey
            } catch {
                self.apiKeyInput = ""
                // Don't show an error if the user canceled authentication
                if case KeychainError.interactionNotAllowed = error {
                    // User cancelled biometric/passcode, which is fine. Input will be empty.
                } else {
                    handleError(error)
                }
            }
        } else {
            self.apiKeyInput = ""
        }

        self.isEditing = true
        self.isLoading = false
    }
    
    /// Cancels editing and resets the editable provider
    @MainActor
    func cancelEditing() {
        if let provider = provider {
            // Reset to the original provider
            editableProvider = provider
            self.apiKeyStored = provider.apiKeyStored
        }
        
        // Reset API key state
        apiKeyInput = ""
        retrievedApiKey = nil
        showFullApiKey = false
        errorMessage = nil
        isEditing = false
    }
    
    /// Opens a URL in the default browser
    func handleUrlOpening(url: URL) {
        UIApplication.shared.open(url)
    }
    
    // MARK: - Private Methods
    
    /// Validates input before saving
    @MainActor
    private func validateInput() -> Bool {
        // Name must not be empty
        if editableProvider.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errorMessage = "Provider name cannot be empty"
            return false
        }
        
        // API style specific validation
        if editableProvider.apiStyle == .openaiCompatible {
            // API base URL validation for OpenAI compatible providers
            if let apiBaseUrl = editableProvider.apiBaseUrl, !apiBaseUrl.isEmpty {
                if !apiBaseUrl.starts(with: "http://") && !apiBaseUrl.starts(with: "https://") {
                    errorMessage = "API Base URL must start with http:// or https://"
                    return false
                }
            }
        }
        
        return true
    }
    
    /// Handles errors and updates the errorMessage
    @MainActor
    private func handleError(_ error: Error) {
        if let modelError = error as? ModelManagementError {
            switch modelError {
            case .providerNotFound:
                errorMessage = "Provider not found"
            case .cannotDeleteBuiltInProvider:
                errorMessage = "Cannot delete a built-in provider"
            case .providerDoesNotRequireApiKey:
                errorMessage = "This provider does not require an API key"
            case .providerHasActiveInstances:
                errorMessage = "Cannot delete provider with active instances"
            case .apiKeyNotStored:
                errorMessage = "API key not found"
            case .cannotCreateProviderDoesNotRequireApiKey:
                errorMessage = "Cannot create a provider without an API key"
            case .emptyApiKey:
                errorMessage = "API key cannot be empty"
            case .persistenceError(let underlyingError):
                errorMessage = "Database error: \(underlyingError.localizedDescription)"
            default:
                errorMessage = "An unexpected error occurred: \(modelError)"
            }
        } else if let keychainError = error as? KeychainError {
            switch keychainError {
            case .itemNotFound:
                errorMessage = "API key not found in keychain"
            case .duplicateItem:
                errorMessage = "API key already exists"
            case .invalidData:
                errorMessage = "Invalid API key data"
            case .interactionNotAllowed:
                // Don't show an error for canceled authentication
                return
            case .keychainNotAvailable:
                errorMessage = "Keychain is not available"
            case .unexpectedStatus(let status):
                errorMessage = "Keychain error: \(status)"
            }
        } else {
            errorMessage = "An error occurred: \(error.localizedDescription)"
        }
        
        print("ProviderDetailViewModel error: \(error)")
    }
}
