import Foundation
import SwiftUI
import Combine

/// Generic ViewModel for handling sharing functionality across different ShareableItem types
final class ShareViewModel<T: ShareableItem>: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Dependencies
    
    private let item: T
    private let shareInstanceUseCase: ShareInstanceUseCaseProtocol?
    private let shareChatSessionUseCase: ShareChatSessionUseCaseProtocol?
    private let shareMessageActionUseCase: ShareMessageActionUseCaseProtocol?
    private let shareChatSessionSettingUseCase: ShareChatSessionSettingUseCaseProtocol?
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(
        item: T,
        shareInstanceUseCase: ShareInstanceUseCaseProtocol? = nil,
        shareChatSessionUseCase: ShareChatSessionUseCaseProtocol? = nil,
        shareMessageActionUseCase: ShareMessageActionUseCaseProtocol? = nil,
        shareChatSessionSettingUseCase: ShareChatSessionSettingUseCaseProtocol? = nil
    ) {
        self.item = item
        self.shareInstanceUseCase = shareInstanceUseCase
        self.shareChatSessionUseCase = shareChatSessionUseCase
        self.shareMessageActionUseCase = shareMessageActionUseCase
        self.shareChatSessionSettingUseCase = shareChatSessionSettingUseCase
    }
    
    // MARK: - Public Methods
    
    /// Start the sharing process by resetting state
    @MainActor
    func startSharing() {
        resetState()
    }
    
    /// Generate share content on-demand (called when ShareLink is tapped)
    func generateShareContent(for format: ShareFormat) async throws -> Any {
        switch format {
        case .file:
            return try await shareAsFile()
            
        case .icloud:
            return try await shareViaICloud()
            
        case .qrCode:
            return try await generateQRCode()
        }
    }
    
    /// Reset sharing state
    @MainActor
    func resetState() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    
    private func shareAsFile() async throws -> URL {
        switch item.shareContentType {
        case .llmInstance:
            guard let useCase = shareInstanceUseCase,
                  let instance = item as? LLMInstance else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareAsFile(instance, fileName: nil)

        case .chatSession:
            guard let useCase = shareChatSessionUseCase,
                  let session = item as? ChatSession else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareAsFile(session, fileName: nil)

        case .messageAction:
            guard let useCase = shareMessageActionUseCase,
                  let action = item as? MessageAction else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareAsFile(action, fileName: nil)

        case .chatSessionSetting:
            guard let useCase = shareChatSessionSettingUseCase,
                  let setting = item as? ChatSessionSetting else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareAsFile(setting, fileName: nil)
        }
    }
    
    private func shareViaICloud() async throws -> URL {
        let permissions = ICloudSharePermissions()

        switch item.shareContentType {
        case .llmInstance:
            guard let useCase = shareInstanceUseCase,
                  let instance = item as? LLMInstance else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareViaICloud(instance, permissions: permissions)

        case .chatSession:
            guard let useCase = shareChatSessionUseCase,
                  let session = item as? ChatSession else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareViaICloud(session, permissions: permissions)

        case .messageAction:
            guard let useCase = shareMessageActionUseCase,
                  let action = item as? MessageAction else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareViaICloud(action, permissions: permissions)

        case .chatSessionSetting:
            guard let useCase = shareChatSessionSettingUseCase,
                  let setting = item as? ChatSessionSetting else {
                throw ShareError.invalidContent
            }
            return try await useCase.shareViaICloud(setting, permissions: permissions)
        }
    }
    
    private func generateQRCode() async throws -> Data {
        let size = CGSize(width: 300, height: 300)

        switch item.shareContentType {
        case .llmInstance:
            guard let useCase = shareInstanceUseCase,
                  let instance = item as? LLMInstance else {
                throw ShareError.invalidContent
            }
            return try await useCase.generateQRCode(for: instance, size: size)

        case .chatSession:
            guard let useCase = shareChatSessionUseCase,
                  let session = item as? ChatSession else {
                throw ShareError.invalidContent
            }
            return try await useCase.generateQRCode(for: session, size: size)

        case .messageAction:
            guard let useCase = shareMessageActionUseCase,
                  let action = item as? MessageAction else {
                throw ShareError.invalidContent
            }
            return try await useCase.generateQRCode(for: action, size: size)

        case .chatSessionSetting:
            guard let useCase = shareChatSessionSettingUseCase,
                  let setting = item as? ChatSessionSetting else {
                throw ShareError.invalidContent
            }
            return try await useCase.generateQRCode(for: setting, size: size)
        }
    }
    
    @MainActor
    private func handleError(_ error: Error) {
        if let shareError = error as? ShareError {
            errorMessage = shareError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
    }
}
