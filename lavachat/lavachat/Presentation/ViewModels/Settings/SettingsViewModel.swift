import Foundation
import Combine
import SwiftUI

final class SettingsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var errorMessage: String? = nil
    @Published var toolbarVisibility: Visibility = .visible

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        // No dependencies needed for basic settings navigation
    }

    // MARK: - Toolbar Management

    /// Hide toolbar with animation
    @MainActor
    func hideToolbarWithAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            toolbarVisibility = .hidden
        }
    }

    /// Show toolbar with animation
    @MainActor
    func showToolbarWithAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            toolbarVisibility = .visible
        }
    }

    // MARK: - Error Handling

    @MainActor
    func clearError() {
        errorMessage = nil
    }
}
