import Foundation
import Combine

final class MyActionsViewModel: ObservableObject {

    // MARK: - Supporting Types

    struct ActionStatistics {
        let totalCustomActions: Int
        let inputActions: Int
        let userMessageActions: Int
        let assistantMessageActions: Int
    }

    struct ActionCategoryDisplayInfo {
        let title: String
        let icon: String
        let color: String
        let count: Int
        let description: String

        static func inputActions(count: Int) -> ActionCategoryDisplayInfo {
            return ActionCategoryDisplayInfo(
                title: "Input Actions",
                icon: "plus.circle.fill",
                color: "blue",
                count: count,
                description: "Actions available in the input panel"
            )
        }

        static func userMessageActions(count: Int) -> ActionCategoryDisplayInfo {
            return ActionCategoryDisplayInfo(
                title: "User Message Actions",
                icon: "person.circle.fill",
                color: "green",
                count: count,
                description: "Actions for user messages"
            )
        }

        static func assistantMessageActions(count: Int) -> ActionCategoryDisplayInfo {
            return ActionCategoryDisplayInfo(
                title: "Assistant Message Actions",
                icon: "sparkles.rectangle.stack.fill",
                color: "purple",
                count: count,
                description: "Actions for assistant messages"
            )
        }
    }
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var actionStatistics: ActionStatistics = ActionStatistics(totalCustomActions: 0, inputActions: 0, userMessageActions: 0, assistantMessageActions: 0)

    // MARK: - Navigation State
    @Published var showInputActions: Bool = false
    @Published var showUserMessageActions: Bool = false
    @Published var showAssistantMessageActions: Bool = false
    
    // MARK: - Dependencies
    private let getAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(getAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol) {
        self.getAllMessageActionsUseCase = getAllMessageActionsUseCase
    }
    
    // MARK: - Public Methods
    
    /// Load action statistics
    @MainActor
    func loadActionStatistics() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let allActions = try await getAllMessageActionsUseCase.execute()
            let customActions = allActions.filter { action in
                if case .system = action.actionType {
                    return false
                }
                return true
            }
            
            actionStatistics = ActionStatistics(
                totalCustomActions: customActions.count,
                inputActions: customActions.filter { $0.suitableForActionPanel }.count,
                userMessageActions: customActions.filter { $0.suitableForUserMessage }.count,
                assistantMessageActions: customActions.filter { $0.suitableForAssistantMessage }.count
            )
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Navigate to Input Actions management
    @MainActor
    func navigateToInputActions() {
        showInputActions = true
    }
    
    /// Navigate to User Message Actions management
    @MainActor
    func navigateToUserMessageActions() {
        showUserMessageActions = true
    }
    
    /// Navigate to Assistant Message Actions management
    @MainActor
    func navigateToAssistantMessageActions() {
        showAssistantMessageActions = true
    }
    
    // MARK: - Error Handling
    
    @MainActor
    private func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        isLoading = false
    }
    
    @MainActor
    func clearError() {
        errorMessage = nil
    }
}


