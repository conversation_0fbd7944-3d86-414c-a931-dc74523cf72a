import Foundation
import Combine

final class ActionManagementViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var allActions: [MessageAction] = []
    @Published var systemActions: [MessageAction] = []
    @Published var customActions: [MessageAction] = []

    // MARK: - Navigation State
    @Published var showActionEditor: Bool = false
    @Published var editingAction: MessageAction? = nil
    
    // MARK: - Dependencies
    private let category: MessageActionCategory
    private let getAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        category: MessageActionCategory,
        getAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol
    ) {
        self.category = category
        self.getAllMessageActionsUseCase = getAllMessageActionsUseCase
    }
    
    // MARK: - Public Methods
    
    /// Load all actions for the category
    @MainActor
    func loadActions() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let allAvailableActions = try await getAllMessageActionsUseCase.executeIncludingSystemActions()
            
            // Filter actions suitable for this category
            let suitableActions = allAvailableActions.filter(category.suitabilityCheck)
            
            // Separate system and custom actions
            let systemActionsFiltered = suitableActions.filter { action in
                if case .system = action.actionType {
                    return true
                }
                return false
            }
            
            let customActionsFiltered = suitableActions.filter { action in
                if case .system = action.actionType {
                    return false
                }
                return true
            }
            
            allActions = suitableActions
            systemActions = systemActionsFiltered
            customActions = customActionsFiltered
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// Create a new action
    @MainActor
    func createNewAction() {
        editingAction = nil
        showActionEditor = true
    }
    
    /// Edit an existing action
    @MainActor
    func editAction(_ action: MessageAction) {
        // Only allow editing custom actions
        if case .system = action.actionType {
            errorMessage = "System actions cannot be edited"
            return
        }
        
        editingAction = action
        showActionEditor = true
    }
    

    
    /// Handle action editor completion
    @MainActor
    func onActionEditorComplete() {
        showActionEditor = false
        editingAction = nil
        
        // Reload actions to reflect changes
        Task {
            await loadActions()
        }
    }
    
    // MARK: - Computed Properties
    
    var categoryTitle: String {
        return category.rawValue
    }
    
    var categoryDescription: String {
        switch category {
        case .actionPanel:
            return "Actions available in the input panel for adding content and functionality."
        case .userMessage:
            return "Actions available for user messages, such as copy and edit."
        case .assistantCard:
            return "Actions displayed on assistant message cards."
        case .assistantMenu:
            return "Actions available in assistant message context menus."
        }
    }
    
    var hasCustomActions: Bool {
        return !customActions.isEmpty
    }
    
    var hasSystemActions: Bool {
        return !systemActions.isEmpty
    }
    
    // MARK: - Error Handling
    
    @MainActor
    private func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        isLoading = false
    }
    
    @MainActor
    func clearError() {
        errorMessage = nil
    }
}
