import Foundation
import Combine
import SwiftUI

final class ChatSettingEditorViewModel: ObservableObject {
    
    // MARK: - Published Properties

    @Published var name: String = ""
    @Published var shouldExpandThinking: Bool = true
    @Published var shouldAutoGenerateTitle: Bool = false
    @Published var contextMessageCount: Int64 = Int64.max
    @Published var auxiliaryLLMInstanceId: UUID?

    // Message Action Settings
    @Published var messageActionSettings: MessageActionSettings?
    @Published var resolvedMessageActionSettings: ResolvedMessageActionSettings?

    // Auxiliary Instance Details
    @Published var auxiliaryLLMInstance: LLMInstance?

    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var isSaveEnabled: Bool = false
    @Published var showDeleteConfirmation: Bool = false
    
    // MARK: - Dependencies

    private let editingSetting: ChatSessionSetting?
    private let createChatSessionSettingUseCase: CreateChatSessionSettingUseCaseProtocol
    private let updateChatSessionSettingUseCase: UpdateChatSessionSettingUseCaseProtocol
    private let deleteChatSessionSettingUseCase: DeleteChatSessionSettingUseCaseProtocol
    private let getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol
    private let getAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol
    private let getInstanceUseCase: GetInstanceUseCaseProtocol
    private let getCurrentUserUseCase: GetCurrentUserUseCaseProtocol

    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()
    @Published private var currentUser: User?
    
    // MARK: - Computed Properties
    
    var isEditing: Bool {
        return editingSetting != nil
    }
    
    var navigationTitle: String {
        return isEditing ? "Edit Setting" : "New Setting"
    }
    
    var auxiliaryLLMInstanceDisplayText: String {
        if let instance = auxiliaryLLMInstance {
            return instance.name
        } else {
            return "None"
        }
    }
    
    var contextMessageCountDisplayText: String {
        if contextMessageCount == Int64.max {
            return "Unlimited"
        } else {
            return "\(contextMessageCount)"
        }
    }
    
    var shouldAutoGenerateTitleEnabled: Bool {
        return auxiliaryLLMInstanceId != nil
    }

    /// Whether the current setting can be deleted
    var canDelete: Bool {
        guard let setting = editingSetting else { return false }

        // Cannot delete system default settings
        if setting.isSystemDefault {
            return false
        }

        // Cannot delete the current user's default setting
        if let userDefaultId = currentUser?.defaultChatSettingsId,
           setting.id == userDefaultId {
            return false
        }

        return true
    }

    /// Whether to show the delete section in the UI
    var shouldShowDeleteSection: Bool {
        return editingSetting != nil && canDelete
    }
    
    // MARK: - Initialization
    
    init(
        editingSetting: ChatSessionSetting?,
        createChatSessionSettingUseCase: CreateChatSessionSettingUseCaseProtocol,
        updateChatSessionSettingUseCase: UpdateChatSessionSettingUseCaseProtocol,
        deleteChatSessionSettingUseCase: DeleteChatSessionSettingUseCaseProtocol,
        getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol,
        getAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol,
        getInstanceUseCase: GetInstanceUseCaseProtocol,
        getCurrentUserUseCase: GetCurrentUserUseCaseProtocol
    ) {
        self.editingSetting = editingSetting
        self.createChatSessionSettingUseCase = createChatSessionSettingUseCase
        self.updateChatSessionSettingUseCase = updateChatSessionSettingUseCase
        self.deleteChatSessionSettingUseCase = deleteChatSessionSettingUseCase
        self.getChatSessionSettingUseCase = getChatSessionSettingUseCase
        self.getAllMessageActionsUseCase = getAllMessageActionsUseCase
        self.getInstanceUseCase = getInstanceUseCase
        self.getCurrentUserUseCase = getCurrentUserUseCase

        setupValidation()
        loadInitialData()
    }
    
    // MARK: - Private Methods
    
    private func setupValidation() {
        $name
            .map { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            .assign(to: &$isSaveEnabled)
    }
    
    private func loadInitialData() {
        // Load current user for delete permission check
        Task {
            await loadCurrentUser()
        }

        if let setting = editingSetting {
            name = setting.name
            shouldExpandThinking = setting.shouldExpandThinking
            shouldAutoGenerateTitle = setting.shouldAutoGenerateTitle
            contextMessageCount = setting.contextMessageCount
            auxiliaryLLMInstanceId = setting.auxiliaryLLMInstanceId
            messageActionSettings = setting.messageActionSettings

            // Load auxiliary instance and resolved message actions
            Task {
                await loadAuxiliaryInstanceAndMessageActions()
            }
        } else {
            // For new settings, set default message action settings
            messageActionSettings = MessageActionSettings(
                actionPanelActions: [
                    SystemMessageActions.camera.id,
                    SystemMessageActions.photo.id,
                    SystemMessageActions.file.id,
                ],
                userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.edit.id],
                assistantMessageCardActions: [
                    SystemMessageActions.copy.id,
                    SystemMessageActions.regenerate.id,
                    SystemMessageActions.like.id,
                    SystemMessageActions.dislike.id,
                ],
                assistantMessageMenuActions: [
                    SystemMessageActions.copy.id,
                    SystemMessageActions.regenerate.id,
                    SystemMessageActions.like.id,
                    SystemMessageActions.dislike.id,
                    SystemMessageActions.select.id,
                ]
            )

            // Load resolved message actions for new settings
            Task {
                await loadResolvedMessageActions()
            }
        }

        // Setup validation
        setupValidation()
    }

    // MARK: - Public Methods

    @MainActor
    func updateAuxiliaryLLMInstance(_ instanceId: UUID?) async {
        auxiliaryLLMInstanceId = instanceId

        // Disable auto-generate title if no auxiliary instance
        if instanceId == nil {
            shouldAutoGenerateTitle = false
            auxiliaryLLMInstance = nil
        } else if let instanceId = instanceId {
            // Load the auxiliary instance details
            do {
                auxiliaryLLMInstance = try await getInstanceUseCase.execute(id: instanceId)
            } catch {
                errorMessage = "Failed to load auxiliary instance: \(error.localizedDescription)"
            }
        }
    }

    @MainActor
    func updateContextMessageCount(_ count: Int64) async {
        contextMessageCount = count
    }

    @MainActor
    func save() async -> ChatSessionSetting? {
        guard isSaveEnabled else { return nil }

        isLoading = true
        errorMessage = nil

        do {
            let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)

            if let existingSetting = editingSetting {
                // Update existing setting
                var updatedSetting = existingSetting
                updatedSetting.name = trimmedName
                updatedSetting.shouldExpandThinking = shouldExpandThinking
                updatedSetting.shouldAutoGenerateTitle = shouldAutoGenerateTitle
                updatedSetting.contextMessageCount = contextMessageCount
                updatedSetting.auxiliaryLLMInstanceId = auxiliaryLLMInstanceId
                updatedSetting.messageActionSettings = messageActionSettings

                let result = try await updateChatSessionSettingUseCase.execute(updatedSetting)
                isLoading = false
                return result
            } else {
                // Create new setting
                let newSetting = ChatSessionSetting(
                    id: UUID(),
                    name: trimmedName,
                    isSystemDefault: false,
                    shouldExpandThinking: shouldExpandThinking,
                    messageActionSettings: messageActionSettings,
                    auxiliaryLLMInstanceId: auxiliaryLLMInstanceId,
                    shouldAutoGenerateTitle: shouldAutoGenerateTitle,
                    contextMessageCount: contextMessageCount
                )

                let result = try await createChatSessionSettingUseCase.execute(newSetting)
                isLoading = false
                return result
            }
        } catch {
            errorMessage = "Failed to save setting: \(error.localizedDescription)"
            isLoading = false
            return nil
        }
    }

    @MainActor
    func clearError() {
        errorMessage = nil
    }

    /// Show delete confirmation dialog
    @MainActor
    func requestDeleteConfirmation() {
        guard canDelete else { return }
        showDeleteConfirmation = true
    }

    /// Delete the current setting
    @MainActor
    func deleteSetting() async -> Bool {
        guard let setting = editingSetting, canDelete else { return false }

        isLoading = true
        errorMessage = nil

        do {
            try await deleteChatSessionSettingUseCase.execute(settingId: setting.id)
            isLoading = false
            return true
        } catch {
            errorMessage = "Failed to delete setting: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }

    // MARK: - Message Action Settings Management

    @MainActor
    func updateMessageActionSettings(_ newSettings: MessageActionSettings) async {
        messageActionSettings = newSettings

        // Reload resolved actions
        await loadResolvedMessageActions()
    }

    // MARK: - Private Helper Methods

    @MainActor
    private func loadAuxiliaryInstanceAndMessageActions() async {
        // Load auxiliary instance if set
        if let auxiliaryId = auxiliaryLLMInstanceId {
            do {
                auxiliaryLLMInstance = try await getInstanceUseCase.execute(id: auxiliaryId)
            } catch {
                print("Failed to load auxiliary instance: \(error)")
            }
        }

        // Load resolved message actions
        await loadResolvedMessageActions()
    }

    @MainActor
    private func loadResolvedMessageActions() async {
        guard let actionSettings = messageActionSettings else {
            resolvedMessageActionSettings = ResolvedMessageActionSettings()
            return
        }

        do {
            // Fetch all custom actions by their IDs
            let customActions = try await getAllMessageActionsUseCase.execute()
            let customActionsDict = Dictionary(uniqueKeysWithValues: customActions.map { ($0.id, $0) })

            // Resolve system and custom actions
            let resolveActions = { (actionIDs: [UUID]?) -> [MessageAction] in
                guard let actionIDs = actionIDs else { return [] }
                return actionIDs.compactMap { id in
                    if let systemAction = SystemMessageActions.actions.first(where: { $0.id == id }) {
                        return systemAction
                    } else if let customAction = customActionsDict[id] {
                        return customAction
                    } else {
                        // Handle case where action is not found (e.g., deleted custom action)
                        print("Warning: MessageAction with ID \(id.uuidString) not found.")
                        return nil
                    }
                }
            }

            let resolvedActionPanelActions = resolveActions(actionSettings.actionPanelActions)
            let resolvedUserActions = resolveActions(actionSettings.userMessageActions)
            let resolvedAssistantCardActions = resolveActions(actionSettings.assistantMessageCardActions)
            let resolvedAssistantMenuActions = resolveActions(actionSettings.assistantMessageMenuActions)

            resolvedMessageActionSettings = ResolvedMessageActionSettings(
                actionPanelActions: resolvedActionPanelActions,
                userMessageActions: resolvedUserActions,
                assistantMessageCardActions: resolvedAssistantCardActions,
                assistantMessageMenuActions: resolvedAssistantMenuActions
            )
        } catch {
            print("Failed to load resolved message actions: \(error)")
            resolvedMessageActionSettings = ResolvedMessageActionSettings()
        }
    }

    @MainActor
    private func loadCurrentUser() async {
        do {
            currentUser = try await getCurrentUserUseCase.execute()
        } catch {
            print("Failed to load current user: \(error)")
        }
    }
}
