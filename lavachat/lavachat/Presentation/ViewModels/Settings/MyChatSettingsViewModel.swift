import Foundation
import Combine
import SwiftUI

final class MyChatSettingsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var allSettings: [ChatSessionSetting] = []
    @Published var currentUser: User?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // MARK: - Navigation States
    
    @Published var showCreateNewSetting: Bool = false
    @Published var showEditSetting: Bool = false
    @Published var editingSetting: ChatSessionSetting?
    
    // MARK: - Dependencies
    
    private let getAllChatSessionSettingsUseCase: GetAllChatSessionSettingsUseCaseProtocol
    private let getCurrentUserUseCase: GetCurrentUserUseCaseProtocol
    private let updateUserDefaultChatSettingUseCase: UpdateUserDefaultChatSettingUseCaseProtocol
    private let getDefaultChatSessionSettingUseCase: GetDefaultChatSessionSettingUseCaseProtocol
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(
        getAllChatSessionSettingsUseCase: GetAllChatSessionSettingsUseCaseProtocol,
        getCurrentUserUseCase: GetCurrentUserUseCaseProtocol,
        updateUserDefaultChatSettingUseCase: UpdateUserDefaultChatSettingUseCaseProtocol,
        getDefaultChatSessionSettingUseCase: GetDefaultChatSessionSettingUseCaseProtocol
    ) {
        self.getAllChatSessionSettingsUseCase = getAllChatSessionSettingsUseCase
        self.getCurrentUserUseCase = getCurrentUserUseCase
        self.updateUserDefaultChatSettingUseCase = updateUserDefaultChatSettingUseCase
        self.getDefaultChatSessionSettingUseCase = getDefaultChatSessionSettingUseCase
    }
    
    // MARK: - Computed Properties
    
    /// Get the current default setting ID (user's default or system default)
    var currentDefaultSettingId: UUID? {
        if let userDefaultId = currentUser?.defaultChatSettingsId {
            return userDefaultId
        }
        // Fallback to system default
        return allSettings.first(where: { $0.isSystemDefault })?.id
    }
    
    /// Get the current default setting name for display
    var currentDefaultSettingName: String {
        guard let defaultId = currentDefaultSettingId,
              let defaultSetting = allSettings.first(where: { $0.id == defaultId }) else {
            return "System Default"
        }
        return defaultSetting.name
    }
    
    // MARK: - Public Methods
    
    /// Load all settings and current user
    @MainActor
    func loadData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load settings and user concurrently
            async let settingsResult = getAllChatSessionSettingsUseCase.execute()
            async let userResult = getCurrentUserUseCase.execute()
            
            allSettings = try await settingsResult
            currentUser = try await userResult
            
        } catch {
            errorMessage = "Failed to load chat settings: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// Set a setting as the default
    @MainActor
    func setDefaultSetting(_ settingId: UUID) async {
        guard allSettings.contains(where: { $0.id == settingId }) else {
            errorMessage = "Setting not found"
            return
        }
        
        do {
            let updatedUser = try await updateUserDefaultChatSettingUseCase.execute(settingId: settingId)
            currentUser = updatedUser
        } catch {
            errorMessage = "Failed to update default setting: \(error.localizedDescription)"
        }
    }
    
    /// Check if a setting is currently the default
    func isDefaultSetting(_ settingId: UUID) -> Bool {
        return currentDefaultSettingId == settingId
    }
    
    /// Clear error message
    @MainActor
    func clearError() {
        errorMessage = nil
    }
    
    /// Refresh data
    @MainActor
    func refresh() async {
        await loadData()
    }
}
