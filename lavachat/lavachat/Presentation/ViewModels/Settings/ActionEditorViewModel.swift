import Foundation
import Combine
import UIKit

final class ActionEditorViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var isSaving: Bool = false
    
    // MARK: - Form Fields
    @Published var name: String = ""
    @Published var icon: String = ""
    @Published var selectedActionType: ActionTypeOption = .promptInsert
    @Published var prompts: [String] = [""]

    // MARK: - UI State
    @Published var iconPickerPresented: Bool = false
    @Published var fullScreenTextEditorPresented: Bool = false
    @Published var editingPromptIndex: Int? = nil
    @Published var editingPromptText: String = ""

    // MARK: - Validation
    @Published var nameError: String? = nil
    @Published var iconError: String? = nil
    @Published var promptsError: String? = nil

    // MARK: - Delete State
    @Published var showDeleteConfirmation: Bool = false
    @Published var usingSettings: [ChatSessionSetting] = []
    @Published var isLoadingUsage: Bool = false
    
    // MARK: - Dependencies
    private let category: MessageActionCategory
    private let editingAction: MessageAction?
    private let createMessageActionUseCase: CreateMessageActionUseCaseProtocol
    private let updateMessageActionUseCase: UpdateMessageActionUseCaseProtocol
    private let deleteMessageActionUseCase: DeleteMessageActionUseCaseProtocol
    private let getMessageActionUsageUseCase: GetMessageActionUsageUseCaseProtocol
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        category: MessageActionCategory,
        editingAction: MessageAction?,
        createMessageActionUseCase: CreateMessageActionUseCaseProtocol,
        updateMessageActionUseCase: UpdateMessageActionUseCaseProtocol,
        deleteMessageActionUseCase: DeleteMessageActionUseCaseProtocol,
        getMessageActionUsageUseCase: GetMessageActionUsageUseCaseProtocol
    ) {
        self.category = category
        self.editingAction = editingAction
        self.createMessageActionUseCase = createMessageActionUseCase
        self.updateMessageActionUseCase = updateMessageActionUseCase
        self.deleteMessageActionUseCase = deleteMessageActionUseCase
        self.getMessageActionUsageUseCase = getMessageActionUsageUseCase

        setupInitialValues()
        setupValidation()

        // Load usage information for existing actions
        if editingAction != nil {
            Task {
                await loadUsageInformation()
            }
        }
    }
    
    // MARK: - Public Methods
    
    /// Save the action (create or update)
    @MainActor
    func saveAction() async -> MessageAction? {
        guard validateForm() else { return nil }

        isSaving = true
        errorMessage = nil

        do {
            let actionType = selectedActionType.toMessageActionType()
            let action = MessageAction(
                id: editingAction?.id ?? UUID(),
                name: name.trimmingCharacters(in: .whitespacesAndNewlines),
                icon: icon.isEmpty ? nil : icon,
                actionType: actionType,
                prompts: prompts.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty },
                targetLLMInstanceId: nil
            )

            let savedAction: MessageAction
            if editingAction != nil {
                savedAction = try await updateMessageActionUseCase.execute(action)
            } else {
                savedAction = try await createMessageActionUseCase.execute(action)
            }

            isSaving = false
            return savedAction

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isSaving = false
            }
            return nil
        }
    }
    
    /// Add a new prompt field
    @MainActor
    func addPrompt() {
        prompts.append("")
    }
    
    /// Remove a prompt at the given index
    @MainActor
    func removePrompt(at index: Int) {
        guard index < prompts.count && prompts.count > 1 else { return }
        prompts.remove(at: index)
    }
    
    /// Update prompt at index
    @MainActor
    func updatePrompt(at index: Int, with text: String) {
        guard index < prompts.count else { return }
        prompts[index] = text
    }

    /// Move prompt from one index to another
    @MainActor
    func movePrompt(from source: IndexSet, to destination: Int) {
        prompts.move(fromOffsets: source, toOffset: destination)
    }

    /// Present full screen text editor for prompt
    @MainActor
    func presentFullScreenEditor(for index: Int) {
        guard index < prompts.count else { return }
        editingPromptIndex = index
        editingPromptText = prompts[index]
        fullScreenTextEditorPresented = true
    }

    /// Save text from full screen editor
    @MainActor
    func saveFullScreenEditorText(with newText: String) {
        guard let index = editingPromptIndex else { return }
        updatePrompt(at: index, with: newText)
        editingPromptIndex = nil
        editingPromptText = ""
        fullScreenTextEditorPresented = false
    }

    /// Cancel full screen editor
    @MainActor
    func cancelFullScreenEditor() {
        editingPromptIndex = nil
        editingPromptText = ""
        fullScreenTextEditorPresented = false
    }

    /// Present icon picker
    @MainActor
    func presentIconPicker() {
        iconPickerPresented = true
    }

    /// Clear selected icon
    @MainActor
    func clearIcon() {
        icon = ""
    }
    
    // MARK: - Computed Properties
    
    var isEditing: Bool {
        return editingAction != nil
    }

    var title: String {
        return isEditing ? "Edit Action" : "New Action"
    }

    var currentEditingAction: MessageAction? {
        return editingAction
    }
    
    var availableActionTypes: [ActionTypeOption] {
        switch category {
        case .actionPanel:
            return [.promptInsert, .promptRewrite]
        case .userMessage, .assistantCard, .assistantMenu:
            return [.assistantRegenerate]
        }
    }
    
    var showPromptsSection: Bool {
        return selectedActionType.requiresPrompts
    }
    
    var canSave: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !isSaving &&
               nameError == nil &&
               iconError == nil &&
               promptsError == nil &&
               (showPromptsSection ? hasValidPrompts : true)
    }
    
    private var hasValidPrompts: Bool {
        return prompts.contains { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
    }

    var shouldShowDeleteSection: Bool {
        return isEditing
    }

    var canDelete: Bool {
        guard isEditing else { return false }

        // Cannot delete system actions
        if let action = editingAction, case .system = action.actionType {
            return false
        }

        // Cannot delete if currently being used by any settings
        return usingSettings.isEmpty
    }

    var deleteButtonText: String {
        return canDelete ? "Delete Action" : "Delete Action"
    }

    var deleteFooterText: String {
        if let action = editingAction, case .system = action.actionType {
            return "System actions cannot be deleted."
        } else if !usingSettings.isEmpty {
            let settingNames = usingSettings.map { "\($0.name)" }.joined(separator: ", ")
            return "Cannot delete this action. This action is currently used by: \(settingNames)"
        } else if canDelete {
            return "This action cannot be undone."
        } else {
            return ""
        }
    }
    
    // MARK: - Private Methods
    
    private func setupInitialValues() {
        if let action = editingAction {
            // Editing existing action
            name = action.name
            icon = action.icon ?? ""
            prompts = action.prompts.isEmpty ? [""] : action.prompts

            // Set action type based on the action
            switch action.actionType {
            case .assistantRegenerate:
                selectedActionType = .assistantRegenerate
            case .actionPanelPromptInsert:
                selectedActionType = .promptInsert
            case .actionPanelPromptRewrite:
                selectedActionType = .promptRewrite
            case .system:
                // Should not happen for custom actions
                selectedActionType = .promptInsert
            }
        } else {
            // Creating new action - set default action type based on category
            switch category {
            case .actionPanel:
                selectedActionType = .promptInsert
            case .userMessage, .assistantCard, .assistantMenu:
                selectedActionType = .assistantRegenerate
            }
        }
    }
    
    private func setupValidation() {
        // Name validation
        $name
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] name in
                self?.validateName(name)
            }
            .store(in: &cancellables)

        // Icon validation
        $icon
            .sink { [weak self] icon in
                self?.validateIcon(icon)
            }
            .store(in: &cancellables)

        // Prompts validation with debounce for text input
        $prompts
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] prompts in
                self?.validatePrompts(prompts)
            }
            .store(in: &cancellables)

        // Action type validation - immediate validation when type changes
        $selectedActionType
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                    self.validatePrompts(self.prompts)
            }
            .store(in: &cancellables)
    }
    
    private func validateName(_ name: String) {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            nameError = "Name is required"
        } else if trimmedName.count < 2 {
            nameError = "Name must be at least 2 characters"
        } else if trimmedName.count > 50 {
            nameError = "Name must be less than 50 characters"
        } else {
            nameError = nil
        }
    }

    private func validateIcon(_ icon: String) {
        let trimmedIcon = icon.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedIcon.isEmpty {
            iconError = "Icon is required"
        } else {
            // Validate if it's a valid SF Symbol name
            if UIImage(systemName: trimmedIcon) == nil {
                iconError = "Invalid SF Symbol name"
            } else {
                iconError = nil
            }
        }
    }
    
    private func validatePrompts(_ prompts: [String]) {
        if showPromptsSection {
            let validPrompts = prompts.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            if validPrompts.isEmpty {
                promptsError = "At least one prompt is required"
                return
            }

            // Special validation for Prompt Rewrite type
            if selectedActionType == .promptRewrite {
                var missingElements: [String] = []

                for prompt in validPrompts {
                    let hasRewrittenPromptTag = prompt.contains("<rewritten_prompt>")
                    let hasUserInputPlaceholder = prompt.contains("{{user_original_input}}")
                    
                    if !hasRewrittenPromptTag {
                        missingElements.append("<rewritten_prompt>")
                    }
                    if !hasUserInputPlaceholder {
                        missingElements.append("{{user_original_input}}")
                    }

                    if !missingElements.isEmpty {
                        let uniqueMissing = Array(Set(missingElements))
                        promptsError = "Missing required elements: \(uniqueMissing.joined(separator: ", "))"
                        return
                    }
                }
            }

            promptsError = nil
        } else {
            promptsError = nil
        }
    }
    
    private func validateForm() -> Bool {
        validateName(name)
        validateIcon(icon)
        validatePrompts(prompts)

        return nameError == nil && iconError == nil && promptsError == nil
    }
    
    // MARK: - Delete Methods

    /// Load usage information for the current action
    @MainActor
    func loadUsageInformation() async {
        guard let action = editingAction else { return }

        isLoadingUsage = true

        do {
            usingSettings = try await getMessageActionUsageUseCase.execute(actionId: action.id)
        } catch {
            print("Failed to load usage information: \(error)")
            usingSettings = []
        }

        isLoadingUsage = false
    }

    /// Show delete confirmation dialog
    @MainActor
    func requestDeleteConfirmation() {
        guard canDelete else { return }
        showDeleteConfirmation = true
    }

    /// Delete the current action
    @MainActor
    func deleteAction() async -> Bool {
        guard let action = editingAction, canDelete else { return false }

        isSaving = true
        errorMessage = nil

        do {
            try await deleteMessageActionUseCase.execute(actionId: action.id)
            isSaving = false
            return true
        } catch {
            errorMessage = "Failed to delete action: \(error.localizedDescription)"
            isSaving = false
            return false
        }
    }

    // MARK: - Error Handling

    @MainActor
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Supporting Types

enum ActionTypeOption: String, CaseIterable {
    case promptInsert = "Prompt Insert"
    case promptRewrite = "Prompt Rewrite"
    case assistantRegenerate = "Assistant Regenerate"
    
    var description: String {
        switch self {
        case .promptInsert:
            return "Insert prompt text at end of input field"
        case .promptRewrite:
            return """
            Rewrite user input using custom prompt. The app will automatically replace {{user_original_input}} with the user's actual input and extract the result from <rewritten_prompt> tags.

            Please reference the existing prompts for proper format examples.
            """
        case .assistantRegenerate:
            return "Regenerate assistant message using custom prompt"
        }
    }
    
    var requiresPrompts: Bool {
        switch self {
        case .promptInsert, .promptRewrite, .assistantRegenerate:
            return true
        }
    }
    
    func toMessageActionType() -> MessageAction.ActionType {
        switch self {
        case .promptInsert:
            return .actionPanelPromptInsert
        case .promptRewrite:
            return .actionPanelPromptRewrite
        case .assistantRegenerate:
            return .assistantRegenerate
        }
    }
}
