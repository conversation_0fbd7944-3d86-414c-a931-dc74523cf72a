import Foundation
import SwiftUI

final class GeneralSettingsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var shouldRestoreLastChat: Bool = true
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Dependencies
    
    private let getCurrentUserUseCase: GetCurrentUserUseCaseProtocol
    private let updateUserUseCase: UpdateUserUseCaseProtocol
    private let appStorageManager = AppStorageManager.shared
    
    // MARK: - Initialization
    
    init(
        getCurrentUserUseCase: GetCurrentUserUseCaseProtocol,
        updateUserUseCase: UpdateUserUseCaseProtocol
    ) {
        self.getCurrentUserUseCase = getCurrentUserUseCase
        self.updateUserUseCase = updateUserUseCase
        
        // Load initial value from current user
        Task {
            await loadUserSettings()
        }
    }
    
    // MARK: - Public Methods
    
    /// Load user settings from CoreData
    @MainActor
    func loadUserSettings() async {
        isLoading = true
        errorMessage = nil
        
        do {
            if let currentUser = try await getCurrentUserUseCase.execute() {
                // Use user's saved setting or default to true
                let userSetting = currentUser.appSettings?.shouldRestoreLastChat ?? true
                shouldRestoreLastChat = userSetting
                
                // Sync with AppStorage for immediate UI updates
                appStorageManager.shouldRestoreLastChat = userSetting
            } else {
                // No current user, use AppStorage value
                shouldRestoreLastChat = appStorageManager.shouldRestoreLastChat
            }
        } catch {
            print("❌ GeneralSettingsViewModel: Failed to load user settings: \(error)")
            errorMessage = "Failed to load settings"
            // Fallback to AppStorage value
            shouldRestoreLastChat = appStorageManager.shouldRestoreLastChat
        }
        
        isLoading = false
    }
    
    /// Update the shouldRestoreLastChat setting
    @MainActor
    func updateRestoreLastChatSetting(_ newValue: Bool) {
        shouldRestoreLastChat = newValue
        
        // Update AppStorage immediately for UI responsiveness
        appStorageManager.shouldRestoreLastChat = newValue
        
        // Save to CoreData in background
        Task {
            await saveUserSettings()
        }
    }
    
    /// Clear any error message
    @MainActor
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    
    /// Save user settings to CoreData
    private func saveUserSettings() async {
        do {
            if let currentUser = try await getCurrentUserUseCase.execute() {
                // Update user's app settings
                var updatedUser = currentUser
                var appSettings = updatedUser.appSettings ?? AppSettings()
                appSettings.shouldRestoreLastChat = shouldRestoreLastChat
                updatedUser.appSettings = appSettings
                
                // Save to CoreData
                _ = try await updateUserUseCase.execute(updatedUser)
                print("✅ GeneralSettingsViewModel: User settings saved successfully")
            } else {
                print("⚠️ GeneralSettingsViewModel: No current user found, settings saved to AppStorage only")
            }
        } catch {
            print("❌ GeneralSettingsViewModel: Failed to save user settings: \(error)")
            await MainActor.run {
                errorMessage = "Failed to save settings"
            }
        }
    }
}
