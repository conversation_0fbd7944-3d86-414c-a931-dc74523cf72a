import SwiftUI
import Combine

public extension Notification {
    var keyboardHeight: CGFloat {
        return (userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect)?.height ?? 0
    }
}

public struct KeyboardAvoiding: ViewModifier {
    @Binding var scrollToBottomIfAtBottom: Bool
    @State private var keyboardActiveAdjustment: CGFloat = 0
    @State private var cancellable: AnyCancellable?

    public func body(content: Content) -> some View {
        content
            .onAppear {
                startKeyboardMonitoring()
            }
            .onDisappear {
                stopKeyboardMonitoring()
            }
    }

    private func startKeyboardMonitoring() {
        // Create keyboard height publisher when needed
        let keyboardHeight = NotificationCenter.default.publisher(for: UIApplication.keyboardWillChangeFrameNotification)
            .map { $0.keyboardHeight }
            .debounce(for: .milliseconds(100), scheduler: DispatchQueue.main)
            .eraseToAnyPublisher()

        cancellable = keyboardHeight.sink { height in
            keyboardActiveAdjustment = height

            // Trigger scroll to bottom when keyboard appears
            if height > 0 {
                scrollToBottomIfAtBottom = true
            }
        }
    }

    private func stopKeyboardMonitoring() {
        cancellable?.cancel()
        cancellable = nil
    }
}
    
public extension View {
    func keyboardAvoiding(scrollToBottomIfAtBottom: Binding<Bool>) -> some View {
        modifier(KeyboardAvoiding(scrollToBottomIfAtBottom: scrollToBottomIfAtBottom))
    }
}
