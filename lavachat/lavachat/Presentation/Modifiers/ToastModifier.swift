import SwiftUI
import Combine

// MARK: - ToastView

struct ToastView: View {
    let icon: String
    let message: String
    let duration: TimeInterval
    let iconColor: Color
    
    @Environment(\.colorScheme) private var colorScheme
    
    init(icon: String, message: String, duration: TimeInterval = 2.0, iconColor: Color = .white) {
        self.icon = icon
        self.message = message
        self.duration = duration
        if icon == "checkmark.circle.fill" {
            self.iconColor = .green
        } else if icon == "hand.thumbsup.fill" {
            self.iconColor = .green
        } else if icon == "info.circle.fill" {
            self.iconColor = .blue
        } else if icon == "exclamationmark.triangle.fill" {
            self.iconColor = .yellow
        } else if icon == "xmark.circle.fill" {
            self.iconColor = .red
        } else {
            self.iconColor = iconColor
        }
    }
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(iconColor)
            
            Text(message)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.8))
        )
        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
    }
}

// MARK: - ToastModifier

struct ToastModifier: ViewModifier {
    let icon: String
    let message: String
    @Binding var isShowing: Bool
    let duration: TimeInterval
    
    @State private var cancellable: AnyCancellable?
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Group {
                    if isShowing {
                        ToastView(icon: icon, message: message, duration: duration)
                            .onAppear {
                                startTimer()
                            }
                            .onDisappear {
                                cancelTimer()
                            }
                    }
                }
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isShowing),
                alignment: .center
            )
            .onChange(of: isShowing) { newValue in
                if newValue {
                    // Cancel any existing timer when showing new toast
                    cancelTimer()
                    startTimer()
                } else {
                    cancelTimer()
                }
            }
    }
    
    private func startTimer() {
        cancellable = Timer.publish(every: duration, on: .main, in: .common)
            .autoconnect()
            .first()
            .sink { _ in
                isShowing = false
            }
    }
    
    private func cancelTimer() {
        cancellable?.cancel()
        cancellable = nil
    }
}

// MARK: - View Extension

extension View {
    func toast(
        icon: String,
        message: String,
        isShowing: Binding<Bool>,
        duration: TimeInterval = 2.0
    ) -> some View {
        self.modifier(
            ToastModifier(
                icon: icon,
                message: message,
                isShowing: isShowing,
                duration: duration
            )
        )
    }
}

// MARK: - Preview

#if DEBUG
#Preview("Toast Preview") {
    VStack {
        Text("Main Content")
            .font(.title)
        
        Spacer()
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color.gray.opacity(0.1))
    .toast(
        icon: "checkmark.circle.fill",
        message: "Copied",
        isShowing: .constant(true)
    )
}
#endif 