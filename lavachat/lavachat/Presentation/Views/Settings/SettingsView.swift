import SwiftUI

struct SettingsView: View {
    // MARK: - Properties
    @StateObject private var viewModel: SettingsViewModel
    @EnvironmentObject private var container: DIContainer
    @StateObject private var appStorageManager = AppStorageManager.shared

    // MARK: - Initialization
    init(viewModel: SettingsViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    // MARK: - Body
    var body: some View {
        NavigationStack {
            List {
                // Main Settings Section
                Section {
                    // General
                    NavigationLink(destination:
                                    GeneralSettingsView(viewModel: container.makeGeneralSettingsViewModel())
                            .onAppear {
                                viewModel.toolbarVisibility = .hidden
                            }
                    ) {
                        SettingsRowView(
                            icon: "gear",
                            iconColor: .gray,
                            title: "General"
                        )
                    }

                    // My Actions
                    NavigationLink(destination:
                        MyActionsView(viewModel: container.makeMyActionsViewModel())
                            .environmentObject(container)
                            .onAppear {
                                viewModel.toolbarVisibility = .hidden
                            }
                    ) {
                        SettingsRowView(
                            icon: "bolt.fill",
                            iconColor: .orange,
                            title: "My Actions"
                        )
                    }

                    // My Chat Settings
                    NavigationLink(destination:
                        MyChatSettingsView(viewModel: container.makeMyChatSettingsViewModel())
                            .environmentObject(container)
                            .onAppear {
                                viewModel.toolbarVisibility = .hidden
                            }
                    ) {
                        SettingsRowView(
                            icon: "message.fill",
                            iconColor: .blue,
                            title: "My Chat Settings"
                        )
                    }

                    // Notifications (Future)
                    SettingsRowView(
                        icon: "bell.fill",
                        iconColor: .red,
                        title: "Notifications",
                        isDisabled: true
                    )
                }

                // Privacy & Security Section
                Section {
                    SettingsRowView(
                        icon: "hand.raised.fill",
                        iconColor: .blue,
                        title: "Privacy & Safety",
                        isDisabled: true
                    )

                    SettingsRowView(
                        icon: "lock.fill",
                        iconColor: .gray,
                        title: "Security",
                        isDisabled: true
                    )
                }

                // Support & About Section
                Section {
                    SettingsRowView(
                        icon: "questionmark.circle.fill",
                        iconColor: .blue,
                        title: "Help & Support",
                        isDisabled: true
                    )

                    SettingsRowView(
                        icon: "info.circle.fill",
                        iconColor: .gray,
                        title: "About",
                        isDisabled: true
                    )
                }
            }
            .listStyle(.insetGrouped)
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                viewModel.showToolbarWithAnimation()
            }
            .toolbar(viewModel.toolbarVisibility, for: .tabBar)
            .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
                Button("OK") {
                    viewModel.clearError()
                }
            } message: {
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                }
            }
        }
    }
}

// MARK: - Settings Row Component
struct SettingsRowView: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String?
    let isDisabled: Bool

    init(icon: String, iconColor: Color, title: String, subtitle: String? = nil, isDisabled: Bool = false) {
        self.icon = icon
        self.iconColor = iconColor
        self.title = title
        self.subtitle = subtitle
        self.isDisabled = isDisabled
    }

    var body: some View {
        HStack(spacing: 12) {
            // Icon with background
            ZStack {
                RoundedRectangle(cornerRadius: 6)
                    .fill(isDisabled ? Color.gray.opacity(0.3) : iconColor)
                    .frame(width: 28, height: 28)

                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }

            // Text content
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .foregroundColor(isDisabled ? .secondary : .primary)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()
        }
        .padding(.vertical, 2)
        .contentShape(Rectangle())
    }
}

// MARK: - Preview
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    SettingsView(viewModel: container.makeSettingsViewModel())
        .environmentObject(container)
}
