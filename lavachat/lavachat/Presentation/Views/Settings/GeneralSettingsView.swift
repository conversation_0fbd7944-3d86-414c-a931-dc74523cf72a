import SwiftUI

struct GeneralSettingsView: View {
    @StateObject private var viewModel: GeneralSettingsViewModel

    // MARK: - Initialization
    init(viewModel: GeneralSettingsViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        List {
            Section {
                HStack {
                    Text("Restore Last Chat")
                        .font(.body)

                    Spacer()

                    Toggle("", isOn: Binding(
                        get: { viewModel.shouldRestoreLastChat },
                        set: { viewModel.updateRestoreLastChatSetting($0) }
                    ))
                    .labelsHidden()
                    .disabled(viewModel.isLoading)
                }
            } footer: {
                Text("When enabled, the app will automatically open the last chat you were viewing when you restart the app.")
                    .font(.footnote)
            }
            
            // Future general settings can be added here
            Section {
                // Placeholder for future settings
            } header: {
                Text("App Behavior")
            } footer: {
                Text("Additional app behavior settings will be available in future updates.")
                    .font(.footnote)
            }
        }
        .listStyle(.insetGrouped)
        .navigationTitle("General")
        .navigationBarTitleDisplayMode(.inline)
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
}

#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        GeneralSettingsView(viewModel: container.makeGeneralSettingsViewModel())
    }
}
