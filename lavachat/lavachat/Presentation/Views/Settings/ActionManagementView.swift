import SwiftUI

struct ActionManagementView: View {
    // MARK: - Properties
    let category: MessageActionCategory
    @StateObject private var viewModel: ActionManagementViewModel
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Initialization
    init(category: MessageActionCategory, viewModel: ActionManagementViewModel) {
        self.category = category
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                ProgressView("Loading actions...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    // System Actions Section (Read-only)
                    if viewModel.hasSystemActions {
                        Section {
                            ForEach(viewModel.systemActions) { action in
                                createActionRow(action: action, isSystemAction: true)
                            }
                        } header: {
                            Text("System Actions")
                        } footer: {
                            Text("Built-in actions that cannot be modified.")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Custom Actions Section (Editable)
                    Section {
                        if viewModel.hasCustomActions {
                            ForEach(viewModel.customActions) { action in
                                createActionRow(action: action, isSystemAction: false)
                            }
                        } else {
                            Text("No custom actions yet")
                                .foregroundColor(.secondary)
                                .font(.body)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding(.vertical, 20)
                        }
                    } header: {
                        HStack {
                            Text("My Custom Actions")
                            Spacer()
                            Button(action: {
                                viewModel.createNewAction()
                            }) {
                                Image(systemName: "plus")
                                    .foregroundColor(.accentColor)
                            }
                        }
                    } footer: {
                        VStack(alignment: .leading, spacing: 8) {
                            Text(viewModel.categoryDescription)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("Actions with a single prompt will execute immediately. Actions with multiple prompts will show a selection sheet for you to choose.")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .listStyle(.insetGrouped)
            }
        }
        .navigationTitle(viewModel.categoryTitle)
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            Task {
                await viewModel.loadActions()
            }
        }
        .navigationDestination(isPresented: $viewModel.showActionEditor) {
            ActionEditorView(
                category: category,
                editingAction: viewModel.editingAction,
                viewModel: container.makeActionEditorViewModel(
                    category: category,
                    editingAction: viewModel.editingAction
                ),
                onComplete: { _ in
                    viewModel.onActionEditorComplete()
                }
            )
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    @ViewBuilder
    private func createActionRow(action: MessageAction, isSystemAction: Bool) -> some View {
        HStack(spacing: 12) {
            // Icon
            if let iconName = action.icon {
                Image(systemName: iconName)
                    .font(.title3)
                    .foregroundColor(isSystemAction ? .secondary : .accentColor)
                    .frame(width: 24, height: 24)
            } else {
                Circle()
                    .fill(isSystemAction ? Color.secondary.opacity(0.3) : Color.accentColor.opacity(0.3))
                    .frame(width: 24, height: 24)
            }
            
            // Content
            HStack(alignment: .center) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(action.name)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    // Prompts count (if applicable)
                    if !action.prompts.isEmpty {
                        Text("\(action.prompts.count) prompt\(action.prompts.count == 1 ? "" : "s")")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Right side content - vertically centered
                Text(actionTypeShortDescription(action.actionType))
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.1))
                    .clipShape(Capsule())
            }
            
            if !isSystemAction {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            if !isSystemAction {
                viewModel.editAction(action)
            }
        }
    }
    
    private func actionTypeDescription(_ actionType: MessageAction.ActionType) -> String {
        switch actionType {
        case .system(let kind):
            return "System • \(kind.rawValue.capitalized)"
        case .assistantRegenerate:
            return "Assistant Regenerate"
        case .actionPanelPromptInsert:
            return "Prompt Insert"
        case .actionPanelPromptRewrite:
            return "Prompt Rewrite"
        }
    }

    private func actionTypeShortDescription(_ actionType: MessageAction.ActionType) -> String {
        switch actionType {
        case .system:
            return "System"
        case .assistantRegenerate:
            return "Regenerate"
        case .actionPanelPromptInsert:
            return "Insert"
        case .actionPanelPromptRewrite:
            return "Rewrite"
        }
    }
}

// MARK: - Preview
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationView {
        ActionManagementView(
            category: .actionPanel,
            viewModel: container.makeActionManagementViewModel(category: .actionPanel)
        )
    }
}
