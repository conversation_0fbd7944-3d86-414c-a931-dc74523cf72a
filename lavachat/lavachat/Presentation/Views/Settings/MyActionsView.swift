import SwiftUI

struct MyActionsView: View {
    // MARK: - Properties
    @StateObject private var viewModel: MyActionsViewModel
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Initialization
    init(viewModel: MyActionsViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                ProgressView("Loading actions...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                        // Statistics Section
                        Section {
                            HStack {
                                Text("Total Custom Actions")
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Text("\(viewModel.actionStatistics.totalCustomActions)")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.accentColor)
                            }
                            .padding(.vertical, 8)
                        } header: {
                            Text("Overview")
                        }
                        
                        // Action Categories Section
                        Section {
                            // Input Actions
                            NavigationLink(destination: ActionManagementView(
                                category: .actionPanel,
                                viewModel: container.makeActionManagementViewModel(category: .actionPanel)
                            )) {
                                createCategoryRow(
                                    info: MyActionsViewModel.ActionCategoryDisplayInfo.inputActions(count: viewModel.actionStatistics.inputActions)
                                )
                            }

                            // User Message Actions
                            NavigationLink(destination: ActionManagementView(
                                category: .userMessage,
                                viewModel: container.makeActionManagementViewModel(category: .userMessage)
                            )) {
                                createCategoryRow(
                                    info: MyActionsViewModel.ActionCategoryDisplayInfo.userMessageActions(count: viewModel.actionStatistics.userMessageActions)
                                )
                            }

                            // Assistant Message Actions
                            NavigationLink(destination: ActionManagementView(
                                category: .assistantCard,
                                viewModel: container.makeActionManagementViewModel(category: .assistantCard)
                            )) {
                                createCategoryRow(
                                    info: MyActionsViewModel.ActionCategoryDisplayInfo.assistantMessageActions(count: viewModel.actionStatistics.assistantMessageActions)
                                )
                            }
                            
                        } header: {
                            Text("Action Categories")
                        } footer: {
                            Text("Tap a category to manage its actions. You can create, edit, and organize custom actions for different parts of the interface.")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .listStyle(.insetGrouped)
                }
        }
        .navigationTitle("My Actions")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            Task {
                await viewModel.loadActionStatistics()
            }
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    @ViewBuilder
    private func createCategoryRow(info: MyActionsViewModel.ActionCategoryDisplayInfo) -> some View {
        HStack {
            Image(systemName: info.icon)
                .font(.title2)
                .foregroundColor(colorForString(info.color))

            Text(info.title)
                .font(.body)

            Spacer()

            Text("\(info.count)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(Color.secondary.opacity(0.1))
                .clipShape(Capsule())
        }
        .padding(.vertical, 2)
    }
    
    private func colorForString(_ colorString: String) -> Color {
        switch colorString {
        case "blue":
            return .blue
        case "green":
            return .green
        case "purple":
            return .purple
        case "orange":
            return .orange
        case "red":
            return .red
        default:
            return .accentColor
        }
    }
}

// MARK: - Preview
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    MyActionsView(viewModel: container.makeMyActionsViewModel())
        .environmentObject(container)
}