import SwiftUI
import SymbolPicker

struct ActionEditorView: View {
    // MARK: - Properties
    let category: MessageActionCategory
    let editingAction: MessageAction?
    @StateObject private var viewModel: ActionEditorViewModel
    @Environment(\.dismiss) private var dismiss

    let onComplete: (MessageAction) -> Void

    // MARK: - Initialization
    init(
        category: MessageActionCategory,
        editingAction: MessageAction?,
        viewModel: ActionEditorViewModel,
        onComplete: @escaping (MessageAction) -> Void
    ) {
        self.category = category
        self.editingAction = editingAction
        self._viewModel = StateObject(wrappedValue: viewModel)
        self.onComplete = onComplete
    }
    
    // MARK: - Body
    var body: some View {
        Form {
            // Basic Information Section
            Section {
                // Name Field
                HStack {
                    Text("Name")
                        .foregroundColor(.primary)

                    Spacer()

                    TextField("Required", text: $viewModel.name)
                        .multilineTextAlignment(.trailing)
                        .foregroundColor(.primary)
                }

                // Icon Field
                HStack {
                    Text("Icon")
                        .foregroundColor(.primary)

                    Spacer()

                    Button {
                        viewModel.presentIconPicker()
                    } label: {
                        HStack(spacing: 8) {
                            if !viewModel.icon.isEmpty {
                                Image(systemName: viewModel.icon)
                                    .foregroundColor(.accentColor)
                                    .frame(width: 20, height: 20)

                                Text(viewModel.icon)
                                    .foregroundColor(.accentColor)
                            } else {
                                Text("Required")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }

            } header: {
                Text("Basic Information")
            } footer: {
                if let nameError = viewModel.nameError {
                    Text(nameError)
                        .foregroundColor(.red)
                } else if let iconError = viewModel.iconError {
                    Text(iconError)
                        .foregroundColor(.red)
                }
            }

            // Action Type Section
            Section {
                HStack {
                    Text("Type")
                        .foregroundColor(.primary)

                    Spacer()

                    Picker("", selection: $viewModel.selectedActionType) {
                        ForEach(viewModel.availableActionTypes, id: \.self) { actionType in
                            Text(actionType.rawValue)
                                .tag(actionType)
                        }
                    }
                    .pickerStyle(.menu)
                    .foregroundColor(.secondary)
                }
            } header: {
                Text("Action Type")
            } footer: {
                Text(viewModel.selectedActionType.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Prompts Section (conditional)
            if viewModel.showPromptsSection {
                Section {
                    ForEach(Array(viewModel.prompts.enumerated()), id: \.offset) { index, prompt in
                        HStack {
                            Text(prompt.isEmpty ? "Tap to add prompt text" : prompt)
                                .font(.body)
                                .foregroundColor(prompt.isEmpty ? .secondary : .primary)
                                .multilineTextAlignment(.leading)
                                .lineLimit(3)
                                .frame(maxWidth: .infinity, alignment: .leading)

                            if viewModel.prompts.count > 1 {
                                Button(action: {
                                    viewModel.removePrompt(at: index)
                                }) {
                                    Image(systemName: "minus.circle.fill")
                                        .foregroundColor(.red)
                                        .font(.title3)
                                }
                                .contentShape(Rectangle())
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.vertical, 4)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            viewModel.presentFullScreenEditor(for: index)
                        }
                    }
                    .onMove(perform: viewModel.movePrompt)

                    Button(action: {
                        viewModel.addPrompt()
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                                .foregroundColor(.accentColor)
                            Text("Add Prompt")
                                .foregroundColor(.accentColor)
                        }
                    }

                } header: {
                    Text("Prompts")
                } footer: {
                    VStack(alignment: .leading, spacing: 8) {
                        if let promptsError = viewModel.promptsError {
                            Text(promptsError)
                                .foregroundColor(.red)
                        } else {
                            Text("Tap a prompt to edit it in full screen. You can reorder prompts by dragging.")
                        }

                        Text("Actions with a single prompt will execute immediately. Actions with multiple prompts will show a selection sheet for users to choose.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Delete Section (only show for existing actions that can be deleted)
            if viewModel.shouldShowDeleteSection {
                Section {
                    Button(action: {
                        viewModel.requestDeleteConfirmation()
                    }) {
                        HStack {
                            Text(viewModel.deleteButtonText)
                                .foregroundColor(viewModel.canDelete ? .red : .secondary)
                                .fontWeight(.medium)
                            Spacer()
                        }
                        .frame(maxWidth: .infinity)
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(!viewModel.canDelete)
                } footer: {
                    if !viewModel.deleteFooterText.isEmpty {
                        Text(viewModel.deleteFooterText)
                            .foregroundColor(viewModel.canDelete ? .secondary : .red)
                    }
                }
            }
        }
        .navigationTitle(viewModel.title)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(viewModel.isSaving ? "Saving..." : "Save") {
                    Task {
                        if let savedAction = await viewModel.saveAction() {
                            onComplete(savedAction)
                        }
                    }
                }
                .disabled(!viewModel.canSave)
                .fontWeight(.semibold)
            }
        }
        .sheet(isPresented: $viewModel.iconPickerPresented) {
            SymbolPicker(symbol: $viewModel.icon)
        }
        .sheet(isPresented: $viewModel.fullScreenTextEditorPresented) {
            FullScreenTextEditorView(
                title: "Edit Prompt",
                placeholder: "Enter your prompt text here...",
                initialText: viewModel.editingPromptText,
                onSave: { newText in
                    viewModel.saveFullScreenEditorText(with: newText)
                }
            )
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("Delete Action", isPresented: $viewModel.showDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                Task {
                    if await viewModel.deleteAction() {
                        onComplete(viewModel.currentEditingAction!)
                    }
                }
            }
        } message: {
            Text("Are you sure you want to delete this action? This action cannot be undone.")
        }
    }
}

// MARK: - Preview
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    ActionEditorView(
        category: .actionPanel,
        editingAction: nil,
        viewModel: container.makeActionEditorViewModel(category: .actionPanel, editingAction: nil),
        onComplete: { _ in }
    )
}
