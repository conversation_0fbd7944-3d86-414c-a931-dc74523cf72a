import SwiftUI

struct MyChatSettingsView: View {
    
    // MARK: - Properties
    
    @StateObject private var viewModel: MyChatSettingsViewModel
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) private var dismiss
        
    // MARK: - Initialization
    
    init(viewModel: MyChatSettingsViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        List {
            if viewModel.isLoading {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, alignment: .center)
            } else {
                // Default Setting Section
                Section {
                    HStack {
                        Text(viewModel.currentDefaultSettingName)
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                    .padding(.vertical, 2)
                } header: {
                    Text("Default Chat Setting")
                } footer: {
                    Text("New chat sessions will use this setting by default.")
                        .font(.footnote)
                }
                
                // All Settings Section
                Section {
                    ForEach(viewModel.allSettings) { setting in
                        ChatSettingRowView(
                            setting: setting,
                            isDefault: viewModel.isDefaultSetting(setting.id),
                            onTap: {
                                Task {
                                    await viewModel.setDefaultSetting(setting.id)
                                }
                            },
                            onEdit: {
                                viewModel.editingSetting = setting
                                viewModel.showEditSetting = true
                            }
                        )
                    }
                    
                    // Create New Setting Button
                    Button(action: {
                        viewModel.showCreateNewSetting = true
                    }) {
                        HStack {
                            Image(systemName: "plus")
                                .font(.body)
                                .foregroundColor(.accentColor)

                            Text("Create New Setting")
                                .font(.body)
                                .foregroundColor(.accentColor)

                            Spacer()
                        }
                        .padding(.vertical, 2)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                } header: {
                    Text("All Chat Settings")
                }
            }
        }
        .listStyle(.insetGrouped)
        .navigationTitle("My Chat Settings")
        .navigationBarTitleDisplayMode(.inline)
        .task {
            await viewModel.loadData()
        }
        .refreshable {
            await viewModel.refresh()
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .navigationDestination(isPresented: $viewModel.showCreateNewSetting) {
            ChatSettingEditorView(
                viewModel: container.makeChatSettingEditorViewModel(editingSetting: nil)
            )
            .environmentObject(container)
            .onDisappear {
                // Refresh the list when returning from editor
                Task {
                    await viewModel.refresh()
                }
            }
        }
        .navigationDestination(isPresented: $viewModel.showEditSetting) {
            ChatSettingEditorView(
                viewModel: container.makeChatSettingEditorViewModel(editingSetting: viewModel.editingSetting)
            )
            .environmentObject(container)
            .onDisappear {
                // Refresh the list when returning from editor
                Task {
                    await viewModel.refresh()
                }
            }
        }
    }
}

// MARK: - Chat Setting Row Component

struct ChatSettingRowView: View {
    let setting: ChatSessionSetting
    let isDefault: Bool
    let onTap: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        HStack {
            // Setting name with checkmark
            HStack(spacing: 12) {
                if isDefault {
                    Image(systemName: "checkmark")
                        .font(.body)
                        .foregroundColor(.accentColor)
                } else {
                    // Invisible placeholder to maintain alignment
                    Image(systemName: "checkmark")
                        .font(.body)
                        .foregroundColor(.clear)
                }
                
                Text(setting.name)
                    .font(.body)
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            // Info button for editing
            Button(action: onEdit) {
                Image(systemName: "info.circle")
                    .font(.body)
                    .foregroundColor(.accentColor)
            }
            .contentShape(Rectangle())
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 2)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        MyChatSettingsView(viewModel: container.makeMyChatSettingsViewModel())
    }
    .environmentObject(container)
}
#endif
