import SwiftUI

struct ChatSettingEditorView: View {

    // MARK: - Properties

    @StateObject private var viewModel: ChatSettingEditorViewModel
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) private var dismiss

    // MARK: - Navigation States

    @State private var showAuxiliaryInstanceSelection = false
    @State private var showContextMessageCountPicker = false

    // Message Action Management States
    @State private var showActionPanelManagement = false
    @State private var showUserMessageManagement = false
    @State private var showAssistantCardManagement = false
    @State private var showAssistantMenuManagement = false

    // MARK: - Initialization

    init(
        viewModel: ChatSettingEditorViewModel
    ) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    // MARK: - Body

    var body: some View {
        Form {
            if viewModel.isLoading {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, alignment: .center)
            } else {
                // Basic Information Section
                Section {
                    // Name Field
                    HStack {
                        Text("Name")
                            .foregroundColor(.primary)

                        Spacer()

                        TextField("Required", text: $viewModel.name)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(.primary)
                    }
                } header: {
                    Text("Basic Information")
                }

                // Interface Configuration Section
                Section {
                    // Should Expand Thinking Toggle
                    HStack {
                        Text("Default Thinking Expansion")
                            .fixedSize(horizontal: true, vertical: false)
                        Spacer()
                        Toggle("", isOn: $viewModel.shouldExpandThinking)
                    }
                } header: {
                    Text("UI & Display")
                }

                // Message Actions Section
                if let resolvedActions = viewModel.resolvedMessageActionSettings {
                    Section {
                        // Input Actions (Action Panel)
                        createMessageActionRow(
                            title: "Input Actions",
                            actions: resolvedActions.actionPanelActions,
                            showManagement: $showActionPanelManagement
                        )

                        // User Message Actions
                        createMessageActionRow(
                            title: "User Message",
                            actions: resolvedActions.userMessageActions,
                            showManagement: $showUserMessageManagement
                        )

                        // Assistant Card Actions
                        createMessageActionRow(
                            title: "Assistant Card",
                            actions: resolvedActions.assistantMessageCardActions,
                            showManagement: $showAssistantCardManagement
                        )

                        // Assistant Menu Actions
                        createMessageActionRow(
                            title: "Assistant Menu",
                            actions: resolvedActions.assistantMessageMenuActions,
                            showManagement: $showAssistantMenuManagement
                        )
                    } header: {
                        Text("Message Actions")
                    } footer: {
                        Text("Configure which actions are available in different parts of the chat interface.")
                    }
                }

                // Chat Settings Section
                Section {
                    // Auxiliary Instance Row
                    HStack {
                        Text("Auxiliary Instance")
                            .foregroundColor(.primary)

                        Spacer()

                        Text(viewModel.auxiliaryLLMInstanceDisplayText)
                            .foregroundColor(.secondary)

                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        showAuxiliaryInstanceSelection = true
                    }

                    // Auto Generate Title Toggle
                    HStack {
                        Text("Auto Generate Title")
                            .foregroundColor(viewModel.shouldAutoGenerateTitleEnabled ? .primary : .secondary)
                        Spacer()
                        Toggle("", isOn: $viewModel.shouldAutoGenerateTitle)
                            .disabled(!viewModel.shouldAutoGenerateTitleEnabled)
                    }

                    // Context Message Count Row
                    HStack {
                        Text("Context Message Count")
                            .foregroundColor(.primary)

                        Spacer()

                        Text(viewModel.contextMessageCountDisplayText)
                            .foregroundColor(.secondary)

                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        showContextMessageCountPicker = true
                    }
                } header: {
                    Text("Chat Settings")
                } footer: {
                    Text("Auto-generate title triggers once after the first message in new chats when auxiliary instance is configured.")
                }

                // Delete Section (only show for existing settings that can be deleted)
                if viewModel.shouldShowDeleteSection {
                    Section {
                        Button(action: {
                            viewModel.requestDeleteConfirmation()
                        }) {
                            HStack {
                                Text("Delete Setting")
                                    .foregroundColor(.red)
                                    .fontWeight(.medium)
                                Spacer()
                            }
                            .frame(maxWidth: .infinity)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                    } footer: {
                        Text("This action cannot be undone. Any chat sessions using this setting will be switched to the system default. Cannot delete system default settings or your current default setting.")
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .navigationTitle(viewModel.navigationTitle)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    Task {
                        if let savedSetting = await viewModel.save() {
                            dismiss()
                        }
                    }
                }
                .fontWeight(.semibold)
                .disabled(!viewModel.isSaveEnabled || viewModel.isLoading)
            }
        }

        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("Delete Setting", isPresented: $viewModel.showDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                Task {
                    if await viewModel.deleteSetting() {
                        dismiss()
                    }
                }
            }
        } message: {
            Text("Are you sure you want to delete this setting? This action cannot be undone.")
        }
        .navigationDestination(isPresented: $showAuxiliaryInstanceSelection) {
            NewChatSheetView(
                viewModel: container.makeNewChatSheetViewModel(
                    mode: .auxiliaryInstanceSelection,
                    onSelectAuxiliaryInstance: { instanceId in
                        Task {
                            await viewModel.updateAuxiliaryLLMInstance(instanceId)
                        }
                        showAuxiliaryInstanceSelection = false
                    }
                )
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showContextMessageCountPicker) {
            ContextMessageCountPickerView(
                initialValue: viewModel.contextMessageCount
            ) { selectedCount in
                Task {
                    await viewModel.updateContextMessageCount(selectedCount)
                }
                showContextMessageCountPicker = false
            }
            .navigationTitle("Context Message Count")
            .navigationBarTitleDisplayMode(.inline)
        }
        .navigationDestination(isPresented: $showActionPanelManagement) {
            MessageActionCategoryManagementView(
                category: .actionPanel,
                currentActionIds: viewModel.messageActionSettings?.actionPanelActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.actionPanel, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showUserMessageManagement) {
            MessageActionCategoryManagementView(
                category: .userMessage,
                currentActionIds: viewModel.messageActionSettings?.userMessageActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.userMessage, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showAssistantCardManagement) {
            MessageActionCategoryManagementView(
                category: .assistantCard,
                currentActionIds: viewModel.messageActionSettings?.assistantMessageCardActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.assistantCard, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showAssistantMenuManagement) {
            MessageActionCategoryManagementView(
                category: .assistantMenu,
                currentActionIds: viewModel.messageActionSettings?.assistantMessageMenuActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.assistantMenu, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
    }

    // MARK: - Helper Methods

    @ViewBuilder
    private func createMessageActionRow(
        title: String,
        actions: [MessageAction],
        showManagement: Binding<Bool>
    ) -> some View {
        HStack {
            Text(title)
            Spacer()

            HStack(spacing: 4) {
                // Show up to 5 icons
                let displayActions = Array(actions.prefix(5))
                ForEach(displayActions, id: \.id) { action in
                    if let icon = action.icon {
                        Image(systemName: icon)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // Show count if more than 5
                if actions.count > 5 {
                    Text("+\(actions.count - 5)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // Show empty state
                if actions.isEmpty {
                    Text("None")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            showManagement.wrappedValue = true
        }
    }

    private func updateMessageActionCategory(_ category: MessageActionCategory, actionIds: [UUID]) async {
        var messageActionSettings = viewModel.messageActionSettings ?? MessageActionSettings()

        switch category {
        case .actionPanel:
            messageActionSettings.actionPanelActions = actionIds
        case .userMessage:
            messageActionSettings.userMessageActions = actionIds
        case .assistantCard:
            messageActionSettings.assistantMessageCardActions = actionIds
        case .assistantMenu:
            messageActionSettings.assistantMessageMenuActions = actionIds
        }

        await viewModel.updateMessageActionSettings(messageActionSettings)
    }
}

// MARK: - Preview

#if DEBUG
#Preview("New Setting") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ChatSettingEditorView(
            viewModel: container.makeChatSettingEditorViewModel(editingSetting: nil)
        )
    }
    .environmentObject(container)
}

#Preview("Edit Setting") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    let sampleSetting = ChatSessionSetting(
        id: UUID(),
        name: "Sample Setting",
        isSystemDefault: false,
        shouldExpandThinking: true,
        auxiliaryLLMInstanceId: nil,
        shouldAutoGenerateTitle: false,
        contextMessageCount: 20
    )

    NavigationStack {
        ChatSettingEditorView(
            viewModel: container.makeChatSettingEditorViewModel(editingSetting: sampleSetting)
        )
    }
    .environmentObject(container)
}
#endif
