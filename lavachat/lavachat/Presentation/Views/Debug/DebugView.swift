#if DEBUG
import SwiftUI

struct DebugView: View {
    // MARK: - Dependencies
    @EnvironmentObject private var container: DIContainer
    
    // MARK: - State Properties
    @State private var selectedInstanceIds: Set<UUID> = []
    @State private var testMessage: String = "Hello! Please introduce yourself and explain your capabilities."
    @State private var isLoading: Bool = false
    @State private var responses: [LLMStreamingResponse] = []
    @State private var currentTask: Task<Void, Never>?
    @State private var errorMessage: String?
    
    // MARK: - Use Cases (for testing)
    @State private var messageTreeManager: MessageTreeManagerUseCaseProtocol?
    @State private var prepareMessagesUseCase: PrepareMessagesUseCaseProtocol?
    @State private var sendMessageUseCase: SendMessageUseCaseProtocol?
    
    // MARK: - Computed Properties
    
    /// Available test instance IDs from MockChatPreviewData
    private var availableInstanceIds: [UUID] {
        [
            MockChatPreviewData.gemini25FlashInstanceId,
            MockChatPreviewData.ORDSV30324FreeInstanceId,
            MockChatPreviewData.ORDSR10528FreeInstanceId,
            MockChatPreviewData.NVDeepSeekR10528FreeInstanceId,
            MockChatPreviewData.SFDeepSeekR10528Qwen38BInstanceId
        ]
    }
    
    /// Instance names for display
    private func instanceName(for id: UUID) -> String {
        switch id {

        case MockChatPreviewData.gemini25FlashInstanceId:
            return "Gemini 2.5 Flash"
        case MockChatPreviewData.ORDSV30324FreeInstanceId:
            return "OpenRouter DeepSeek V3 0324 Free"
        case MockChatPreviewData.ORDSR10528FreeInstanceId:
            return "OpenRouter DeepSeek R1 0528 Free"
        case MockChatPreviewData.NVDeepSeekR10528FreeInstanceId:
            return "Nvidia DeepSeek R1 0528 Free"
        case MockChatPreviewData.SFDeepSeekR10528Qwen38BInstanceId:
            return "SiliconFlow DeepSeek R1 0528 Qwen3-8B"
        default:
            return "Unknown Instance"
        }
    }
    
    /// Responses grouped by instance ID
    private var responsesByInstance: [UUID: [LLMStreamingResponse]] {
        Dictionary(grouping: responses) { $0.instanceId }
    }
    
    /// Set of completed instances
    private var completedInstances: Set<UUID> {
        Set(responses.filter { $0.responseType.isCompletion }.map { $0.instanceId })
    }
    
    /// Instance errors
    private var instanceErrors: [UUID: ChatError] {
        responses.reduce(into: [:]) { result, response in
            if let error = response.error {
                let chatError = ChatError.from(error, instanceId: response.instanceId)
                print("=== ERROR DEBUG INFO ===")
                print("Instance ID: \(response.instanceId)")
                print("Error Type: \(chatError)")
                print("Error Description: \(chatError.errorDescription ?? "Unknown")")
                print("Is Retryable: \(chatError.isRetryable)")
                print("========================")
                result[response.instanceId] = chatError
            }
        }
    }
    
    // MARK: - Quick Message Templates
    private let quickMessages = [
        "Hello! Please introduce yourself and explain your capabilities.",
        "Write a Python function to calculate fibonacci numbers."
    ]
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Instance Selection Section
                    instanceSelectionSection
                    
                    // Message Input Section
                    messageInputSection
                    
                    // Control Buttons Section
                    controlButtonsSection
                    
                    // Error Display
                    if let errorMessage = errorMessage {
                        errorDisplaySection(errorMessage)
                    }
                    
                    // Responses Section
                    if !responses.isEmpty {
                        responsesSection
                    }
                }
                .padding()
            }
            .navigationTitle("LLM API Debug")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            // Initialize Use Cases for testing
            messageTreeManager = container.getSharedMessageTreeManagerUseCase()
            prepareMessagesUseCase = container.makePrepareMessagesUseCase()
            sendMessageUseCase = container.getSharedSendMessageUseCase()
        }
    }
    
    // MARK: - UI Components
    
    private var instanceSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Select Test Instances")
                    .font(.headline)
                Spacer()
                Text("\(selectedInstanceIds.count) selected")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            VStack(spacing: 8) {
                ForEach(availableInstanceIds, id: \.self) { instanceId in
                    HStack {
                        Button(action: {
                            toggleInstanceSelection(instanceId)
                        }) {
                            HStack {
                                Image(systemName: selectedInstanceIds.contains(instanceId) ? "checkmark.square.fill" : "square")
                                    .foregroundColor(selectedInstanceIds.contains(instanceId) ? .blue : .gray)
                                
                                MockInstanceLogoView(instanceId: instanceId, size: 24)
                                
                                Text(instanceName(for: instanceId))
                                    .foregroundColor(.primary)
                                
                                Spacer()
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .padding(.vertical, 4)
                }
                
                // Quick Select Buttons
                HStack {
                    Button("Select All") {
                        selectedInstanceIds = Set(availableInstanceIds)
                    }
                    .buttonStyle(.bordered)
                    .disabled(isLoading)
                    
                    Button("Clear All") {
                        selectedInstanceIds.removeAll()
                    }
                    .buttonStyle(.bordered)
                    .disabled(isLoading)
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var messageInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Test Message")
                .font(.headline)
            
            TextEditor(text: $testMessage)
                .frame(minHeight: 80, maxHeight: 120)
                .padding(8)
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                .disabled(isLoading)
            
            Text("\(testMessage.count) characters")
                .font(.caption)
                .foregroundColor(.secondary)
            
            // Quick Message Templates
            Text("Quick Templates:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            LazyVGrid(columns: [GridItem(.adaptive(minimum: 150))], spacing: 8) {
                ForEach(Array(quickMessages.enumerated()), id: \.offset) { index, message in
                    Button(action: {
                        testMessage = message
                    }) {
                        Text(message.prefix(30) + (message.count > 30 ? "..." : ""))
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(6)
                    }
                    .disabled(isLoading)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var controlButtonsSection: some View {
        HStack(spacing: 16) {
            Button(action: {
                Task {
                    await sendTestMessage()
                }
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "paperplane.fill")
                    }
                    Text(isLoading ? "Sending..." : "Send Test")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(sendButtonEnabled ? Color.blue : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!sendButtonEnabled)
            
            if isLoading {
                Button(action: {
                    cancelCurrentRequest()
                }) {
                    HStack {
                        Image(systemName: "stop.fill")
                        Text("Cancel")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            } else if !responses.isEmpty {
                Button(action: {
                    clearResponses()
                }) {
                    HStack {
                        Image(systemName: "trash")
                        Text("Clear")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
        }
    }
    
    private var sendButtonEnabled: Bool {
        !selectedInstanceIds.isEmpty && !testMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isLoading
    }
    
    private func errorDisplaySection(_ message: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                Text("Error")
                    .font(.headline)
                    .foregroundColor(.red)
                Spacer()
            }
            
            Text(message)
                .font(.body)
                .foregroundColor(.primary)
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var responsesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Responses")
                    .font(.headline)
                Spacer()
                Text("\(completedInstances.count)/\(selectedInstanceIds.count) completed")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ForEach(Array(selectedInstanceIds), id: \.self) { instanceId in
                ResponseCardView(
                    instanceId: instanceId,
                    instanceName: instanceName(for: instanceId),
                    responses: responsesByInstance[instanceId] ?? [],
                    isCompleted: completedInstances.contains(instanceId),
                    error: instanceErrors[instanceId]
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Actions
    
    private func toggleInstanceSelection(_ instanceId: UUID) {
        if selectedInstanceIds.contains(instanceId) {
            selectedInstanceIds.remove(instanceId)
        } else {
            selectedInstanceIds.insert(instanceId)
        }
    }
    
    @MainActor
    private func sendTestMessage() async {
        guard !selectedInstanceIds.isEmpty && !testMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        guard let messageTreeManager = messageTreeManager,
              let prepareMessagesUseCase = prepareMessagesUseCase,
              let sendMessageUseCase = sendMessageUseCase else {
            errorMessage = "Use Cases not initialized"
            return
        }
        
        isLoading = true
        errorMessage = nil
        responses.removeAll()
        
        do {
            let sessionId = MockChatPreviewData.exampleSession1.id
            // let messageId1 = MockChatPreviewData.messagesForExampleSession1[1].id
            // let messageId2 = MockChatPreviewData.messagesForExampleSession1[3].id
            
            // Add validation checking
            print("=== SENDING MESSAGE DEBUG (Use Cases) ===")
            print("Selected Instance IDs: \(selectedInstanceIds)")
            print("Test Message Content: '\(testMessage)'")
            print("Session ID: \(sessionId)")
            print("MessageTreeManager Type: \(type(of: messageTreeManager))")
            print("SendMessage UseCase Type: \(type(of: sendMessageUseCase))")
            
            // Validate instances exist (for debugging purposes)
            let llmRepo = container.getSharedLLMInstanceRepository()
            for instanceId in selectedInstanceIds {
                do {
                    if let instance = try await llmRepo.getInstance(byId: instanceId) {
                        print("✅ Instance \(instanceId) found: \(instance.name)")
                        
                        if let model = try await llmRepo.getModel(byId: instance.modelId) {
                            print("✅ Model \(instance.modelId) found: \(model.name)")
                            if let provider = try await llmRepo.getProvider(byId: model.providerId) {
                                print("✅ Provider \(model.providerId) found: \(provider.name)")
                            } else {
                                print("❌ Provider \(model.providerId) NOT found")
                            }
                        } else {
                            print("❌ Model \(instance.modelId) NOT found")
                        }
                    } else {
                        print("❌ Instance \(instanceId) NOT found")
                    }
                } catch {
                    print("❌ Error checking instance \(instanceId): \(error)")
                }
            }
            
            // Initialize message tree cache
            do {
                try await messageTreeManager.initializeTreeFromRepository(sessionId: sessionId)
                print("✅ Message tree cache initialized successfully")
            } catch {
                print("❌ Failed to initialize message tree: \(error)")
                throw error
            }
            
            print("=============================")
            
            let task = Task {
                do {
                    // Get chat session for prepare messages
                    let chatRepo = container.getSharedChatRepository()
                    guard let chatSession = try await chatRepo.getChatSession(byId: sessionId) else {
                        throw ChatError.sessionNotFound
                    }

                    // Get chat session setting
                    guard let settingsId = chatSession.settingsId,
                          let chatSessionSetting = try await chatRepo.getSetting(byId: settingsId) else {
                        throw ChatError.sessionNotFound
                    }

                    // Step 1: Prepare messages using PrepareMessagesUseCase
                    let preparedMessages = try await prepareMessagesUseCase.execute(
                        chatSession: chatSession,
                        chatSessionSetting: chatSessionSetting,
                        content: [.text(testMessage)],
                        instanceIds: Array(selectedInstanceIds),
                        operationType: .newMessage,
                        parentMessageId: nil, // Root message for simplicity
                        originalMessageId: nil,
                        userMessageDepth: 0
                    )
                    
                    // Step 2: Send messages using SendMessageUseCase
                    let stream = sendMessageUseCase.execute(preparedMessages: preparedMessages)
                    
                    for try await response in stream {
                        await MainActor.run {
                            print("=== RECEIVED RESPONSE (Use Cases) ===")
                            print("Instance ID: \(response.instanceId)")
                            print("Response Type: \(response.responseType)")
                            print("Is Completion: \(response.responseType.isCompletion)")
                            print("Is Error: \(response.responseType.isError)")
                            switch response.responseType {
                            case .contentDelta(let content):
                                print("Content Delta: '\(content)'")
                            case .thinkingDelta(let content):
                                print("Thinking Delta: '\(content)'")
                            case .completionMarker(let info):
                                print("Completion Marker - Finish Reason: \(info.finishReason ?? "nil")")
                                print("Completion Marker - Tokens: prompt=\(info.promptTokens ?? 0), completion=\(info.completionTokens ?? 0)")
                            case .error(let error):
                                print("Error: \(error)")
                            case .statusUpdate(let status):
                                print("Status Update: '\(status)'")
                            }
                            print("========================")
                            responses.append(response)
                        }
                    }
                    
                    await MainActor.run {
                        isLoading = false
                        currentTask = nil
                    }
                } catch {
                    await MainActor.run {
                        if let chatError = error as? ChatError {
                            errorMessage = "Failed to send message: \(chatError.errorDescription ?? chatError.localizedDescription)"
                        } else {
                            errorMessage = "Failed to send message: \(error.localizedDescription)"
                        }
                        isLoading = false
                        currentTask = nil
                    }
                }
            }
            
            currentTask = task
            
        } catch {
            if let chatError = error as? ChatError {
                errorMessage = "Failed to send message: \(chatError.errorDescription ?? chatError.localizedDescription)"
            } else {
                errorMessage = "Failed to send message: \(error.localizedDescription)"
            }
            isLoading = false
        }
    }
    
    private func cancelCurrentRequest() {
        print("=== ENHANCED CANCEL DEBUG (Use Cases) ===")
        print("Cancel requested at: \(Date().timeIntervalSince1970)")
        print("Current task exists: \(currentTask != nil)")
        print("Is loading: \(isLoading)")
        
        currentTask?.cancel()
        currentTask = nil
        isLoading = false
        
        Task {
            do {
                // Still use API service for cancellation as it handles the low-level request cancellation
                let apiService = container.getSharedLLMAPIService()
                print("Calling apiService.cancelAllRequests()...")
                await apiService.cancelAllRequests()
                print("apiService.cancelAllRequests() completed")
                print("=== CANCEL DEBUG ===")
                print("All requests cancelled")
                print("===================")
            } catch {
                print("Failed to cancel requests: \(error)")
            }
        }
        
        print("===============================")
    }
    
    private func clearResponses() {
        responses.removeAll()
        errorMessage = nil
    }
}

// MARK: - Response Card View

private struct ResponseCardView: View {
    let instanceId: UUID
    let instanceName: String
    let responses: [LLMStreamingResponse]
    let isCompleted: Bool
    let error: ChatError?
    
    @Environment(\.colorScheme) var colorScheme
    
    private var accumulatedContent: String {
        responses.compactMap { response in
            switch response.responseType {
            case .contentDelta(let content):
                return content
            case .thinkingDelta(let content):
                return content
            default:
                return nil
            }
        }.joined()
    }
    
    private var completionInfo: CompletionInfo? {
        responses.compactMap { response in
            switch response.responseType {
            case .completionMarker(let info):
                return info
            default:
                return nil
            }
        }.first
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                MockInstanceLogoView(instanceId: instanceId, size: 20)
                
                Text(instanceName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                if let _ = error {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                } else if isCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(0.7)
                }
            }
            
            // Content
            if let error = error {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Error: \(error.errorDescription ?? error.localizedDescription)")
                        .font(.body)
                        .foregroundColor(.red)
                    
                    Text("Type: \(String(describing: error))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.top, 4)
                    
                    if error.isRetryable {
                        Text("💡 This error is retryable")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .padding(.top, 2)
                    }
                }
                .padding(8)
                .background(Color.red.opacity(0.1))
                .cornerRadius(6)
            } else {
                ScrollView {
                    Text(accumulatedContent.isEmpty ? "Waiting for response..." : accumulatedContent)
                        .font(.body)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(8)
                        .background(colorScheme == .light ? Color(.systemBackground) : Color(red: 0.08, green: 0.08, blue: 0.08))
                        .cornerRadius(6)
                }
                .frame(maxHeight: 200)
                
                // Completion Info
                if let completion = completionInfo {
                    HStack(spacing: 16) {
                        if let promptTokens = completion.promptTokens {
                            Label("\(promptTokens)", systemImage: "arrow.up")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let completionTokens = completion.completionTokens {
                            Label("\(completionTokens)", systemImage: "arrow.down")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let finishReason = completion.finishReason {
                            Text("• \(finishReason)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                }
            }
        }
        .padding()
        .background(colorScheme == .light ? Color(.systemBackground) : Color(red: 0.05, green: 0.05, blue: 0.05))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray4), lineWidth: 0.5)
        )
    }
}

// MARK: - Preview

#Preview("Debug View") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    DebugView()
        .environmentObject(container)
}
#endif