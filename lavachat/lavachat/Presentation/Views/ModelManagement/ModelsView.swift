import SwiftUI
import Foundation

/// View displaying a list of all LLM providers and instances, powered by ModelsViewModel
struct ModelsView: View {
    // MARK: - Properties
    
    @StateObject private var viewModel: ModelsViewModel
    @EnvironmentObject private var container: DIContainer // For DI if needed, or for preview setup
    @State private var showNewModelsSheet = false

    // MARK: - Initialization
    
    init(viewModel: ModelsViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                if viewModel.isLoading {
                    ProgressView("Loading Models...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let errorMessage = viewModel.errorMessage {
                    VStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.red)
                        Text("Error Loading Models")
                            .font(.headline)
                        Text(errorMessage)
                            .font(.caption)
                            .multilineTextAlignment(.center)
                            .padding()
                        Button("Retry") {
                            Task {
                                await viewModel.refreshData()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.filteredProviders.isEmpty && !viewModel.searchText.isEmpty {
                    VStack {
                        Image(systemName: "magnifyingglass")
                            .font(.largeTitle)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 8)
                        Text("No results for " + viewModel.searchText)
                            .font(.headline)
                        Text("Try searching for something else.")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.allProviders.isEmpty {
                     VStack {
                        Image(systemName: "tray.fill")
                            .font(.largeTitle)
                            .padding(.bottom)
                        Text("No Models Available")
                            .font(.headline)
                        Text("There are currently no AI models or providers configured in the app.")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        // Optionally, add a button to guide the user, e.g., to add a provider if that flow exists
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    modelListContentView
                }
            }
            .navigationTitle("Models")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                viewModel.showToolbarWithAnimation()
            }
            .toolbar(viewModel.toolbarVisibility, for: .tabBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showNewModelsSheet = true
                    }) {
                        Image(systemName: "plus")
                    }
                }
            }
            // Use the new searchable API that directly binds to text
            .searchable(text: $viewModel.searchText, prompt: Text("Search models, instances & providers"))
        }
        .onAppear {
            if viewModel.allProviders.isEmpty && !viewModel.isLoading && viewModel.errorMessage == nil {
                Task {
                    print("ModelsView.onAppear: allProviders is empty, not loading, no error. Triggering refreshData.")
                    await viewModel.refreshData()
                }
            }
        }
        .sheet(isPresented: $showNewModelsSheet) {
            NewModelsSheetView(
                viewModel: container.makeNewModelsSheetViewModel()
            )
            .environmentObject(container)
        }
    }
    
    // MARK: - UI Components (Refactored to use ViewModel)
    
    private var modelListContentView: some View {
        List {
            // Quick access section
            if viewModel.searchText.isEmpty {
                Section {
                    // Favorites entry
                    NavigationLink(destination: FavoritesView(viewModel: viewModel)
                        .onAppear {
                            viewModel.hideToolbar()
                        }) {
                        HStack {
                            Image(systemName: "star.fill")
                                .foregroundColor(.yellow)
                                .font(.title2)
                                .frame(width: 24, height: 24)
                            
                            Text("Favorites")
                                .foregroundColor(.primary)
                                .font(.body)
                            
                            Spacer()
                            // Display count if needed, e.g., Text("\(viewModel.favoritedInstances.count)")
                        }
                        .padding(.vertical, 4)
                    }
                    
                    // Instance Groups entry
                    NavigationLink(destination: InstanceGroupsView(viewModel: viewModel)
                        .onAppear {
                            viewModel.hideToolbar()
                        }) {
                        HStack {
                            Image(systemName: "rectangle.stack.fill")
                                .foregroundColor(.blue)
                                .font(.title2)
                                .frame(width: 24, height: 24)
                            
                            Text("Instance Groups")
                                .foregroundColor(.primary)
                                .font(.body)
                            
                            Spacer()
                            // Display count if needed, e.g., Text("\(viewModel.instanceGroups.count)")
                        }
                        .padding(.vertical, 4)
                    }
                }
                .listRowInsets(EdgeInsets(top: 4, leading: 20, bottom: 4, trailing: 20))
            }
            
            // Provider sections with their instances
            ForEach(viewModel.filteredProviders) { provider in
                Section(
                    header: providerSectionHeader(provider: provider)
                ) {
                    // Provider instances
                    if let instances = viewModel.filteredInstancesByProviderId[provider.id], !instances.isEmpty {
                        ForEach(instances) { instance in
                            instanceRow(instance: instance, provider: provider)
                        }
                    } else if !viewModel.searchText.isEmpty {
                        // If searching and no instances match for this provider
                        Text("No matching instances for '\(viewModel.searchText)' in \(provider.name)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                    } else {
                        // If not searching and provider has no instances (or all filtered out by other means)
                         Text("No instances available for \(provider.name).")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                    }
                }
                .listRowInsets(EdgeInsets(top: 4, leading: 20, bottom: 4, trailing: 20))
            }
        }        
        .listStyle(.plain)
    }
    
    /// Creates the provider section header with link to detail view
    @ViewBuilder
    private func providerSectionHeader(provider: LLMProvider) -> some View {
        NavigationLink(destination: ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: provider.id))
            .onAppear {
                viewModel.hideToolbar()
            }) {
            HStack {
                ProviderLogoView(provider: provider, size: 24)
                Text(provider.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                if provider.apiKeyStored {
                    Image(systemName: "key.fill")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary.opacity(0.7))
            }
            .padding(.top, 0)
            .padding(.bottom, 0)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// Creates a row for a single LLM instance
    @ViewBuilder
    private func instanceRow(instance: LLMInstance, provider: LLMProvider) -> some View {
        NavigationLink(destination: InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: instance.id, modelId: nil))
            .onAppear {
                viewModel.hideToolbar()
            }) {
            HStack(spacing: 12) {
                let model = viewModel.allModelsById[instance.modelId]
                InstanceLogoView(instance: instance, model: model, provider: provider, size: 24)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(instance.name)
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    if let model = model, !(instance.name.localizedCaseInsensitiveContains(model.name)) {
                        Text("Model: \(model.name)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                Spacer()
            }
            .padding(.vertical, 4)
        }
    }
}

// MARK: - Placeholder Views for Navigation

struct FavoritesView: View {
    @ObservedObject var viewModel: ModelsViewModel // To access favoritedInstances
    @EnvironmentObject private var container: DIContainer // For DI if needed, or for preview setup

    var body: some View {
        // Check if there are any favorited instances
        if viewModel.favoritedInstances.isEmpty {
            emptyFavoritesView
        } else {
            favoritedInstancesList
        }
    }

    private var favoritedInstancesList: some View {
        List(viewModel.favoritedInstances) { instance in
            // Simplified instance row, or reuse instanceRow if adaptable
            NavigationLink(destination: InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: instance.id, modelId: nil))
                .onAppear {
                    viewModel.hideToolbar()
                }) {
                HStack {
                    let model = viewModel.allModelsById[instance.modelId]
                    let provider = model != nil ? viewModel.allProvidersById[model!.providerId] : nil
                    
                    InstanceLogoView(instance: instance, model: model, provider: provider, size: 24)
                    
                    VStack(alignment: .leading) {
                        Text(instance.name).font(.headline)
                        if let modelName = model?.name {
                            Text("Model: \(modelName)")
                                .font(.caption)
                                .foregroundColor(.gray)
                            if let providerName = provider?.name {
                                Text("Provider: \(providerName)")
                                    .font(.caption2)
                                    .foregroundColor(.gray)
                            }
                        } else {
                            // Fallback if model info is not available
                            Text("Model ID: \(instance.modelId.uuidString.prefix(8))")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                }
            }
        }
        .navigationTitle("Favorites")
        .onAppear {
            viewModel.hideToolbar()
            // Favorites might also need their own filtering if a search bar is added here
        }
    }

    @ViewBuilder
    private var emptyFavoritesView: some View {
        VStack {
            Image(systemName: "star.slash.fill") // More specific icon for no favorites
                .font(.largeTitle)
                .foregroundColor(.secondary)
                .padding(.bottom, 8)
            Text("No Favorite Instances")
                .font(.headline)
            Text("You haven't marked any AI models as favorites yet.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .navigationTitle("Favorites") // Keep title consistent
    }
}

struct InstanceGroupsView: View {
    @ObservedObject var viewModel: ModelsViewModel // To access instanceGroups
    var body: some View {
        // Check if there are any instance groups
        if viewModel.instanceGroups.isEmpty {
            emptyGroupsView
        } else {
            instanceGroupsList
        }
    }

    private var instanceGroupsList: some View {
        List(viewModel.instanceGroups) { group in
            // Placeholder: NavigationLink to a GroupDetailView
            NavigationLink(destination: Text("Detail for Group: \(group.name)")) {
                HStack {
                    // GroupLogoView would be ideal here
                    Image(systemName: "rectangle.stack") // Placeholder icon
                        .foregroundColor(group.isFavorited ? .yellow : .blue) // Example: show favorite status
                    Text(group.name)
                    Spacer()
                    Text("\(group.instanceIds.count) instances")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .navigationTitle("Instance Groups")
        .onAppear {
            viewModel.hideToolbar()
        }
    }

    @ViewBuilder
    private var emptyGroupsView: some View {
        VStack {
            Image(systemName: "rectangle.stack.badge.minus") // More specific icon for no groups
                .font(.largeTitle)
                .foregroundColor(.secondary)
                .padding(.bottom, 8)
            Text("No Instance Groups")
                .font(.headline)
            Text("You haven't created any instance groups yet. Groups allow you to manage multiple AI models together.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .navigationTitle("Instance Groups") // Keep title consistent
    }
}

// MARK: - Preview Provider
#if DEBUG
struct ModelsView_Previews: PreviewProvider {
    static var previews: some View {
        let container = DIContainer(context: PersistenceController.preview.container.viewContext)
        ModelsView(viewModel: container.makeModelsViewModel()).environmentObject(container)
    }
}
#endif
