import SwiftUI

struct ParameterEditView: View {
    @Binding var parameterKey: String
    @Binding var parameterValue: String
    let isCreatingNew: Bool
    let thinkingCapabilities: ThinkingCapabilities?
    let searchingCapabilities: SearchingCapabilities?
    let onSave: (String, String) -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    // Dynamic parameter suggestions based on thinking capabilities
    private var commonParameters: [String] {
        var baseParameters = ["temperature", "top_p", "max_tokens", "presence_penalty", "frequency_penalty"]
        
        // Add thinking-related parameters based on capabilities
        if let capabilities = thinkingCapabilities,
           let controlType = capabilities.controlType,
           let parameterName = capabilities.parameterName,
           !parameterName.isEmpty {
            
            switch controlType {
            case .reasoningEffort, .thinkingBudget, .parameterBased, .promptBased:
                baseParameters.append(parameterName)
            default:
                break
            }
        }

        // Add searching-related parameters based on capabilities
        if let capabilities = searchingCapabilities,
           let controlType = capabilities.controlType,
           let parameterName = capabilities.parameterName,
           !parameterName.isEmpty {
            
            switch controlType {
            case .parameterBased:
                baseParameters.append(parameterName)
            default:
                break
            }
        }
        
        return baseParameters
    }
    
    var body: some View {
        Form {
            Section(header: Text("PARAMETER DETAILS")) {
                TextField("Parameter Name", text: $parameterKey)
                    .font(.body)
                    .textInputAutocapitalization(.never)
                    .disableAutocorrection(true)
                    .padding(.horizontal, 4)
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $parameterValue)
                        .textInputAutocapitalization(.never)
                        .disableAutocorrection(true)
                        .frame(minHeight: 100)
                        .cornerRadius(8)
                    
                    if parameterValue.isEmpty {
                        Text("Parameter Value")
                            .foregroundColor(Color(.systemGray3))
                            .padding(.horizontal, 4)
                            .padding(.vertical, 8)
                            .allowsHitTesting(false)
                    }
                }
                .padding(.vertical, 8)
            }
            
            Section {
                FlowLayout(horizontalSpacing: 10, verticalSpacing: 10) { // Adjust spacing as needed
                    ForEach(commonParameters, id: \.self) { param in
                        Button {
                            self.parameterKey = param
                        } label: {
                            Text(param)
                                .font(.callout) // Adjust font size if needed
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(parameterKey == param ? Color.accentColor : Color(.systemGray5))
                                .foregroundColor(parameterKey == param ? .white : .primary)
                                .cornerRadius(16)
                                .contentShape(RoundedRectangle(cornerRadius: 16))
                        }
                        .buttonStyle(.plain) // Keep button style plain
                    }
                }
                .padding(.vertical, 8)
                .listRowInsets(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                .listRowBackground(Color.clear)
            } header: {
                HStack(spacing: 8) {
                    Image(systemName: "lightbulb")
                        .foregroundColor(.accentColor)
                        .fontWeight(.bold)
                    Text("Suggested Parameters")
                        .foregroundColor(.primary)
                }
                .listRowInsets(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
            } footer: {
                Text(footerText)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .navigationTitle(isCreatingNew ? "Add Parameter" : "Edit Parameter")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    guard !parameterKey.isEmpty else { return }
                    onSave(parameterKey, parameterValue)
                }
                .disabled(parameterKey.isEmpty)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var footerText: String {
        var baseText = "Please double-check the Provider's API reference for the proper names and values. Adding incorrect parameters may lead to unexpected behavior."
        baseText += "\n\nSupport JSON & array, e.g. `{\"key\": \"value\"}` or `[1, 2, 3]`."

        // Add thinking-specific explanations
        if let capabilities = thinkingCapabilities,
           let controlType = capabilities.controlType,
           let parameterName = capabilities.parameterName,
           !parameterName.isEmpty {
            
            let thinkingExplanation: String
            switch controlType {
            case .reasoningEffort:
                thinkingExplanation = "\n\nBased on model's thinking capabilities, we recommend adding the `\(parameterName)` parameter. This controls reasoning intensity (e.g., 'low', 'medium', 'high'). Toggle at Chat page will leverage this parameter. If not configured, the thinking functionality may not work properly."
            case .thinkingBudget:
                thinkingExplanation = "\n\nBased on model's thinking capabilities, we recommend adding the `\(parameterName)` parameter. This sets the maximum tokens for the thinking process. Toggle at Chat page will leverage this parameter. If not configured, the thinking functionality may not work properly."
            case .parameterBased:
                thinkingExplanation = "\n\nBased on model's thinking capabilities, we recommend adding the `\(parameterName)` parameter. This controls the thinking process. Toggle at Chat page will leverage this parameter. If not configured, the thinking functionality may not work properly."
            case .promptBased:
                thinkingExplanation = "\n\nBased on model's thinking capabilities, we recommend adding the `\(parameterName)` parameter. This provides a custom prompt for thinking that the app automatically includes in every request. Toggle at Chat page will leverage this parameter. If not configured, the thinking functionality may not work properly."
            default:
                thinkingExplanation = ""
            }
            
            baseText += thinkingExplanation
        }

        // Add searching-specific explanations
        if let capabilities = searchingCapabilities,
           let controlType = capabilities.controlType,
           let parameterName = capabilities.parameterName,
           !parameterName.isEmpty {
            let searchingExplanation: String
            switch controlType {
            case .parameterBased:
                searchingExplanation = "\n\nBased on model's searching capabilities, we recommend adding the `\(parameterName)` parameter. This controls the searching process. Toggle at Chat page will leverage this parameter. If not configured, the searching functionality may not work properly."
            default:
                searchingExplanation = ""
            }

            baseText += searchingExplanation
        }
        
        return baseText
    }
}

// MARK: - Preview Provider

#if DEBUG
#Preview("Parameter Edit View") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ParameterEditView(
            parameterKey: .constant("temperature"),
            parameterValue: .constant("0.7"),
            isCreatingNew: false,
            thinkingCapabilities: nil,
            searchingCapabilities: nil,
            onSave: { _, _ in }
        )
    }.environmentObject(container)
}
#endif
