import SwiftUI

/// Selection view for configuring searching capabilities
struct SearchingCapabilitiesSelectionView: View {
    @Binding var capabilities: SearchingCapabilities
    
    // Temporary state for editing
    @State private var selectedControlType: SearchingCapabilities.ControlType
    @State private var parameterName: String = ""
    
    init(capabilities: Binding<SearchingCapabilities>) {
        self._capabilities = capabilities
        // Initialize temporary state from capabilities
        self._selectedControlType = State(initialValue: capabilities.wrappedValue.controlType ?? .none)
        
        // Initialize parameter name if available
        self._parameterName = State(initialValue: capabilities.wrappedValue.parameterName ?? "")
    }
    
    var body: some View {
        SelectionView(
            title: "Searching Capabilities",
            selection: $capabilities,
            contentBuilder: { tempSelection in
                List {
                    // Control Type Selection
                    Section(header: Text("CONTROL TYPE")) {
                        ForEach(SearchingCapabilities.ControlType.allCases, id: \.self) { type in
                            Button(action: {
                                selectedControlType = type
                                updateCapabilities(in: tempSelection)
                            }) {
                                HStack {
                                    Text(controlTypeTitle(type))
                                    
                                    Spacer()
                                    
                                    if selectedControlType == type {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentColor)
                                    }
                                }
                            }
                            .foregroundColor(.primary)
                        }
                    }
                    
                    // Dynamic Section based on selected Control Type
                    if selectedControlType == .parameterBased {
                        parameterBasedSection(tempSelection)
                    } else if selectedControlType == .appApi {
                        appApiSection(tempSelection)
                    }
                }
            },
            onSave: { newCapabilities in
                // No special processing needed for searching capabilities
            }
        )
    }
    
    // MARK: - Dynamic Sections
    
    @ViewBuilder
    private func parameterBasedSection(_ tempSelection: Binding<SearchingCapabilities>) -> some View {
        Section {
            HStack {
                Text("Parameter Name")
                Spacer()
                TextField("tools", text: $parameterName)
                    .multilineTextAlignment(.trailing)
                    .onChange(of: parameterName) { _ in
                        updateCapabilities(in: tempSelection)
                    }
            }
        } header: {
            Text("PARAMETER CONFIGURATION")
        } footer: {
            Text("This is the API parameter for search control. When using instance with this model, the app will send this parameter to the API to enable/disable search functionality. Please refer to your provider and model API documentation to set the correct parameter name and values.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private func appApiSection(_ tempSelection: Binding<SearchingCapabilities>) -> some View {
        Section {
            Text("App-managed Search")
                .foregroundColor(.secondary)
        } header: {
            Text("APP API CONFIGURATION")
        } footer: {
            Text("Search functionality is managed by the app using its own search APIs and services. No additional configuration is required.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Helper Methods
    
    private func controlTypeTitle(_ type: SearchingCapabilities.ControlType) -> String {
        switch type {
        case .none:
            return "Not Supported"
        case .parameterBased:
            return "Parameter Based"
        case .appApi:
            return "App API"
        }
    }
    
    private func updateCapabilities(in tempSelection: Binding<SearchingCapabilities>) {
        var updatedCapabilities = SearchingCapabilities(controlType: selectedControlType)
        
        switch selectedControlType {
        case .parameterBased:
            updatedCapabilities.parameterName = parameterName.isEmpty ? "tools" : parameterName
        case .appApi:
            // No additional configuration needed for app API
            break
        default:
            break
        }
        
        tempSelection.wrappedValue = updatedCapabilities
    }
}

// MARK: - Preview Provider

struct SearchingCapabilitiesSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview 1: Searching Capabilities (None)
            NavigationStack {
                SearchingCapabilitiesSelectionView(
                    capabilities: .constant(SearchingCapabilities(controlType: .none))
                )
            }
            .previewDisplayName("Searching (None)")
            
            // Preview 2: Searching Capabilities (Parameter Based)
            NavigationStack {
                SearchingCapabilitiesSelectionView(
                    capabilities: .constant(
                        SearchingCapabilities(
                            controlType: .parameterBased,
                            parameterName: "tools"
                        )
                    )
                )
            }
            .previewDisplayName("Searching (Parameter)")
            
            // Preview 3: Searching Capabilities (App API)
            NavigationStack {
                SearchingCapabilitiesSelectionView(
                    capabilities: .constant(
                        SearchingCapabilities(
                            controlType: .appApi
                        )
                    )
                )
            }
            .previewDisplayName("Searching (App API)")
        }
    }
} 