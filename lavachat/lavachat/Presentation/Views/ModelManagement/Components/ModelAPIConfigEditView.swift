import SwiftUI

/// View for editing a single ModelAPIConfig object
struct ModelAPIConfigEditView: View {
    // MARK: - Properties
    
    /// The API configuration being edited
    @Binding var apiConfigToEdit: ModelAPIConfig
    
    /// Model's input modalities for validation
    let modelInputModalities: [ModelModality]
    
    /// Model's output modalities for validation
    let modelOutputModalities: [ModelModality]
    
    /// Whether we're creating a new config or editing an existing one
    let isCreatingNew: Bool
    
    /// Callback when saving is successful
    let onSave: () -> Void
    
    /// For dismissing the view
    @Environment(\.dismiss) private var dismiss
    
    /// Temporary config for editing
    @State private var tempApiConfig: ModelAPIConfig
    
    /// Alert state
    @State private var showAlert: Bool = false
    @State private var alertTitle: String = ""
    @State private var alertMessage: String = ""
    
    // MARK: - Initialization
    
    init(
        apiConfig: Binding<ModelAPIConfig>,
        modelInputModalities: [ModelModality],
        modelOutputModalities: [ModelModality],
        isCreatingNew: Bool,
        onSave: @escaping () -> Void
    ) {
        self._apiConfigToEdit = apiConfig
        self.modelInputModalities = modelInputModalities
        self.modelOutputModalities = modelOutputModalities
        self.isCreatingNew = isCreatingNew
        self.onSave = onSave
        
        // Initialize temporary state with the current value
        self._tempApiConfig = State(initialValue: apiConfig.wrappedValue)
    }
    
    // MARK: - Body
    
    var body: some View {
        Form {
            Section {
                // API Style Picker
                Picker("API Style", selection: $tempApiConfig.apiStyle) {
                    ForEach(APIStyle.allCases, id: \.self) { style in
                        Text(style.displayName).tag(style)
                    }
                }
                .pickerStyle(.menu)
                
                // API Endpoint Path Field
                TextField("API Endpoint Path", text: $tempApiConfig.apiEndpointPath)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .font(.system(.body, design: .monospaced))
            } footer: {
                VStack(alignment: .leading, spacing: 8) {
                    if let description = tempApiConfig.apiStyle.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("Important: Ensure the model's declared input/output modalities are compatible with the selected API style. For example, 'OpenAI Image Generation' requires text input and image output from the model.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 4)
            }
        }
        .navigationTitle(isCreatingNew ? "Add API Configuration" : "Edit API Configuration")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            // Leading item (Cancel)
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Cancel") {
                    dismiss()
                }
            }
            
            // Trailing item (Save)
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    validateAndSave()
                }
                .disabled(tempApiConfig.apiEndpointPath.isEmpty)
            }
        }
        .alert(alertTitle, isPresented: $showAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - Methods
    
    /// Validates the configuration and saves if valid
    private func validateAndSave() {
        let currentStyle = tempApiConfig.apiStyle
        var isValid = true
        var validationMessage = ""
        
        // Validate API endpoint path is not empty
        if tempApiConfig.apiEndpointPath.isEmpty {
            isValid = false
            validationMessage = "API endpoint path cannot be empty."
        }
        
        // Validate modalities based on API style
        switch currentStyle {
        case .openaiImageGeneration:
            let requiredInputs: Set<ModelModality> = [.text]
            let requiredOutputs: Set<ModelModality> = [.image]
            
            if !requiredInputs.isSubset(of: Set(modelInputModalities)) || !requiredOutputs.isSubset(of: Set(modelOutputModalities)) {
                isValid = false
                validationMessage = "OpenAI Image Generation API requires the model to support text input and image output."
            }
            
        case .openaiImageEdit:
            let requiredInputs: Set<ModelModality> = [.text, .image]
            let requiredOutputs: Set<ModelModality> = [.image]
            
            if !requiredInputs.isSubset(of: Set(modelInputModalities)) || !requiredOutputs.isSubset(of: Set(modelOutputModalities)) {
                isValid = false
                validationMessage = "OpenAI Image Edit API requires the model to support text and image input, and image output."
            }
            
        default:
            // No specific modality validation for other API styles in this implementation
            break
        }
        
        if isValid {
            // Update the binding and save
            apiConfigToEdit = tempApiConfig
            onSave()
            dismiss()
        } else {
            // Show validation error
            alertTitle = "Validation Error"
            alertMessage = validationMessage
            showAlert = true
        }
    }
}

// MARK: - Preview Provider

#if DEBUG
#Preview("Edit API Config") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    
    // Sample config for preview
    let sampleConfig = ModelAPIConfig(
        apiStyle: .openaiCompatible,
        apiEndpointPath: "/v1/chat/completions"
    )
    
    return NavigationStack {
        ModelAPIConfigEditView(
            apiConfig: .constant(sampleConfig),
            modelInputModalities: [.text, .image],
            modelOutputModalities: [.text],
            isCreatingNew: false,
            onSave: {}
        )
    }.environmentObject(container)
}

#Preview("Create API Config") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    
    // Empty config for new creation
    let emptyConfig = ModelAPIConfig(
        apiStyle: .openaiCompatible,
        apiEndpointPath: ""
    )
    
    return NavigationStack {
        ModelAPIConfigEditView(
            apiConfig: .constant(emptyConfig),
            modelInputModalities: [.text, .image],
            modelOutputModalities: [.text, .image],
            isCreatingNew: true,
            onSave: {}
        )
    }.environmentObject(container)
}
#endif
