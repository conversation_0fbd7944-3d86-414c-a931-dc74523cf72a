import SwiftUI

/// Selection view for configuring thinking capabilities
struct ThinkingCapabilitiesSelectionView: View {
    @Binding var capabilities: ThinkingCapabilities
    
    // Temporary state for editing
    @State private var selectedControlType: ThinkingCapabilities.ControlType
    @State private var parameterOptions: [String] = []
    @State private var customOption: String = ""
    @State private var maxBudget: String = ""
    @State private var parameterName: String = ""
    
    init(capabilities: Binding<ThinkingCapabilities>) {
        self._capabilities = capabilities
        // Initialize temporary state from capabilities
        self._selectedControlType = State(initialValue: capabilities.wrappedValue.controlType ?? .none)
        
        // Initialize parameter options if available
        if let options = capabilities.wrappedValue.parameterOptions {
            self._parameterOptions = State(initialValue: options)
        }
        
        // Initialize max budget if available
        if let budget = capabilities.wrappedValue.maxBudget {
            self._maxBudget = State(initialValue: String(budget))
        }
        
        // Initialize parameter name if available
        self._parameterName = State(initialValue: capabilities.wrappedValue.parameterName ?? "")
    }
    
    var body: some View {
        SelectionView(
            title: "Thinking Capabilities",
            selection: $capabilities,
            contentBuilder: { tempSelection in
                List {
                    // Control Type Selection
                    Section(header: Text("CONTROL TYPE")) {
                        ForEach(ThinkingCapabilities.ControlType.allCases, id: \.self) { type in
                            Button(action: {
                                selectedControlType = type
                                updateCapabilities(in: tempSelection)
                            }) {
                                HStack {
                                    Text(controlTypeTitle(type))
                                    
                                    Spacer()
                                    
                                    if selectedControlType == type {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentColor)
                                    }
                                }
                            }
                            .foregroundColor(.primary)
                        }
                    }
                    
                    // Dynamic Section based on selected Control Type
                    if selectedControlType == .reasoningEffort {
                        reasoningEffortSection(tempSelection)
                    } else if selectedControlType == .thinkingBudget {
                        thinkingBudgetSection(tempSelection)
                    } else if selectedControlType == .parameterBased {
                        parameterBasedSection(tempSelection)
                    } else if selectedControlType == .promptBased {
                        promptBasedSection(tempSelection)
                    }
                }
            },
            onSave: { newCapabilities in
                if newCapabilities.controlType == .reasoningEffort {
                    if newCapabilities.parameterOptions == nil || newCapabilities.parameterOptions?.isEmpty == true {
                        newCapabilities.parameterOptions = ["low"]
                    }
                }
            }
        )
    }
    
    // MARK: - Dynamic Sections
    
    @ViewBuilder
    private func reasoningEffortSection(_ tempSelection: Binding<ThinkingCapabilities>) -> some View {
        Section {
            HStack {
                Text("Parameter Name")
                Spacer()
                TextField("reasoning_effort", text: $parameterName)
                    .multilineTextAlignment(.trailing)
                    .onChange(of: parameterName) { _ in
                        updateCapabilities(in: tempSelection)
                    }
            }
        } header: {
            Text("PARAMETER CONFIGURATION")
        } footer: {
            Text("This is the API parameter for reasoning effort control. When using instance with this model, the app will send this parameter to the API to enable/disable thinking functionality. Please refer to your provider and model API documentation to set the correct parameter name and values.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        
        Section {
            // Existing options
            ForEach(parameterOptions, id: \.self) { option in
                HStack {
                    Text(option)
                    Spacer()
                    Button(action: {
                        parameterOptions.removeAll { $0 == option }
                        updateCapabilities(in: tempSelection)
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .foregroundColor(.red)
                    }
                }
            }
            
            // Add new option
            HStack {
                TextField("Add custom option", text: $customOption)
                Button(action: {
                    if !customOption.isEmpty && !parameterOptions.contains(customOption) {
                        parameterOptions.append(customOption)
                        customOption = ""
                        updateCapabilities(in: tempSelection)
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.accentColor)
                }
                .disabled(customOption.isEmpty)
            }
        } header: {
            Text("REASONING EFFORT OPTIONS")
        } footer: {
            Text("Default parameter option if not specified: [\"low\"].")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private func thinkingBudgetSection(_ tempSelection: Binding<ThinkingCapabilities>) -> some View {
        Section {
            HStack {
                Text("Parameter Name")
                Spacer()
                TextField("thinkingBudget", text: $parameterName)
                    .multilineTextAlignment(.trailing)
                    .onChange(of: parameterName) { _ in
                        updateCapabilities(in: tempSelection)
                    }
            }
        } header: {
            Text("PARAMETER CONFIGURATION")
        } footer: {
            Text("This is the API parameter for thinking budget control. When using instance with this model, the app will send this parameter to the API to set the maximum tokens for the thinking process. Please refer to your provider and model API documentation to set the correct parameter values.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        
        Section {
            HStack {
                Text("Maximum Budget")
                Spacer()
                TextField("Token Budget", text: $maxBudget)
                    .keyboardType(.numberPad)
                    .multilineTextAlignment(.trailing)
                    .onChange(of: maxBudget) { _ in
                        updateCapabilities(in: tempSelection)
                    }
            }
        } header: {
            Text("BUDGET CONFIGURATION")
        } footer: {
            Text("No default maximum budget if not specified. Normally the maximum tokens of the model is used.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    @ViewBuilder
    private func parameterBasedSection(_ tempSelection: Binding<ThinkingCapabilities>) -> some View {
        Section {
            HStack {
                Text("Parameter Name")
                Spacer()
                TextField("thinking", text: $parameterName)
                    .multilineTextAlignment(.trailing)
                    .onChange(of: parameterName) { _ in
                        updateCapabilities(in: tempSelection)
                    }
            }
        } header: {
            Text("PARAMETER CONFIGURATION")
        } footer: {
            Text("This is the API parameter for parameter-based thinking control. When using instance with this model, the app will send this parameter to the API to enable/disable thinking functionality. Please refer to your provider and model API documentation to set the correct parameter name and values.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    
    @ViewBuilder
    private func promptBasedSection(_ tempSelection: Binding<ThinkingCapabilities>) -> some View {
        Section {
            HStack {
                Text("Parameter Name")
                Spacer()
                TextField("thinking_prompt", text: $parameterName)
                    .multilineTextAlignment(.trailing)
                    .onChange(of: parameterName) { _ in
                        updateCapabilities(in: tempSelection)
                    }
            }
        } header: {
            Text("PARAMETER CONFIGURATION")
        } footer: {
            Text("For models that control thinking through prompt content (e.g., Anthropic), the app will automatically read this parameter value from your instance settings and include it in the prompt to enable/disable thinking functionality.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Helper Methods
    
    private func controlTypeTitle(_ type: ThinkingCapabilities.ControlType) -> String {
        switch type {
        case .none:
            return "Not Supported"
        case .defaultOn:
            return "Always On"
        case .reasoningEffort:
            return "Configurable Reasoning"
        case .thinkingBudget:
            return "Budget Based"
        case .parameterBased:
            return "Parameter Based"
        case .promptBased:
            return "Prompt Based"
        }
    }
    
    private func updateCapabilities(in tempSelection: Binding<ThinkingCapabilities>) {
        var updatedCapabilities = ThinkingCapabilities(controlType: selectedControlType)
        
        switch selectedControlType {
        case .reasoningEffort:
            updatedCapabilities.parameterName = parameterName.isEmpty ? "reasoning_effort" : parameterName
            updatedCapabilities.parameterOptions = parameterOptions.isEmpty ? nil : parameterOptions
        case .thinkingBudget:
            updatedCapabilities.parameterName = parameterName.isEmpty ? "thinkingBudget" : parameterName
            if let budget = Int64(maxBudget) {
                updatedCapabilities.maxBudget = budget
            }
        case .parameterBased:
            updatedCapabilities.parameterName = parameterName.isEmpty ? "thinking" : parameterName
        case .promptBased:
            updatedCapabilities.parameterName = parameterName.isEmpty ? "thinking_prompt" : parameterName
        default:
            break
        }
        
        tempSelection.wrappedValue = updatedCapabilities
    }
}

// MARK: - Preview Provider

struct ThinkingCapabilitiesSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview 1: Thinking Capabilities (None)
            NavigationStack {
                ThinkingCapabilitiesSelectionView(
                    capabilities: .constant(ThinkingCapabilities(controlType: .none))
                )
            }
            .previewDisplayName("Thinking (None)")
            
            // Preview 2: Thinking Capabilities (Reasoning Effort)
            NavigationStack {
                ThinkingCapabilitiesSelectionView(
                    capabilities: .constant(
                        ThinkingCapabilities(
                            controlType: .reasoningEffort,
                            parameterName: "reasoning_effort",
                            parameterOptions: ["low", "medium", "high"]
                        )
                    )
                )
            }
            .previewDisplayName("Thinking (Reasoning)")
            
            // Preview 3: Thinking Capabilities (Budget Based)
            NavigationStack {
                ThinkingCapabilitiesSelectionView(
                    capabilities: .constant(
                        ThinkingCapabilities(
                            controlType: .thinkingBudget,
                            parameterName: "thinkingBudget",
                            maxBudget: 10000
                        )
                    )
                )
            }
            .previewDisplayName("Thinking (Budget)")
        }
    }
}
