import SwiftUI
import PhotosUI // Added for PhotosPicker

/// View for displaying, editing, and creating LLM model details
struct ModelDetailView: View {
    // MARK: - Properties
    
    @EnvironmentObject private var container: DIContainer
    @StateObject private var viewModel: ModelDetailViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingDeleteConfirmation: Bool = false
    @State private var navigateToInputModalities: Bool = false
    @State private var navigateToOutputModalities: Bool = false
    @State private var navigateToThinkingCapabilities: Bool = false
    @State private var navigateToSearchingCapabilities: Bool = false
    @State private var selectedLogoItem: PhotosPickerItem? = nil
    
    // State for API Config editing
    @State private var showingAPIConfigEditView = false
    @State private var editingAPIConfigIndex: Int? = nil
    @State private var currentAPIConfigToEdit = ModelAPIConfig(apiStyle: .openaiCompatible, apiEndpointPath: "")
    
    // MARK: - Initialization
    
    /// Initialize with a view model
    init(viewModel: ModelDetailViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        Form {
            // Header section with logo and name
            Section {
                if viewModel.isEditing {
                    // Editable Logo Section
                    HStack {
                        Text("Logo")
                        Spacer()
                        PhotosPicker(
                            selection: $selectedLogoItem,
                            matching: .images, // We only want images
                            photoLibrary: .shared() // Use the shared photo library
                        ) {
                            // Display current logo or placeholder, making the whole area tappable
                            ModelLogoView(model: viewModel.editableModel, provider: viewModel.associatedProvider, size: 48)
                        }
                        .buttonStyle(.plain) // Ensure it looks like a button only where the image is
                    }
                    .onChange(of: selectedLogoItem) { newItem in
                        Task {
                            if let data = try? await newItem?.loadTransferable(type: Data.self) {
                                viewModel.editableModel.customLogoData = data
                                // If a custom logo is set, you might want to clear any default logo name
                                // to ensure the custom one always takes precedence.
                                // viewModel.editableModel.logoImageName = nil
                            }
                        }
                    }

                    HStack {
                        Text("Name")
                        Spacer()
                        TextField("Model Name", text: $viewModel.editableModel.name)
                            .multilineTextAlignment(.trailing)
                            .font(.headline)
                    }
                    
                    HStack {
                        Text("Description")
                        Spacer()
                        TextField("Model Description", text: Binding(
                            get: { viewModel.editableModel.modelDescription ?? "" },
                            set: { 
                                var model = viewModel.editableModel
                                model.modelDescription = $0.isEmpty ? nil : $0
                                viewModel.editableModel = model
                            }
                        ))
                        .multilineTextAlignment(.trailing)
                    }
                    
                } else {
                    // Display-only model name and description
                    HStack {
                        // Model logo - Prioritize customLogoData
                        ModelLogoView(model: viewModel.editableModel, provider: viewModel.associatedProvider, size: 48)
                            .padding(.trailing, 16)
                        
                        // Model name and description
                        VStack(alignment: .leading, spacing: 4) {
                            Text(viewModel.editableModel.name)
                                .font(.headline)
                                .lineLimit(1)
                            
                            if let description = viewModel.editableModel.modelDescription, !description.isEmpty {
                                Text(description)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .lineLimit(2)
                            }
                        }
                    }
                    .padding(.vertical, -4)
                    .listRowBackground(Color.clear)
                }
            }
            
            // Core Information section
            Section {
                // Provider link
                if let provider = viewModel.associatedProvider {
                    NavigationLink(destination: ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: provider.id))) {
                        HStack {
                            Text("Provider")
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            ProviderLogoView(provider: provider, size: 20)
                                .padding(.trailing, 8)
                            
                            Text(provider.name)
                                .foregroundColor(.secondary)
                        }
                    }
                    .disabled(viewModel.isEditing)
                }
                
                // Model identifier
                HStack {
                    Text("Identifier")
                    Spacer()
                    if viewModel.isEditing {
                        TextField("Model Identifier", text: $viewModel.editableModel.modelIdentifier)
                            .multilineTextAlignment(.trailing)
                            .autocorrectionDisabled()
                            .textInputAutocapitalization(.never)
                            .disableAutocorrection(true)
                    } else {
                        Text(viewModel.editableModel.modelIdentifier)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Pricing info
                HStack {
                    Text("Pricing")
                    Spacer()
                    if viewModel.isEditing {
                        TextEditor(text: Binding(
                            get: { viewModel.editableModel.pricingInfo ?? "" },
                            set: { 
                                var model = viewModel.editableModel
                                model.pricingInfo = $0.isEmpty ? nil : $0
                                viewModel.editableModel = model
                            }
                        ))
                        .frame(minHeight: 60)
                        .multilineTextAlignment(.trailing)
                    } else {
                        Text(viewModel.editableModel.pricingInfo ?? "Not specified")
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.trailing)
                    }
                }
            }
            
            // Capabilities section
            Section(header: Text("CAPABILITIES")) {
                // Input Modalities
                HStack(alignment: .center) {
                    Text("Input Modalities")
                    Spacer()
                    
                    HStack(alignment: .center) {
                        ForEach(viewModel.editableModel.inputModalities, id: \.self) { modality in
                            Image(systemName: modalityIcon(for: modality))
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if viewModel.isEditing {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary.opacity(0.7))
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if viewModel.isEditing {
                        navigateToInputModalities = true
                    }
                }

                // Output Modalities
                HStack(alignment: .center) {
                    Text("Output Modalities")
                    Spacer()
                    
                    HStack(alignment: .center) {
                        ForEach(viewModel.editableModel.outputModalities, id: \.self) { modality in
                            Image(systemName: modalityIcon(for: modality))
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if viewModel.isEditing {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary.opacity(0.7))
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if viewModel.isEditing {
                        navigateToOutputModalities = true
                    }
                }
                
                // Context window
                HStack {
                    Text("Context Window")
                    Spacer()
                    if viewModel.isEditing {
                        TextField("Token Count", text: Binding(
                            get: { 
                                if let size = viewModel.editableModel.contextWindowSize {
                                    return "\(size)"
                                }
                                return ""
                            },
                            set: { 
                                var model = viewModel.editableModel
                                if let value = Int64($0) {
                                    model.contextWindowSize = value
                                } else if $0.isEmpty {
                                    model.contextWindowSize = nil
                                }
                                viewModel.editableModel = model
                            }
                        ))
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.trailing)
                    } else if let contextSize = viewModel.editableModel.contextWindowSize {
                        Text(formatTokenCount(contextSize))
                            .foregroundColor(.secondary)
                    } else {
                        Text("Not specified")
                            .foregroundColor(.secondary)
                    }
                }
                
                // Max output tokens
                HStack {
                    Text("Max Output")
                    Spacer()
                    if viewModel.isEditing {
                        TextField("Token Count", text: Binding(
                            get: { 
                                if let maxTokens = viewModel.editableModel.maxOutputTokens {
                                    return "\(maxTokens)"
                                }
                                return ""
                            },
                            set: { 
                                var model = viewModel.editableModel
                                if let value = Int64($0) {
                                    model.maxOutputTokens = value
                                } else if $0.isEmpty {
                                    model.maxOutputTokens = nil
                                }
                                viewModel.editableModel = model
                            }
                        ))
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.trailing)
                    } else if let maxTokens = viewModel.editableModel.maxOutputTokens {
                        Text(formatTokenCount(maxTokens))
                            .foregroundColor(.secondary)
                    } else {
                        Text("Not specified")
                            .foregroundColor(.secondary)
                    }
                }
                
                // Thinking capabilities
                HStack {
                    Text("Thinking")
                    Spacer()
                    Text(thinkingCapabilitiesDescription())
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.trailing)
                    
                    if viewModel.isEditing {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary.opacity(0.7))
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if viewModel.isEditing {
                        navigateToThinkingCapabilities = true
                    }
                }
                
                // Searching capabilities
                HStack {
                    Text("Searching")
                    Spacer()
                    Text(searchingCapabilitiesDescription())
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.trailing)
                    
                    if viewModel.isEditing {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary.opacity(0.7))
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if viewModel.isEditing {
                        navigateToSearchingCapabilities = true
                    }
                }
            }

            // API Configuration Overrides Section
            if (viewModel.editableModel.apiConfigsOverride != nil && !viewModel.editableModel.apiConfigsOverride!.isEmpty) || viewModel.isEditing {
                Section {
                    if let configs = viewModel.editableModel.apiConfigsOverride, !configs.isEmpty {
                        ForEach(configs.indices, id: \.self) { index in
                            let apiConfig = configs[index]
                            Button {
                                if viewModel.isEditing {
                                    editingAPIConfigIndex = index
                                    currentAPIConfigToEdit = apiConfig
                                    showingAPIConfigEditView = true
                                }
                            } label: {
                                HStack {
                                    VStack(alignment: .leading) {
                                        Text(apiConfig.apiStyle.displayName)
                                            .foregroundColor(.primary)
                                        Text(apiConfig.apiEndpointPath)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    Spacer()
                                    if viewModel.isEditing {
                                        Image(systemName: "chevron.right")
                                            .font(.caption)
                                            .foregroundColor(.secondary.opacity(0.7))
                                    }
                                }
                            }
                            .buttonStyle(.plain) // Ensure the button doesn't have default styling interfering
                            .disabled(!viewModel.isEditing) // Disable interaction when not editing
                        }
                        .onDelete(perform: viewModel.isEditing ? { indexSet in
                            viewModel.editableModel.apiConfigsOverride?.remove(atOffsets: indexSet)
                        } : nil)
                    } else {
                        Text("No overrides. Uses Provider default.")
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                    }
                } header: {
                    apiConfigOverridesHeader
                } footer: {
                    Text("First matching config is used. Falls back to provider default.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Delete Model Section (conditionally displayed)
            if viewModel.isEditing && viewModel.editableModel.isUserCreated && !viewModel.isCreatingNewModel {
                Section {
                    Button(role: .destructive) {
                        showingDeleteConfirmation = true
                    } label: {
                        HStack {
                            Text("Delete Model")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                    .contentShape(Rectangle()) // Make the whole row tappable
                }
            }

            // Auto-create default instance option (only for new models)
            if viewModel.isCreatingNewModel && viewModel.isEditing {
                Section {
                    HStack {
                        Toggle("Auto-create default instance", isOn: $viewModel.shouldAutoCreateDefaultInstance)
                    }
                } footer: {
                    Text("Automatically create a default instance with the same name as the model when saving.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Available instances section
            if !viewModel.isCreatingNewModel {
                Section(header: listHeader) {
                    if viewModel.associatedInstances.isEmpty {
                        Text("No instances available")
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                    } else {
                        ForEach(viewModel.associatedInstances) { instance in
                            NavigationLink(destination: InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: instance.id, modelId: nil))) {
                                HStack(spacing: 12) {
                                    // Instance logo
                                    InstanceLogoView(instance: instance, model: viewModel.editableModel, provider: viewModel.associatedProvider, size: 24)
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(instance.name)
                                            .font(.body)
                                        
                                        // Show favorited status if applicable
                                        if instance.isFavorited {
                                            Text("Favorited")
                                                .font(.caption)
                                                .foregroundColor(.yellow)
                                        }
                                    }
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                }
            }
        }
        .onAppear {
            if let modelId = viewModel.model?.id, !viewModel.isCreatingNewModel {
                Task {
                    await viewModel.loadAssociatedInstances(modelId: modelId)
                }
            }
        }
        .navigationTitle(navigationTitle)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(viewModel.isEditing)
        .toolbar {
            // Leading items (Cancel for creation mode or edit mode)
            ToolbarItem(placement: .navigationBarLeading) {
                if viewModel.isCreatingNewModel || (viewModel.isEditing && !viewModel.isCreatingNewModel) {
                    Button("Cancel") {
                        if viewModel.isCreatingNewModel {
                            // Navigate back if creating a new model
                            dismiss()
                        } else {
                            // Exit edit mode and revert changes
                            viewModel.cancelEditing()
                        }
                    }
                }
            }
            
            // Trailing items (Edit/Done/Save)
            ToolbarItem(placement: .navigationBarTrailing) {
                if viewModel.isCreatingNewModel {
                    // Creation mode - Save button
                    Button("Save") {
                        Task {
                            await viewModel.saveChanges()
                            // If save was successful (no error), dismiss
                            if viewModel.errorMessage == nil {
                                dismiss()
                            }
                        }
                    }
                    .disabled(viewModel.isLoading)
                } else if viewModel.isEditing {
                    // Edit mode - Done button
                    Button("Done") {
                        Task {
                            await viewModel.saveChanges()
                        }
                    }
                    .disabled(viewModel.isLoading)
                } else {
                    // View mode - Edit button
                    Button("Edit") {
                        viewModel.prepareForEditing()
                    }
                }
            }
        }
        .overlay {
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.2))
                    .ignoresSafeArea()
            }
        }
        .alert("Error", isPresented: Binding(
            get: { viewModel.errorMessage != nil },
            set: { if !$0 { viewModel.errorMessage = nil } }
        )) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("Delete Model", isPresented: $showingDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                Task {
                    await viewModel.deleteModel()
                    // If deletion is successful (no error message), dismiss the view
                    if viewModel.errorMessage == nil {
                        dismiss()
                    }
                }
            }
        } message: {
            Text("Are you sure you want to delete this model? This action cannot be undone.")
        }
        .navigationDestination(isPresented: $navigateToInputModalities) {
            ModalitySelectionView(
                selectedModalities: Binding(
                    get: { viewModel.editableModel.inputModalities },
                    set: { 
                        var model = viewModel.editableModel
                        model.inputModalities = $0
                        viewModel.editableModel = model
                    }
                ),
                title: "Input Modalities"
            )
        }
        .navigationDestination(isPresented: $navigateToOutputModalities) {
            ModalitySelectionView(
                selectedModalities: Binding(
                    get: { viewModel.editableModel.outputModalities },
                    set: { 
                        var model = viewModel.editableModel
                        model.outputModalities = $0
                        viewModel.editableModel = model
                    }
                ),
                title: "Output Modalities"
            )
        }
        .navigationDestination(isPresented: $navigateToThinkingCapabilities) {
            ThinkingCapabilitiesSelectionView(
                capabilities: Binding(
                    get: { viewModel.editableModel.thinkingCapabilities ?? ThinkingCapabilities(controlType: .none) },
                    set: { 
                        var model = viewModel.editableModel
                        model.thinkingCapabilities = $0
                        viewModel.editableModel = model
                    }
                )
            )
        }
        .navigationDestination(isPresented: $navigateToSearchingCapabilities) {
            SearchingCapabilitiesSelectionView(
                capabilities: Binding(
                    get: { viewModel.editableModel.searchingCapabilities ?? SearchingCapabilities(controlType: .none) },
                    set: { 
                        var model = viewModel.editableModel
                        model.searchingCapabilities = $0
                        viewModel.editableModel = model
                    }
                )
            )
        }
        .navigationDestination(isPresented: $showingAPIConfigEditView) {
            ModelAPIConfigEditView(
                apiConfig: $currentAPIConfigToEdit, // Bind to the temporary editing state
                modelInputModalities: viewModel.editableModel.inputModalities,
                modelOutputModalities: viewModel.editableModel.outputModalities,
                isCreatingNew: editingAPIConfigIndex == nil,
                onSave: {
                    if let index = editingAPIConfigIndex {
                        // Editing existing: update the item in the array
                        if viewModel.editableModel.apiConfigsOverride != nil && index < viewModel.editableModel.apiConfigsOverride!.count {
                            viewModel.editableModel.apiConfigsOverride![index] = currentAPIConfigToEdit
                        }
                    } else {
                        // Creating new: append the item
                        if viewModel.editableModel.apiConfigsOverride == nil {
                            viewModel.editableModel.apiConfigsOverride = []
                        }
                        viewModel.editableModel.apiConfigsOverride?.append(currentAPIConfigToEdit)
                    }
                    // Reset state after saving
                    editingAPIConfigIndex = nil
                }
            )
        }
        .animation(.default, value: viewModel.isEditing)
    }
    
    // MARK: - Computed Properties
    
    /// Dynamic navigation title based on mode
    private var navigationTitle: String {
        if viewModel.isCreatingNewModel {
            return "Add Model"
        } else {
            return "Model Details"
        }
    }
    
    /// Header view for the instances list section
    private var listHeader: some View {
        HStack {
            Text("AVAILABLE INSTANCES")
            
            Spacer()
            
            if let modelId = viewModel.model?.id {
                NavigationLink(destination: InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: nil, modelId: modelId))) {
                    Image(systemName: "plus")
                        .foregroundColor(.accentColor)
                }
            }
        }
    }
    
    /// Header view for the API Configuration Overrides section
    private var apiConfigOverridesHeader: some View {
        HStack {
            Text("API CONFIGURATION OVERRIDES")
            Spacer()
            if viewModel.isEditing {
                Button {
                    editingAPIConfigIndex = nil
                    currentAPIConfigToEdit = ModelAPIConfig(apiStyle: .openaiCompatible, apiEndpointPath: "")
                    showingAPIConfigEditView = true
                } label: {
                    Image(systemName: "plus")
                        .foregroundColor(.accentColor)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    /// Returns an icon name for a given modality
    private func modalityIcon(for modality: ModelModality) -> String {
        switch modality {
        case .text:
            return "t.square"
        case .image:
            return "photo"
        case .audio:
            return "waveform"
        case .video:
            return "video"
        case .pdf:
            return "doc.text"
        }
    }
    
    /// Formats token count with commas and "tokens" suffix
    private func formatTokenCount(_ count: Int64) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        
        if let formattedCount = formatter.string(from: NSNumber(value: count)) {
            return "\(formattedCount) tokens"
        }
        
        return "\(count) tokens"
    }
    
    /// Gets a user-friendly description of thinking capabilities
    private func thinkingCapabilitiesDescription() -> String {
        guard let capabilities = viewModel.editableModel.thinkingCapabilities,
              let controlType = capabilities.controlType else {
            return "Not supported"
        }
        
        switch controlType {
        case .none:
            return "Not supported"
        case .defaultOn:
            return "Always on"
        case .reasoningEffort:
            if let options = capabilities.parameterOptions, let paramName = capabilities.parameterName {
                return "Parameter: \(paramName)\nOptions: \(options.joined(separator: ", "))"
            }
            return "Configurable"
        case .thinkingBudget:
            if let max = capabilities.maxBudget, let paramName = capabilities.parameterName {
                return "Parameter: \(paramName)\nMax Budget: \(formatTokenCount(max))"
            } else if let paramName = capabilities.parameterName {
                return "Parameter: \(paramName)\nNo Max Budget"
            }
            return "Budget based"
        case .parameterBased:
            if let paramName = capabilities.parameterName {
                return "Parameter based\nParameter: \(paramName)"
            }
            return "Parameter based"
        case .promptBased:
            if let paramName = capabilities.parameterName {
                return "Prompt based\nParameter: \(paramName)"
            }
            return "Prompt based"
        }
    }
    
    /// Gets a user-friendly description of searching capabilities
    private func searchingCapabilitiesDescription() -> String {
        guard let capabilities = viewModel.editableModel.searchingCapabilities,
              let controlType = capabilities.controlType else {
            return "Not supported"
        }
        
        switch controlType {
        case .none:
            return "Not supported"
        case .parameterBased:
            if let paramName = capabilities.parameterName {
                return "Parameter based\nParameter: \(paramName)"
            }
            return "Parameter based"
        case .appApi:
            return "App API"
        }
    }
}

// MARK: - Preview Provider

#Preview("GPT-4.1 (View)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!, providerId: nil))
    }.environmentObject(container)
}

#Preview("Claude 3.7 (View)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!, providerId: nil))
    }.environmentObject(container)
}

#Preview("Custom Model (View)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!, providerId: nil))
    }.environmentObject(container)
}

#Preview("Create New Model for OpenAI") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: nil, providerId: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!))
    }.environmentObject(container)
}
