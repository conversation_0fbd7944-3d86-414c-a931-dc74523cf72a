import SwiftUI
import PhotosUI
/// View for displaying, editing, and creating LLM provider details
struct ProviderDetailView: View {
    // MARK: - Properties
    
    @EnvironmentObject private var container: DIContainer
    @StateObject private var viewModel: ProviderDetailViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var showingDeleteConfirmation: Bool = false
    @State private var selectedLogoItem: PhotosPickerItem? = nil
    @FocusState private var isApiKeyFieldFocused: Bool
    
    // MARK: - Initialization
    
    /// Initialize with a view model
    init(viewModel: ProviderDetailViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        Form {
            // Header section with logo and name
            Section {
                if viewModel.isEditing && (viewModel.isCreatingNewProvider || 
                                           viewModel.editableProvider.isUserCreated) {
                    // Editable Logo Section
                    HStack {
                        Text("Logo")
                        Spacer()
                        PhotosPicker(
                            selection: $selectedLogoItem,
                            matching: .images,
                            photoLibrary: .shared()
                        ) {
                            ProviderLogoView(provider: viewModel.editableProvider, size: 48)
                        }
                        .buttonStyle(.plain)
                    }
                    .onChange(of: selectedLogoItem) { newItem in
                        Task {
                            if let data = try? await newItem?.loadTransferable(type: Data.self) {
                                viewModel.editableProvider.customLogoData = data
                                // viewModel.editableProvider.logoImageName = nil // Optional: clear default if custom is set
                            }
                        }
                    }
                    
                    HStack {
                        Text("Name")
                        Spacer()
                        TextField("Provider Name", text: $viewModel.editableProvider.name)
                            .font(.headline)
                            .multilineTextAlignment(.trailing)
                            .lineLimit(1)
                    }
                } else {
                    HStack {
                        // Provider logo
                        ProviderLogoView(provider: viewModel.editableProvider, size: 48)
                            .padding(.trailing, 16)
                        
                        // Provider name
                        Text(viewModel.editableProvider.name)
                            .font(.headline)
                    }
                    .padding(.vertical, -4)
                    .listRowBackground(Color.clear)
                }
            }
            
            // Information section
            Section {
                // Website
                HStack {
                    Text("Website")
                    Spacer()
                    if viewModel.isEditing && (viewModel.isCreatingNewProvider || 
                                              viewModel.editableProvider.isUserCreated) {
                        TextField("Website URL", text: Binding(
                            get: { viewModel.editableProvider.websiteUrl ?? "" },
                            set: { 
                                var provider = viewModel.editableProvider
                                provider.websiteUrl = $0.isEmpty ? nil : $0
                                viewModel.editableProvider = provider
                            }
                        ))
                        .multilineTextAlignment(.trailing)
                        .lineLimit(1)
                    } else if let websiteUrl = viewModel.editableProvider.websiteUrl, 
                              let url = URL(string: websiteUrl) {
                        Link(destination: url) {
                            Text(websiteUrl)
                                .foregroundColor(.accentColor)
                                .lineLimit(1)
                        }
                    } else {
                        Text("Not specified")
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                
                // API Documentation
                HStack {
                    Text("API Documentation")
                    Spacer()
                    if viewModel.isEditing && (viewModel.isCreatingNewProvider || 
                                              viewModel.editableProvider.isUserCreated) {
                        TextField("Documentation URL", text: Binding(
                            get: { viewModel.editableProvider.apiDocumentationUrl ?? "" },
                            set: { 
                                var provider = viewModel.editableProvider
                                provider.apiDocumentationUrl = $0.isEmpty ? nil : $0
                                viewModel.editableProvider = provider
                            }
                        ))
                        .multilineTextAlignment(.trailing)
                        .lineLimit(1)
                    } else if let apiDocUrl = viewModel.editableProvider.apiDocumentationUrl, 
                              let url = URL(string: apiDocUrl) {
                        Link(destination: url) {
                            Text(apiDocUrl)
                                .foregroundColor(.accentColor)
                                .lineLimit(1)
                        }
                    } else {
                        Text("Not specified")
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
            }
            
            // API Configuration section
            Section(header: Text("API CONFIGURATION")) {
                // API Base URL
                HStack {
                    Text("API Base URL")
                    Spacer()
                    if viewModel.isEditing && (viewModel.isCreatingNewProvider || 
                                              viewModel.editableProvider.isUserCreated) {
                        TextField("Base URL", text: Binding(
                            get: { viewModel.editableProvider.apiBaseUrl ?? "" },
                            set: { 
                                var provider = viewModel.editableProvider
                                provider.apiBaseUrl = $0.isEmpty ? nil : $0
                                viewModel.editableProvider = provider
                            }
                        ))
                        .multilineTextAlignment(.trailing)
                        .lineLimit(1)
                    } else {
                        Text(viewModel.editableProvider.apiBaseUrl ?? "Not specified")
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                
                // API Endpoint Path
                HStack {
                    Text("API Endpoint Path")
                    Spacer()
                    if viewModel.isEditing && (viewModel.isCreatingNewProvider || 
                                              viewModel.editableProvider.isUserCreated) {
                        TextField("Endpoint Path", text: Binding(
                            get: { viewModel.editableProvider.apiEndpointPath ?? "" },
                            set: { 
                                var provider = viewModel.editableProvider
                                provider.apiEndpointPath = $0.isEmpty ? nil : $0
                                viewModel.editableProvider = provider
                            }
                        ))
                        .multilineTextAlignment(.trailing)
                        .lineLimit(1)
                    } else {
                        Text(viewModel.editableProvider.apiEndpointPath ?? "Not specified")
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                
                // API Style
                HStack {
                    Text("API Style")
                    Spacer()
                    if viewModel.isEditing && (viewModel.isCreatingNewProvider || 
                                              viewModel.editableProvider.isUserCreated) {
                        Picker("", selection: Binding(
                            get: { viewModel.editableProvider.apiStyle },
                            set: {
                                var provider = viewModel.editableProvider
                                provider.apiStyle = $0
                                viewModel.editableProvider = provider
                            }
                        )) {
                            ForEach(APIStyle.allCases, id: \.self) { style in
                                Text(style.displayName).tag(style)
                            }
                        }
                        .pickerStyle(.menu)
                    } else {
                        Text(viewModel.editableProvider.apiStyle.displayName)
                            .foregroundColor(.secondary)
                    }
                }
                
                // API Key (only for user_api_key type)
                if viewModel.editableProvider.providerType == .userApiKey {
                    HStack {
                        Text("API Key")
                        Spacer()
                        
                        if viewModel.isEditing {
                            // Show input field
                            SecureField("Enter API Key", text: $viewModel.apiKeyInput)
                                .multilineTextAlignment(.trailing)
                                .focused($isApiKeyFieldFocused)
                        } else {
                            if viewModel.apiKeyStored {
                                // Show SecureField with toggle to reveal the key
                                if viewModel.showFullApiKey {
                                    Text(viewModel.retrievedApiKey ?? "••••••••••••••••")
                                    .multilineTextAlignment(.trailing)
                                    .foregroundColor(.secondary)
                                    .disabled(true)
                                } else {
                                    SecureField("API Key", text: .constant("••••••••••••••••"))
                                        .multilineTextAlignment(.trailing)
                                        .foregroundColor(.secondary)
                                        .disabled(true)
                                }
                            } else {
                                Button("No API Key set") {
                                    Task {
                                        await viewModel.prepareForEditing()
                                        // Auto-focus the API key input field after entering edit mode
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                            isApiKeyFieldFocused = true
                                        }
                                    }
                                }
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    .contentShape(Rectangle()) // Make the entire row tappable
                    .onTapGesture {
                        if !viewModel.isEditing && viewModel.apiKeyStored && !viewModel.showFullApiKey {
                            Task {
                                await viewModel.requestApiKeyAccess()
                            }
                        }
                    }
                }
            }

            // Delete Provider Section (conditionally displayed)
            if viewModel.isEditing && viewModel.editableProvider.isUserCreated && !viewModel.isCreatingNewProvider {
                Section {
                    Button(role: .destructive) {
                        showingDeleteConfirmation = true
                    } label: {
                        HStack {
                            Text("Delete Provider")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                    .contentShape(Rectangle()) // Make the whole row tappable
                }
            }
            
            // Available Models Section
            if !viewModel.isCreatingNewProvider {
                Section(header: listHeader) {
                    if viewModel.associatedModels.isEmpty {
                        Text("No models available")
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                    } else {
                        ForEach(viewModel.associatedModels) { model in
                            NavigationLink(destination: ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: model.id, providerId: nil))) {
                                HStack(spacing: 12) {
                                    // Model logo - Prioritize model's customLogoData
                                    ModelLogoView(model: model, provider: viewModel.editableProvider, size: 24)
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(model.name)
                                            .font(.body)
                                        
                                        Text(model.modelDescription ?? "")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .lineLimit(1)
                                    }
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                }
            }
        }
        .onAppear {
            viewModel.showFullApiKey = false
            if !viewModel.isCreatingNewProvider {
                Task {
                    await viewModel.loadAssociatedModels()
                }
            }
        }
        .navigationTitle(navigationTitle)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(viewModel.isEditing)
        .toolbar {
            // Leading items (Cancel for creation mode or edit mode)
            ToolbarItem(placement: .navigationBarLeading) {
                if viewModel.isCreatingNewProvider || (viewModel.isEditing && !viewModel.isCreatingNewProvider) {
                    Button("Cancel") {
                        if viewModel.isCreatingNewProvider {
                            // Navigate back if creating a new provider
                            dismiss()
                        } else {
                            // Exit edit mode and revert changes
                            viewModel.cancelEditing()
                        }
                    }
                }
            }
            
            // Trailing items (Edit/Done/Save)
            ToolbarItem(placement: .navigationBarTrailing) {
                if viewModel.isCreatingNewProvider {
                    // Creation mode - Save button
                    Button("Save") {
                        Task {
                            await viewModel.saveChanges()
                            // If save was successful (no error), dismiss
                            if viewModel.errorMessage == nil {
                                dismiss()
                            }
                        }
                    }
                    .disabled(viewModel.isLoading)
                } else if viewModel.isEditing {
                    // Edit mode - Done button
                    Button("Done") {
                        Task {
                            await viewModel.saveChanges()
                        }
                    }
                    .disabled(viewModel.isLoading)
                } else {
                    // View mode - Edit button
                    Button("Edit") {
                        Task {
                            await viewModel.prepareForEditing()
                        }
                    }
                }
            }
        }
        .toolbar(.hidden, for: .tabBar)
        .overlay {
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.2))
                    .ignoresSafeArea()
            }
        }
        .alert("Error", isPresented: Binding(
            get: { viewModel.errorMessage != nil },
            set: { if !$0 { viewModel.errorMessage = nil } }
        )) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("Delete Provider", isPresented: $showingDeleteConfirmation) { // Add this alert
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                Task {
                    await viewModel.deleteProvider()
                    // If deletion is successful (no error message), dismiss the view
                    if viewModel.errorMessage == nil {
                        dismiss()
                    }
                }
            }
        } message: {
            Text("Are you sure you want to delete this provider? This action cannot be undone.")
        }
        .animation(.default, value: viewModel.isEditing)
    }
    
    // MARK: - Computed Properties
    
    /// Header view for the models list section
    private var listHeader: some View {
        HStack {
            Text("AVAILABLE MODELS")
            
            Spacer()
            
            
            NavigationLink(destination: ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: nil, providerId: viewModel.editableProvider.id))) {
                Image(systemName: "plus")
                    .foregroundColor(.accentColor)
            }
        }
    }
    
    /// Dynamic navigation title based on mode
    private var navigationTitle: String {
        if viewModel.isCreatingNewProvider {
            return "Add Provider"
        } else {
            return "Provider Details"
        }
    }
}

// MARK: - Preview Provider

#Preview("OpenAI Provider (View)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!))
    }.environmentObject(container)
}

#Preview("Anthropic Provider (View)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!))
    }.environmentObject(container)
}

#Preview("Custom Provider (View)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!))
    }.environmentObject(container)
}

#Preview("Create New Provider") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: nil))
    }.environmentObject(container)
}
