import SwiftUI

struct NewModelsSheetView: View {
    // MARK: - Properties
    
    @StateObject private var viewModel: NewModelsSheetViewModel
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Initialization
    
    init(viewModel: NewModelsSheetViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                if viewModel.isLoading {
                    loadingView
                } else if let errorMessage = viewModel.errorMessage {
                    errorView(errorMessage)
                } else if viewModel.filteredProviders.isEmpty && !viewModel.searchText.isEmpty {
                    emptySearchResultView
                } else {
                    contentView
                }
            }
            .navigationTitle("Add Models")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                }
            }
            .searchable(text: $viewModel.searchText, prompt: Text("Search providers and models"))
        }
    }
    
    // MARK: - UI Components
    
    private var loadingView: some View {
        ProgressView("Loading Models...")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.largeTitle)
                .foregroundColor(.red)
            Text("Error Loading Models")
                .font(.headline)
            Text(message)
                .font(.caption)
                .multilineTextAlignment(.center)
                .padding()
            Button("Retry") {
                Task {
                    await viewModel.refreshData()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptySearchResultView: some View {
        VStack {
            Image(systemName: "magnifyingglass")
                .font(.largeTitle)
                .foregroundColor(.secondary)
                .padding(.bottom, 8)
            Text("No results for '\(viewModel.searchText)'")
                .font(.headline)
            Text("Please try searching for something else.")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var contentView: some View {
        List {
            if viewModel.searchText.isEmpty {
                quickActionButtons
            }
            
            ForEach(viewModel.filteredProviders) { provider in
                providerSection(provider)
            }
        }
    }
    
    private var quickActionButtons: some View {
        Section {
            // Add Provider Button
            NavigationLink(destination: ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: nil))) {
                quickActionButton(
                    title: "Add Provider",
                    systemImage: "plus.circle"
                )
            }
            // .buttonStyle(PlainButtonStyle())
            
            // Scan QR Code Button
            quickActionButton(
                title: "Scan QR Code",
                systemImage: "qrcode.viewfinder"
            ) {
                // TODO: Action for scanning QR code
                print("Scan QR code")
                dismiss()
            }
            
            // Import Provider / Model / Instance Button
            quickActionButton(
                title: "Import Provider / Model / Instance",
                systemImage: "arrow.down.doc"
            ) {
                // TODO: Action for importing
                print("Import Provider / Model / Instance")
                dismiss()
            }
        }
    }
    
    @ViewBuilder
    private func quickActionButton(
        title: String,
        systemImage: String,
        action: (() -> Void)? = nil
    ) -> some View {
        let buttonContent = HStack {
            Image(systemName: systemImage)
                .foregroundColor(.accentColor)
                .font(.body)
                .frame(width: 24, height: 24)
                .padding(.trailing, 8)
            
            Text(title)
                .foregroundColor(.accentColor)
                .font(.body)
            
            Spacer()
        }
        .padding(.vertical, 4)

        if let action = action {
            Button(action: action) {
                buttonContent
            }
        } else {
            buttonContent
        }
    }
    
    private func providerSection(_ provider: LLMProvider) -> some View {
        Section {
            // Provider Models
            if let models = viewModel.filteredModelsByProviderId[provider.id], !models.isEmpty {
                ForEach(models) { model in
                    modelRow(model: model, provider: provider)
                }
            } else if !viewModel.searchText.isEmpty {
                Text("No matching models for '\(viewModel.searchText)'")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 8)
            } else {
                Text("No models available for \(provider.name).")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 8)
            }
        } header: {
            HStack {
                ProviderLogoView(provider: provider, size: 24)
                Text(provider.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                if provider.apiKeyStored {
                    Image(systemName: "key.fill")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Add Model Button
                NavigationLink(destination: ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: nil, providerId: provider.id))) {
                    Image(systemName: "plus")
                        .foregroundColor(.accentColor)
                        .font(.body)
                }
            }
            .padding(.vertical, 0)
        }
    }
    
    private func modelRow(model: LLMModel, provider: LLMProvider) -> some View {
        NavigationLink(destination: InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: nil, modelId: model.id))) {
            HStack(spacing: 12) {
                ModelLogoView(model: model, provider: provider, size: 24)
                
                Text(model.name)
                        .font(.body)
                        .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.vertical, 4)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#if DEBUG
struct NewModelsSheetView_Previews: PreviewProvider {
    static var previews: some View {
        let container = DIContainer(context: PersistenceController.preview.container.viewContext)
        NewModelsSheetView(
            viewModel: container.makeNewModelsSheetViewModel()
        )
        .environmentObject(container)
    }
}
#endif 