import SwiftUI

// MARK: - Message Row View

/// A view that displays messages using the MessageRowViewModel architecture
struct MessageRowView: View {
    @ObservedObject var messageRowViewModel: MessageRowViewModel
    var minHeight: CGFloat = 100
    var maxHeight: CGFloat = 500
    
    var body: some View {
        if let firstMessageViewModel = messageRowViewModel.messageViewModels.first {
            switch firstMessageViewModel.role {
            case .user:
                UserMessageView(messageRowViewModel: messageRowViewModel)
                
            case .assistant, .mergedAssistant:
                AssistantMessagesView(
                    messageRowViewModel: messageRowViewModel,
                    minHeight: minHeight,
                    maxHeight: maxHeight
                )
                .toast(
                    icon: messageRowViewModel.toastIcon,
                    message: messageRowViewModel.toastMessage,
                    isShowing: $messageRowViewModel.showToast,
                    duration: 1.5
                )
                
            default:
                // System messages or others are not displayed in this view
                EmptyView()
            }
        } else {
            // No messages to display
            EmptyView()
        }
    }
}

// MARK: - User Message View

private struct UserMessageView: View {
    @ObservedObject var messageRowViewModel: MessageRowViewModel

    let messageBubbleShape = UnevenRoundedRectangle(
        topLeadingRadius: 16,
        bottomLeadingRadius: 16,
        bottomTrailingRadius: 4,
        topTrailingRadius: 16
    )
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 8) {
            // File preview section (if has multimodal content)
            if let activeMessageViewModel = messageRowViewModel.activeUserMessageViewModel,
               activeMessageViewModel.hasMultimodalContent {
                messageFilePreviewSection(for: activeMessageViewModel)
            }

            // Message bubble
            if let activeMessageViewModel = messageRowViewModel.activeUserMessageViewModel {
                MarkdownView(markdownContentString: activeMessageViewModel.displayContentText)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .clipShape(messageBubbleShape)
                    .contentShape(.contextMenuPreview, messageBubbleShape)
                    .contextMenu {
                        let actions = messageRowViewModel.resolvedMessageActionSettings?.validUserMessageActions ?? SystemMessageActions.defaultUserMessageActions
                        ForEach(actions, id: \.id) { action in
                            createActionButton(for: action, messageViewModel: activeMessageViewModel)
                        }
                    }
            }
            
            // Edit navigation controls (if multiple edits exist)
            if messageRowViewModel.userMessageViewModels.count > 1 {
                HStack(spacing: 4) {
                    Button(action: {
                        messageRowViewModel.showPreviousUserEdit()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.callout)
                            .fontWeight(.medium)
                            .foregroundColor(currentEditIndex <= 0 ? Color(.systemGray4) : Color(.systemGray))
                    }
                    .disabled(currentEditIndex <= 0)
                    
                    Text("\(currentEditIndex + 1) / \(messageRowViewModel.userMessageViewModels.count)")
                        .font(.callout)
                        .foregroundColor(.gray)
                    
                    Button(action: {
                        messageRowViewModel.showNextUserEdit()
                    }) {
                        Image(systemName: "chevron.right")
                            .font(.callout)
                            .fontWeight(.medium)
                            .foregroundColor(currentEditIndex >= messageRowViewModel.userMessageViewModels.count - 1 ? Color(.systemGray4) : Color(.systemGray))
                    }
                    .disabled(currentEditIndex >= messageRowViewModel.userMessageViewModels.count - 1)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .trailing)
        .padding(.leading, 16)
        .padding(.trailing, 4)
    }
    
    private var currentEditIndex: Int {
        guard let activeId = messageRowViewModel.activeUserMessageId,
              let index = messageRowViewModel.userMessageViewModels.firstIndex(where: { $0.id == activeId }) else {
            return messageRowViewModel.userMessageViewModels.count - 1
        }
        return index
    }

    @ViewBuilder
    private func createActionButton(for action: MessageAction, messageViewModel: MessageViewModel) -> some View {
        switch action.name {
        case "Copy":
            Button(action: {
                messageViewModel.copyMessage()
            }) {
                Label(action.name, systemImage: action.icon ?? "square.on.square")
            }
        case "Edit":
            Button(action: {
                messageViewModel.editMessage()
            }) {
                Label(action.name, systemImage: action.icon ?? "pencil")
            }
        default:
            // For custom actions not yet implemented
            Button(action: {
                print("Custom action '\(action.name)' not implemented yet")
            }) {
                Label(action.name, systemImage: action.icon ?? "questionmark.circle")
            }
        }
    }

    /// File preview section for user messages
    @ViewBuilder
    private func messageFilePreviewSection(for messageViewModel: MessageViewModel) -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                // Display files (reversed to maintain correct order with RTL layout)
                ForEach(messageViewModel.getFileBlocks().indices.reversed(), id: \.self) { index in
                    let fileInfo = messageViewModel.getFileBlocks()[index]
                    MessageFileView(fileInfo: fileInfo)
                }

                // Display images (reversed to maintain correct order with RTL layout)
                ForEach(messageViewModel.getImageBlocks().indices.reversed(), id: \.self) { index in
                    let imageInfo = messageViewModel.getImageBlocks()[index]
                    MessageImageView(imageInfo: imageInfo)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .scrollBounceBehavior(.basedOnSize, axes: .horizontal)
        .frame(maxWidth: .infinity, alignment: .trailing) // Right-align for user messages
    }
}

// MARK: - Assistant Messages View

private struct AssistantMessagesView: View {
    @ObservedObject var messageRowViewModel: MessageRowViewModel
    let minHeight: CGFloat
    let maxHeight: CGFloat
    
    @State private var scrollViewHeight: CGFloat = 100
    
    var body: some View {
        VStack(alignment: .leading) {
            if !messageRowViewModel.visibleAssistantReplyGroupViewModels.isEmpty {
                // Card stack for assistant replies
                AssistantReplyCardStack(
                    messageRowViewModel: messageRowViewModel,
                    minHeight: minHeight,
                    maxHeight: maxHeight
                )
                .frame(height: scrollViewHeight)
                .onPreferenceChange(StackMeasuredHeightPreferenceKey.self) { newHeight in
                    let totalHeight = newHeight + 40 + 8 + CGFloat(messageRowViewModel.visibleAssistantReplyGroupViewModels.count) * 12
                    
                    if totalHeight > scrollViewHeight {
                        self.scrollViewHeight = max(minHeight, min(totalHeight, maxHeight))
                    } else if totalHeight < minHeight {
                        self.scrollViewHeight = minHeight
                    }
                }
            } else {
                // All replies disliked, show regenerate button
                VStack {
                    Text("No responses available")
                        .font(.callout)
                        .foregroundColor(.gray)
                    
                    Button(action: {
                        messageRowViewModel.regenerateAllMessages()
                    }) {
                        Label("Regenerate Response", systemImage: "arrow.clockwise")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.blue.opacity(0.1))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)
            }
        }
        .fixedSize(horizontal: false, vertical: true)
    }
}

// MARK: - Assistant Reply Card Stack

private struct AssistantReplyCardStack: View {
    @ObservedObject var messageRowViewModel: MessageRowViewModel
    let minHeight: CGFloat
    let maxHeight: CGFloat
    
    @State private var offset = CGSize.zero
    @State private var removingTopCard = false
    @State private var isVerticalScrollDisabled = false
    @State private var swipeProgress: Double = 0.0
    @State private var swipeDirection: SwipeDirection? = nil
    @State private var showProgressIndicator: Bool = false
    
    var visibleGroups: [AssistantReplyGroupViewModel] {
        messageRowViewModel.visibleAssistantReplyGroupViewModels
    }
    
    var body: some View {
        ZStack {
            if visibleGroups.isEmpty {
                Text("No responses available")
                    .foregroundColor(.gray)
                    .padding()
            } else {
                GeometryReader { geometry in
                    ZStack {
                        ForEach(Array(visibleGroups.enumerated()), id: \.element.id) { index, groupViewModel in
                            let isTopCard = index == 0
                            let workingIndex = index - (removingTopCard && index > 0 ? 1 : 0)
                            
                            // Scale and opacity calculations
                            let heightFactor = CGFloat(1.0 - (0.05 * CGFloat(workingIndex)))
                            let widthFactor = CGFloat(1.0 - (0.03 * CGFloat(workingIndex)))
                            let heightOffset = -12 * CGFloat(workingIndex)
                            
                            // Only the top card moves during drag gesture
                            let xOffset = isTopCard ? offset.width : 0
                            let yOffset = isTopCard ? offset.height : 0
                            
                            // Rotation effect for top card
                            let maxAbsDegrees = xOffset < 0 ? max(-5, xOffset * 0.05) : min(5, xOffset * 0.05)
                            let angle = isTopCard ? Angle(degrees: maxAbsDegrees) : Angle.zero
                            
                            // Calculate opacity for the overlay based on swipe distance
                            let overlayOpacity = isTopCard ? min(abs(xOffset) / (geometry.size.width * 0.3), 0.8) : 0
                            let overlayColor = xOffset < 0 ? Color.red : Color.green
                            
                            AssistantReplyCardView(
                                groupViewModel: groupViewModel,
                                isVerticalScrollDisabled: $isVerticalScrollDisabled
                            )
                            .overlay(
                                ZStack {
                                    if isTopCard {
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(overlayColor.opacity(overlayOpacity))
                                            .allowsHitTesting(false)
                                        
                                        // Circular progress indicator
                                        if showProgressIndicator, let direction = swipeDirection {
                                            CircularProgressIndicator(
                                                progress: swipeProgress,
                                                direction: direction,
                                                size: 60,
                                                lineWidth: 4
                                            )
                                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: direction == .right ? .leading : .trailing)
                                            .padding(.horizontal, 30)
                                        }
                                    }
                                }
                            )
                            .scaleEffect(
                                CGSize(width: widthFactor, height: heightFactor),
                                anchor: .top
                            )
                            .offset(x: 0, y: heightOffset)
                            .zIndex(-Double(index))
                            .offset(x: xOffset, y: yOffset)
                            .rotationEffect(angle, anchor: .top)
                            .contentShape(RoundedRectangle(cornerRadius: 16))
                            .allowsHitTesting(isTopCard)
                            .opacity(isTopCard && removingTopCard ? 0 : 1)
                        }
                    }
                    .gesture(
                        DragGesture(minimumDistance: 25)
                            .onChanged { gesture in
                                if !messageRowViewModel.isSwipeAnimationInProgress && 
                                   abs(gesture.translation.width) > abs(gesture.translation.height) * 1.5 {
                                    
                                    if messageRowViewModel.isSingleCard && gesture.translation.width > 0 {
                                        // Single card rubber band effect
                                        let maxTranslation = geometry.size.width * 0.6
                                        let clampedTranslation = min(gesture.translation.width, maxTranslation)
                                        offset = CGSize(width: clampedTranslation, height: gesture.translation.height)
                                                                                
                                        // Update progress indicator
                                        let threshold = geometry.size.width * 0.3
                                        let progress = min(1.0, clampedTranslation / threshold)
                                        
                                        swipeProgress = progress
                                        swipeDirection = .right
                                        showProgressIndicator = progress > 0.1
                                    } else {
                                        // Normal multi-card behavior
                                        offset = gesture.translation
                                        
                                        // Update progress indicator
                                        let threshold = geometry.size.width * 0.3
                                        let progress = min(1.0, abs(gesture.translation.width) / threshold)
                                        let direction: SwipeDirection = gesture.translation.width < 0 ? .left : .right
                                        
                                        swipeProgress = progress
                                        swipeDirection = direction
                                        showProgressIndicator = progress > 0.1
                                    }
                                    
                                    isVerticalScrollDisabled = true
                                }
                            }
                            .onEnded { gesture in
                                if !messageRowViewModel.isSwipeAnimationInProgress {
                                    if messageRowViewModel.isSingleCard && gesture.translation.width > 0 {
                                        handleSingleCardSwipeEnded(gesture, geometry)
                                    } else {
                                        onSwipeEnded(gesture, geometry)
                                    }
                                    isVerticalScrollDisabled = false
                                    
                                    // Reset progress indicator
                                    swipeProgress = 0.0
                                    swipeDirection = nil
                                    showProgressIndicator = false
                                }
                            }
                    )
                }
            }
        }
        .padding(.top, 12 * (CGFloat(visibleGroups.count) - 1))
        .onReceive(messageRowViewModel.$shouldTriggerProgrammaticSwipe) { shouldTrigger in
            if shouldTrigger {
                triggerProgrammaticSwipeAnimation()
            }
        }
    }
    
    private func handleSingleCardSwipeEnded(_ gesture: DragGesture.Value, _ geometry: GeometryProxy) {
        let threshold = geometry.size.width * 0.3
        
        if gesture.translation.width > threshold && !visibleGroups.isEmpty {
            // Trigger like operation
            let swipedGroup = visibleGroups.first!
            messageRowViewModel.handleSingleCardLike(groupId: swipedGroup.id)
        }
        
        // Always bounce back with spring animation
        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
            offset = .zero
        }
    }
    
    private func onSwipeEnded(_ gesture: DragGesture.Value, _ geometry: GeometryProxy) {
        let actualTranslationX = gesture.translation.width
        let threshold = geometry.size.width * 0.3
        
        if abs(actualTranslationX) > threshold && !visibleGroups.isEmpty {
            messageRowViewModel.isSwipeAnimationInProgress = true
            
            let direction: SwipeDirection = actualTranslationX < 0 ? .left : .right
            let swipedGroup = visibleGroups.first!
            
            withAnimation(.easeInOut(duration: 0.25)) {
                removingTopCard = true
                offset = CGSize(
                    width: actualTranslationX * 1.5,
                    height: gesture.translation.height * 1.5
                )
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                offset = .zero
                removingTopCard = false
                
                messageRowViewModel.handleAssistantReplySwipe(direction: direction, groupId: swipedGroup.id)
                messageRowViewModel.isSwipeAnimationInProgress = false
            }
        } else {
            withAnimation(.spring) {
                offset = .zero
            }
        }
    }
    
    private func triggerProgrammaticSwipeAnimation() {
        guard let direction = messageRowViewModel.programmaticSwipeDirection,
              let groupId = messageRowViewModel.programmaticSwipeGroupId,
              !visibleGroups.isEmpty else { return }
        
        // Find the group to animate
        guard visibleGroups.first?.id == groupId else {
            // If the group is not the top card, complete immediately
            messageRowViewModel.completeProgrammaticSwipe()
            return
        }
        
        // Set animation state
        messageRowViewModel.isSwipeAnimationInProgress = true
        
        // Determine animation direction and distance
        let screenWidth = UIScreen.main.bounds.width
        let animationDistance = screenWidth * 0.8
        let targetOffset = CGSize(
            width: direction == .right ? animationDistance : -animationDistance,
            height: 0
        )
        
        // Set progress indicator
        swipeProgress = 1.0
        swipeDirection = direction
        showProgressIndicator = true
        
        // Animate the swipe
        withAnimation(.easeInOut(duration: 0.5)) {
            removingTopCard = true
            offset = targetOffset
        }
        
        // Complete the swipe after animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            offset = .zero
            removingTopCard = false
            swipeProgress = 0.0
            swipeDirection = nil
            showProgressIndicator = false
            
            messageRowViewModel.completeProgrammaticSwipe()
        }
    }
}

// MARK: - Assistant Reply Card View

private struct AssistantReplyCardView: View {
    @ObservedObject var groupViewModel: AssistantReplyGroupViewModel
    @Binding var isVerticalScrollDisabled: Bool
    
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 8) {
            // Message content
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    if let messageViewModel = groupViewModel.activeMessageViewModel {
                        MarkdownView(
                            markdownContentString: messageViewModel.displayContentText,
                            markdownThinkingString: messageViewModel.displayThinkingText,
                            shouldExpandThinking: groupViewModel.currentChatSessionSetting?.shouldExpandThinking ?? true
                        )
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding()
                .background(GeometryReader { geometry in
                    Color.clear.preference(
                        key: StackMeasuredHeightPreferenceKey.self,
                        value: geometry.size.height
                    )
                })
            }
            .scrollBounceBehavior(.basedOnSize)
            .disabled(isVerticalScrollDisabled)
            
            // Action buttons
            HStack(spacing: 12) {
                // Regeneration navigation (if multiple regenerations exist)
                if groupViewModel.messageViewModels.count > 1 {
                    HStack(spacing: 4) {
                        Button(action: {
                            groupViewModel.showPreviousRegeneration()
                        }) {
                            Image(systemName: "chevron.left")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(groupViewModel.activeMessageIndex <= 0 ? Color(.systemGray4) : Color(.systemGray))
                        }
                        .disabled(groupViewModel.activeMessageIndex <= 0)

                        Text("\(groupViewModel.activeMessageIndex + 1) / \(groupViewModel.messageViewModels.count)")
                            .font(.callout)
                            .foregroundColor(.gray)

                        Button(action: {
                            groupViewModel.showNextRegeneration()
                        }) {
                            Image(systemName: "chevron.right")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(groupViewModel.activeMessageIndex >= groupViewModel.messageViewModels.count - 1 ? Color(.systemGray4) : Color(.systemGray))
                        }
                        .disabled(groupViewModel.activeMessageIndex >= groupViewModel.messageViewModels.count - 1)
                    }
                    .padding(.leading, 8)
                }

                // Action buttons in a scrollable container
                let actions = groupViewModel.resolvedMessageActionSettings?.validAssistantMessageCardActions ?? SystemMessageActions.defaultAssistantMessageCardActions
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(actions, id: \.id) { action in
                            createCardActionButton(for: action)
                        }
                    }
                    .padding(.horizontal, 1) // Minimal padding to prevent clipping
                }
                .scrollBounceBehavior(.basedOnSize, axes: .horizontal)
                .frame(maxWidth: .infinity) // Take available space between regeneration nav and right elements

                // Error indicator and Instance logo (always visible on the right)
                HStack(spacing: 8) {
                    // Error indicator
                    if groupViewModel.activeMessageViewModel?.status == .error {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.system(size: 16))
                    }

                    // Instance logo
                    if let LLMInstanceContext = groupViewModel.activeMessageViewModel?.LLMInstanceContext {
                        InstanceLogoView(
                            instance: LLMInstanceContext.instance,
                            model: LLMInstanceContext.model,
                            provider: LLMInstanceContext.provider,
                            size: 24
                        )
                    } else if let instanceId = groupViewModel.activeMessageViewModel?.llmInstanceId {
                        MockInstanceLogoView(instanceId: instanceId, size: 24)
                    }
                }
            }
            .padding(.horizontal, 8)
            .padding(.bottom, 8)
        }
        .background(colorScheme == .light ? Color(.systemBackground) : Color(red: 0.08, green: 0.08, blue: 0.08))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .overlay(
            // Error state border
            RoundedRectangle(cornerRadius: 16)
                .stroke(groupViewModel.activeMessageViewModel?.status == .error ? Color.red : Color.clear, lineWidth: 2)
        )
        .contentShape(.contextMenuPreview, RoundedRectangle(cornerRadius: 16))
        .contextMenu {
            // Instance name as header/title if available
            if let instanceName = groupViewModel.activeMessageViewModel?.LLMInstanceContext?.instance.name {
                Text(instanceName)
                    .font(.headline)
                    .foregroundColor(.primary)
            }

            // Use resolved message action settings for assistant message menu actions
            let actions = groupViewModel.resolvedMessageActionSettings?.validAssistantMessageMenuActions ?? SystemMessageActions.defaultAssistantMessageMenuActions
            ForEach(actions, id: \.id) { action in
                createContextMenuActionButton(for: action)
            }
        }
        .sheet(isPresented: Binding<Bool>(
            get: { groupViewModel.showingRawTextSheet },
            set: { newValue in groupViewModel.showingRawTextSheet = newValue }
        )) {
            if let messageViewModel = groupViewModel.activeMessageViewModel {
                RawTextSheetView(textContent: messageViewModel.getRawTextContent())
            } else {
                RawTextSheetView(textContent: "No content available")
            }
        }
    }

    /// Create a card action button (icon-only) based on MessageAction
    @ViewBuilder
    private func createCardActionButton(for action: MessageAction) -> some View {
        switch action.actionType {
        case .system(let kind):
            switch kind {
            case .copy:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.copyMessage()
                }) {
                    Image(systemName: action.icon ?? "square.on.square")
                        .frame(width: 24, height: 24)
                        .foregroundColor(.gray)
                        .fontWeight(.medium)
                }
            case .regenerate:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.regenerateSingleMessage()
                }) {
                    Image(systemName: action.icon ?? "arrow.clockwise")
                        .frame(width: 24, height: 24)
                        .foregroundColor(.gray)
                        .fontWeight(.medium)
                }
            case .like:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.likeMessageFromButton()
                }) {
                    Image(systemName: groupViewModel.activeMessageViewModel?.userFeedback == .liked ? "hand.thumbsup.fill" : (action.icon ?? "hand.thumbsup"))
                        .frame(width: 24, height: 24)
                        .foregroundColor(groupViewModel.activeMessageViewModel?.userFeedback == .liked ? .green : .gray)
                        .fontWeight(.medium)
                }
            case .dislike:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.dislikeMessageFromButton()
                }) {
                    Image(systemName: groupViewModel.activeMessageViewModel?.userFeedback == .disliked ? "hand.thumbsdown.fill" : (action.icon ?? "hand.thumbsdown"))
                        .frame(width: 24, height: 24)
                        .foregroundColor(groupViewModel.activeMessageViewModel?.userFeedback == .disliked ? .red : .gray)
                        .fontWeight(.medium)
                }
            case .edit:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.editMessage()
                }) {
                    Image(systemName: action.icon ?? "pencil")
                        .frame(width: 24, height: 24)
                        .foregroundColor(.gray)
                        .fontWeight(.medium)
                }
            case .share:
                Button(action: {
                    // TODO: Implement share functionality
                    print("Share action not implemented yet")
                }) {
                    Image(systemName: action.icon ?? "square.and.arrow.up")
                        .frame(width: 24, height: 24)
                        .foregroundColor(.gray)
                        .fontWeight(.medium)
                }
            case .select, .camera, .photo, .file:
                // These actions are not typically shown as card buttons
                Button(action: {
                    print("Action '\(action.name)' not suitable for card display")
                }) {
                    Image(systemName: action.icon ?? "questionmark.circle")
                        .frame(width: 24, height: 24)
                        .foregroundColor(.gray)
                        .fontWeight(.medium)
                }
            }
        case .assistantRegenerate:
            Button(action: {
                groupViewModel.activeMessageViewModel?.regenerateSingleMessageWithAction(action)
            }) {
                Image(systemName: action.icon ?? "wand.and.stars")
                    .frame(width: 24, height: 24)
                    .foregroundColor(.gray)
                    .fontWeight(.medium)
            }
        case .actionPanelPromptInsert, .actionPanelPromptRewrite:
            // Prompt suggestions are not shown as card buttons
            Button(action: {
                print("🚨 [MessageRowView] Warning: ActionPanel Prompt action '\(action.name)' should be handled in action panel")
            }) {
                Image(systemName: action.icon ?? "text.bubble")
                    .frame(width: 24, height: 24)
                    .foregroundColor(.gray)
                    .fontWeight(.medium)
            }
        }
    }

    /// Create a context menu action button based on MessageAction
    @ViewBuilder
    private func createContextMenuActionButton(for action: MessageAction) -> some View {
        switch action.actionType {
        case .system(let kind):
            switch kind {
            case .copy:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.copyMessage()
                }) {
                    Label(action.name, systemImage: action.icon ?? "square.on.square")
                }
            case .regenerate:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.regenerateSingleMessage()
                }) {
                    Label(action.name, systemImage: action.icon ?? "arrow.clockwise")
                }
            case .like:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.likeMessageFromButton()
                }) {
                    Label(action.name, systemImage: groupViewModel.activeMessageViewModel?.userFeedback == .liked ? "hand.thumbsup.fill" : (action.icon ?? "hand.thumbsup"))
                }
            case .dislike:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.dislikeMessageFromButton()
                }) {
                    Label(action.name, systemImage: groupViewModel.activeMessageViewModel?.userFeedback == .disliked ? "hand.thumbsdown.fill" : (action.icon ?? "hand.thumbsdown"))
                }
            case .edit:
                Button(action: {
                    groupViewModel.activeMessageViewModel?.editMessage()
                }) {
                    Label(action.name, systemImage: action.icon ?? "pencil")
                }
            case .select:
                Button(action: {
                    groupViewModel.showRawTextSheetForActiveMessage()
                }) {
                    Label(action.name, systemImage: action.icon ?? "selection.pin.in.out")
                }
            case .share:
                Button(action: {
                    // TODO: Implement share functionality
                    print("Share action not implemented yet")
                }) {
                    Label(action.name, systemImage: action.icon ?? "square.and.arrow.up")
                }
            case .camera, .photo, .file:
                // These actions are handled in action panel, not in context menu
                Button(action: {
                    print("Action '\(action.name)' should be handled in action panel")
                }) {
                    Label(action.name, systemImage: action.icon ?? "questionmark.circle")
                }
            }
        case .assistantRegenerate:
            Button(action: {
                groupViewModel.activeMessageViewModel?.regenerateSingleMessageWithAction(action)
            }) {
                Label(action.name, systemImage: action.icon ?? "wand.and.stars")
            }
        case .actionPanelPromptInsert, .actionPanelPromptRewrite:
            // Prompt suggestions are not shown in context menu
            Button(action: {
                print("ActionPanel Prompt action '\(action.name)' should be handled in action panel")
            }) {
                Label(action.name, systemImage: action.icon ?? "text.bubble")
            }
        }
    }
}

// MARK: - Mock Instance Logo View (for Preview)

struct MockInstanceLogoView: View {
    let instanceId: UUID
    let size: CGFloat
    
    // Map instance IDs to colors for preview
    private var color: Color {
        return .gray
    }
    
    var body: some View {
        ZStack {
            Circle()
                .fill(color.opacity(0.3))
                .frame(width: size, height: size)
            
            Text("I")
                .font(.system(size: size * 0.5, weight: .bold))
                .foregroundColor(color)
        }
    }
}
