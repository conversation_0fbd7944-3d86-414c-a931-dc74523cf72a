import SwiftUI

struct NewChatSheetView: View {
    // MARK: - Properties
    
    @StateObject private var viewModel: NewChatSheetViewModel
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Initialization
    
    init(viewModel: NewChatSheetViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                if viewModel.isLoading {
                    loadingView
                } else if let errorMessage = viewModel.errorMessage {
                    errorView(errorMessage)
                } else if viewModel.filteredProviders.isEmpty && !viewModel.searchText.isEmpty {
                    emptySearchResultView
                } else {
                    contentView
                }
            }
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(viewModel.mode == .auxiliaryInstanceSelection ? "Done" : "Cancel") {
                        dismiss()
                    }
                }
            }
            .searchable(text: $viewModel.searchText, prompt: Text(searchPrompt))
        }
    }

    // MARK: - Computed Properties

    private var navigationTitle: String {
        switch viewModel.mode {
        case .chatCreation:
            return "New Chat"
        case .instanceSelection:
            return "Select Instance"
        case .auxiliaryInstanceSelection:
            return "Auxiliary Instance"
        }
    }

    private var searchPrompt: String {
        switch viewModel.mode {
        case .chatCreation:
            return "Search models and providers"
        case .instanceSelection, .auxiliaryInstanceSelection:
            return "Search instances"
        }
    }

    // MARK: - UI Components
    
    private var loadingView: some View {
        ProgressView("Loading Models...")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.largeTitle)
                .foregroundColor(.red)
            Text("Error Loading Models")
                .font(.headline)
            Text(message)
                .font(.caption)
                .multilineTextAlignment(.center)
                .padding()
            Button("Retry") {
                Task {
                    await viewModel.refreshData()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptySearchResultView: some View {
        VStack {
            Image(systemName: "magnifyingglass")
                .font(.largeTitle)
                .foregroundColor(.secondary)
                .padding(.bottom, 8)
            Text("No results for '\(viewModel.searchText)'")
                .font(.headline)
            Text("Please try searching for something else.")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var contentView: some View {
        List {
            if viewModel.searchText.isEmpty {
                if viewModel.mode == .chatCreation {
                    quickActionButtons
                } else if viewModel.mode == .auxiliaryInstanceSelection {
                    auxiliaryNoneSection
                }
            }

            ForEach(viewModel.filteredProviders) { provider in
                providerSection(provider)
            }
        }
    }
    
    private var quickActionButtons: some View {
        Section{
            // New Empty Chat Button
            quickActionButton(
                title: "Empty Chat",
                systemImage: "plus.bubble",
                color: .blue
            ) {
                // TODO: Action for new empty chat
                print("Create new empty chat")
                dismiss()
            }
            
            // Import Chat Button
            quickActionButton(
                title: "Import Chat",
                systemImage: "arrow.down.doc",
                color: .green
            ) {
                // TODO: Action for importing chat
                print("Import chat")
                dismiss()
            }

            // Edit Document Button
            quickActionButton(
                title: "Edit Document",
                systemImage: "doc.text",
                color: .orange
            ) {
                // TODO: Action for editing document
                print("Edit document")
                dismiss()
            }
            
            // Scan QR Code Button
            quickActionButton(
                title: "Scan QR Code",
                systemImage: "qrcode.viewfinder",
                color: .purple
            ) {
                // TODO: Action for scanning QR code
                print("Scan QR code")
                dismiss()
            }
        }
    }

    
    private func quickActionButton(
        title: String,
        systemImage: String,
        color: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: systemImage)
                    .foregroundColor(.accentColor)
                    .font(.body)
                    .frame(width: 24, height: 24)
                    .padding(.trailing, 8)
                
                Text(title)
                    .foregroundColor(.accentColor)
                    .font(.body)
                
                Spacer()
            }
            .padding(.vertical, 4)
        }
    }

    private var auxiliaryNoneSection: some View {
        Section {
            quickActionButton(
                title: "None",
                systemImage: "minus.circle",
                color: .gray
            ) {
                // Select no auxiliary instance (nil)
                if case .auxiliaryInstanceSelection = viewModel.mode {
                    viewModel.onInstanceSelection?(nil)
                }
                dismiss()
            }
        } header: {
            Text("Disable Auxiliary Instance")
        } footer: {
            Text("Auxiliary instances are used for title generation and other utility tasks. Select 'None' to disable these features for this session. Or select an instance below to enable these features.")
        }
    }

    private func providerSection(_ provider: LLMProvider) -> some View {
        Section{ 
            // Provider Instances
            if let instances = viewModel.filteredInstancesByProviderId[provider.id], !instances.isEmpty {
                ForEach(instances) { instance in
                    instanceRow(instance: instance, provider: provider)
                }
            } else if !viewModel.searchText.isEmpty {
                Text("No matching instances for '\(viewModel.searchText)'")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 8)
            } else {
                Text("No instances available for \(provider.name).")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 8)
            }
        } header: {
            HStack {
                ProviderLogoView(provider: provider, size: 24)
                Text(provider.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                if provider.apiKeyStored {
                    Image(systemName: "key.fill")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding(.vertical, 0)
            .disabled(true)
        }
    }
    
    private func instanceRow(instance: LLMInstance, provider: LLMProvider) -> some View {
        Button {
            viewModel.handleInstanceSelection(with: instance.id)
            dismiss()
        } label: {
            HStack(spacing: 12) {
                let model = viewModel.allModelsById[instance.modelId]
                InstanceLogoView(instance: instance, model: model, provider: provider, size: 24)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(instance.name)
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    if let model = model, !(instance.name.localizedCaseInsensitiveContains(model.name)) {
                        Text("Model: \(model.name)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 4)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#if DEBUG
struct NewChatSheetView_Previews: PreviewProvider {
    static var previews: some View {
        let container = DIContainer(context: PersistenceController.preview.container.viewContext)
        NewChatSheetView(
            viewModel: container.makeNewChatSheetViewModel { instanceId in
                print("Creating new chat with instance ID: \(instanceId)")
            }
        )
    }
}
#endif
