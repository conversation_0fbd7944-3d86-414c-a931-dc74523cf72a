import SwiftUI

/// Main view that displays a list of chat sessions
struct ChatsView: View {
    // MARK: - Properties
    
    @StateObject private var viewModel: ChatsViewModel
    @EnvironmentObject private var container: DIContainer
    @State private var showNewChatSheet = false

    // MARK: - Initialization
    
    init(viewModel: ChatsViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                if viewModel.isLoading && viewModel.sessions.isEmpty {
                    loadingView
                } else if let errorMessage = viewModel.errorMessage {
                    errorView(errorMessage)
                } else if viewModel.filteredSessions.isEmpty && !viewModel.searchText.isEmpty {
                    emptySearchResultView
                } else if viewModel.sessions.isEmpty {
                    emptyStateView
                } else {
                    chatSessionsList
                }
            }
            .navigationTitle("Chats")
            .navigationBarTitleDisplayMode(.inline)
            .navigationDestination(isPresented: $viewModel.isNavigatingToChatSession) {
                if let chatView = viewModel.selectedChatView {
                    chatView
                        .id(viewModel.chatViewId) // Force recreation when chatViewId changes
                        .environmentObject(viewModel) // Pass ChatsViewModel as environment
                        .onDisappear {
                            viewModel.resetNavigation()
                        }
                }
            }
            // .onAppear {
            //     withAnimation {
            //         viewModel.toolbarVisibility = .visible
            //     }
            // }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showNewChatSheet = true
                    }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .toolbar(viewModel.toolbarVisibility, for: .tabBar)
            .searchable(text: $viewModel.searchText, prompt: Text("Search Chats"))
            .sheet(isPresented: $showNewChatSheet) {
                NewChatSheetView(
                    viewModel: container.makeNewChatSheetViewModel { sessionId in
                        // The session has already been created by NewChatSheetViewModel
                        // We just need to navigate to it
                        Task {
                            // Find the session in our current list or wait for observer to update
                            if let session = viewModel.sessions.first(where: { $0.id == sessionId }) {
                                await MainActor.run {
                                    viewModel.navigateToChatSession(session)
                                }
                            } else {
                                // If session not found immediately, trigger a refresh and then navigate
                                await viewModel.refreshSessions()
                                if let session = viewModel.sessions.first(where: { $0.id == sessionId }) {
                                    await MainActor.run {
                                        viewModel.navigateToChatSession(session)
                                    }
                                }
                            }
                        }
                    }
                )
            }
        }
    }
    
    // MARK: - UI Components
    
    private var loadingView: some View {
        ProgressView("Loading Chats...")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.largeTitle)
                .foregroundColor(.red)
            Text("Error Loading Chats")
                .font(.headline)
            Text(message)
                .font(.caption)
                .multilineTextAlignment(.center)
                .padding()
            Button("Retry") {
                Task {
                    await viewModel.refreshSessions()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptySearchResultView: some View {
        VStack {
            Image(systemName: "magnifyingglass")
                .font(.largeTitle)
                .foregroundColor(.secondary)
                .padding(.bottom, 8)
            Text("No results for " + viewModel.searchText)
                .font(.headline)
            Text("Try searching for something else.")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack {
            Image(systemName: "bubble.left.and.bubble.right")
                .font(.system(size: 50))
                .foregroundColor(.gray)
                .padding(.bottom, 8)
            Text("No Chats")
                .font(.headline)
            Text("Tap the '+ ' button to start a new chat")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var chatSessionsList: some View {
        List {
            ForEach(viewModel.filteredSessions) { session in
                Button {
                    viewModel.navigateToChatSession(session)
                } label: {
                    ChatSessionRowView(
                        session: session, 
                        activeMessage: viewModel.sessionActiveMessageMap[session.id],
                        instanceContexts: viewModel.sessionActiveInstanceContextsMap[session.id] ?? []
                    )
                }
                .buttonStyle(.plain)
            }
            .onDelete { indexSet in
                viewModel.deleteSession(at: indexSet)
            }
        }
        .listStyle(.plain)
    }
}

#if DEBUG
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    ChatsView(viewModel: container.makeChatsViewModel())
        .environmentObject(container)
}
#endif
