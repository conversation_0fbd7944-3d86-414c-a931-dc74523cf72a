import SwiftUI

/// A sheet view for displaying and selecting raw text content from messages
struct RawTextSheetView: View {
    let textContent: String
    @Environment(\.dismiss) private var dismiss
    @State private var showCopiedToast = false
    
    var body: some View {
        NavigationView {
            ReadOnlyTextView(text: textContent)
            .navigationTitle("Select Text")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    <PERSON><PERSON>("Copy All") {
                        UIPasteboard.general.string = textContent
                        showCopiedToast = true
                    }
                }
            }
        }
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
        .toast(
            icon: "checkmark.circle.fill",
            message: "Copied",
            isShowing: $showCopiedToast
        )
    }
}

#if DEBUG
struct RawTextSheetView_Previews: PreviewProvider {
    static var previews: some View {
        RawTextSheetView(textContent: "This is a sample message content for preview. It contains multiple lines of text to demonstrate how the raw text sheet will look when displaying actual message content.")
    }
}
#endif 
