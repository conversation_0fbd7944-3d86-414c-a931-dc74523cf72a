import SwiftUI

/// Displays a single chat session in the chat list
struct ChatSessionRowView: View {
    // MARK: - Properties
    
    let session: ChatSession
    let activeMessage: Message?
    let instanceContexts: [LLMInstanceContext]

    @EnvironmentObject private var container: DIContainer
    
    /// Computed property to get a sample of the last message for the chat preview
    private var lastMessagePreview: String {
        guard let lastMessage = activeMessage else {
            return String(localized: "No messages yet")
        }
        
        // Get the text content
        return lastMessage.textContentCleaned.isEmpty ? String(localized: "No text content") : lastMessage.textContentCleaned
    }
    
    /// Formats the last modified date into a user-friendly string
    private var formattedDate: String {
        let calendar = Calendar.current
        let now = Date()
        let dateToFormat = session.lastModifiedAt

        if calendar.isDateInToday(dateToFormat) {
            // e.g., "10:30 AM"
            return dateToFormat.formatted(date: .omitted, time: .shortened)
        }

        // Check for dates within the last 7 days
        if let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: now), dateToFormat > sevenDaysAgo {
            // e.g., "Wed"
            return dateToFormat.formatted(.dateTime.weekday(.abbreviated))
        }

        // Check for dates within the current year
        if calendar.isDate(dateToFormat, equalTo: now, toGranularity: .year) {
            // e.g., "28/06"
            return dateToFormat.formatted(.dateTime.day().month(.twoDigits))
        }

        // e.g., "06/28/25"
        return dateToFormat.formatted(date: .numeric, time: .omitted)
    }
    
    /// Generates intelligent title based on session title or instance names
    private var intelligentTitle: String {
        // Check if session has a valid title
        if let title = session.title, !title.isEmpty && title != ChatSession.defaultSessionName {
            return title
        }
        
        // Generate title from instance contexts
        let instanceNames = instanceContexts.map { $0.instance.name }
        
        switch instanceNames.count {
        case 0:
            return ChatSession.defaultSessionName
        case 1:
            return instanceNames[0]
        default:
            return instanceNames.joined(separator: ", ")
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        HStack(alignment: .center, spacing: 12) {
            // Logo display (using the existing ChatSessionInstancesLogoView)
            ChatSessionInstancesLogoView(
                instanceContexts: instanceContexts,
                size: 40
            )
            
            // Main content (title and preview)
            VStack(alignment: .leading, spacing: 4) {
                HStack{
                    Text(intelligentTitle)
                        .font(.body)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    Spacer()
                    Text(formattedDate)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                
                Text(lastMessagePreview)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
    }
}

#if DEBUG
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    List {
        ForEach(MockChatPreviewData.getAllExampleSessions()) { session in
            ChatSessionRowView(
                session: session, 
                activeMessage: MockChatPreviewData.getMessagesForSession(sessionId: session.id).last,
                instanceContexts: [] // TODO: Update with mock instance contexts
            )
        }
    }
    .environmentObject(container)
}
#endif 
