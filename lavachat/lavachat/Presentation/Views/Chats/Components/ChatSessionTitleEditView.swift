import SwiftUI

struct ChatSessionTitleEditView: View {
    // MARK: - Properties
    
    @State private var title: String
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isTextFieldFocused: Bool
    
    private let onSave: (String) -> Void
    
    // MARK: - Initialization
    
    init(initialTitle: String, onSave: @escaping (String) -> Void) {
        _title = State(initialValue: initialTitle)
        self.onSave = onSave
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
                // Text Field Section
                List {
                    Section {
                        TextField("Chat title", text: $title)
                            .focused($isTextFieldFocused)
                            .submitLabel(.done)
                            .onSubmit {
                                saveAndDismiss()
                            }
                    } footer: {
                        Text("Enter a custom title for this chat session.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Edit Title")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveAndDismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        .onAppear {
            // Auto-focus the text field when the view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isTextFieldFocused = true
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func saveAndDismiss() {
        onSave(title.trimmingCharacters(in: .whitespacesAndNewlines))
        dismiss()
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    ChatSessionTitleEditView(
        initialTitle: "Example Chat Title",
        onSave: { newTitle in
            print("New title: \(newTitle)")
        }
    )
}
#endif
