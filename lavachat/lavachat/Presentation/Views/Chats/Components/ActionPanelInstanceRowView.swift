import SwiftUI

/// View for displaying an instance row in the ActionPanel
struct ActionPanelInstanceRowView: View {
    
    // MARK: - Properties
    
    let instanceContext: LLMInstanceContext
    @ObservedObject var viewModel: ActionPanelViewModel
    
    @Environment(\.colorScheme) var colorScheme
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: 12) {
            // Instance Logo
            InstanceLogoView(
                instance: instanceContext.instance,
                model: instanceContext.model,
                provider: instanceContext.provider,
                size: 32
            )
            .frame(width: 32, height: 32)
            
            // Instance Name
            VStack(alignment: .leading, spacing: 2) {
                Text(instanceContext.instance.name)
                    .font(.body)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text("Model: \(instanceContext.model.name)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // Status Icons
            HStack(spacing: 16) {
                // Thinking Icon
                thinkingIconView
                
                // Network Icon
                networkIconView
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 4)
        .background(Color(.systemBackground))
        .contentShape(Rectangle()) // Make entire row tappable
        .onTapGesture {
            // Only trigger row tap if not tapping on icons
            viewModel.handleInstanceRowTap(for: instanceContext)
        }
    }
    
    // MARK: - Subviews
    
    /// Thinking status icon
    @ViewBuilder
    private var thinkingIconView: some View {
        let thinkingState = viewModel.getThinkingState(for: instanceContext)
        
        Button(action: {
            viewModel.handleThinkingTap(for: instanceContext)
        }) {
            Image(systemName: thinkingState.iconName)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(thinkingState.iconColor == "gray" ? .gray : .blue)
                .frame(width: 24, height: 24)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// Network status icon
    @ViewBuilder
    private var networkIconView: some View {
        let networkState = viewModel.getNetworkState(for: instanceContext)
        
        Button(action: {
            viewModel.handleNetworkTap(for: instanceContext)
        }) {
            Image(systemName: networkState.iconName)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(networkState.iconColor == "gray" ? .gray : .blue)
                .frame(width: 24, height: 24)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// View for the "Add New Instance" row
struct AddActionPanelInstanceRowView: View {
    
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Plus Icon
            Image(systemName: "plus.circle.fill")
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(.blue)
                .frame(width: 32, height: 32)
            
            // Add Instance Text
            Text("Add Instance")
                .font(.body)
                .foregroundColor(.blue)
            
            Spacer()
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 4)
        .background(Color(.systemBackground))
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Preview Provider

#if DEBUG
#Preview("Instance Row") {
    // Create sample context manually using available mock data
    let sampleProvider = LLMProvider(
        id: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
        name: "Anthropic",
        logoImageName: "provider_logo_anthropic",
        websiteUrl: "https://anthropic.com",
        apiDocumentationUrl: "https://docs.anthropic.com/claude/reference",
        apiBaseUrl: "https://api.anthropic.com",
        providerType: .userApiKey,
        apiKeyStored: false,
        apiStyle: .anthropic,
        apiEndpointPath: "/v1/messages",
        isUserCreated: false,
        isUserModified: false,
        metadata: nil
    )
    
    let sampleModel = LLMModel(
        id: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!,
        providerId: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
        modelIdentifier: "claude-3-7-sonnet-20250219",
        name: "Claude 3.7 Sonnet",
        modelDescription: "Anthropic most intelligent model.",
        logoImageName: "model_logo_anthropic_claude",
        contextWindowSize: 200000,
        inputModalities: [.text, .image, .pdf],
        outputModalities: [.text],
        thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "thinking"),
        maxOutputTokens: 64000,
        pricingInfo: "Input: $15/M tokens\nOutput: $75/M tokens",
        availabilityStatus: .available,
        isDefaultRecommendation: true,
        isUserCreated: false,
        isUserModified: false,
        metadata: nil
    )
    
    let sampleInstance = LLMInstance(
        id: UUID(uuidString: "D2D3D4D5-E6E7-7890-D2D3-D4D5E6E77890")!,
        modelId: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!,
        name: "Claude 3.7 Sonnet",
        systemPrompt: "You are a helpful assistant.",
        defaultParameters: ["temperature": "0.5", "thinking_prompt": "Think step by step."],
        totalPromptTokensUsed: 500,
        totalCompletionTokensUsed: 250,
        createdAt: Date().addingTimeInterval(-86400 * 3),
        lastUsedAt: Date().addingTimeInterval(-7200),
        isFavorited: false,
        isUserModified: false,
        metadata: nil
    )
    
    let sampleContext = LLMInstanceContext(
        instance: sampleInstance,
        model: sampleModel,
        provider: sampleProvider
    )
    
    let mockData = ActionPanelData(
        instances: [sampleContext],
        instanceSettings: [
            sampleInstance.id: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false)
        ],
        savedPromptSegments: [],
        resolvedMessageActionSettings: ResolvedMessageActionSettings(
            actionPanelActions: [
                SystemMessageActions.camera,
                SystemMessageActions.photo,
                SystemMessageActions.file
            ]
        )
    )
    let mockCallbacks = ActionPanelCallbacks(
        toggleInstanceThinking: { _ in return true },
        toggleInstanceNetwork: { _ in return true },
        navigateToInstanceDetail: { _ in },
        removeInstanceFromSession: { _ in return true },
        addInstanceToSession: { instanceId in
            print("Add instance to session: \(instanceId)")
            return nil
        },
        handleActionPanelPromptInsert: { _ in },
        handleActionPanelPromptRewrite: { _ in },
        handleCameraAction: { },
        handlePhotoAction: { },
        handleFileAction: { }
    )
    let viewModel = ActionPanelViewModel(data: mockData, callbacks: mockCallbacks)
    
    List {
        ActionPanelInstanceRowView(
            instanceContext: sampleContext,
            viewModel: viewModel
        )
        
        AddActionPanelInstanceRowView {
            print("Add instance tapped")
        }
    }
    .listStyle(.insetGrouped)
}
#endif 