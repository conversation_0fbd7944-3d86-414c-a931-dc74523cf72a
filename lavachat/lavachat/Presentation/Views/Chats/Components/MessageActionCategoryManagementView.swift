import SwiftUI

struct MessageActionCategoryManagementView: View {
    // MARK: - Properties
    
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var container: DIContainer
    
    let category: MessageActionCategory
    let currentActionIds: [UUID]
    let onSave: ([UUID]) -> Void
    
    @State private var selectedActionIds: [UUID] = []
    @State private var allAvailableActions: [MessageAction] = []
    @State private var selectedActions: [MessageAction] = []
    @State private var unselectedActions: [MessageAction] = []
    @State private var isLoading: Bool = true
    @State private var errorMessage: String? = nil
    @State private var hasChanges: Bool = false
    
    // MARK: - Initialization
    
    init(
        category: MessageActionCategory,
        currentActionIds: [UUID],
        onSave: @escaping ([UUID]) -> Void
    ) {
        self.category = category
        self.currentActionIds = currentActionIds
        self.onSave = onSave
        self._selectedActionIds = State(initialValue: currentActionIds)
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack {
            if isLoading {
                ProgressView("Loading actions...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if allAvailableActions.isEmpty {
                Text("No actions available for this category")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    // Selected Actions Section (can be reordered)
                    if !selectedActions.isEmpty {
                        Section {
                            ForEach(selectedActions) { action in
                                createActionRow(action: action, isSelected: true)
                            }
                            .onMove(perform: moveSelectedActions)
                        } header: {
                            Text("Selected Actions")
                        } footer: {
                            Text(category.locationDescription)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Available Actions Section (cannot be reordered)
                    if !unselectedActions.isEmpty {
                        Section("Available Actions") {
                            ForEach(unselectedActions) { action in
                                createActionRow(action: action, isSelected: false)
                                    .id("unselected-\(action.id)")
                            }
                        }
                    }
                }
                // .listStyle(.plain)
                .environment(\.editMode, .constant(.active))
            }
        }
        .navigationTitle(category.rawValue)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    onSave(selectedActionIds)
                    dismiss()
                }
                .foregroundColor(hasChanges ? .accentColor : .gray)
                .disabled(!hasChanges)
            }
        }
        .onAppear {
            Task {
                await loadAvailableActions()
            }
        }
        .onChange(of: selectedActionIds) { _ in
            hasChanges = selectedActionIds != currentActionIds
        }
        .alert("Error", isPresented: .constant(errorMessage != nil)) {
            Button("OK") {
                errorMessage = nil
            }
        } message: {
            Text(errorMessage ?? "")
        }
    }
    
    // MARK: - Helper Methods

    @ViewBuilder
    private func createActionRow(action: MessageAction, isSelected: Bool) -> some View {
        HStack {
            // Action content
            HStack(spacing: 12) {
                if let icon = action.icon {
                    Image(systemName: icon)
                        .frame(width: 24, height: 24)
                        .foregroundColor(.accentColor)
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text(action.name)
                        .foregroundColor(.primary)

                    if !action.prompts.isEmpty {
                        Text("\(action.prompts.count) prompt\(action.prompts.count == 1 ? "" : "s")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Spacer()

            // Selection indicator
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.accentColor)
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.gray)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            toggleSelection(for: action.id)
        }
    }

    private func updateActionLists() {
        // Update selected actions in the correct order
        selectedActions = selectedActionIds.compactMap { selectedId in
            allAvailableActions.first { $0.id == selectedId }
        }

        // Update unselected actions
        unselectedActions = allAvailableActions.filter { action in
            !selectedActionIds.contains(action.id)
        }
    }

    private func loadAvailableActions() async {
        do {
            // Get all actions including system actions
            let getAllMessageActionsUseCase = container.makeGetAllMessageActionsUseCase()
            let allActions = try await getAllMessageActionsUseCase.executeIncludingSystemActions()
            
            // Filter actions suitable for this category
            let suitableActions = allActions.filter(category.suitabilityCheck)
            
            await MainActor.run {
                self.allAvailableActions = suitableActions
                self.updateActionLists()
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load actions: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }
    
    private func toggleSelection(for actionId: UUID) {
        if selectedActionIds.contains(actionId) {
            selectedActionIds.removeAll { $0 == actionId }
        } else {
            selectedActionIds.append(actionId)
        }
        updateActionLists()
    }
    
    private func moveSelectedActions(from source: IndexSet, to destination: Int) {
        print("🔄 Move selected actions: source=\(source), destination=\(destination)")
        print("📋 Current selectedActionIds before move: \(selectedActionIds)")

        // Move the IDs in the source-of-truth array
        selectedActionIds.move(fromOffsets: source, toOffset: destination)
        
        print("📋 Current selectedActionIds after move: \(selectedActionIds)")

        // Now, update the derived array of full objects.
        // This will maintain the new order.
        updateActionLists()
        print("✅ Move completed successfully")
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    NavigationStack {
        MessageActionCategoryManagementView(
            category: .userMessage,
            currentActionIds: [SystemMessageActions.copy.id, SystemMessageActions.edit.id],
            onSave: { actionIds in
                print("Saved action IDs: \(actionIds)")
            }
        )
        .environmentObject(DIContainer(context: PersistenceController.preview.container.viewContext))
    }
}
#endif
