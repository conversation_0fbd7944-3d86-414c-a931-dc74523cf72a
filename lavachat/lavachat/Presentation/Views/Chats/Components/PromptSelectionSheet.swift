import SwiftUI

/// Sheet for selecting a prompt from multiple options
struct PromptSelectionSheet: View {
    let action: MessageAction
    let onSelect: (String) -> Void
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var container: DIContainer
    @State private var expandedPromptIndex: Int? = nil
    @State private var showActionEditor: Bool = false
    @State private var currentAction: MessageAction

    // MARK: - Initialization
    init(action: MessageAction, onSelect: @escaping (String) -> Void) {
        self.action = action
        self.onSelect = onSelect
        self._currentAction = State(initialValue: action)
    }

    var body: some View {
        NavigationStack {
            List {
                // Prompts list section
                Section {
                    ForEach(Array(currentAction.prompts.enumerated()), id: \.offset) { index, prompt in
                        PromptRowView(
                            prompt: prompt,
                            index: index,
                            isExpanded: expandedPromptIndex == index,
                            onTap: {
                                onSelect(prompt)
                                dismiss()
                            },
                            onExpandToggle: {
                                expandedPromptIndex = expandedPromptIndex == index ? nil : index
                            }
                        )
                    }
                } footer: {
                    Text(currentAction.actionType.promptSelectionDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .listStyle(.plain)
            .navigationTitle(currentAction.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        showActionEditor = true
                    }) {
                        Image(systemName: "pencil.line")
                            .foregroundColor(.accentColor)
                    }
                    .foregroundColor(.accentColor)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.accentColor)
                    }
                }
            }
            .navigationDestination(isPresented: $showActionEditor) {
                ActionEditorView(
                    category: currentAction.defaultCategory,
                    editingAction: currentAction,
                    viewModel: container.makeActionEditorViewModel(
                        category: currentAction.defaultCategory,
                        editingAction: currentAction
                    ),
                    onComplete: { updatedAction in
                        // Update the current action with the modified version
                        currentAction = updatedAction
                        showActionEditor = false
                    }
                )
            }
        }
        .presentationDetents([.medium, .large], selection: .constant(showActionEditor ? .large : .medium))
        .presentationDragIndicator(.hidden)
    }
}

/// Individual prompt row view
struct PromptRowView: View {
    let prompt: String
    let index: Int
    let isExpanded: Bool
    let onTap: () -> Void
    let onExpandToggle: () -> Void

    @State private var isTruncated: Bool? = nil

    // Fast pre-check for obvious truncation cases
    private var needsExpansion: Bool {
        // Quick checks that don't require layout calculation
        let hasLineBreaks = prompt.contains("\n")
        let isVeryLong = prompt.count > 100

        return hasLineBreaks || isVeryLong
    }

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Prompt text with smart truncation detection
            VStack(alignment: .leading, spacing: 8) {
                Text(prompt)
                    .font(.body)
                    .foregroundColor(.primary)
                    .lineLimit(!isExpanded ? 1 : nil)
                    .multilineTextAlignment(.leading)
                    .background(
                        // Only run smart detection for edge cases
                        Group {
                            if needsExpansion {
                                Color.clear
                            } else {
                                calculateTruncation(text: prompt)
                            }
                        }
                    )
            }

            Spacer()

            // Expand/collapse button (show if fast check says yes OR smart detection says yes)
            if needsExpansion || isTruncated == true {
                Button(action: onExpandToggle) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(width: 24, height: 24) // Ensure adequate touch target
                        .contentShape(Rectangle()) // Make entire frame tappable
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
        // Re-calculate isTruncated for the new text
        .onChange(of: prompt) { _ in
            isTruncated = nil
        }
        // Set initial state based on fast check
        .onAppear {
            if needsExpansion {
                isTruncated = true
            }
        }
    }

    /// Smart truncation detection using ViewThatFits
    func calculateTruncation(text: String) -> some View {
        // First check if text has explicit line breaks
        let hasLineBreaks = text.contains("\n")

        return ViewThatFits(in: .horizontal) {
            // Try to fit the text in one line
            Text(text)
                .font(.body)
                .lineLimit(1)
                .fixedSize(horizontal: true, vertical: false)
                .hidden()
                .onAppear {
                    // If text fits horizontally in one line, check if it has line breaks
                    guard isTruncated == nil else { return }
                    isTruncated = hasLineBreaks
                }

            // If text doesn't fit horizontally in one line
            Color.clear
                .hidden()
                .onAppear {
                    // Text needs truncation (either horizontal overflow or line breaks)
                    guard isTruncated == nil else { return }
                    isTruncated = true
                }
        }
    }
}

// MARK: - Preview

#if DEBUG
#Preview("Prompt Selection Sheet") {
    let sampleAction = MessageAction(
        name: "Translate",
        icon: "translate",
        actionType: .assistantRegenerate,
        prompts: [
            "Please translate your last response to English.",
            "Please translate your last response to Simplified Chinese.",
            "Please translate your last response to Traditional Chinese with proper cultural context and formal tone.",
            "Please translate your last response to Japanese.",
            "Please translate your last response to Korean.",
            "Please translate your last response to French.",
            "Please translate your last response to Spanish."
        ]
    )
    
    PromptSelectionSheet(action: sampleAction) { selectedPrompt in
        print("Selected prompt: \(selectedPrompt)")
    }
}

#Preview("Action Panel Prompt Insert") {
    let sampleAction = MessageAction(
        name: "Quick Prompts",
        icon: "lightbulb",
        actionType: .actionPanelPromptInsert,
        prompts: [
            "Please explain this in simple terms.",
            "Can you provide examples?",
            "Break this down step by step.",
            "What are the pros and cons?"
        ]
    )
    
    PromptSelectionSheet(action: sampleAction) { selectedPrompt in
        print("Selected prompt: \(selectedPrompt)")
    }
}
#endif
