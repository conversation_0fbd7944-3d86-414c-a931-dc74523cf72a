import SwiftUI
import UIKit
import PhotosUI
import QuickLook

// MARK: - Document Picker

struct DocumentPickerView: UIViewControllerRepresentable {
    @Binding var selectedFiles: [URL]
    
    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [.item], asCopy: true)
        picker.allowsMultipleSelection = true
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {
        // Nothing to update
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPickerView
        
        init(_ parent: DocumentPickerView) {
            self.parent = parent
        }
        
        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            parent.selectedFiles = urls
        }
    }
}

// MARK: - Camera Picker (for camera only)

struct CameraPickerView: View {
    @Binding var selectedImages: [UIImage]
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        CameraPickerRepresentable(selectedImages: $selectedImages)
            .ignoresSafeArea(.all)
    }
}

struct CameraPickerRepresentable: UIViewControllerRepresentable {
    @Binding var selectedImages: [UIImage]
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = context.coordinator
        picker.allowsEditing = false
        picker.modalPresentationStyle = .fullScreen
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {
        // Nothing to update
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraPickerRepresentable

        init(_ parent: CameraPickerRepresentable) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImages.append(image)
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

// MARK: - Photos Picker (iOS 16+)

struct PhotosPickerWrapper: View {
    @Binding var selectedImages: [UIImage]
    @Binding var isPresented: Bool
    @State private var selectedItems: [PhotosPickerItem] = []

    var body: some View {
        PhotosPicker(
            selection: $selectedItems,
            maxSelectionCount: 10,
            matching: .images,
            photoLibrary: .shared()
        ) {
            EmptyView() // This will be triggered programmatically
        }
        .onChange(of: selectedItems) { items in
            Task {
                for item in items {
                    if let data = try? await item.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        await MainActor.run {
                            selectedImages.append(image)
                        }
                    }
                }
                await MainActor.run {
                    selectedItems = []
                    isPresented = false
                }
            }
        }
    }
}

// MARK: - File Thumbnail Views

struct ImageThumbnailView: View {
    let image: UIImage
    let onDelete: () -> Void

    var body: some View {
        ZStack(alignment: .topTrailing) {
            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 64, height: 64)
                .clipShape(RoundedRectangle(cornerRadius: 12))

            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
                    .background(Color.black.opacity(0.6))
                    .clipShape(Circle())
            }
            .offset(x: 4, y: -4)
        }
        .padding(.top, 4)
        .padding(.trailing, 4)
    }
}

struct FileThumbnailView: View {
    let url: URL
    let onDelete: () -> Void

    var body: some View {
        ZStack(alignment: .topTrailing) {
            VStack(spacing: 6) {
                Image(systemName: fileIcon)
                    .font(.system(size: 24))
                    .foregroundColor(.blue)

                Text(url.lastPathComponent)
                    .font(.caption2)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
            }
            .frame(width: 64, height: 64)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))

            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
                    .background(Color.black.opacity(0.6))
                    .clipShape(Circle())
            }
            .offset(x: 4, y: -4)
        }
        .padding(.top, 4)
        .padding(.trailing, 4)
    }
    
    private var fileIcon: String {
        let ext = url.pathExtension.lowercased()
        switch ext {
        case "pdf": return "document.fill"
        case "txt", "md": return "text.document.fill"
        case "zip", "rar": return "archivebox.fill"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        case "jpg", "jpeg", "png", "gif": return "photo"
        default: return "doc"
        }
    }
}

// MARK: - File Preview Section

struct FilePreviewSection: View {
    @Binding var selectedImages: [UIImage]
    @Binding var selectedFiles: [URL]
    
    var body: some View {
        if !selectedImages.isEmpty || !selectedFiles.isEmpty {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // Image preview
                    ForEach(selectedImages.indices, id: \.self) { index in
                        ImageThumbnailView(
                            image: selectedImages[index],
                            onDelete: {
                                selectedImages.remove(at: index)
                            }
                        )
                    }
                    
                    // File preview
                    ForEach(selectedFiles.indices, id: \.self) { index in
                        FileThumbnailView(
                            url: selectedFiles[index],
                            onDelete: {
                                selectedFiles.remove(at: index)
                            }
                        )
                    }
                }
                .padding(.horizontal, 4)
            }
            .frame(height: 80)
        }
    }
}

// MARK: - Message Content Views

struct MessageImageView: View {
    let imageInfo: ImageInfo
    @State private var isFullScreenPresented = false

    var body: some View {
        Group {
            if let data = imageInfo.data, let uiImage = UIImage(data: data) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else if let url = imageInfo.url, !url.isEmpty {
                AsyncImage(url: URL(string: url)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        )
                }
            } else {
                Rectangle()
                    .fill(Color(.systemGray5))
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    )
            }
        }
        .frame(width: 80, height: 80)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onTapGesture {
            isFullScreenPresented = true
        }
        .fullScreenCover(isPresented: $isFullScreenPresented) {
            FullScreenImageView(imageInfo: imageInfo)
        }
    }
}

struct MessageFileView: View {
    let fileInfo: FileInfo
    @State private var isQuickLookPresented = false

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: fileIcon)
                .font(.system(size: 24))
                .foregroundColor(.blue)

            Text(fileInfo.fileName)
                .font(.caption2)
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .foregroundColor(.primary)
        }
        .frame(width: 80, height: 80)
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onTapGesture {
            isQuickLookPresented = true
        }
        .fullScreenCover(isPresented: $isQuickLookPresented) {
            QuickLookPreview(fileInfo: fileInfo)
        }
    }

    private var fileIcon: String {
        let ext = (fileInfo.fileName as NSString).pathExtension.lowercased()
        switch ext {
        case "pdf": return "document.fill"
        case "txt", "md": return "text.document.fill"
        case "zip", "rar": return "archivebox.fill"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        case "jpg", "jpeg", "png", "gif": return "photo"
        default: return "doc"
        }
    }
}

// MARK: - Extensions

extension URL {
    func mimeType() -> String {
        let pathExtension = self.pathExtension.lowercased()
        
        switch pathExtension {
        case "jpg", "jpeg":
            return "image/jpeg"
        case "png":
            return "image/png"
        case "gif":
            return "image/gif"
        case "pdf":
            return "application/pdf"
        case "txt":
            return "text/plain"
        case "md":
            return "text/markdown"
        case "json":
            return "application/json"
        case "zip":
            return "application/zip"
        case "mp3":
            return "audio/mpeg"
        case "mp4":
            return "video/mp4"
        case "mov":
            return "video/quicktime"
        default:
            return "application/octet-stream"
        }
    }
}

// MARK: - Full Screen Image Viewer

struct FullScreenImageView: View {
    let imageInfo: ImageInfo
    @Environment(\.dismiss) private var dismiss
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero

    var body: some View {
        NavigationView {
            ZStack {
                Color.black
                    .ignoresSafeArea()

                Group {
                    if let data = imageInfo.data, let uiImage = UIImage(data: data) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } else if let url = imageInfo.url, !url.isEmpty {
                        AsyncImage(url: URL(string: url)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                        } placeholder: {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        }
                    } else {
                        Image(systemName: "photo")
                            .font(.system(size: 100))
                            .foregroundColor(.gray)
                    }
                }
                .scaleEffect(scale)
                .offset(offset)
                .gesture(
                    SimultaneousGesture(
                        MagnificationGesture()
                            .onChanged { value in
                                scale = max(1.0, min(value, 5.0))
                            },
                        DragGesture()
                            .onChanged { value in
                                offset = CGSize(
                                    width: lastOffset.width + value.translation.width,
                                    height: lastOffset.height + value.translation.height
                                )
                            }
                            .onEnded { _ in
                                lastOffset = offset
                            }
                    )
                )
                .onTapGesture(count: 2) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        if scale > 1.0 {
                            scale = 1.0
                            offset = .zero
                            lastOffset = .zero
                        } else {
                            scale = 2.0
                        }
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }
}

// MARK: - QuickLook Preview

struct QuickLookPreview: UIViewControllerRepresentable {
    let fileInfo: FileInfo
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UINavigationController {
        let previewController = QLPreviewController()
        previewController.dataSource = context.coordinator
        previewController.delegate = context.coordinator

        // Wrap in navigation controller to add close button
        let navController = UINavigationController(rootViewController: previewController)

        // Configure navigation bar appearance to eliminate white borders
        let navAppearance = UINavigationBarAppearance()
        navAppearance.configureWithOpaqueBackground()
        navAppearance.backgroundColor = UIColor.systemBackground
        navAppearance.shadowColor = .clear // Remove shadow/border

        navController.navigationBar.standardAppearance = navAppearance
        navController.navigationBar.scrollEdgeAppearance = navAppearance
        navController.navigationBar.compactAppearance = navAppearance

        // Configure toolbar appearance to match navigation bar
        let toolbarAppearance = UIToolbarAppearance()
        toolbarAppearance.configureWithOpaqueBackground()
        toolbarAppearance.backgroundColor = UIColor.systemBackground
        toolbarAppearance.shadowColor = .clear // Remove shadow/border

        navController.toolbar.standardAppearance = toolbarAppearance
        navController.toolbar.scrollEdgeAppearance = toolbarAppearance
        navController.toolbar.compactAppearance = toolbarAppearance

        // Ensure the navigation bar and toolbar extend to the edges
        navController.navigationBar.isTranslucent = false
        navController.toolbar.isTranslucent = false
        navController.extendedLayoutIncludesOpaqueBars = true

        // Configure the preview controller to eliminate gaps
        previewController.edgesForExtendedLayout = [.top, .bottom]
        previewController.extendedLayoutIncludesOpaqueBars = true

        // Add close button
        let closeButton = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: context.coordinator,
            action: #selector(context.coordinator.closePreview)
        )
        previewController.navigationItem.rightBarButtonItem = closeButton

        // Store dismiss action in coordinator
        context.coordinator.dismissAction = { dismiss() }

        return navController
    }

    func updateUIViewController(_ uiViewController: UINavigationController, context: Context) {
        // Nothing to update
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, QLPreviewControllerDataSource, QLPreviewControllerDelegate {
        let parent: QuickLookPreview
        private var tempFileURL: URL?
        var dismissAction: (() -> Void)?

        init(_ parent: QuickLookPreview) {
            self.parent = parent
            super.init()
            createTempFile()
        }

        @objc func closePreview() {
            dismissAction?()
        }

        deinit {
            // Clean up temp file
            if let tempFileURL = tempFileURL {
                try? FileManager.default.removeItem(at: tempFileURL)
            }
        }

        private func createTempFile() {
            // Convert base64 data to Data
            guard let data = Data(base64Encoded: parent.fileInfo.base64Data) else {
                print("QuickLook: Failed to decode base64 data for file: \(parent.fileInfo.fileName)")
                print("QuickLook: Base64 data length: \(parent.fileInfo.base64Data.count)")
                print("QuickLook: Base64 data prefix: \(String(parent.fileInfo.base64Data.prefix(50)))")
                return
            }

            let tempDir = FileManager.default.temporaryDirectory
            // Ensure the file has the correct extension for QuickLook to recognize the type
            var fileName = parent.fileInfo.fileName
            if !fileName.contains(".") {
                // Add extension based on MIME type if missing
                let fileExt = fileExtension(for: parent.fileInfo.mimeType)
                fileName = "\(fileName).\(fileExt)"
            }
            let tempFileURL = tempDir.appendingPathComponent(fileName)

            do {
                // Remove existing file if it exists
                if FileManager.default.fileExists(atPath: tempFileURL.path) {
                    try FileManager.default.removeItem(at: tempFileURL)
                }

                try data.write(to: tempFileURL)
                self.tempFileURL = tempFileURL
                print("QuickLook: Successfully created temp file at: \(tempFileURL)")
                print("QuickLook: File size: \(data.count) bytes")
                print("QuickLook: MIME type: \(parent.fileInfo.mimeType)")
                print("QuickLook: Final filename: \(fileName)")
            } catch {
                print("QuickLook: Failed to create temp file: \(error)")
            }
        }

        private func fileExtension(for mimeType: String) -> String {
            switch mimeType.lowercased() {
            case "application/pdf":
                return "pdf"
            case "application/msword":
                return "doc"
            case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                return "docx"
            case "application/vnd.ms-excel":
                return "xls"
            case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                return "xlsx"
            case "application/vnd.ms-powerpoint":
                return "ppt"
            case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
                return "pptx"
            case "text/plain":
                return "txt"
            case "application/zip":
                return "zip"
            case "application/x-rar-compressed":
                return "rar"
            case "image/jpeg":
                return "jpg"
            case "image/png":
                return "png"
            case "image/gif":
                return "gif"
            default:
                return "bin"
            }
        }

        func numberOfPreviewItems(in controller: QLPreviewController) -> Int {
            return tempFileURL != nil ? 1 : 0
        }

        func previewController(_ controller: QLPreviewController, previewItemAt index: Int) -> QLPreviewItem {
            guard let tempFileURL = tempFileURL else {
                print("QuickLook: No temp file URL available")
                return NSURL()
            }
            print("QuickLook: Returning preview item for URL: \(tempFileURL)")
            return tempFileURL as QLPreviewItem
        }
    }
}
