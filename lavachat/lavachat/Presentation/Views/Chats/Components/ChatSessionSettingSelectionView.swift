import SwiftUI

struct ChatSessionSettingSelectionView: View {
    // MARK: - Properties
    
    @Environment(\.dismiss) private var dismiss
    
    let allSettings: [ChatSessionSetting]
    let currentSettingId: UUID?
    let onSelect: (UUID) -> Void
    
    @State private var temporarySelection: UUID?
    @State private var hasChanges: Bool = false
    
    // MARK: - Initialization
    
    init(
        allSettings: [ChatSessionSetting],
        currentSettingId: UUID?,
        onSelect: @escaping (UUID) -> Void
    ) {
        self.allSettings = allSettings
        self.currentSettingId = currentSettingId
        self.onSelect = onSelect
        self._temporarySelection = State(initialValue: currentSettingId)
    }
    
    // MARK: - Body
    
    var body: some View {
        List {
            ForEach(allSettings) { setting in
                Button(action: {
                    temporarySelection = setting.id
                }) {
                    HStack(spacing: 16) {
                        // Setting content
                        VStack(alignment: .leading, spacing: 6) {
                            // Setting name with system indicator
                            HStack(spacing: 8) {
                                Text(setting.name)
                                    .font(.body)
                                    .foregroundColor(.primary)

                                if setting.isSystemDefault {
                                    Image(systemName: "lock.fill")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }

                            // Setting details
                            createSettingDetailsView(for: setting)
                        }

                        Spacer()

                        // Selection indicator
                        Image(systemName: temporarySelection == setting.id ? "checkmark.circle.fill" : "circle")
                            .font(.title2)
                            .foregroundColor(temporarySelection == setting.id ? .accentColor : .secondary)
                    }
                    .padding(.vertical, 4)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .listStyle(.insetGrouped)
        .navigationTitle("Select Setting")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    if let selectedId = temporarySelection {
                        onSelect(selectedId)
                    }
                    dismiss()
                }
                .fontWeight(.semibold)
                .disabled(!hasChanges)
            }
        }
        .onChange(of: temporarySelection) { newValue in
            hasChanges = newValue != currentSettingId
        }
    }

    // MARK: - Helper Methods

    @ViewBuilder
    private func createSettingDetailsView(for setting: ChatSessionSetting) -> some View {
        HStack(spacing: 16) {
            // Thinking indicator
            HStack(spacing: 4) {
                Image(systemName: "lightbulb")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(setting.shouldExpandThinking ? .accentColor : .secondary)
                Text("Thinking")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Separator dot
            Circle()
                .fill(Color.secondary)
                .frame(width: 3, height: 3)

            // Actions count
            HStack(spacing: 4) {
                Image(systemName: "button.programmable")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                if let actionSettings = setting.messageActionSettings {
                    let totalActions = calculateTotalActions(actionSettings)
                    Text("\(totalActions) actions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    Text("0 actions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }

    private func calculateTotalActions(_ actionSettings: MessageActionSettings) -> Int {
        let panelCount = actionSettings.actionPanelActions?.count ?? 0
        let userCount = actionSettings.userMessageActions?.count ?? 0
        let cardCount = actionSettings.assistantMessageCardActions?.count ?? 0
        let menuCount = actionSettings.assistantMessageMenuActions?.count ?? 0
        return panelCount + userCount + cardCount + menuCount
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    let mockSettings = [
        ChatSessionSetting(
            id: UUID(),
            name: "System Default",
            isSystemDefault: true,
            shouldExpandThinking: true,
            messageActionSettings: MessageActionSettings(
                actionPanelActions: [UUID(), UUID()],
                userMessageActions: [UUID()],
                assistantMessageCardActions: [UUID(), UUID(), UUID()],
                assistantMessageMenuActions: [UUID(), UUID()]
            ),
            auxiliaryLLMInstanceId: nil,
            shouldAutoGenerateTitle: false,
            contextMessageCount: Int64.max
        ),
        ChatSessionSetting(
            id: UUID(),
            name: "User Custom",
            isSystemDefault: false,
            shouldExpandThinking: false,
            messageActionSettings: MessageActionSettings(
                actionPanelActions: [UUID()],
                userMessageActions: [UUID(), UUID()],
                assistantMessageCardActions: [UUID()],
                assistantMessageMenuActions: [UUID()]
            ),
            auxiliaryLLMInstanceId: UUID(),
            shouldAutoGenerateTitle: true,
            contextMessageCount: 10
        ),
        ChatSessionSetting(
            id: UUID(),
            name: "Minimal Setup",
            isSystemDefault: false,
            shouldExpandThinking: true,
            messageActionSettings: MessageActionSettings(
                actionPanelActions: [],
                userMessageActions: [UUID()],
                assistantMessageCardActions: [UUID()],
                assistantMessageMenuActions: []
            ),
            auxiliaryLLMInstanceId: nil,
            shouldAutoGenerateTitle: false,
            contextMessageCount: 5
        )
    ]

    NavigationView {
        ChatSessionSettingSelectionView(
            allSettings: mockSettings,
            currentSettingId: mockSettings.first?.id,
            onSelect: { settingId in
                print("Selected setting: \(settingId)")
            }
        )
    }
}
#endif
