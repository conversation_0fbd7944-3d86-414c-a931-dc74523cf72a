import SwiftUI

/// View that displays an editing indicator when user is editing a message
struct EditingIndicatorView: View {
    let onCancel: () -> Void
    
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        HStack {
            // Editing icon and text
            HStack(spacing: 8) {
                Image(systemName: "pencil.line")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.secondary)
                
                Text("Editing message")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Cancel button
            Button(action: onCancel) {
                Image(systemName: "xmark")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.secondary)
                    .frame(width: 20, height: 20)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.systemGray5))
        .clipShape(
            UnevenRoundedRectangle(
                topLeadingRadius: 16,
                bottomLeadingRadius: 0,
                bottomTrailingRadius: 0,
                topTrailingRadius: 16
            )
        )
    }
}

// MARK: - Preview

#Preview {
    VStack {
        EditingIndicatorView(onCancel: {
            print("Cancel editing")
        })
        
        // Mock input field to show the visual connection
        RoundedRectangle(cornerRadius: 16)
            .fill(Color(.systemGray6))
            .frame(height: 40)
            .overlay(
                Text("Type your edited message...")
                    .foregroundColor(.secondary)
                    .padding(.leading, 12),
                alignment: .leading
            )
    }
    .padding()
}
