import SwiftUI
import MarkdownUI
import UIKit
import PhotosUI

struct ChatView: View {
    // MARK: - Properties

    @StateObject private var viewModel: ChatViewModel
    @EnvironmentObject private var container: DIContainer
    @EnvironmentObject private var chatsViewModel: ChatsViewModel
    @Environment(\.dismiss) private var dismiss

    @Environment(\.colorScheme) var colorScheme 
    
    // UI Control States
    @State private var isActionPanelPresented = false
    @State private var scrollViewLongestHeight: CGFloat = 0
    @FocusState private var isInputFieldFocused: Bool

    // Navigation States
    @State private var showChatSessionDetail = false
    
    // MARK: - Initialization
    
    init(viewModel: ChatViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            if (viewModel.isLoadingSession || viewModel.isLoadingMessages 
                || (viewModel.getMessageRowViewModelsForView().isEmpty && viewModel.chatSession?.activeMessageId != nil)) {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(errorMessage)
            } else {
                chatContent()
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbar(.hidden, for: .tabBar)
        .toolbar {
            ToolbarItem(placement: .principal) {
                titleView
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    Task {
                        let _ = await viewModel.startNewSessionBasedOnCurrent()
                    }
                }) {
                    Image(systemName: "plus.message")
                        .font(.system(size: 16, weight: .medium))
                }
            }
        }
    }
    
    // MARK: - UI Components
    
    // Loading View
    private var loadingView: some View {
        ProgressView("Loading Chat...")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(colorScheme == .light ? Color(.systemGray6) : Color.black)
    }
    
    // Error View
    private func errorView(_ message: String) -> some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.largeTitle)
                .foregroundColor(.red)
            Text("Error")
                .font(.headline)
            Text(message)
                .font(.caption)
                .multilineTextAlignment(.center)
                .padding()
            Button("Retry") {
                Task {
                    await viewModel.loadInitialData()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // Navigation Bar Title View
    private var titleView: some View {
        Button(action: {
            showChatSessionDetail = true
        }) {
            HStack {
                Text(viewModel.chatSession?.title ?? "Chat")
                    .font(.headline)
                    .foregroundColor(.primary)

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
    }

    // Floating Scroll to Bottom Button
    private var scrollToBottomButton: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                if viewModel.showScrollToBottomButton {
                    Button(action: {
                        viewModel.scrollToBottomButtonTapped()
                    }) {
                        Image(systemName: "arrow.down")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.black)
                            .frame(width: 44, height: 44)
                            .background(Color.white)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                            )
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    }
                    .transition(.scale.combined(with: .opacity))
                    .padding(.trailing, 16)
                    .padding(.bottom, 16)
                }
            }
        }
        .allowsHitTesting(viewModel.showScrollToBottomButton)
        .animation(.easeInOut(duration: 0.3), value: viewModel.showScrollToBottomButton)
    }
    
    // Main Chat Content
    private func chatContent() -> some View {
        
        return VStack(spacing: 0) {
            // Messages List with Floating Button
            ZStack {
                ScrollViewReader { scrollProxy in
                    ScrollView {
                        VStack(spacing: 0) {
                            // NEW ARCHITECTURE: Use MessageRowViewModels
                            ForEach(viewModel.getMessageRowViewModelsForView(), id: \.id) { messageRowViewModel in
                                MessageRowView(
                                    messageRowViewModel: messageRowViewModel,
                                    minHeight: 100,
                                    maxHeight: scrollViewLongestHeight - 20
                                )
                                .id(messageRowViewModel.id)
                            }
                            .padding(.horizontal, 8)
                            .padding(.bottom, 12)
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // Only dismiss keyboard when tapping on message content area
                            isInputFieldFocused = false
                        }
                        .padding(.top, 12)
                        .background(GeometryReader { geometry in
                            let contentHeight = geometry.size.height
                            let offset = -geometry.frame(in: .named("scrollView")).origin.y
                            
                            return Color.clear
                                .onAppear {
                                    // Get viewport height from the outer GeometryReader
                                    let viewportHeight = scrollViewLongestHeight > 0 ? scrollViewLongestHeight : geometry.size.height
                                    viewModel.updateScrollPositions(
                                        contentHeight: contentHeight,
                                        viewportHeight: viewportHeight,
                                        offset: offset
                                    )
                                }
                                .onChange(of: contentHeight) { _ in
                                    let viewportHeight = scrollViewLongestHeight > 0 ? scrollViewLongestHeight : geometry.size.height
                                    viewModel.updateScrollPositions(
                                        contentHeight: contentHeight,
                                        viewportHeight: viewportHeight,
                                        offset: offset
                                    )
                                }
                                .onChange(of: offset) { _ in
                                    let viewportHeight = scrollViewLongestHeight > 0 ? scrollViewLongestHeight : geometry.size.height
                                    viewModel.updateScrollPositions(
                                        contentHeight: contentHeight,
                                        viewportHeight: viewportHeight,
                                        offset: offset
                                    )
                                }
                        })
                    }
                    .coordinateSpace(name: "scrollView")
                    .background(GeometryReader { geometry in
                        Color.clear.onAppear {
                            // print("\(Date().timeIntervalSince1970) onAppear isLoadingSession: \(viewModel.isLoadingSession)")
                            // print("\(Date().timeIntervalSince1970) onAppear isLoadingMessages: \(viewModel.isLoadingMessages)")
                            // print("\(Date().timeIntervalSince1970) onAppear viewModel.getMessageRowViewModelsForView().count: \(viewModel.getMessageRowViewModelsForView().count)")
                            // print("\(Date().timeIntervalSince1970) onAppear geometry.size: \(geometry.size)")
                            // print("\(Date().timeIntervalSince1970) onAppear geometry.frame: \(geometry.frame(in: .global))")
                            // print("\(Date().timeIntervalSince1970) onAppear geometry.frame in scrollView: \(geometry.frame(in: .named("scrollView")))")
                            // // print("\(Date().timeIntervalSince1970) onAppear geometry.position: \(geometry.position(in: .global))")
                            // print("\(Date().timeIntervalSince1970) onAppear geometry.safeAreaInsets: \(geometry.safeAreaInsets)")
                            let frame = geometry.frame(in: .global)
                            if frame.origin.x >= 0 && frame.origin.y >= 0 {
                                scrollViewLongestHeight = geometry.size.height
                            }
                        }
                        .onChange(of: geometry.size.height) { newHeight in
                            // print("\(Date().timeIntervalSince1970) onChange isLoadingSession: \(viewModel.isLoadingSession)")
                            // print("\(Date().timeIntervalSince1970) onChange isLoadingMessages: \(viewModel.isLoadingMessages)")
                            // print("\(Date().timeIntervalSince1970) onChange viewModel.getMessageRowViewModelsForView().count: \(viewModel.getMessageRowViewModelsForView().count)")
                            // print("\(Date().timeIntervalSince1970) onChange geometry.size: \(geometry.size)")
                            // print("\(Date().timeIntervalSince1970) onChange geometry.frame: \(geometry.frame(in: .global))")
                            // print("\(Date().timeIntervalSince1970) onChange geometry.frame in scrollView: \(geometry.frame(in: .named("scrollView")))")
                            // // print("\(Date().timeIntervalSince1970) onChange geometry.position: \(geometry.position(in: .global))")
                            // print("\(Date().timeIntervalSince1970) onChange geometry.safeAreaInsets: \(geometry.safeAreaInsets)")
                            if newHeight > scrollViewLongestHeight {
                                let frame = geometry.frame(in: .global)
                                if frame.origin.x >= 0 && frame.origin.y >= 0 {
                                    scrollViewLongestHeight = newHeight
                                }
                            }
                        }
                    })
                    .onAppear {
                        // print("\(Date().timeIntervalSince1970) onAppear first task")
                        // print("\(Date().timeIntervalSince1970) onAppear viewModel.getMessageRowViewModelsForView().count: \(viewModel.getMessageRowViewModelsForView().count)")
                        if let lastRowViewModel = viewModel.getMessageRowViewModelsForView().last {
                            // DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            //     withAnimation {
                                    scrollProxy.scrollTo(lastRowViewModel.id, anchor: .bottom)
                            //     }
                            // }
                        }
                    }
                    .onChange(of: viewModel.scrollToBottom) { scrollToBottom in
                        if scrollToBottom {
                            viewModel.scrollToBottom = false
                            if let lastRowViewModel = viewModel.getMessageRowViewModelsForView().last {
                                scrollProxy.scrollTo(lastRowViewModel.id, anchor: .bottom)
                            }
                        }
                    }
                    .onChange(of: viewModel.scrollToBottomWithAnimation) { scrollToBottomWithAnimation in
                        if scrollToBottomWithAnimation {
                            viewModel.scrollToBottomWithAnimation = false
                            if let lastRowViewModel = viewModel.getMessageRowViewModelsForView().last {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    scrollProxy.scrollTo(lastRowViewModel.id, anchor: .bottom)
                                }
                            }
                        }
                    }
                    .onChange(of: viewModel.scrollToBottomIfAtBottom) { shouldScroll in
                        if shouldScroll {
                            viewModel.scrollToBottomIfAtBottom = false
                            // Only scroll if near bottom (within 100px)
                            if viewModel.isNearBottom {
                                if let lastRowViewModel = viewModel.getMessageRowViewModelsForView().last {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        scrollProxy.scrollTo(lastRowViewModel.id, anchor: .bottom)
                                    }
                                }
                            }
                        }
                    }
                    .keyboardAvoiding(scrollToBottomIfAtBottom: $viewModel.scrollToBottomIfAtBottom)
                }

                // Floating Scroll to Bottom Button
                scrollToBottomButton
            }

            inputArea
        }
        .background(colorScheme == .light ? Color(.systemGray6) : Color.black)
        .sheet(isPresented: $viewModel.isDocumentPickerPresented, onDismiss: {
            viewModel.handleSelectedFiles()
        }) {
            // Document Picker
            DocumentPickerView(selectedFiles: $viewModel.selectedFiles)
        }
        .fullScreenCover(isPresented: $viewModel.isImagePickerPresented) {
            // Camera Picker (for camera only)
            if viewModel.imageSourceType == .camera {
                CameraPickerView(selectedImages: $viewModel.selectedImages)
            }
        }
        .photosPicker(
            isPresented: $viewModel.isPhotosPickerPresented,
            selection: $viewModel.photosPickerItems,
            maxSelectionCount: 10,
            matching: .images
        )
        .sheet(isPresented: $isActionPanelPresented) {
            ActionPanelView(
                data: viewModel.getActionPanelData(),
                callbacks: viewModel.getActionPanelCallbacks(),
                isDocumentPickerPresented: $viewModel.isDocumentPickerPresented,
                container: container,
                onDismiss: {
                    isActionPanelPresented = false
                }
            )
        }
        .sheet(isPresented: $viewModel.showingPromptSelection) {
            if let action = viewModel.promptSelectionAction {
                PromptSelectionSheet(action: action) { selectedPrompt in
                    viewModel.handlePromptSelection(selectedPrompt)
                }
            }
        }
        .alert("Large File Warning", isPresented: $viewModel.isShowingTokenWarningAlert) {
            Button("Cancel", role: .cancel) {
                viewModel.clearSelectedMedia()
            }
            Button("Send Anyway") {
                Task {
                    await viewModel.sendFileContents()
                }
            }
        } message: {
            Text("The selected file(s) might contain a large number of tokens (\(viewModel.estimatedTokenCount) estimated). Sending large files could exceed model context limits or consume many tokens.")
        }
        .alert("File Processing Error", isPresented: $viewModel.isShowingFileProcessingErrorAlert) {
            Button("OK") {
                // Dismiss the alert
            }
        } message: {
            Text(viewModel.fileProcessingErrorMessage)
        }
        .toast(
            icon: "checkmark.circle.fill",
            message: "Copied",
            isShowing: $viewModel.showCopyToast
        )
        .toast(
            icon: "exclamationmark.triangle.fill",
            message: "Input is empty",
            isShowing: $viewModel.showEmptyInputToast
        )
        .toast(
            icon: "xmark.circle.fill",
            message: "Rewrite failed, revert to original",
            isShowing: $viewModel.showRewriteFailedToast
        )
        .onChange(of: viewModel.isEditingMode) { isEditing in
            if isEditing {
                // Auto-focus input field when entering editing mode
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isInputFieldFocused = true
                }
            } else {
                isInputFieldFocused = false
            }
        }
        .navigationDestination(isPresented: $showChatSessionDetail) {
            if let sessionId = viewModel.chatSession?.id {
                ChatSessionDetailView(
                    viewModel: container.makeChatSessionDetailViewModel(
                        chatSessionId: sessionId
                    )
                )
                .environmentObject(container)
                .onAppear {
                    chatsViewModel.enterSubNavigation()
                }
                .onDisappear {
                    chatsViewModel.exitSubNavigation()
                }
            }
        }
    }
    
    // Input Area Component
    private var inputArea: some View {
        VStack(spacing: 0) {
            // Editing Indicator (shown when in editing mode)
            if viewModel.isEditingMode {
                EditingIndicatorView(onCancel: {
                    viewModel.cancelEditing()
                })
                .background(colorScheme == .light ? Color(.systemGray6) : Color.black)
            }

            // Input Bar with File Preview
            VStack(spacing: 0) {
                // File Preview Section (inside inputArea background)
                FilePreviewSection(
                    selectedImages: $viewModel.selectedImages,
                    selectedFiles: $viewModel.selectedFiles
                )
                .padding(.horizontal, 12)
                .padding(.top, 8)

                // Input Bar
                HStack(alignment: .bottom, spacing: 8) {
                    // Plus Button
                    Button(action: {
                        isActionPanelPresented = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.blue)
                    }
                    .padding(.bottom, 8)

                    // Text Input
                    ZStack {
                        TextEditor(text: $viewModel.newMessageText)
                            .padding(.horizontal, 8)
                            .background(Color(.systemGray6))
                            .cornerRadius(16)
                            .frame(minHeight: 40, maxHeight: 188)
                            .fixedSize(horizontal: false, vertical: true)
                            .scrollContentBackground(.hidden)
                            .focused($isInputFieldFocused)
                            .scrollDismissesKeyboard(.never)

                        // Placeholder when text is empty
                        if viewModel.newMessageText.isEmpty {
                            Text(viewModel.isEditingMode ? "Edit your message..." : "Message...")
                                .foregroundColor(Color(.gray))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .allowsHitTesting(false)
                        }
                    }
                    .padding(.bottom, 2)

                    // Send/Stop Button
                    Button(action: {
                        Task {
                            if viewModel.isSendingMessage {
                                await viewModel.stopSendingMessage()
                            } else {
                                // Check if we have files to send
                                if !viewModel.selectedImages.isEmpty || !viewModel.selectedFiles.isEmpty {
                                    await viewModel.sendFileContents()
                                } else {
                                    await viewModel.sendMessage()
                                }
                            }
                        }
                    }) {
                        Image(systemName: viewModel.isSendingMessage ? "stop.circle.fill" : "arrow.up.circle.fill")
                            .font(.system(size: 28))
                            .foregroundColor(viewModel.isSendingMessage ? .accentColor : (canSendMessage ? .accentColor : .gray))
                    }
                    .disabled(!viewModel.isSendingMessage && !canSendMessage)
                    .padding(.bottom, 6)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
            }
            .background(Color(.systemBackground))
            .overlay(
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.gray.opacity(0.3)),
                alignment: .top
            )
        }
    }

    /// Whether we can send a message (has text)
    private var canSendMessage: Bool {
        !viewModel.newMessageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}



// MARK: - Preview

#if DEBUG
#Preview("Simple Chat") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ChatView(viewModel: container.makeChatViewModel(chatSessionId: MockChatPreviewData.exampleSession1.id))
    }
    .environmentObject(container)
}

#Preview("Multiple Chats") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ChatView(viewModel: container.makeChatViewModel(chatSessionId: MockChatPreviewData.exampleSession2.id))
    }
    .environmentObject(container)
}

#Preview("Empty Chat") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ChatView(viewModel: container.makeChatViewModel(chatSessionId: MockChatPreviewData.exampleSession4.id))
    }
    .environmentObject(container)
}
#endif
