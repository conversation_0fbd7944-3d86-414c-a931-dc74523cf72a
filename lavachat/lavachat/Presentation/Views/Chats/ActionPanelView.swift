import SwiftUI

struct ActionPanelView: View {
    
    // MARK: - Properties
    
    @StateObject private var viewModel: ActionPanelViewModel
    @Binding var isDocumentPickerPresented: Bool
    let onDismiss: () -> Void
    let container: DIContainer
    
    @Environment(\.colorScheme) var colorScheme
    
    // MARK: - Initialization
    
    init(
        data: ActionPanelData,
        callbacks: ActionPanelCallbacks,
        isDocumentPickerPresented: Binding<Bool>,
        container: DIContainer,
        onDismiss: @escaping () -> Void
    ) {
        self._viewModel = StateObject(wrappedValue: ActionPanelViewModel(data: data, callbacks: callbacks))
        self._isDocumentPickerPresented = isDocumentPickerPresented
        self.container = container
        self.onDismiss = onDismiss
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Action Buttons Section
                actionButtonsSection
                    .padding(.horizontal, 16)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                
                // Instance List Section
                instanceListSection
            }
        }
        .presentationDetents([.medium])
        .presentationDragIndicator(.hidden)
        .toast(
            icon: viewModel.toastIcon,
            message: viewModel.toastMessage,
            isShowing: $viewModel.showToast
        )
        .sheet(isPresented: $viewModel.showInstanceSelectionSheet) {
            NewChatSheetView(
                viewModel: container.makeNewChatSheetViewModel(
                    mode: .instanceSelection,
                    onSelectInstance: { instanceId in
                        // Handle instance selection
                        Task {
                            await viewModel.handleInstanceSelection(instanceId: instanceId)
                        }
                    }
                )
            )
        }
    }
    
    // MARK: - Action Buttons Section

    private var actionButtonsSection: some View {
        ViewThatFits(in: .horizontal) {
            // First try: HStack with flexible button widths
            HStack(spacing: 12) {
                ForEach(viewModel.actionPanelActions, id: \.id) { action in
                    createActionButton(for: action)
                }
            }

            // Fallback: ScrollView with fixed button widths
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(viewModel.actionPanelActions, id: \.id) { action in
                        createActionButton(for: action)
                            .frame(width: 64) // Fixed width for scrollable buttons
                    }
                }
                .padding(.horizontal, 8)
            }
        }
    }
    
    // MARK: - Instance List Section
    
    private var instanceListSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Instance List
            List {
                // Active Instances
                ForEach(viewModel.instances, id: \.instance.id) { instanceContext in
                    ActionPanelInstanceRowView(
                        instanceContext: instanceContext,
                        viewModel: viewModel
                    )
                    .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                        Button(role: .destructive) {
                            viewModel.handleInstanceDeletion(for: instanceContext)
                        } label: {
                            Label("Delete", systemImage: "trash")
                        }
                    }
                }
                
                // Add New Instance Row
                AddActionPanelInstanceRowView {
                    viewModel.handleAddNewInstance()
                }
            }
            .listStyle(.plain)
            .scrollContentBackground(.hidden)
        }
    }
    
    // MARK: - Helper Methods

    /// Create an action button based on MessageAction
    @ViewBuilder
    private func createActionButton(for action: MessageAction) -> some View {
        actionButton(
            title: action.name,
            systemImage: action.icon ?? "questionmark.circle"
        ) {
            handleActionButtonTap(action)
        }
    }

    /// Handle action button tap based on action type
    private func handleActionButtonTap(_ action: MessageAction) {
        switch action.actionType {
        case .system(let kind):
            switch kind {
            case .camera:
                print("📷 [ActionPanelView] Camera tapped")
                viewModel.handleCameraAction()
                onDismiss()
            case .photo:
                print("🖼️ [ActionPanelView] Photo tapped")
                viewModel.handlePhotoAction()
                onDismiss()
            case .file:
                print("📁 [ActionPanelView] File tapped")
                viewModel.handleFileAction()
                onDismiss()
            default:
                print("🚨 [ActionPanelView] Warning: System action '\(action.name)' not implemented")
                onDismiss()
            }
        case .actionPanelPromptInsert:
            print("📝 [ActionPanelView] Prompt insert action: \(action.name)")
            viewModel.handleActionPanelPromptInsert(action)
            onDismiss()
        case .actionPanelPromptRewrite:
            print("✨ [ActionPanelView] Prompt rewrite action: \(action.name)")
            viewModel.handleActionPanelPromptRewrite(action)
            onDismiss()
        case .assistantRegenerate:
            // This should not happen since we filter by suitableForActionPanel
            print("🚨 [ActionPanelView] Error: Assistant regenerate action should not appear in action panel")
            onDismiss()
        }
    }

    /// Create an action button with consistent styling
    private func actionButton(
        title: String,
        systemImage: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                // Icon in a rounded background
                Image(systemName: systemImage)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.blue)
                    .frame(width: 64, height: 64)
                    .background(Color.blue.opacity(0.12)) // Tonal background
                    .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))

                // Title below the icon
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Preview Provider

#if DEBUG
#Preview("Action Panel") {
    // Create sample contexts manually using available mock data
    let claudeProvider = LLMProvider(
        id: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
        name: "Anthropic",
        logoImageName: "provider_logo_anthropic",
        websiteUrl: "https://anthropic.com",
        apiDocumentationUrl: "https://docs.anthropic.com/claude/reference",
        apiBaseUrl: "https://api.anthropic.com",
        providerType: .userApiKey,
        apiKeyStored: false,
        apiStyle: .anthropic,
        apiEndpointPath: "/v1/messages",
        isUserCreated: false,
        isUserModified: false,
        metadata: nil
    )
    
    let claudeModel = LLMModel(
        id: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!,
        providerId: claudeProvider.id,
        modelIdentifier: "claude-3-7-sonnet-20250219",
        name: "Claude 3.7 Sonnet",
        modelDescription: "Anthropic most intelligent model.",
        logoImageName: "model_logo_anthropic_claude",
        contextWindowSize: 200000,
        inputModalities: [.text, .image, .pdf],
        outputModalities: [.text],
        thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "thinking"),
        maxOutputTokens: 64000,
        pricingInfo: "Input: $15/M tokens\nOutput: $75/M tokens",
        availabilityStatus: .available,
        isDefaultRecommendation: true,
        isUserCreated: false,
        isUserModified: false,
        metadata: nil
    )
    
    let claudeInstance = LLMInstance(
        id: UUID(uuidString: "D2D3D4D5-E6E7-7890-D2D3-D4D5E6E77890")!,
        modelId: claudeModel.id,
        name: "Claude 3.7 Sonnet",
        systemPrompt: "You are a helpful assistant.",
        defaultParameters: ["temperature": "0.5", "thinking_prompt": "Think step by step."],
        totalPromptTokensUsed: 500,
        totalCompletionTokensUsed: 250,
        createdAt: Date().addingTimeInterval(-86400 * 3),
        lastUsedAt: Date().addingTimeInterval(-7200),
        isFavorited: false,
        isUserModified: false,
        metadata: nil
    )
    
    let gpt4Provider = LLMProvider(
        id: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
        name: "OpenAI",
        logoImageName: "provider_logo_openai",
        websiteUrl: "https://openai.com",
        apiDocumentationUrl: "https://platform.openai.com/docs/api-reference",
        apiBaseUrl: "https://api.openai.com",
        providerType: .userApiKey,
        apiKeyStored: true,
        apiStyle: .openaiCompatible,
        apiEndpointPath: "/v1/chat/completions",
        isUserCreated: false,
        isUserModified: false,
        metadata: nil
    )
    
    let gpt4Model = LLMModel(
        id: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!,
        providerId: gpt4Provider.id,
        modelIdentifier: "gpt-4.1",
        name: "GPT-4.1",
        modelDescription: "Flagship GPT model for complex tasks.",
        logoImageName: "provider_logo_openai",
        contextWindowSize: 1047576,
        inputModalities: [.text, .image],
        outputModalities: [.text],
        thinkingCapabilities: ThinkingCapabilities(controlType: .none),
        maxOutputTokens: 32768,
        pricingInfo: "Input: $2/M tokens\nOutput: $8/M tokens",
        availabilityStatus: .available,
        isDefaultRecommendation: true,
        isUserCreated: false,
        isUserModified: false,
        metadata: nil
    )
    
    let gpt4Instance = LLMInstance(
        id: UUID(uuidString: "D1D2D3D4-E5E6-7890-D1D2-D3D4E5E67890")!,
        modelId: gpt4Model.id,
        name: "GPT-4.1",
        systemPrompt: "You are a helpful assistant.",
        defaultParameters: ["temperature": "0.7"],
        totalPromptTokensUsed: 1000,
        totalCompletionTokensUsed: 500,
        createdAt: Date().addingTimeInterval(-86400 * 7),
        lastUsedAt: Date().addingTimeInterval(-3600),
        isFavorited: true,
        isUserModified: false,
        metadata: nil
    )
    
    let mockData = ActionPanelData(
        instances: [
            LLMInstanceContext(instance: claudeInstance, model: claudeModel, provider: claudeProvider),
            LLMInstanceContext(instance: gpt4Instance, model: gpt4Model, provider: gpt4Provider)
        ],
        instanceSettings: [
            claudeInstance.id: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false),
            gpt4Instance.id: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false)
        ],
        savedPromptSegments: MockChatPreviewData.systemDefaultChatSessionSetting.savedPromptSegments ?? [],
        resolvedMessageActionSettings: ResolvedMessageActionSettings(
            actionPanelActions: [
                SystemMessageActions.camera,
                SystemMessageActions.photo,
                SystemMessageActions.file,
                MessageAction(name: "Insert", icon: "text.insert", actionType: .actionPanelPromptInsert),
                MessageAction(name: "Optimize", icon: "wand.and.sparkles", actionType: .actionPanelPromptRewrite),
                MessageAction(name: "Enhance", icon: "sparkles", actionType: .actionPanelPromptRewrite),
            ]
        )
    )
    
    let mockCallbacks = ActionPanelCallbacks(
        toggleInstanceThinking: { instanceId in
            print("Toggle thinking for: \(instanceId)")
            return true
        },
        toggleInstanceNetwork: { instanceId in
            print("Toggle network for: \(instanceId)")
            return true
        },
        navigateToInstanceDetail: { instanceId in
            print("Navigate to detail for: \(instanceId)")
        },
        removeInstanceFromSession: { instanceId in
            print("Remove instance: \(instanceId)")
            return true
        },
        addInstanceToSession: { instanceId in
            print("Add instance to session: \(instanceId)")
            return nil
        },
        handleActionPanelPromptInsert: { action in
            print("Handle action panel prompt insert: \(action.name)")
        },
        handleActionPanelPromptRewrite: { action in
            print("Handle action panel prompt rewrite: \(action.name)")
        },
        handleCameraAction: {
            print("Handle camera action")
        },
        handlePhotoAction: {
            print("Handle photo action")
        },
        handleFileAction: {
            print("Handle file action")
        }
    )
    
    ActionPanelView(
        data: mockData,
        callbacks: mockCallbacks,
        isDocumentPickerPresented: .constant(false),
        container: DIContainer(context: PersistenceController.preview.container.viewContext),
        onDismiss: {}
    )
}

#Preview("Action Panel - Few Actions") {
    let mockDataFew = ActionPanelData(
        instances: [],
        instanceSettings: [:],
        savedPromptSegments: [],
        resolvedMessageActionSettings: ResolvedMessageActionSettings(
            actionPanelActions: [
                SystemMessageActions.camera,
                SystemMessageActions.photo,
                SystemMessageActions.file,
                MessageAction(name: "Insert", icon: "text.insert", actionType: .actionPanelPromptInsert),
                MessageAction(name: "Optimize", icon: "wand.and.sparkles", actionType: .actionPanelPromptRewrite),
            ]
        )
    )

    let mockCallbacks = ActionPanelCallbacks(
        toggleInstanceThinking: { _ in return true },
        toggleInstanceNetwork: { _ in return true },
        navigateToInstanceDetail: { _ in },
        removeInstanceFromSession: { _ in return true },
        addInstanceToSession: { _ in return nil },
        handleActionPanelPromptInsert: { _ in },
        handleActionPanelPromptRewrite: { _ in },
        handleCameraAction: { },
        handlePhotoAction: { },
        handleFileAction: { }
    )

    ActionPanelView(
        data: mockDataFew,
        callbacks: mockCallbacks,
        isDocumentPickerPresented: .constant(false),
        container: DIContainer(context: PersistenceController.preview.container.viewContext),
        onDismiss: {}
    )
}
#endif
