import SwiftUI

struct ChatSessionDetailView: View {
    // MARK: - Properties
    
    @StateObject private var viewModel: ChatSessionDetailViewModel
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) private var dismiss
    
    // Navigation States
    @State private var showTitleEdit = false
    @State private var showSettingSelection = false
    @State private var showActionPanelManagement = false
    @State private var showUserMessageManagement = false
    @State private var showAssistantCardManagement = false
    @State private var showAssistantMenuManagement = false
    @State private var showAuxiliaryInstanceSelection = false
    @State private var showContextMessageCountPicker = false
    
    // MARK: - Initialization
    
    init(viewModel: ChatSessionDetailViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        List {
                if viewModel.isLoading {
                    ProgressView("Loading...")
                        .frame(maxWidth: .infinity, alignment: .center)
                } else {
                    // Basic Information Section
                    Section {
                        // Title Row
                        HStack {
                            Text("Title")
                            Spacer()
                            Text(viewModel.displayTitle)
                                .foregroundColor(.secondary)
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            showTitleEdit = true
                        }
                    }
                    
                    // Setting Selection Section
                    Section {
                        HStack {
                            Text("Setting")
                            Spacer()
                            HStack(spacing: 4) {
                                if viewModel.isCurrentSettingSystemDefault {
                                    Image(systemName: "lock.fill")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                Text(viewModel.currentSettingName)
                                    .foregroundColor(.secondary)
                            }
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            showSettingSelection = true
                        }
                    } footer: {
                        Text("Changes to the settings below will affect all chat sessions using this setting.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Interface Configuration Section
                    if let setting = viewModel.currentChatSessionSetting {
                        Section {
                            // Should Expand Thinking Toggle
                            HStack {
                                Text("Default Thinking Expansion")
                                    .fixedSize(horizontal: true, vertical: false)
                                Spacer()
                                Toggle("", isOn: Binding(
                                    get: { setting.shouldExpandThinking },
                                    set: { newValue in
                                        Task {
                                            await viewModel.updateShouldExpandThinking(newValue)
                                        }
                                    }
                                ))
                            }

                            // Message Action Settings
                            createMessageActionRow(
                                title: "Input Actions",
                                actions: viewModel.resolvedMessageActionSettings?.actionPanelActions ?? [],
                                showManagement: $showActionPanelManagement
                            )

                            createMessageActionRow(
                                title: "User Message",
                                actions: viewModel.resolvedMessageActionSettings?.userMessageActions ?? [],
                                showManagement: $showUserMessageManagement
                            )

                            createMessageActionRow(
                                title: "Assistant Card",
                                actions: viewModel.resolvedMessageActionSettings?.assistantMessageCardActions ?? [],
                                showManagement: $showAssistantCardManagement
                            )

                            createMessageActionRow(
                                title: "Assistant Menu",
                                actions: viewModel.resolvedMessageActionSettings?.assistantMessageMenuActions ?? [],
                                showManagement: $showAssistantMenuManagement
                            )
                        } header: {
                            Text("UI & Display")
                        }

                        // Auxiliary Settings Section
                        Section {
                            // Auxiliary Instance Row
                            HStack {
                                Text("Auxiliary Instance")
                                    .foregroundColor(.primary)

                                Spacer()

                                if let instance = viewModel.auxiliaryLLMInstance {
                                    // Show instance name if available
                                    Text(instance.name)
                                        .foregroundColor(.secondary)
                                } else {
                                    Text("None")
                                        .foregroundColor(.secondary)
                                }

                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                            .contentShape(Rectangle())
                            .onTapGesture {
                                showAuxiliaryInstanceSelection = true
                            }

                            // Auto Generate Title Toggle
                            HStack {
                                Text("Auto Generate Title")
                                    .foregroundColor(viewModel.shouldAutoGenerateTitleEnabled ? .primary : .secondary)
                                Spacer()
                                Toggle("", isOn: Binding(
                                    get: { setting.shouldAutoGenerateTitle },
                                    set: { newValue in
                                        Task {
                                            await viewModel.updateShouldAutoGenerateTitle(newValue)
                                        }
                                    }
                                ))
                                .disabled(!viewModel.shouldAutoGenerateTitleEnabled)
                            }

                            // Context Message Count Row
                            HStack {
                                Text("Context Message Count")
                                    .foregroundColor(.primary)

                                Spacer()

                                Text(viewModel.contextMessageCountDisplayText)
                                    .foregroundColor(.secondary)

                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                            .contentShape(Rectangle())
                            .onTapGesture {
                                showContextMessageCountPicker = true
                            }
                        } header: {
                            Text("Chat Settings")
                        } footer: {
                            Text("Auto-generate title triggers once after the first message in new chats when auxiliary instance is configured.")
                        }
                    }
                }
            }
            .navigationTitle("Chat Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        .navigationDestination(isPresented: $showTitleEdit) {
            ChatSessionTitleEditView(
                initialTitle: viewModel.displayTitle,
                onSave: { newTitle in
                    Task {
                        await viewModel.updateTitle(newTitle)
                    }
                }
            )
        }
        .navigationDestination(isPresented: $showSettingSelection) {
            ChatSessionSettingSelectionView(
                allSettings: viewModel.allChatSessionSettings,
                currentSettingId: viewModel.currentChatSessionSetting?.id,
                onSelect: { settingId in
                    Task {
                        await viewModel.switchToSetting(settingId)
                    }
                }
            )
        }
        .navigationDestination(isPresented: $showActionPanelManagement) {
            MessageActionCategoryManagementView(
                category: .actionPanel,
                currentActionIds: viewModel.currentChatSessionSetting?.messageActionSettings?.actionPanelActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.actionPanel, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showUserMessageManagement) {
            MessageActionCategoryManagementView(
                category: .userMessage,
                currentActionIds: viewModel.currentChatSessionSetting?.messageActionSettings?.userMessageActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.userMessage, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showAssistantCardManagement) {
            MessageActionCategoryManagementView(
                category: .assistantCard,
                currentActionIds: viewModel.currentChatSessionSetting?.messageActionSettings?.assistantMessageCardActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.assistantCard, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showAssistantMenuManagement) {
            MessageActionCategoryManagementView(
                category: .assistantMenu,
                currentActionIds: viewModel.currentChatSessionSetting?.messageActionSettings?.assistantMessageMenuActions ?? [],
                onSave: { actionIds in
                    Task {
                        await updateMessageActionCategory(.assistantMenu, actionIds: actionIds)
                    }
                }
            )
            .environmentObject(container)
        }
        .navigationDestination(isPresented: $showAuxiliaryInstanceSelection) {
            NewChatSheetView(
                viewModel: container.makeNewChatSheetViewModel(
                    mode: .auxiliaryInstanceSelection,
                    onSelectAuxiliaryInstance: { instanceId in
                        Task {
                            await viewModel.updateAuxiliaryLLMInstanceId(instanceId)
                        }
                    }
                )
            )
        }
        .navigationDestination(isPresented: $showContextMessageCountPicker) {
            ContextMessageCountPickerView(
                initialValue: viewModel.currentChatSessionSetting?.contextMessageCount ?? Int64.max,
                onSave: { count in
                    Task {
                        await viewModel.updateContextMessageCount(count)
                    }
                }
            )
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            Text(viewModel.errorMessage ?? "")
        }
    }

    // MARK: - Helper Methods
    
    @ViewBuilder
    private func createMessageActionRow(
        title: String,
        actions: [MessageAction],
        showManagement: Binding<Bool>
    ) -> some View {
        HStack {
            Text(title)
            Spacer()
            
            HStack(spacing: 4) {
                // Show up to 4 icons
                let displayActions = Array(actions.prefix(5))
                ForEach(displayActions, id: \.id) { action in
                    if let icon = action.icon {
                        Image(systemName: icon)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Show count if more than 5
                if actions.count > 5 {
                    Text("+\(actions.count - 5)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Show empty state
                if actions.isEmpty {
                    Text("None")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            showManagement.wrappedValue = true
        }
    }

    // MARK: - Helper Methods

    private func updateMessageActionCategory(_ category: MessageActionCategory, actionIds: [UUID]) async {
        guard let setting = viewModel.currentChatSessionSetting else { return }

        var messageActionSettings = setting.messageActionSettings ?? MessageActionSettings()

        switch category {
        case .actionPanel:
            messageActionSettings.actionPanelActions = actionIds
        case .userMessage:
            messageActionSettings.userMessageActions = actionIds
        case .assistantCard:
            messageActionSettings.assistantMessageCardActions = actionIds
        case .assistantMenu:
            messageActionSettings.assistantMessageMenuActions = actionIds
        }

        await viewModel.updateMessageActionSettings(messageActionSettings)
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        ChatSessionDetailView(
            viewModel: container.makeChatSessionDetailViewModel(
                chatSessionId: MockChatPreviewData.exampleSession1.id
            )
        )
    }
    .environmentObject(container)
}
#endif
