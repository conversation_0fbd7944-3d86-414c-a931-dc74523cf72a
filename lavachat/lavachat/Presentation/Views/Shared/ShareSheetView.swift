import SwiftUI
import UniformTypeIdentifiers
import LinkPresentation

// MARK: - ShareSheetView (Main Share Interface)

/// Main share interface that shows all three sharing options as ShareLinks
struct ShareSheetView<T: ShareableItem>: View {
    @ObservedObject var viewModel: ShareViewModel<T>
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(spacing: 16) {
                    // File sharing
                    LazyFormatShareLink(
                        format: .file,
                        viewModel: viewModel
                    )
                    
                    // iCloud sharing
                    LazyFormatShareLink(
                        format: .icloud,
                        viewModel: viewModel
                    )
                    
                    // QR Code sharing
                    LazyFormatShareLink(
                        format: .qrCode,
                        viewModel: viewModel
                    )
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("Share")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

/// A ShareLink for a specific format that generates content lazily when tapped
private struct LazyFormatShareLink<T: ShareableItem>: View {
    let format: ShareFormat
    @ObservedObject var viewModel: ShareViewModel<T>
    @State private var shareContent: Any?
    @State private var isGenerating = false
    @State private var generationError: String?
    @State private var showingActivityView = false

    var body: some View {
        VStack {
            contentView
        }
        .sheet(isPresented: $showingActivityView) {
            ActivityViewController(activityItems: processedActivityItems)
        }
    }

    @ViewBuilder
    private var contentView: some View {
        if isGenerating {
            generatingView
        } else if let error = generationError {
            retryView(error: error)
        } else if let content = shareContent {
            shareContentView(content: content)
        } else {
            initialStateView
        }
    }
    
    @ViewBuilder
    private var generatingView: some View {
        HStack(spacing: 12) {
            ProgressView()
                .scaleEffect(0.8)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Preparing \(format.displayName)")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("Please wait...")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(.systemBackground))
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
        .cornerRadius(10)
    }
    
    @ViewBuilder
    private func retryView(error: String) -> some View {
        Button(
            String(localized: "Retry \(format.displayName)\nFailed due to: \(error)")
        ) {
            generateContentAndShare()
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemRed).opacity(0.1))
        .foregroundColor(.red)
        .cornerRadius(10)
    }
    
    @ViewBuilder
    private func shareContentView(content: Any) -> some View {
        // All formats use the same button style
        // Second click will directly share the already generated content
        Button(action: triggerShare) {
            shareButtonContent
        }
    }
    
    @ViewBuilder
    private var initialStateView: some View {
        Button(action: generateContentAndShare) {
            shareButtonContent
        }
    }
    
    private var shareButtonContent: some View {
        HStack(spacing: 12) {
            Image(systemName: format.systemImageName)
                .font(.title2)
                .foregroundColor(.accentColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(format.displayName)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(format.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(.systemBackground))
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
        .cornerRadius(10)
    }
    
    /// Generate content and auto-trigger share (for first-time generation)
    private func generateContentAndShare() {
        isGenerating = true
        generationError = nil

        Task {
            do {
                let content = try await viewModel.generateShareContent(for: format)
                await MainActor.run {
                    shareContent = content
                    isGenerating = false

                    // Auto-trigger share after generation for all formats
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        triggerShare()
                    }
                }
            } catch {
                await MainActor.run {
                    generationError = error.localizedDescription
                    isGenerating = false
                }
            }
        }
    }

    /// Trigger share with already generated content
    private func triggerShare() {
        guard shareContent != nil else { return }
        showingActivityView = true
    }
    
    /// Convert share content to appropriate format for ActivityViewController
    private var processedActivityItems: [Any] {
        guard let content = shareContent else { return [] }
        
        // For QR code format, use custom activity item source for better preview
        if format == .qrCode, let data = content as? Data, let image = UIImage(data: data) {
            return [QRCodeActivityItemSource(qrCodeImage: image, title: "LavaChat QR Code")]
        }
        
        // For other formats (file URLs, iCloud URLs), use as-is
        return [content]
    }
}

// MARK: - Supporting structs

/// UIActivityViewController wrapper for SwiftUI
private struct ActivityViewController: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}

// MARK: - Preview Provider

#Preview {
    ShareSheetPreview()
}

private struct ShareSheetPreview: View {
    var body: some View {
        ShareSheetView(viewModel: mockViewModel)
    }
    
    private var mockViewModel: ShareViewModel<LLMInstance> {
        let mockInstance = createMockInstance()
        let container = createDIContainer()
        return container.makeInstanceShareViewModel(instance: mockInstance)
    }
    
    private func createMockInstance() -> LLMInstance {
        LLMInstance(
            id: UUID(),
            modelId: UUID(),
            name: "Test Instance",
            systemPrompt: nil,
            defaultParameters: nil,
            isFavorited: false
        )
    }
    
    private func createDIContainer() -> DIContainer {
        DIContainer(context: PersistenceController.preview.container.viewContext)
    }
}
