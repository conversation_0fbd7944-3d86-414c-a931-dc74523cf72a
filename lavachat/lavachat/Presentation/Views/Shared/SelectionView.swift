import SwiftUI

/// A generic selection view that supports different selection modes and configurations.
/// Used for modality selection, thinking capabilities configuration, etc.
struct SelectionView<T: Hashable, ContentType: View>: View {
    // MARK: - Properties

    @Environment(\.presentationMode) var presentationMode
    
    // Selection state
    @Binding var selection: T
    @State private var temporarySelection: T
    @State private var hasChanges: Bool = false
    
    // View configuration
    let title: String
    let contentBuilder: (Binding<T>) -> ContentType
    let onSave: (inout T) -> Void
    
    // MARK: - Initialization
    
    /// Initializes a selection view with custom content
    /// - Parameters:
    ///   - title: The title to display in the navigation bar
    ///   - selection: Binding to the current selection value
    ///   - contentBuilder: A closure that returns the content view, receives a binding to the temporary selection
    ///   - onSave: A closure that handles saving the selection (can modify the value)
    init(
        title: String,
        selection: Binding<T>,
        @ViewBuilder contentBuilder: @escaping (Binding<T>) -> ContentType,
        onSave: @escaping (inout T) -> Void
    ) {
        self.title = title
        self._selection = selection
        self.contentBuilder = contentBuilder
        self.onSave = onSave
        self._temporarySelection = State(initialValue: selection.wrappedValue)
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack {
            contentBuilder($temporarySelection)
        }
        .navigationTitle(title)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    var valueToSave = temporarySelection
                    onSave(&valueToSave)
                    selection = valueToSave
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(hasChanges ? .accentColor : .gray)
                .disabled(!hasChanges)
            }
        }
        .onChange(of: temporarySelection) { newValue in
            hasChanges = newValue != selection
        }
    }
}

// MARK: - Specialized Selection Views

/// Selection view for choosing input/output modalities
struct ModalitySelectionView: View {
    @Binding var selectedModalities: [ModelModality]
    let title: String
    
    var body: some View {
        SelectionView(
            title: title,
            selection: $selectedModalities,
            contentBuilder: { tempSelection in
                List {
                    ForEach(ModelModality.allCases, id: \.self) { modality in
                        Button(action: {
                            toggleSelection(modality, in: tempSelection)
                        }) {
                            HStack {
                                Label(
                                    title: { Text(modalityTitle(modality)) },
                                    icon: { Image(systemName: modalityIcon(modality)) }
                                )
                                
                                Spacer()
                                
                                if tempSelection.wrappedValue.contains(modality) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.accentColor)
                                }
                            }
                        }
                        .foregroundColor(.primary)
                    }
                }
            },
            onSave: { newSelection in
                selectedModalities = newSelection
            }
        )
    }
    
    private func toggleSelection(_ modality: ModelModality, in selection: Binding<[ModelModality]>) {
        if selection.wrappedValue.contains(modality) {
            selection.wrappedValue.removeAll { $0 == modality }
        } else {
            selection.wrappedValue.append(modality)
        }
    }
    
    private func modalityTitle(_ modality: ModelModality) -> String {
        switch modality {
        case .text:
            return "Text"
        case .image:
            return "Images"
        case .audio:
            return "Audio"
        case .video:
            return "Video"
        case .pdf:
            return "PDF Documents"
        }
    }
    
    private func modalityIcon(_ modality: ModelModality) -> String {
        switch modality {
        case .text:
            return "t.square"
        case .image:
            return "photo"
        case .audio:
            return "waveform"
        case .video:
            return "video"
        case .pdf:
            return "doc.text"
        }
    }
}

// MARK: - Preview Provider

struct SelectionView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview 1: Modality Selection
            NavigationStack {
                ModalitySelectionView(
                    selectedModalities: .constant([.text, .image]),
                    title: "Input Modalities"
                )
            }
            .previewDisplayName("Modality Selection")
        }
    }
}
