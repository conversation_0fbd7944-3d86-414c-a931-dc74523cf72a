import SwiftUI

struct ContextMessageCountPickerView: View {
    // MARK: - Properties
    
    let initialValue: Int64
    let onSave: (Int64) -> Void
    
    @State private var selectedValue: Int64
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Static Properties
    
    private static let contextMessageCountOptions: [(String, Int64)] = [
        ("0", 0),
        ("2", 2),
        ("4", 4),
        ("6", 6),
        ("8", 8),
        ("10", 10),
        ("14", 14),
        ("20", 20),
        ("50", 50),
        ("100", 100),
        ("200", 200),
        ("500", 500),
        ("Unlimited", Int64.max)
    ]
    
    // MARK: - Initialization
    
    init(initialValue: Int64, onSave: @escaping (Int64) -> Void) {
        self.initialValue = initialValue
        self.onSave = onSave
        self._selectedValue = State(initialValue: initialValue)
    }
    
    // MARK: - Body
    
    var body: some View {
        List {
            Section {
                ForEach(Self.contextMessageCountOptions, id: \.1) { option in
                    let (displayText, value) = option
                    let isSelected = selectedValue == value

                    HStack {
                        Text(displayText)
                        Spacer()
                        if isSelected {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedValue = value
                    }
                }
            } footer: {
                Text("Sets the number of previous messages to include as context when sending requests to the LLM. More context helps the AI understand the conversation but uses more tokens. The current user message is always included.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .navigationTitle("Context Message Count")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Cancel") {
                    dismiss()
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    onSave(selectedValue)
                    dismiss()
                }
                .disabled(selectedValue == initialValue)
            }
        }
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    NavigationStack {
        ContextMessageCountPickerView(
            initialValue: Int64.max,
            onSave: { value in
                print("Selected value: \(value)")
            }
        )
    }
}
#endif
