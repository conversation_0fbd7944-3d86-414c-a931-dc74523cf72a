import MarkdownUI
import Splash
import SwiftUI

struct MarkdownView: View {
    @Environment(\.colorScheme) private var colorScheme

    var markdownContentString: String
    var markdownThinkingString: String
    var shouldExpandThinking: Bool

    @State private var isThinkingSectionExpanded: Bool
    @ScaledMetric(relativeTo: .body) var contentFontSize: CGFloat = 16
    @ScaledMetric(relativeTo: .body) var thinkingFontSize: CGFloat = 15

    init(markdownContentString: String, markdownThinkingString: String = "", shouldExpandThinking: Bool = true) {
        self.markdownContentString = markdownContentString
        self.markdownThinkingString = markdownThinkingString
        self.shouldExpandThinking = shouldExpandThinking
        self._isThinkingSectionExpanded = State(initialValue: shouldExpandThinking)
    }
    
    var body: some View {
        if markdownContentString.isEmpty && markdownThinkingString.isEmpty {
            PulsingDotsIndicator()
                .padding(.all, 8)
        } else {
            VStack(alignment: .leading, spacing: 16) {
                if !markdownThinkingString.isEmpty {
                    thinkingSection
                }
                if !markdownContentString.isEmpty {
                    mainContentSection
                }
            }
            .onChange(of: shouldExpandThinking) { newValue in
                isThinkingSectionExpanded = newValue
            }
        }
    }

    @ViewBuilder
    private var thinkingSection: some View {
        // Determine title based on main content
        let title = markdownContentString.isEmpty ? "Thinking..." : "Thinking Process"
        
        VStack(alignment: .leading) {
            // Clickable area for collapsing/expanding
            HStack {
                Text(title)
                    .font(.callout)
                    .fontWeight(.medium)
                    .foregroundColor(Color(.systemGray))
                
                Image(systemName: isThinkingSectionExpanded ? "chevron.up" : "chevron.down")
                    .font(.callout)
                    .fontWeight(.medium)
                    .foregroundColor(Color(.systemGray))
            }
            .contentShape(Rectangle()) // Make the entire HStack area clickable
            .onTapGesture {
                isThinkingSectionExpanded.toggle()
            }
            
            // If expanded, show "Thinking" content
            if isThinkingSectionExpanded {
                HStack(alignment: .top, spacing: 12) {
                    // Left vertical line
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(width: 2)
                    
                    // Thinking Markdown content
                    Markdown(markdownThinkingString)
                        .markdownTheme(.gitHub.text {
                            // Set text color to secondaryLabel
                            ForegroundColor(Color(.systemGray))
                            FontSize(thinkingFontSize)
                        })
                }
            }
        }
    }

    @ViewBuilder
    private var mainContentSection: some View {
        Markdown(markdownContentString)
            .markdownBlockStyle(\.codeBlock) {
                codeBlock($0)
            }
            .markdownCodeSyntaxHighlighter(.splash(theme: self.theme))
            .markdownTheme(
                .gitHub.text {
                    ForegroundColor(Color(.label))
                    FontSize(contentFontSize)
                }
            )
    }
    
    @ViewBuilder
    private func codeBlock(_ configuration: CodeBlockConfiguration) -> some View {
        VStack(spacing: 0) {
            HStack {
                Text(configuration.language ?? "plain text")
                    .font(.system(.caption, design: .monospaced))
                    .fontWeight(.semibold)
                    .foregroundColor(Color(theme.plainTextColor))
                Spacer()

                Image(systemName: "square.on.square")
                    .font(.system(.callout))
                    .onTapGesture {
                        copyToClipboard(configuration.content)
                        print("copy pressed")
                    }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background {
                Color(theme.backgroundColor)
            }

            Divider()

            ScrollView(.horizontal) {
                configuration.label
                    .relativeLineSpacing(.em(0.25))
                    .markdownTextStyle {
                        FontFamilyVariant(.monospaced)
                        FontSize(.em(0.85))
                    }
                    .padding()
            }
        }
        .background(Color(.secondarySystemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .markdownMargin(top: .zero, bottom: .em(0.8))
    }

    private var theme: Splash.Theme {
        // NOTE: We are ignoring the Splash theme font
        switch self.colorScheme {
        case .dark:
            return .wwdc18(withFont: .init(size: contentFontSize))
        default:
            return .presentation(withFont: .init(size: contentFontSize))
        }
    }

    private func copyToClipboard(_ string: String) {
        #if os(macOS)
            if let pasteboard = NSPasteboard.general {
            pasteboard.clearContents()
            pasteboard.setString(string, forType: .string)
            }
        #elseif os(iOS)
            UIPasteboard.general.string = string
        #endif
    }
}

struct TextOutputFormat: OutputFormat {
    private let theme: Splash.Theme

    init(theme: Splash.Theme) {
        self.theme = theme
    }

    func makeBuilder() -> Builder {
        Builder(theme: self.theme)
    }
}

extension TextOutputFormat {
    struct Builder: OutputBuilder {
        private let theme: Splash.Theme
        private var attributedString: AttributedString
        
        fileprivate init(theme: Splash.Theme) {
            self.theme = theme
            self.attributedString = AttributedString()
        }
        
        mutating func addToken(_ token: String, ofType type: TokenType) {
            let color = self.theme.tokenColors[type] ?? self.theme.plainTextColor
            var tokenString = AttributedString(token)
            tokenString.foregroundColor = Color(uiColor: color)
            attributedString.append(tokenString)
        }
        
        mutating func addPlainText(_ text: String) {
            var textString = AttributedString(text)
            textString.foregroundColor = Color(uiColor: self.theme.plainTextColor)
            attributedString.append(textString)
        }
        
        mutating func addWhitespace(_ whitespace: String) {
            attributedString.append(AttributedString(whitespace))
        }
        
        func build() -> Text {
            Text(attributedString)
        }
    }
}

struct SplashCodeSyntaxHighlighter: CodeSyntaxHighlighter {
    private let syntaxHighlighter: SyntaxHighlighter<TextOutputFormat>

    init(theme: Splash.Theme) {
        self.syntaxHighlighter = SyntaxHighlighter(format: TextOutputFormat(theme: theme))
    }

    func highlightCode(_ content: String, language: String?) -> Text {
        guard language != nil else {
            return Text(content)
        }

        return self.syntaxHighlighter.highlight(content)
    }
}

extension CodeSyntaxHighlighter where Self == SplashCodeSyntaxHighlighter {
    static func splash(theme: Splash.Theme) -> Self {
        SplashCodeSyntaxHighlighter(theme: theme)
    }
}

#Preview {
    let markdownContentString = """
    # 这是一个标题

    这是**第一段**，包含一些 *斜体* 和 `Code Inline`。
    Markdown 的换行符规则：单个换行符通常被视为空格。
    要开始一个新段落，需要一个空行。

    这是第二段。
    它包含一个故意的
    换行，但这仍在第二段内。

    ---

    ### 表格示例（AttributedString(markdown:) 不会转为 Text 可见的表格）

    | 表头1 | 表头2 |
    |---|---|
    | 单元格1.1 | 单元格1.2 |
    | 单元格2.1 | 单元格2.2 |

    *   列表项 1
    *   列表项 2
        *   嵌套列表项 2.1

    > 这是一个引用块。 它通常会被正确解析并带有引用样式。

    ```swift
    // 这是一个代码块
    struct MyStruct {
        var name: String
    }
    ```

    ```python
    // 这是一个代码块
    print("test")
    func newFunc():
        return "test"
    ```
    """
    ScrollView {
        MarkdownView(markdownContentString: "")

        Rectangle()
            .fill(Color.blue.opacity(0.2))
            .frame(width: 100, height: 2)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .clipShape(RoundedRectangle(cornerRadius: 16))
        
        MarkdownView(markdownContentString: "", markdownThinkingString: markdownContentString)

        Rectangle()
            .fill(Color.blue.opacity(0.2))
            .frame(width: 100, height: 2)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .clipShape(RoundedRectangle(cornerRadius: 16))

        MarkdownView(markdownContentString: "test input")
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.blue.opacity(0.2))
            .clipShape(RoundedRectangle(cornerRadius: 16))
        MarkdownView(markdownContentString: markdownContentString, markdownThinkingString: markdownContentString)
    }
    .padding()
}
