import SwiftUI

/// Pulsing Dots Indicator for loading state
struct PulsingDotsIndicator: View {
    let dotSize: CGFloat
    let dotColor: Color
    
    // Internal state for animation
    @State private var isAnimating = false
    
    /// Initialize a pulsing dots indicator.
    /// - Parameters:
    ///   - dotSize: The diameter of each dot, defaults to 5.
    ///   - dotColor: The color of each dot, defaults to `.systemGray4`.
    init(dotSize: CGFloat = 5, dotColor: Color = Color(.systemGray4)) {
        self.dotSize = dotSize
        self.dotColor = dotColor
    }
    
    var body: some View {
        HStack(spacing: dotSize) { // spacing is related to dotSize
            ForEach(0..<3) { i in
                Circle()
                    .fill(dotColor)
                    .frame(width: dotSize, height: dotSize)
                    // .scaleEffect(isAnimating ? 1.4 : 0.6)
                    .opacity(isAnimating ? 1.0 : 0.3)
                    .animation(
                        .easeInOut(duration: 0.8)
                         .repeatForever(autoreverses: true)
                         .delay(0.2 * Double(i)),
                        value: isAnimating
                    )
            }
        }
        .onAppear {
            self.isAnimating = true
        }
    }
}