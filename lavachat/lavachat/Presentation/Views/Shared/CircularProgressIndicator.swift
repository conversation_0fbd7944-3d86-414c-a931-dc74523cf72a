import SwiftUI

/// Circular progress indicator for swipe gestures
struct CircularProgressIndicator: View {
    let progress: Double // 0.0 to 1.0
    let direction: SwipeDirection
    let size: CGFloat
    let lineWidth: CGFloat
    
    init(progress: Double, direction: SwipeDirection, size: CGFloat = 60, lineWidth: CGFloat = 4) {
        self.progress = progress
        self.direction = direction
        self.size = size
        self.lineWidth = lineWidth
    }
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(Color.white.opacity(0.3), lineWidth: lineWidth)
                .frame(width: size, height: size)
            
            // Progress circle
            Circle()
                .trim(from: 0, to: min(1.0, max(0.0, progress)))
                .stroke(Color.white, lineWidth: lineWidth)
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90)) // Start from top
                .animation(.easeInOut(duration: 0.1), value: progress)
            
            // Center icon (always show when progress > 0.1)
            if progress > 0.1 {
                let iconName = direction == .right ? 
                    (progress >= 1.0 ? "hand.thumbsup.fill" : "hand.thumbsup") :
                    (progress >= 1.0 ? "hand.thumbsdown.fill" : "hand.thumbsdown")
                
                Image(systemName: iconName)
                    .font(.system(size: size * 0.4, weight: .bold))
                    .foregroundColor(.white)
                    .scaleEffect(progress >= 1.0 ? 1.1 : 1.0) // Confirmation effect at 100%
                    .animation(.spring(response: 0.2, dampingFraction: 0.5), value: progress >= 1.0)
            }
        }
        .opacity(progress > 0.1 ? 1.0 : 0.0)
        .scaleEffect(progress > 0.1 ? 1.0 : 0.8)
        .animation(.easeInOut(duration: 0.2), value: progress > 0.1)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 30) {
        HStack(spacing: 20) {
            CircularProgressIndicator(progress: 0.3, direction: .right)
            Text("30% - Unfilled thumb")
        }
        
        HStack(spacing: 20) {
            CircularProgressIndicator(progress: 0.7, direction: .left)
            Text("70% - Unfilled thumb")
        }
        
        HStack(spacing: 20) {
            CircularProgressIndicator(progress: 1.0, direction: .right)
            Text("100% - Filled thumb + effect")
        }
        
        HStack(spacing: 20) {
            CircularProgressIndicator(progress: 1.0, direction: .left)
            Text("100% - Filled thumb + effect")
        }
    }
    .padding()
    .background(Color.blue.opacity(0.3))
} 