import SwiftUI

// Custom Layout for Flowing Items (Requires iOS 16+/macOS 13+)
struct FlowLayout: Layout {
    var horizontalSpacing: CGFloat = 8 // Spacing between items on the same row
    var verticalSpacing: CGFloat = 8   // Spacing between rows

    // Calculates the total size needed for the layout
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        guard !subviews.isEmpty else { return .zero }

        let availableWidth = proposal.width ?? .infinity // Use proposed width or infinite if nil
        var currentX: CGFloat = 0
        // var currentY: CGFloat = 0
        var currentRowHeight: CGFloat = 0
        var totalHeight: CGFloat = 0
        var totalWidth: CGFloat = 0

        for subview in subviews {
            let subviewSize = subview.sizeThatFits(.unspecified) // Get intrinsic size

            // Check if the item fits on the current row
            if currentX + subviewSize.width > availableWidth && currentX > 0 {
                // Doesn't fit, move to the next row
                totalHeight += currentRowHeight + verticalSpacing
                // currentY = totalHeight
                currentX = 0
                currentRowHeight = 0
            }

            // Place the item conceptually
            currentX += subviewSize.width
            currentRowHeight = max(currentRowHeight, subviewSize.height)
            totalWidth = max(totalWidth, currentX) // Track max width reached

            // Add spacing if it's not the last item on the line potentially
             if currentX < availableWidth {
                 currentX += horizontalSpacing
             }
        }

        // Add the height of the last row
        totalHeight += currentRowHeight

        // Ensure the calculated width doesn't exceed available width
        totalWidth = min(totalWidth, availableWidth)

        return CGSize(width: totalWidth, height: totalHeight)
    }

    // Places the subviews within the given bounds
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        guard !subviews.isEmpty else { return }

        var currentX = bounds.minX // Start at the leading edge of the bounds
        var currentY = bounds.minY // Start at the top edge
        var currentRowHeight: CGFloat = 0

        for subview in subviews {
            let subviewSize = subview.sizeThatFits(.unspecified)

            // Check if the item fits on the current row
            // Use bounds.width here for actual placement
            if currentX + subviewSize.width > bounds.maxX && currentX > bounds.minX {
                // Move to the next row
                currentY += currentRowHeight + verticalSpacing
                currentX = bounds.minX
                currentRowHeight = 0
            }

            // Calculate the position for this subview
            let position = CGPoint(x: currentX + subviewSize.width / 2, // Center X within its space
                                   y: currentY + subviewSize.height / 2) // Center Y within its space

            // Place the subview
            subview.place(at: position, anchor: .center, proposal: ProposedViewSize(subviewSize))

            // Update placement coordinates for the next item
            currentX += subviewSize.width + horizontalSpacing
            currentRowHeight = max(currentRowHeight, subviewSize.height)
        }
    }
}