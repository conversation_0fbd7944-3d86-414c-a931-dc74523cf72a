import SwiftUI

/// View for displaying the result of an import operation
struct ImportResultView: View {
    
    // MARK: - Properties
    
    let result: URLHandlingResult
    let onDismiss: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                resultContentView
                Spacer()
            }
            .padding()
            .navigationTitle("Import Result")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        onDismiss()
                    }
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.hidden)
    }
    
    // MARK: - Content Views
    
    @ViewBuilder
    private var resultContentView: some View {
        switch result {
        case .success(let info):
            successView(info)
        case .failure(let error):
            errorView(error)
        case .unsupported:
            unsupportedView
        }
    }
    
    @ViewBuilder
    private func successView(_ info: ImportSuccessInfo) -> some View {
        let totalItems = info.importedItemIds.count + info.skippedItemIds.count
        let hasNewItems = info.importedItemIds.count > 0

        VStack(spacing: 16) {
            // Icon - different based on whether we actually imported anything
            Image(systemName: hasNewItems ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundColor(hasNewItems ? .green : .orange)

            // Title - different based on whether we actually imported anything
            Text(hasNewItems ? "Import Successful" : (totalItems == 0 ? "No Content Found" : "Already Imported"))
                .font(.title2)
                .fontWeight(.semibold)

            // Description
            Text(result.description)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            // Navigation hint - only show if we actually imported something
            if hasNewItems, let hint = result.navigationHint() {
                navigationHintView(hint)
            }

            // Additional details if there are conflicts or skipped items
            if !info.conflictsResolved.isEmpty || !info.skippedItemIds.isEmpty {
                additionalDetailsView(info)
            }
        }
    }
    
    @ViewBuilder
    private func errorView(_ error: ImportError) -> some View {
        VStack(spacing: 16) {
            // Error icon
            Image(systemName: result.systemImageName)
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            // Title
            Text(result.title)
                .font(.title2)
                .fontWeight(.semibold)
            
            // Description
            Text(result.description)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var unsupportedView: some View {
        VStack(spacing: 16) {
            // Unsupported icon
            Image(systemName: result.systemImageName)
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            // Title
            Text(result.title)
                .font(.title2)
                .fontWeight(.semibold)
            
            // Description
            Text(result.description)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    @ViewBuilder
    private func navigationHintView(_ hint: String) -> some View {
        VStack(spacing: 8) {
            Text("You can find the imported content in:")
                .font(.caption)
                .foregroundColor(.secondary)

            Text(hint)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.accentColor)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }

    @ViewBuilder
    private func additionalDetailsView(_ info: ImportSuccessInfo) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            if !info.conflictsResolved.isEmpty {
                Text("Conflicts resolved: \(info.conflictsResolved.count)")
                    .font(.caption)
                    .foregroundColor(.orange)
            }

            if !info.skippedItemIds.isEmpty {
                Text("Items skipped: \(info.skippedItemIds.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }


}

// MARK: - Preview

#if DEBUG
struct ImportResultView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Success preview
            ImportResultView(
                result: .success(ImportSuccessInfo(
                    contentType: .llmInstance,
                    importedItemIds: [UUID(), UUID()],
                    conflictsResolved: ["Duplicate instance name resolved"],
                    skippedItemIds: []
                ))
            ) {
                // Empty dismiss action for preview
            }
            .previewDisplayName("Success")
            
            // Success preview
            ImportResultView(
                result: .success(ImportSuccessInfo(
                    contentType: .llmInstance,
                    importedItemIds: [],
                    conflictsResolved: [],
                    skippedItemIds: [UUID()]
                ))
            ) {
                // Empty dismiss action for preview
            }
            .previewDisplayName("Skipped")

            // Failure preview
            ImportResultView(
                result: .failure(.invalidFileFormat)
            ) {
                // Empty dismiss action for preview
            }
            .previewDisplayName("Failure")

            // Unsupported preview
            ImportResultView(
                result: .unsupported
            ) {
                // Empty dismiss action for preview
            }
            .previewDisplayName("Unsupported")
        }
    }
}
#endif
