import SwiftUI

struct FullScreenTextEditorView: View {
    // MARK: - Properties
    let title: String
    let placeholder: String
    let initialText: String
    @Environment(\.dismiss) private var dismiss

    let onSave: (String) -> Void

    @State private var editingText: String = ""
    @State private var hasChanges: Bool = false
    private let originalText: String

    // MARK: - Initialization
    init(title: String, placeholder: String, initialText: String, onSave: @escaping (String) -> Void) {
        self.title = title
        self.placeholder = placeholder
        self.initialText = initialText
        self.onSave = onSave
        self.originalText = initialText
        self._editingText = State(initialValue: initialText)
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Text Editor
                TextEditor(text: $editingText)
                    .font(.body)
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                    .background(Color(.systemBackground))
                    .overlay(
                        // Placeholder
                        VStack {
                            HStack {
                                if editingText.isEmpty {
                                    Text(placeholder)
                                        .foregroundColor(.secondary)
                                        .padding(.horizontal, 20)
                                        .padding(.top, 16)
                                }
                                Spacer()
                            }
                            Spacer()
                        }
                    )
                
                Spacer()
            }
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        // Don't save changes
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Save changes using callback
                        onSave(editingText)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onChange(of: editingText) { newValue in
            hasChanges = newValue != originalText
        }
    }
}

// MARK: - Preview
#Preview {
    FullScreenTextEditorView(
        title: "Edit Prompt",
        placeholder: "Enter your prompt text here...",
        initialText: "Sample prompt text",
        onSave: { newText in
            print("Saved: \(newText)")
        }
    )
}
