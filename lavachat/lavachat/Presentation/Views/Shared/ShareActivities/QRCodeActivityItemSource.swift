import UIKit
import LinkPresentation

/// Simple activity item source for QR code sharing with title and preview
final class QRCodeActivityItemSource: NSObject, UIActivityItemSource {
    
    private let qrCodeImage: UIImage
    private let title: String
    
    init(qrCodeImage: UIImage, title: String) {
        self.qrCodeImage = qrCodeImage
        self.title = title
        super.init()
    }
    
    func activityViewControllerPlaceholderItem(_ activityViewController: UIActivityViewController) -> Any {
        return qrCodeImage
    }
    
    func activityViewController(
        _ activityViewController: UIActivityViewController,
        itemForActivityType activityType: UIActivity.ActivityType?
    ) -> Any? {
        return qrCodeImage
    }
    
    func activityViewController(
        _ activityViewController: UIActivityViewController,
        subjectForActivityType activityType: UIActivity.ActivityType?
    ) -> String {
        return title
    }
    
    func activityViewControllerLinkMetadata(_ activityViewController: UIActivityViewController) -> LPLinkMetadata? {
        let metadata = LPLinkMetadata()
        metadata.title = title
        metadata.imageProvider = NSItemProvider(object: qrCodeImage)
        
        // Set app icon if available
        if let appIcon = UIImage(named: "AppIcon") {
            metadata.iconProvider = NSItemProvider(object: appIcon)
        }
        
        return metadata
    }
}