import SwiftUI

/// A generic selection view for selecting a single item from a list with detail view access.
/// Used for provider/model selection in InstanceDetailView.
struct ItemSelectionView<Item: Identifiable & Hashable, ItemContent: View, DetailContent: View>: View {
    // MARK: - Properties
    
    @Environment(\.dismiss) private var dismiss
    
    // Selection state
    @Binding var selection: Item.ID?
    @State private var temporarySelection: Item.ID?
    @State private var hasChanges: Bool = false
    
    // Navigation state
    @State private var isShowingDetail: Bool = false
    @State private var selectedDetailItem: Item.ID?
    
    // View configuration
    let title: String
    let items: [Item]
    let itemContentBuilder: (Item) -> ItemContent
    let detailViewBuilder: (Item.ID) -> DetailContent
    let onSave: (Item.ID?) -> Void
    
    // MARK: - Initialization
    
    /// Initializes a selection view for choosing an item from a list
    /// - Parameters:
    ///   - title: The title to display in the navigation bar
    ///   - items: The list of items to choose from
    ///   - selection: Binding to the currently selected item ID
    ///   - itemContentBuilder: A closure that returns the content view for each item
    ///   - detailViewBuilder: A closure that returns the detail view for an item
    ///   - onSave: A closure that handles saving the selection
    init(
        title: String,
        items: [Item],
        selection: Binding<Item.ID?>,
        @ViewBuilder itemContentBuilder: @escaping (Item) -> ItemContent,
        @ViewBuilder detailViewBuilder: @escaping (Item.ID) -> DetailContent,
        onSave: @escaping (Item.ID?) -> Void
    ) {
        self.title = title
        self.items = items
        self._selection = selection
        self.itemContentBuilder = itemContentBuilder
        self.detailViewBuilder = detailViewBuilder
        self.onSave = onSave
        self._temporarySelection = State(initialValue: selection.wrappedValue)
    }
    
    // MARK: - Body
    
    var body: some View {
        List {
            ForEach(items) { item in
                HStack {
                    // Content that handles selection tap
                    HStack {
                        // Left: Checkmark (visible only when selected)
                        Image(systemName: "checkmark")
                            .foregroundColor(.accentColor)
                            .opacity(temporarySelection == item.id ? 1 : 0)
                            .frame(width: 20)
                        
                        // Middle: Item content (logo + name)
                        itemContentBuilder(item)
                        
                        Spacer()
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        temporarySelection = item.id
                    }
                    
                    // Button for the Info button
                    Button {
                        selectedDetailItem = item.id
                        isShowingDetail = true
                    } label: {
                        Image(systemName: "info.circle")
                            .foregroundColor(.accentColor)
                            .padding(8)
                    }
                    .contentShape(Rectangle())
                }
            }
        }
        .navigationTitle(title)
        .navigationBarTitleDisplayMode(.inline)
        .navigationDestination(isPresented: $isShowingDetail) {
            if let selectedDetailItem {
                detailViewBuilder(selectedDetailItem)
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    onSave(temporarySelection)
                    selection = temporarySelection
                    dismiss()
                }
                .foregroundColor(hasChanges ? .accentColor : .gray)
                .disabled(!hasChanges)
            }
        }
        .onChange(of: temporarySelection) { newValue in
            hasChanges = newValue != selection
        }
    }
}

// MARK: - Preview Provider

struct ItemSelectionView_Previews: PreviewProvider {
    // Sample data structures for preview
    struct SampleItem: Identifiable, Hashable {
        let id: UUID
        let name: String
        let icon: String
    }
    
    // Sample data
    static let sampleItems = [
        SampleItem(id: UUID(), name: "Item One", icon: "sparkle"),
        SampleItem(id: UUID(), name: "Item Two", icon: "book"),
        SampleItem(id: UUID(), name: "Item Three", icon: "photo")
    ]
    
    static var previews: some View {
        NavigationStack {
            ItemSelectionView(
                title: "Select Item",
                items: sampleItems,
                selection: .constant(sampleItems.first?.id),
                itemContentBuilder: { item in
                    HStack {
                        Image(systemName: item.icon)
                            .foregroundColor(.accentColor)
                            .padding(.trailing, 8)
                        Text(item.name)
                            .foregroundColor(.primary)
                    }
                },
                detailViewBuilder: { itemId in
                    let item = sampleItems.first { $0.id == itemId }
                    return Text("Details for \(item?.name ?? "Unknown")")
                        .navigationTitle("Item Details")
                },
                onSave: { _ in }
            )
        }
    }
} 
