 // lavachat/lavachat/Presentation/Views/Shared/LogoDisplayView.swift
import SwiftUI

/// Reusable view for displaying logos with dark mode support from asset catalog
struct CatalogLogoImage: View {
    let logoName: String
    let size: CGFloat
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        // Try to load the dark mode version of the logo if in dark mode
        // Assumes dark mode variants are named e.g., "logoName_dark" in Assets
        let imageName = (colorScheme == .dark) ? (logoName + "_dark") : logoName
        
        if UIImage(named: imageName) != nil {
            Image(imageName)
                .resizable()
                .scaledToFit()
                .frame(width: size, height: size)
        } else if UIImage(named: logoName) != nil { // Fallback to base name if specific variant not found
            Image(logoName)
                .resizable()
                .scaledToFit()
                .frame(width: size, height: size)
        } else {
            // Default placeholder if no image is found at all
            Image(systemName: "questionmark.circle.fill")
                .resizable()
                .scaledToFit()
                .foregroundColor(.secondary)
                .frame(width: size, height: size)
        }
    }
}

/// Reusable view for displaying a logo from Data
struct CustomLogoImage: View {
    let imageData: Data?
    let size: CGFloat
    
    var body: some View {
        if let data = imageData, let uiImage = UIImage(data: data) {
            Image(uiImage: uiImage)
                .resizable()
                .scaledToFit()
                .frame(width: size, height: size)
                .clipShape(RoundedRectangle(cornerRadius: size * 0.15)) // Consistent corner radius
        } else {
            // Placeholder if data is nil or invalid
            Image(systemName: "photo.on.rectangle.angled")
                .resizable()
                .scaledToFit()
                .foregroundColor(.secondary)
                .frame(width: size, height: size)
        }
    }
}

/// Displays the logo for an LLMProvider
struct ProviderLogoView: View {
    let provider: LLMProvider?
    let size: CGFloat
    
    var body: some View {
        if let customData = provider?.customLogoData {
            CustomLogoImage(imageData: customData, size: size)
        } else if let logoName = provider?.logoImageName, !logoName.isEmpty {
            CatalogLogoImage(logoName: logoName, size: size)
        } else {
            // Default placeholder for provider
            Image(systemName: "server.rack")
                .resizable()
                .scaledToFit()
                .foregroundColor(.secondary)
                .frame(width: size, height: size)
        }
    }
}

/// Displays the logo for an LLMModel, with fallback to its provider's logo
struct ModelLogoView: View {
    let model: LLMModel?
    let provider: LLMProvider? // Provider is needed for fallback
    let size: CGFloat
    
    var body: some View {
        if let modelCustomData = model?.customLogoData {
            CustomLogoImage(imageData: modelCustomData, size: size)
        } else if let modelLogoName = model?.logoImageName, !modelLogoName.isEmpty {
            CatalogLogoImage(logoName: modelLogoName, size: size)
        } else if let providerCustomData = provider?.customLogoData { // Fallback to provider's custom logo
            CustomLogoImage(imageData: providerCustomData, size: size)
        } else if let providerLogoName = provider?.logoImageName, !providerLogoName.isEmpty { // Fallback to provider's catalog logo
            CatalogLogoImage(logoName: providerLogoName, size: size)
        } else {
            // Default placeholder for model
            Image(systemName: "cube")
                .resizable()
                .scaledToFit()
                .foregroundColor(.secondary)
                .frame(width: size, height: size)
        }
    }
}

/// Displays the logo for an LLMInstance, with fallback to its model's and then provider's logo
struct InstanceLogoView: View {
    let instance: LLMInstance?
    let model: LLMModel?       // Associated model, needed for fallback
    let provider: LLMProvider? // Associated provider, needed for further fallback
    let size: CGFloat
    
    var body: some View {
        if let instanceCustomData = instance?.customLogoData {
            CustomLogoImage(imageData: instanceCustomData, size: size)
        } else if let modelCustomData = model?.customLogoData { // Fallback to model's custom logo
            CustomLogoImage(imageData: modelCustomData, size: size)
        } else if let modelLogoName = model?.logoImageName, !modelLogoName.isEmpty { // Fallback to model's catalog logo
            CatalogLogoImage(logoName: modelLogoName, size: size)
        } else if let providerCustomData = provider?.customLogoData { // Fallback to provider's custom logo
            CustomLogoImage(imageData: providerCustomData, size: size)
        } else if let providerLogoName = provider?.logoImageName, !providerLogoName.isEmpty { // Fallback to provider's catalog logo
            CatalogLogoImage(logoName: providerLogoName, size: size)
        } else {
            // Default placeholder for instance
            Image(systemName: "sparkles")
                .resizable()
                .scaledToFit()
                .foregroundColor(.gray)
                .frame(width: size, height: size)
        }
    }
}