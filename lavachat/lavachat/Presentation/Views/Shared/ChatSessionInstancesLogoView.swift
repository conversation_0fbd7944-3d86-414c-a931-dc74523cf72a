import SwiftUI

/// A view for displaying multiple LLM instance logos, optimized for showing 1 to many instances
/// with special overlapping layout for 2-3 instances inspired by SnowglobeAvatarLayoutConfigurations
struct ChatSessionInstancesLogoView: View {
    // MARK: - Properties
    
    let instanceContexts: [LLMInstanceContext]
    let size: CGFloat
    
    private var logoInfos: [LogoInfo] {
        generateLogoInfos()
    }
    
    // MARK: - Initialization
    
    /// Initializes the view with instance contexts and size
    /// - Parameters:
    ///   - instanceContexts: The instance contexts to display
    ///   - size: The size of the view
    init(instanceContexts: [LLMInstanceContext], size: CGFloat) {
        self.instanceContexts = instanceContexts
        self.size = size
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // Choose the appropriate layout based on number of instances
            switch logoInfos.count {
            case 0:
                // No logos, show placeholder
                emptyLogoPlaceholder
            case 1:
                // Single logo
                singleLogo(logoInfos[0], clipShape: false)
            case 2...3:
                // 2-3 logos with snowglobe-style overlap
                snowglobeLayout
            default:
                // 4+ logos with combined icon
                multiLogoPlaceholder(logoInfos[0])
            }
        }
        .frame(width: size, height: size)
    }
    
    // MARK: - UI Components
    
    /// Placeholder when no logos are available
    private var emptyLogoPlaceholder: some View {
        Image(systemName: "sparkles")
            .resizable()
            .scaledToFit()
            .foregroundColor(.gray)
            .frame(width: size * 0.6, height: size * 0.6)
            .frame(width: size, height: size)
            .background(Color.gray.opacity(0.1))
            .clipShape(Circle())
    }
    
    /// Display for a single logo
    /// - Parameters:
    ///   - logoInfo: The logo info to display
    ///   - clipShape: Whether to clip the shape of the logo
    @ViewBuilder
    private func singleLogo(_ logoInfo: LogoInfo, clipShape: Bool = true) -> some View {
        let logo = logoView(for: logoInfo, size: size * 0.9, clipShape: clipShape)
            .frame(width: size, height: size)
        
        if clipShape {
            logo.clipShape(Circle())
        } else {
            logo.clipShape(Rectangle())
        }
    }
    
    /// Snowglobe-inspired layout for 2-3 logos
    private var snowglobeLayout: some View {
        ZStack {
            // Base container
            Circle()
                .fill(Color.clear)
                .frame(width: size, height: size)
            
            // Create overlapping logos based on count
            ForEach(0..<logoInfos.count, id: \.self) { index in
                if index < logoInfos.count {
                    let logoInfo = logoInfos[index]
                    
                    // Position based on layout configuration for 2 or 3 logos
                    if logoInfos.count == 2 {
                        // Two logo layout
                        if index == 0 {
                            // First logo (larger, top-left)
                            logoView(for: logoInfo, size: size * 0.62)
                                .offset(x: -size * 0.135, y: -size * 0.135)
                        } else {
                            // Second logo (smaller, bottom-right)
                            logoView(for: logoInfo, size: size * 0.36)
                                .offset(x: size * 0.225, y: size * 0.225)
                        }
                    } else {
                        // Three logo layout
                        if index == 0 {
                            // First logo (largest, top-left)
                            logoView(for: logoInfo, size: size * 0.55)
                                .offset(x: -size * 0.16, y: -size * 0.16)
                        } else if index == 1 {
                            // Second logo (medium, top-right)
                            logoView(for: logoInfo, size: size * 0.41)
                                .offset(x: size * 0.27, y: size * 0.10)
                        } else {
                            // Third logo (smallest, bottom-center)
                            logoView(for: logoInfo, size: size * 0.34)
                                .offset(x: -size * 0.08, y: size * 0.31)
                        }
                    }
                }
            }
        }
    }
    
    /// Placeholder for 4+ logos
    private func multiLogoPlaceholder(_ logoInfo: LogoInfo) -> some View {
        ZStack {
            // Base circle
            Circle()
                .fill(Color.gray.opacity(0.1))
                .frame(width: size, height: size)
            
            // Stack icon
            logoView(for: logoInfo, size: size * 0.6, clipShape: false)
            
            // Count indicator
            Text("+\(logoInfos.count)")
                .font(.system(size: size * 0.25, weight: .bold))
                .foregroundColor(.white)
                .padding(size * 0.05)
                .background(Circle().fill(.gray))
                .offset(x: size * 0.3, y: size * 0.3)
        }
    }
    
    /// Creates the appropriate view for a logo based on its source type
    /// - Parameters:
    ///   - logoInfo: The logo info to display
    ///   - size: The size of the logo
    ///   - clipShape: Whether to clip the shape of the logo
    @ViewBuilder
    private func logoView(for logoInfo: LogoInfo, size: CGFloat, clipShape: Bool = true) -> some View {
        let logo = Group {
            switch logoInfo.logoSource {
            case .customData(let data):
                CustomLogoImage(imageData: data, size: size)
            case .assetName(let name):
                CatalogLogoImage(logoName: name, size: size)
            case .sfSymbol(let name):
                Image(systemName: name)
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(.secondary)
                    .frame(width: size * 0.6, height: size * 0.6)
                    .frame(width: size, height: size)
                    .background(Circle().fill(Color.gray.opacity(0.1)))
            }
        }
        if clipShape {
            logo.clipShape(Circle())
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        } else {
            logo.clipShape(Rectangle())
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }        
    }
    
    // MARK: - Private Methods
    
    private func generateLogoInfos() -> [LogoInfo] {
        return instanceContexts.map { context in
            let instance = context.instance
            let model = context.model
            let provider = context.provider
            
            // 1. Check if instance has custom logo data
            if let customLogoData = instance.customLogoData {
                return LogoInfo(
                    id: instance.id,
                    logoSource: .customData(customLogoData),
                    instanceName: instance.name
                )
            }
            
            // 2. Check if model has custom logo data
            if let modelCustomLogoData = model.customLogoData {
                return LogoInfo(
                    id: instance.id,
                    logoSource: .customData(modelCustomLogoData),
                    instanceName: instance.name
                )
            }
            
            // 3. Check if model has logo asset name
            if let modelLogoName = model.logoImageName, !modelLogoName.isEmpty {
                return LogoInfo(
                    id: instance.id,
                    logoSource: .assetName(modelLogoName),
                    instanceName: instance.name
                )
            }
            
            // 4. Check if provider has custom logo data
            if let providerCustomLogoData = provider.customLogoData {
                return LogoInfo(
                    id: instance.id,
                    logoSource: .customData(providerCustomLogoData),
                    instanceName: instance.name
                )
            }
            
            // 5. Check if provider has logo asset name
            if let providerLogoName = provider.logoImageName, !providerLogoName.isEmpty {
                return LogoInfo(
                    id: instance.id,
                    logoSource: .assetName(providerLogoName),
                    instanceName: instance.name
                )
            }
            
            // 6. Default fallback to a SF Symbol
            return LogoInfo(
                id: instance.id,
                logoSource: .sfSymbol("sparkles"),
                instanceName: instance.name
            )
        }
    }
}

// MARK: - Previews

#if DEBUG
#Preview("Previews") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    VStack(spacing: 20) {
        ChatSessionInstancesLogoView(
            instanceContexts: [],
            size: 60
        )

        // TODO: Update preview with actual instance contexts
        // Temporarily using empty array until MockChatPreviewData is updated
        ChatSessionInstancesLogoView(
            instanceContexts: [],
            size: 60
        )
    }.environmentObject(container)
}
#endif
