#if DEBUG
import Foundation

/// Mock implementation of KeychainRepository for previews and testing
final class MockKeychainRepository: KeychainRepository {
    // Mock storage for API keys, mapping provider IDs to keys
    private var keychainStore: [UUID: String] = [
        // Preset mock data
        UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!: "sk_open_ai_mock_key_12345", // OpenAI
        UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!: "custom_provider_api_key_67890" // Custom Provider
    ]
    
    // Error simulation flags
    private var shouldThrowError = false
    private var errorMessage = "Mock keychain operation failed"
    
    // MARK: - Initialization
    
    init(shouldThrowError: Bool = false) {
        self.shouldThrowError = shouldThrowError
    }
    
    // MARK: - Helper Methods
    
    private func simulateAsyncOperation<T>(returning value: T) async throws -> T {
        // Optional: add a tiny delay to simulate keychain operations
        try? await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
        
        return value
    }
    
    // MARK: - KeychainRepository Methods
    
    func saveApiKey(_ apiKey: String, for providerId: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        keychainStore[providerId] = apiKey
    }
    
    func getApiKey(for providerId: UUID) async throws -> String? {
        // This simulates requiring authentication
        // In a real app, this function would trigger Touch ID/Face ID
        try await simulateAsyncOperation(returning: keychainStore[providerId])
    }
    
    func updateApiKey(_ apiKey: String, for providerId: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        
        if keychainStore[providerId] != nil {
            keychainStore[providerId] = apiKey
        } else {
            throw KeychainError.itemNotFound
        }
    }
    
    func deleteApiKey(for providerId: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        keychainStore.removeValue(forKey: providerId)
    }
    
    func checkApiKeyExists(for providerId: UUID) async throws -> Bool {
        try await simulateAsyncOperation(returning: keychainStore[providerId] != nil)
    }
}
#endif
