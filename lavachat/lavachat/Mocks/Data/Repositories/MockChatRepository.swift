#if DEBUG
import Foundation
import Combine

/// Mock implementation of ChatRepository for testing and previews.
final class MockChatRepository: ChatRepository {
    
    // MARK: - Mock Data Storage
    
    private var chatSessions: [ChatSession] = []
    private var messages: [Message] = []
    private var chatSessionSettings: [ChatSessionSetting] = []
    private var messageActions: [MessageAction] = []
    private let dataChangeSubject = PassthroughSubject<Void, Never>()
    private var isBatchUpdating = false
    
    // MARK: - ChatView Relevant Changes
    
    private let chatViewRelevantChangeSubject = PassthroughSubject<(UUID, ChatViewRelevantChange), Never>()
    
    // MARK: - ChatsView Relevant Changes
    
    private let chatsViewRelevantChangeSubject = PassthroughSubject<ChatsViewRelevantChange, Never>()
    
    // Batch operation state tracking
    private var batchSessionCreated = false
    private var batchSessionDeleted = false
    
    // MARK: - Initialization
    
    init() {
        // Initialize with mock data
        loadMockData()
    }
    
    // MARK: - Data Change Observation
    
    var dataDidChangePublisher: AnyPublisher<Void, Never> {
        return dataChangeSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Batch Operations Control
    
    func beginBatchUpdate() async {
        isBatchUpdating = true
        batchSessionCreated = false
        batchSessionDeleted = false
    }
    
    func endBatchUpdateAndPublish() async throws {
        isBatchUpdating = false
        
        // Send accumulated batch notifications
        if batchSessionCreated {
            chatsViewRelevantChangeSubject.send(.sessionCreated)
        }
        if batchSessionDeleted {
            chatsViewRelevantChangeSubject.send(.sessionDeleted)
        }
        
        // Reset batch flags
        batchSessionCreated = false
        batchSessionDeleted = false
        
        publishDataChange()
    }
    
    // MARK: - ChatSession Operations
    
    func createChatSession(_ session: ChatSession) async throws {
        chatSessions.append(session)
        
        // Notify ChatsView-specific changes
        if !isBatchUpdating {
            chatsViewRelevantChangeSubject.send(.sessionCreated)
        } else {
            batchSessionCreated = true
        }
        
        publishDataChangeIfNeeded()
    }
    
    func getChatSession(byId id: UUID) async throws -> ChatSession? {
        return chatSessions.first { $0.id == id }
    }
    
    func getAllChatSessions(for userId: UUID, sortBy sortDescriptors: [NSSortDescriptor]?) async throws -> [ChatSession] {
        let userSessions = chatSessions.filter { $0.userId == userId }
        
        guard let sortDescriptors = sortDescriptors else {
            return userSessions
        }
        
        // Simple sorting implementation for lastModifiedAt
        if let firstDescriptor = sortDescriptors.first,
           firstDescriptor.key == "lastModifiedAt" {
            return userSessions.sorted { session1, session2 in
                if firstDescriptor.ascending {
                    return session1.lastModifiedAt < session2.lastModifiedAt
                } else {
                    return session1.lastModifiedAt > session2.lastModifiedAt
                }
            }
        }
        
        return userSessions
    }
    
    func updateChatSession(_ session: ChatSession) async throws {
        if let index = chatSessions.firstIndex(where: { $0.id == session.id }) {
            let oldSession = chatSessions[index]
            chatSessions[index] = session
            
            // Notify ChatView-specific changes
            if oldSession.title != session.title {
                chatViewRelevantChangeSubject.send((session.id, .sessionTitleChanged(session.title)))
            }
            if oldSession.settingsId != session.settingsId {
                chatViewRelevantChangeSubject.send((session.id, .sessionSettingsChanged(session.settingsId)))
            }
            
            publishDataChangeIfNeeded()
        }
    }
    
    func deleteChatSession(byId id: UUID) async throws {
        chatSessions.removeAll { $0.id == id }
        // Also remove associated messages
        messages.removeAll { $0.sessionId == id }
        
        // Notify ChatView-specific changes
        chatViewRelevantChangeSubject.send((id, .sessionDeleted))
        
        // Notify ChatsView-specific changes
        if !isBatchUpdating {
            chatsViewRelevantChangeSubject.send(.sessionDeleted)
        } else {
            batchSessionDeleted = true
        }
        
        publishDataChangeIfNeeded()
    }
    
    // MARK: - Message Operations
    
    func createMessage(_ message: Message) async throws {
        messages.append(message)
        publishDataChangeIfNeeded()
    }
    
    func getMessage(byId id: UUID) async throws -> Message? {
        return messages.first { $0.id == id }
    }
    
    func getMessages(for sessionId: UUID) async throws -> [Message] {
        return messages.filter { $0.sessionId == sessionId }
    }
    
    func getMessages(for messageIds: [UUID]) async throws -> [Message] {
        return messages.filter { messageIds.contains($0.id) }
    }
    
    func updateMessage(_ message: Message) async throws {
        if let index = messages.firstIndex(where: { $0.id == message.id }) {
            messages[index] = message
            publishDataChangeIfNeeded()
        }
    }
    
    func deleteMessage(byId id: UUID) async throws {
        messages.removeAll { $0.id == id }
        publishDataChangeIfNeeded()
    }
    
    func getMessages(parentId: UUID?) async throws -> [Message] {
        return messages.filter { $0.parentId == parentId }
    }
    
    func getRootMessages(for sessionId: UUID) async throws -> [Message] {
        return messages.filter { $0.sessionId == sessionId && $0.parentId == nil }
    }
    
    func getChildMessages(for parentMessageId: UUID) async throws -> [Message] {
        return messages.filter { $0.parentId == parentMessageId }
    }
    
    // MARK: - Private Methods
    
    private func loadMockData() {
        // Load example sessions from MockChatPreviewData
        chatSessions = MockChatPreviewData.getAllExampleSessions()
        
        // Load example messages for each session
        for session in chatSessions {
            let sessionMessages = MockChatPreviewData.getMessagesForSession(sessionId: session.id)
            messages.append(contentsOf: sessionMessages)
        }
        
        // Load example settings
        let (settings, actions) = createMockSettings()
        chatSessionSettings = settings
        messageActions = actions
    }
    
    private func createMockSettings() -> (settings: [ChatSessionSetting], actions: [MessageAction]) {
        let mockPromptSegments: [SavedPromptSegment] = [
            SavedPromptSegment(
                id: UUID(uuidString: "F1F1F1F1-F1F1-F1F1-F1F1-F1F1F1F1F1F1")!,
                content: "Summarize this in 3 key points",
                usageCount: 50,
                createdAt: Date().addingTimeInterval(-30 * 24 * 3600),
                lastUsedAt: Date().addingTimeInterval(-1 * 24 * 3600)
            ),
            SavedPromptSegment(
                id: UUID(uuidString: "F2F2F2F2-F2F2-F2F2-F2F2-F2F2F2F2F2F2")!,
                content: "Explain this like I'm 5 years old",
                usageCount: 45,
                createdAt: Date().addingTimeInterval(-25 * 24 * 3600),
                lastUsedAt: Date().addingTimeInterval(-2 * 24 * 3600)
            ),
            SavedPromptSegment(
                id: UUID(uuidString: "F3F3F3F3-F3F3-F3F3-F3F3-F3F3F3F3F3F3")!,
                content: "What are the pros and cons?",
                usageCount: 40,
                createdAt: Date().addingTimeInterval(-20 * 24 * 3600),
                lastUsedAt: Date().addingTimeInterval(-3 * 24 * 3600)
            ),
            SavedPromptSegment(
                id: UUID(uuidString: "F4F4F4F4-F4F4-F4F4-F4F4-F4F4F4F4F4F4")!,
                content: "Provide a step-by-step guide",
                usageCount: 35,
                createdAt: Date().addingTimeInterval(-18 * 24 * 3600),
                lastUsedAt: Date().addingTimeInterval(-4 * 24 * 3600)
            ),
            SavedPromptSegment(
                id: UUID(uuidString: "F5F5F5F5-F5F5-F5F5-F5F5-F5F5F5F5F5F5")!,
                content: "Find potential issues or problems",
                usageCount: 30,
                createdAt: Date().addingTimeInterval(-15 * 24 * 3600),
                lastUsedAt: Date().addingTimeInterval(-5 * 24 * 3600)
            )
        ]

        let customAction1 = MessageAction(
            id: UUID(uuidString: "AC710000-C001-0001-AC71-0000C0010001")!,
            name: "Pyramid Principle",
            icon: "pyramid",
            actionType: .assistantRegenerate,
            prompts: ["Act as an expert management consultant trained in the Minto Pyramid Principle. Rewrite your previous response to present it to a busy executive. The goal is to create a top-down, logically structured response that is easy to understand and act upon."]
        )

        let customAction2 = MessageAction(
            id: UUID(uuidString: "AC710000-C002-0002-AC71-0000C0020002")!,
            name: "Nonviolent Reply",
            icon: "heart.text.square",
            actionType: .assistantRegenerate,
            prompts: ["Please rephrase your last response using the four components of Nonviolent Communication (NVC). The goal is to present the information in a way that fosters connection and understanding."]
        )

        let customAction3 = MessageAction(
            id: UUID(uuidString: "AC710000-C003-0003-AC71-0000C0030003")!,
            name: "Translate",
            icon: "translate",
            actionType: .assistantRegenerate,
            prompts: [
                "Translate last response to English",
                "Translate last response to Simplified Chinese (this is a test)",
                "Translate last response to Traditional Chinese (this is a test)",
                "Translate last response to 简体中文",
                "Translate last response to 繁體中文",
                "Translate last response to 日本語",
                "Translate last response to 한국어",
                "Translate last response to Français",
                "Translate last response to Español"
            ]
        )

        let actionPanelPromptInsert = MessageAction(
            id: UUID(uuidString: "AC710000-9001-0001-AC71-000090010001")!,
            name: "Insert",
            icon: "text.insert",
            actionType: .actionPanelPromptInsert,
            prompts: [
                BuiltinPrompt.builtinActionPanelPromptInsert["RIPER-5"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["EXPERT PANEL"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["IDEA-FACTORY"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["SPICE PROTOCOL"]!,
                BuiltinPrompt.builtinActionPanelPromptInsert["FIRST PRINCIPLES"]!,
            ]
        )

        let actionPanelPromptRewrite = MessageAction(
            id: UUID(uuidString: "AC710000-9002-0002-AC71-000090020002")!,
            name: "Optimize",
            icon: "wand.and.sparkles",
            actionType: .actionPanelPromptRewrite,
            prompts: [
                BuiltinPrompt.builtinActionPanelPromptRewrite["Prompt Optimizer"]!
            ]
        )
        
        let actionPanelPromptEnhance = MessageAction(
            id: UUID(uuidString: "AC710000-9003-0003-AC71-000090030003")!,
            name: "Enhance",
            icon: "wand.and.sparkles.inverse",
            actionType: .actionPanelPromptRewrite,
            prompts: [
                BuiltinPrompt.builtinActionPanelPromptRewrite["Prompt Optimizer"]!,
                BuiltinPrompt.builtinActionPanelPromptRewrite["Prompt Clarifier"]!,
                BuiltinPrompt.builtinActionPanelPromptRewrite["Analytical Prompt Enhancer"]!,
                BuiltinPrompt.builtinActionPanelPromptRewrite["Creative Prompt Catalyst"]!
            ]
        ),

        messageActions = [customAction1, customAction2, customAction3, actionPanelPromptInsert, actionPanelPromptRewrite, actionPanelPromptEnhance]
        
        let systemDefaultSetting = ChatSessionSetting(
            id: UUID(uuidString: "C142A374-5536-7778-C142-A37455367778")!,
            name: "System Default",
            isSystemDefault: true,
            createdAt: Date(),
            lastModifiedAt: Date(),
            llmParameterOverrides: nil,
            defaultContextServerIds: nil,
            shouldExpandThinking: true,
            uiThemeSettings: UIThemeSettings(
                themeIdentifier: "system",
                customColors: nil,
                backgroundImage: nil,
                fontSize: .medium
            ),
            messageActionSettings: MessageActionSettings(
                actionPanelActions: [SystemMessageActions.camera.id, SystemMessageActions.photo.id, SystemMessageActions.file.id, actionPanelPromptInsert.id, actionPanelPromptRewrite.id],
                userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.edit.id],
                assistantMessageCardActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id, customAction1.id, customAction3.id],
                assistantMessageMenuActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id, SystemMessageActions.select.id, customAction2.id, customAction3.id]
            ),
            savedPromptSegments: mockPromptSegments,
            auxiliaryLLMInstanceId: nil,
            shouldAutoGenerateTitle: false,
            contextMessageCount: Int64.max,
            metadata: nil
        )
        
        let userCustomSetting = ChatSessionSetting(
            id: UUID(uuidString: "C142A374-7356-7778-C142-A37473567778")!,
            name: "User Custom",
            isSystemDefault: false,
            createdAt: Date(),
            lastModifiedAt: Date(),
            llmParameterOverrides: nil,
            defaultContextServerIds: nil,
            shouldExpandThinking: true,
            uiThemeSettings: UIThemeSettings(
                themeIdentifier: "system",
                customColors: nil,
                backgroundImage: nil,
                fontSize: .medium
            ),
            messageActionSettings: MessageActionSettings(
                actionPanelActions: [SystemMessageActions.camera.id, SystemMessageActions.photo.id, SystemMessageActions.file.id],
                userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.edit.id],
                assistantMessageCardActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id],
                assistantMessageMenuActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id, SystemMessageActions.select.id]
            ),
            savedPromptSegments: mockPromptSegments,
            auxiliaryLLMInstanceId: UUID(uuidString: "C142A374-AUX1-AUX1-AUX1-A37455367778"),
            shouldAutoGenerateTitle: true,
            contextMessageCount: 20,
            metadata: nil
        )
        
        return (settings: [systemDefaultSetting, userCustomSetting], actions: messageActions)
    }

    private func publishDataChangeIfNeeded() {
        if !isBatchUpdating {
            publishDataChange()
        }
    }
    
    private func publishDataChange() {
        DispatchQueue.main.async { [weak self] in
            self?.dataChangeSubject.send()
        }
    }
    
    // MARK: - Test Helpers
    
    /// Resets all data to initial mock state (useful for testing)
    func resetToMockData() {
        chatSessions.removeAll()
        messages.removeAll()
        loadMockData()
        publishDataChange()
    }
    
    /// Adds custom test data
    func addTestSession(_ session: ChatSession) {
        chatSessions.append(session)
    }
    
    func addTestMessage(_ message: Message) {
        messages.append(message)
    }
    
    // MARK: - ChatSessionSetting Operations
    
    func createSetting(_ setting: ChatSessionSetting) async throws {
        chatSessionSettings.append(setting)
        publishDataChangeIfNeeded()
    }
    
    func getSetting(byId id: UUID) async throws -> ChatSessionSetting? {
        return chatSessionSettings.first { $0.id == id }
    }
    
    func getAllSettings() async throws -> [ChatSessionSetting] {
        return chatSessionSettings
    }
    
    func getSystemDefaultSetting() async throws -> ChatSessionSetting? {
        return chatSessionSettings.first { $0.isSystemDefault }
    }
    
    func updateSetting(_ setting: ChatSessionSetting) async throws {
        if let index = chatSessionSettings.firstIndex(where: { $0.id == setting.id }) {
            let oldSetting = chatSessionSettings[index]
            chatSessionSettings[index] = setting
            
            // Notify ChatView-specific changes for sessions using this setting
            let affectedSessions = chatSessions.filter { $0.settingsId == setting.id }
            for session in affectedSessions {
                // Check for specific field changes
                if oldSetting.uiThemeSettings?.themeIdentifier != setting.uiThemeSettings?.themeIdentifier ||
                   oldSetting.uiThemeSettings?.fontSize != setting.uiThemeSettings?.fontSize {
                    chatViewRelevantChangeSubject.send((session.id, .settingUIThemeChanged(setting.uiThemeSettings)))
                }
                
                if oldSetting.messageActionSettings?.actionPanelActions != setting.messageActionSettings?.actionPanelActions ||
                   oldSetting.messageActionSettings?.userMessageActions != setting.messageActionSettings?.userMessageActions ||
                   oldSetting.messageActionSettings?.assistantMessageCardActions != setting.messageActionSettings?.assistantMessageCardActions ||
                   oldSetting.messageActionSettings?.assistantMessageMenuActions != setting.messageActionSettings?.assistantMessageMenuActions {
                    chatViewRelevantChangeSubject.send((session.id, .settingMessageActionsChanged(setting.messageActionSettings)))
                }
                
                if oldSetting.savedPromptSegments?.count != setting.savedPromptSegments?.count ||
                   oldSetting.savedPromptSegments?.first?.content != setting.savedPromptSegments?.first?.content {
                    chatViewRelevantChangeSubject.send((session.id, .settingPromptSegmentsChanged(setting.savedPromptSegments)))
                }

                if oldSetting.shouldExpandThinking != setting.shouldExpandThinking {
                    chatViewRelevantChangeSubject.send((session.id, .settingShouldExpandThinkingChanged(setting.shouldExpandThinking)))
                }

                if oldSetting.auxiliaryLLMInstanceId != setting.auxiliaryLLMInstanceId {
                    chatViewRelevantChangeSubject.send((session.id, .settingAuxiliaryLLMInstanceIdChanged(setting.auxiliaryLLMInstanceId)))
                }

                if oldSetting.shouldAutoGenerateTitle != setting.shouldAutoGenerateTitle {
                    chatViewRelevantChangeSubject.send((session.id, .settingShouldAutoGenerateTitleChanged(setting.shouldAutoGenerateTitle)))
                }

                if oldSetting.contextMessageCount != setting.contextMessageCount {
                    chatViewRelevantChangeSubject.send((session.id, .settingContextMessageCountChanged(setting.contextMessageCount)))
                }
            }
            
            publishDataChangeIfNeeded()
        }
    }
    
    func deleteSetting(byId id: UUID) async throws {
        chatSessionSettings.removeAll { $0.id == id }
        publishDataChangeIfNeeded()
    }
    
    /// Gets current data counts for testing
    var sessionCount: Int { chatSessions.count }
    var messageCount: Int { messages.count }
    var settingCount: Int { chatSessionSettings.count }
    
    // MARK: - ChatView Relevant Changes
    
    func observeChatViewRelevantChanges(for sessionId: UUID) -> AnyPublisher<ChatViewRelevantChange, Never> {
        return chatViewRelevantChangeSubject
            .filter { $0.0 == sessionId }
            .map { $0.1 }
            .eraseToAnyPublisher()
    }
    
    func observeChatsViewRelevantChanges() -> AnyPublisher<ChatsViewRelevantChange, Never> {
        return chatsViewRelevantChangeSubject.eraseToAnyPublisher()
    }
    
    // MARK: - MessageAction Operations
    
    func createMessageAction(_ action: MessageAction) async throws {
        messageActions.append(action)
        publishDataChangeIfNeeded()
    }
    
    func getMessageAction(byId id: UUID) async throws -> MessageAction? {
        if let action = SystemMessageActions.actions.first(where: { $0.id == id }) {
            return action
        }
        return messageActions.first { $0.id == id }
    }
    
    func getMessageActions(for actionIds: [UUID]) async throws -> [MessageAction] {
        let systemActions = SystemMessageActions.actions.filter { actionIds.contains($0.id) }
        let customActions = messageActions.filter { actionIds.contains($0.id) }
        return systemActions + customActions
    }
    
    func getAllMessageActions() async throws -> [MessageAction] {
        return messageActions
    }
    
    func updateMessageAction(_ action: MessageAction) async throws {
        if let index = messageActions.firstIndex(where: { $0.id == action.id }) {
            messageActions[index] = action
            publishDataChangeIfNeeded()
        }
    }
    
    func deleteMessageAction(byId id: UUID) async throws {
        messageActions.removeAll { $0.id == id }
        publishDataChangeIfNeeded()
    }
} 
#endif