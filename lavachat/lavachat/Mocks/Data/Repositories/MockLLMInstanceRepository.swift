#if DEBUG
import Foundation
import Combine

/// Mock implementation of LLMInstanceRepository for previews and testing
final class MockLLMInstanceRepository: LLMInstanceRepository {

    // MARK: - Data Change Publisher
    private static let internalDataDidChangeSubject = PassthroughSubject<Void, Never>()
    var dataDidChangePublisher: AnyPublisher<Void, Never> {
        Self.internalDataDidChangeSubject.eraseToAnyPublisher()
    }
    
    /// Helper to simulate data changes in tests or previews
    func triggerDataDidChange() {
        print("MockLLMInstanceRepository: Manually triggered dataDidChangePublisher.")
        Self.internalDataDidChangeSubject.send()
    }
    
    // MARK: - Mock Data Storage
    
    // Static mock data (shared across all instances)
    private static var providers: [LLMProvider] = [
        // OpenAI (built-in, key stored)
        LLMProvider(
            id: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
            name: "OpenA<PERSON>",
            logoImageName: "provider_logo_openai",
            websiteUrl: "https://openai.com",
            apiDocumentationUrl: "https://platform.openai.com/docs/api-reference",
            apiBaseUrl: "https://api.openai.com",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        
        // Anthropic (built-in, key not stored)
        LLMProvider(
            id: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
            name: "Anthropic",
            logoImageName: "provider_logo_anthropic",
            websiteUrl: "https://anthropic.com",
            apiDocumentationUrl: "https://docs.anthropic.com/claude/reference",
            apiBaseUrl: "https://api.anthropic.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .anthropic,
            apiEndpointPath: "/v1/messages",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // Google
        LLMProvider(
            id: UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!,
            name: "Google",
            logoImageName: "provider_logo_google",
            websiteUrl: "https://ai.google.dev",
            apiDocumentationUrl: "https://ai.google.dev/docs/gemini_api_overview",
            apiBaseUrl: "https://generativelanguage.googleapis.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .google,
            apiEndpointPath: "/v1beta/models",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        
        // DeepSeek
        LLMProvider(
            id: UUID(uuidString: "D4E5F6A1-B2C3-7890-D4E5-F6A1B2C37890")!,
            name: "DeepSeek",
            logoImageName: "provider_logo_deepseek",
            websiteUrl: "https://www.deepseek.com/",
            apiDocumentationUrl: "https://api-docs.deepseek.com/",
            apiBaseUrl: "https://api.deepseek.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        
        // Xai (Grok)
        LLMProvider(
            id: UUID(uuidString: "E5F6A1B2-C3D4-7890-E5F6-A1B2C3D47890")!,
            name: "xAI",
            logoImageName: "provider_logo_xai",
            websiteUrl: "https://x.ai",
            apiDocumentationUrl: "https://docs.x.ai/",
            apiBaseUrl: "https://api.x.ai",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        
        // Qwen (Alibaba)
        LLMProvider(
            id: UUID(uuidString: "F6A1B2C3-D4E5-7890-F6A1-B2C3D4E57890")!,
            name: "Qwen",
            logoImageName: "provider_logo_qwen",
            websiteUrl: "https://www.alibabacloud.com/en/solutions/generative-ai/qwen",
            apiDocumentationUrl: "https://www.alibabacloud.com/help/en/model-studio/use-qwen-by-calling-api",
            apiBaseUrl: "https://dashscope.aliyuncs.com/compatible-mode",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        
        // OpenRouter
        LLMProvider(
            id: UUID(uuidString: "A2B3C4D5-E6F7-7890-A2B3-C4D5E6F77890")!,
            name: "OpenRouter",
            logoImageName: "provider_logo_openrouter",
            websiteUrl: "https://openrouter.ai",
            apiDocumentationUrl: "https://openrouter.ai/docs",
            apiBaseUrl: "https://openrouter.ai/api",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // Nvidia
        LLMProvider(
            id: UUID(uuidString: "A3B4C5D6-E7F8-7890-A3B4-C5D6E7F87890")!,
            name: "Nvidia",
            logoImageName: "provider_logo_nvidia",
            websiteUrl: "https://build.nvidia.com/",
            apiDocumentationUrl: "https://docs.api.nvidia.com/nim/reference/llm-apis",
            apiBaseUrl: "https://integrate.api.nvidia.com",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // SiliconFlow
        LLMProvider(
            id: UUID(uuidString: "A4B5C6D7-E8F9-7890-A4B5-C6D7E8F97890")!,
            name: "SiliconFlow",
            logoImageName: "provider_logo_siliconflow",
            websiteUrl: "https://siliconflow.cn/",
            apiDocumentationUrl: "https://docs.siliconflow.cn/",
            apiBaseUrl: "https://api.siliconflow.cn",
            providerType: .userApiKey,
            apiKeyStored: false,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        
        // Custom Provider (user-defined)
        LLMProvider(
            id: UUID(uuidString: "C3D3E3F3-A1B2-7890-C3D3-E3F3A1B27890")!,
            name: "My Custom Provider",
            logoImageName: nil,
            websiteUrl: "https://example.com",
            apiDocumentationUrl: "https://example.com/docs",
            apiBaseUrl: "https://api.example.com",
            providerType: .userApiKey,
            apiKeyStored: true,
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: true,
            metadata: nil
        )
    ]
    
    private static var models: [LLMModel] = [
        // OpenAI Models
        LLMModel(
            id: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!,
            providerId: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
            modelIdentifier: "gpt-4.1",
            name: "GPT-4.1",
            modelDescription: "Flagship GPT model for complex tasks.",
            logoImageName: "provider_logo_openai",
            contextWindowSize: 1047576,
            inputModalities: [.text, .image],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            maxOutputTokens: 32768,
            pricingInfo: "Input: $2/M tokens\nOutput: $8/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "A2A3A4A5-B6B7-7890-A2A3-A4A5B6B77890")!,
            providerId: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
            modelIdentifier: "gpt-4o-mini",
            name: "GPT-4o Mini",
            modelDescription: "Fast, affordable small model for focused tasks.",
            logoImageName: "provider_logo_openai",
            contextWindowSize: 128000,
            inputModalities: [.text, .image],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 16384,
            pricingInfo: "Input: $0.15/M tokens\nOutput: $0.60/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "A3A4A5A6-B7B8-7890-A3A4-A5A6B7B87890")!,
            providerId: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
            modelIdentifier: "o1-mini",
            name: "o1-mini",
            modelDescription: "A small model alternative to o1.",
            logoImageName: "provider_logo_openai",
            contextWindowSize: 128000,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 65536,
            pricingInfo: "Input: $1.10/M tokens\nOutput: $4.40/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "A4A5A6A7-B8B9-7890-A4A5-A6A7B8B97890")!,
            providerId: UUID(uuidString: "A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890")!,
            modelIdentifier: "gpt-image-1",
            name: "GPT Image 1",
            modelDescription: "OpenAI State-of-the-art image generation model.",
            logoImageName: "provider_logo_openai",
            contextWindowSize: 0,
            inputModalities: [.text, .image],
            outputModalities: [.image],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 0,
            pricingInfo: "Input: $5.00/M tokens\nOutput: $40.00/M tokens\n$0.011/0.042/0.167 per 1024x1024 low/medium/high quality image",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            apiConfigsOverride: [
                ModelAPIConfig(apiStyle: .openaiImageGeneration, apiEndpointPath: "/v1/images/generations"),
                ModelAPIConfig(apiStyle: .openaiImageEdit, apiEndpointPath: "/v1/images/edits")
            ],
            metadata: nil
        ),
        
        // Anthropic Models
        LLMModel(
            id: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!,
            providerId: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
            modelIdentifier: "claude-sonnet-4-0",
            name: "Claude Sonnet 4",
            modelDescription: "Anthropic high-performance model.",
            logoImageName: "model_logo_anthropic_claude",
            contextWindowSize: 200000,
            inputModalities: [.text, .image, .pdf],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "thinking"),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
            maxOutputTokens: 64000,
            pricingInfo: "Input: $3/M tokens\nOutput: $15/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "B2B3B4B5-C6C7-7890-B2B3-B4B5C6C77890")!,
            providerId: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
            modelIdentifier: "claude-3-7-sonnet-latest",
            name: "Claude Sonnet 3.7",
            modelDescription: "Anthropic previous high-performance model.",
            logoImageName: "model_logo_anthropic_claude",
            contextWindowSize: 200000,
            inputModalities: [.text, .image, .pdf],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .parameterBased, parameterName: "thinking"),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
            maxOutputTokens: 64000,
            pricingInfo: "Input: $3/M tokens\nOutput: $15/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "B3B4B5B6-C7C8-7890-B3B4-B5B6C7C87890")!,
            providerId: UUID(uuidString: "B2C3D4E5-F6A1-7890-B2C3-D4E5F6A17890")!,
            modelIdentifier: "claude-3-5-haiku-latest",
            name: "Claude 3 Haiku",
            modelDescription: "Fast and cost-effective model for simple tasks.",
            logoImageName: "model_logo_anthropic_claude",
            contextWindowSize: 200000,
            inputModalities: [.text, .image, .pdf],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .promptBased, parameterName: "thinking_prompt"),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
            maxOutputTokens: 8192,
            pricingInfo: "Input: $0.80/M tokens\nOutput: $4.00/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // Google Models
        LLMModel(
            id: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!,
            providerId: UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!,
            modelIdentifier: "gemini-2.5-pro-preview-06-05",
            name: "Gemini 2.5 Pro",
            modelDescription: "Google's state-of-the-art thinking model for most tasks.",
            logoImageName: "model_logo_google_gemini",
            contextWindowSize: 1048576,
            inputModalities: [.text, .image, .audio, .video],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .thinkingBudget, parameterName: "thinkingBudget", maxBudget: 24576),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
            maxOutputTokens: 65536,
            pricingInfo: "Input: $1.25 or $2.50/M tokens\nOutput: $10 or $15/M tokens, depends on prompts <= 200k or > 200k tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "C2C3C4C5-D6D7-7890-C2C3-C4C5D6D77890")!,
            providerId: UUID(uuidString: "C3D4E5F6-A1B2-7890-C3D4-E5F6A1B27890")!,
            modelIdentifier: "gemini-2.5-flash-preview-05-20",
            name: "Gemini 2.5 Flash",
            modelDescription: "Google's latest flash model for most tasks.",
            logoImageName: "model_logo_google_gemini",
            contextWindowSize: 1048576,
            inputModalities: [.text, .image, .audio, .video],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .thinkingBudget, parameterName: "thinkingBudget", maxBudget: 24576),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "tools"),
            maxOutputTokens: 65536,
            pricingInfo: "Free daily quota. Otherwise: Input: $0.15/M tokens\nOutput: $0.60 (non-thinking) or $3.50 (thinking) /M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // DeepSeek Models
        LLMModel(
            id: UUID(uuidString: "D2D2D2D2-E5E6-7890-D1D2-D3D4E5E67890")!,
            providerId: UUID(uuidString: "D4E5F6A1-B2C3-7890-D4E5-F6A1B2C37890")!,
            modelIdentifier: "deepseek-chat",
            name: "DeepSeek V3",
            modelDescription: "DeepSeek's latest non-reasoning model.",
            logoImageName: "provider_logo_deepseek",
            contextWindowSize: 64000,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 8000,
            pricingInfo: "Input: $0.27/M tokens\nOutput: $1.10/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "D2D2D2D2-E6E7-7890-D2D3-D4D5E6E77890")!,
            providerId: UUID(uuidString: "D4E5F6A1-B2C3-7890-D4E5-F6A1B2C37890")!,
            modelIdentifier: "deepseek-reasoner",
            name: "DeepSeek R1",
            modelDescription: "DeepSeek's latest reasoning model.",
            logoImageName: "provider_logo_deepseek",
            contextWindowSize: 64000,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 8000,
            pricingInfo: "Input: $0.55/M tokens\nOutput: $2.19/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // xAI Models
        LLMModel(
            id: UUID(uuidString: "E1E2E3E4-F5F6-7890-E1E2-E3E4F5F67890")!,
            providerId: UUID(uuidString: "E5F6A1B2-C3D4-7890-E5F6-A1B2C3D47890")!,
            modelIdentifier: "grok-3-beta",
            name: "Grok 3",
            modelDescription: "xAI's latest model.",
            logoImageName: "model_logo_xai_grok",
            contextWindowSize: 131072,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "search_parameters"),
            maxOutputTokens: 65536,
            pricingInfo: "Input: $0.15/M tokens\nOutput: $0.60/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "E2E3E4E5-F6F7-7890-E2E3-E4E5F6F77890")!,
            providerId: UUID(uuidString: "E5F6A1B2-C3D4-7890-E5F6-A1B2C3D47890")!,
            modelIdentifier: "grok-3-mini-beta",
            name: "Grok 3 Mini",
            modelDescription: "xAI's latest mini model.",
            logoImageName: "model_logo_xai_grok",
            contextWindowSize: 131072,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .reasoningEffort, parameterName: "reasoning_effort", parameterOptions: ["low", "high"]),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "search_parameters"),
            maxOutputTokens: 65536,
            pricingInfo: "Input: $0.15/M tokens\nOutput: $0.60/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // Qwen Models
        LLMModel(
            id: UUID(uuidString: "F1F2F3F4-A5A6-7890-F1F2-F3F4A5A67890")!,
            providerId: UUID(uuidString: "F6A1B2C3-D4E5-7890-F6A1-B2C3D4E57890")!,
            modelIdentifier: "qwq-32b",
            name: "Qwen 32B",
            modelDescription: "Qwen's medium-sized reasoning model with SOTA performance.",
            logoImageName: "provider_logo_qwen",
            contextWindowSize: 131072,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 8192,
            pricingInfo: "Input: ¥2.0/M tokens\nOutput: ¥6.0/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // OpenRouter Models
        LLMModel(
            id: UUID(uuidString: "A9A9A9A9-B9B9-7890-A9A9-A9A9B9B97890")!,
            providerId: UUID(uuidString: "A2B3C4D5-E6F7-7890-A2B3-C4D5E6F77890")!,
            modelIdentifier: "deepseek/deepseek-chat-v3-0324:free",
            name: "DeepSeek V3 0324 free",
            modelDescription: "DeepSeek's latest non-reasoning model.",
            logoImageName: "provider_logo_deepseek",
            contextWindowSize: 164000,
            inputModalities: [.text, .image],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "plugins"),
            maxOutputTokens: 164000,
            pricingInfo: "Input: $0/M tokens\nOutput: $0/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),
        LLMModel(
            id: UUID(uuidString: "A9A9A9A9-B8B8-7890-A9A9-A9A9B8B87890")!,
            providerId: UUID(uuidString: "A2B3C4D5-E6F7-7890-A2B3-C4D5E6F77890")!,
            modelIdentifier: "deepseek/deepseek-r1-0528:free",
            name: "DeepSeek R1 0528 free",
            modelDescription: "DeepSeek's latest reasoning model.",
            logoImageName: "provider_logo_deepseek",
            contextWindowSize: 164000,
            inputModalities: [.text, .image],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
            searchingCapabilities: SearchingCapabilities(controlType: .parameterBased, parameterName: "plugins"),
            maxOutputTokens: 164000,
            pricingInfo: "Input: $0/M tokens\nOutput: $0/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // Nvidia Models
        LLMModel(
            id: UUID(uuidString: "A3A3A3A3-B4B4-7890-A3A3-A3A3B4B47890")!,
            providerId: UUID(uuidString: "A3B4C5D6-E7F8-7890-A3B4-C5D6E7F87890")!,
            modelIdentifier: "deepseek-ai/deepseek-r1-0528",
            name: "DeepSeek R1 0528",
            modelDescription: "DeepSeek's latest reasoning model.",
            logoImageName: "provider_logo_deepseek",
            contextWindowSize: 128000,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .defaultOn),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 128000,
            pricingInfo: "Free Up to 40 RPM",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // SiliconFlow Models
        LLMModel(
            id: UUID(uuidString: "A4A4A4A4-B5B5-7890-A4A4-A4A4B5B57890")!,
            providerId: UUID(uuidString: "A4B5C6D7-E8F9-7890-A4B5-C6D7E8F97890")!,
            modelIdentifier: "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
            name: "DeepSeek R1 0528 Qwen3-8B",
            modelDescription: "Qwen3-8B distilled from DeepSeek's latest reasoning model R1 0528.",
            logoImageName: "provider_logo_deepseek",
            contextWindowSize: 128000,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .thinkingBudget, parameterName: "thinking_budget", maxBudget: 32768),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 128000,
            pricingInfo: "Free Up to 1000 RPM and 50000 TPM",
            availabilityStatus: .available,
            isDefaultRecommendation: true,
            isUserCreated: false,
            isUserModified: false,
            metadata: nil
        ),

        // Custom Model for Custom Provider
        LLMModel(
            id: UUID(uuidString: "C9C9C9C9-D9D9-7890-C9C9-C9C9D9D97890")!,
            providerId: UUID(uuidString: "C3D3E3F3-A1B2-7890-C3D3-E3F3A1B27890")!,
            modelIdentifier: "custom-model-1",
            name: "Custom Model 1",
            modelDescription: "A custom model for the Custom Provider.",
            logoImageName: nil,
            contextWindowSize: 100000,
            inputModalities: [.text],
            outputModalities: [.text],
            thinkingCapabilities: ThinkingCapabilities(controlType: .none),
            searchingCapabilities: SearchingCapabilities(controlType: .none),
            maxOutputTokens: 20000,
            pricingInfo: "Input: $1/M tokens\nOutput: $4/M tokens",
            availabilityStatus: .available,
            isDefaultRecommendation: false,
            isUserCreated: true,
            isUserModified: true,
            metadata: nil
        )
    ]
    
    private static var instances: [LLMInstance] = [
        // Default GPT-4.1 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!),
            modelId: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!,
            name: "GPT-4.1",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7"],
            totalPromptTokensUsed: 1000,
            totalCompletionTokensUsed: 500,
            createdAt: Date().addingTimeInterval(-86400 * 7), // 1 week ago
            lastUsedAt: Date().addingTimeInterval(-3600), // 1 hour ago
            isFavorited: true,
            isUserModified: false,
            metadata: nil
        ),

        // Default GPT Image 1 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A4A5A6A7-B8B9-7890-A4A5-A6A7B8B97890")!),
            modelId: UUID(uuidString: "A4A5A6A7-B8B9-7890-A4A5-A6A7B8B97890")!,
            name: "GPT Image 1",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7"],
            totalPromptTokensUsed: 1000,
            totalCompletionTokensUsed: 500,
            createdAt: Date().addingTimeInterval(-86400 * 7), // 1 week ago
            lastUsedAt: Date().addingTimeInterval(-3600), // 1 hour ago
            isFavorited: true,
            isUserModified: false,
            metadata: nil
        ),
        
        // Default Claude Sonnet 4 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!),
            modelId: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!,
            name: "Claude Sonnet 4",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "budget_tokens": "12288", "tools": "[{ \"type\": \"web_search_20250305\", \"name\": \"web_search\", \"max_uses\": 5 }]"],
            totalPromptTokensUsed: 500,
            totalCompletionTokensUsed: 250,
            createdAt: Date().addingTimeInterval(-86400 * 3), // 3 days ago
            lastUsedAt: Date().addingTimeInterval(-7200), // 2 hours ago
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Default Claude Sonnet 3.7 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "B2B3B4B5-C6C7-7890-B2B3-B4B5C6C77890")!),
            modelId: UUID(uuidString: "B2B3B4B5-C6C7-7890-B2B3-B4B5C6C77890")!,
            name: "Claude Sonnet 3.7",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "budget_tokens": "12288", "tools": "[{ \"type\": \"web_search_20250305\", \"name\": \"web_search\", \"max_uses\": 5 }]"],
            totalPromptTokensUsed: 300,
            totalCompletionTokensUsed: 150,
            createdAt: Date().addingTimeInterval(-86400 * 2), // 2 days ago
            lastUsedAt: Date().addingTimeInterval(-3600), // 1 hour ago
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Default Claude 3 Haiku instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "B3B4B5B6-C7C8-7890-B3B4-B5B6C7C87890")!),
            modelId: UUID(uuidString: "B3B4B5B6-C7C8-7890-B3B4-B5B6C7C87890")!,
            name: "Claude 3 Haiku",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "thinking_prompt": "Think step by step.", "tools": "[{ \"type\": \"web_search_20250305\", \"name\": \"web_search\", \"max_uses\": 5 }]"],
            totalPromptTokensUsed: 200,
            totalCompletionTokensUsed: 100,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: Date().addingTimeInterval(-1800), // 30 minutes ago
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // OpenRouter DeepSeek V3 0324 Free instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A9A9A9A9-B9B9-7890-A9A9-A9A9B9B97890")!),
            modelId: UUID(uuidString: "A9A9A9A9-B9B9-7890-A9A9-A9A9B9B97890")!,
            name: "DeepSeek V3 0324 Free",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.5"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 3), // 3 days ago
            lastUsedAt: Date().addingTimeInterval(-7200), // 2 hours ago
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // OpenRouter DeepSeek R1 0528 Free instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A9A9A9A9-B8B8-7890-A9A9-A9A9B8B87890")!),
            modelId: UUID(uuidString: "A9A9A9A9-B8B8-7890-A9A9-A9A9B8B87890")!,
            name: "DeepSeek R1 0528 Free",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.5"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 3), // 3 days ago
            lastUsedAt: Date().addingTimeInterval(-7200), // 2 hours ago
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Google Gemini 2.5 Pro instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!),
            modelId: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!,
            name: "Gemini 2.5 Pro",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "thinkingBudget": "12288", "tools": "[{ \"google_search\": {} }]"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Google Gemini 2.5 Flash instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C2C3C4C5-D6D7-7890-C2C3-C4C5D6D77890")!),
            modelId: UUID(uuidString: "C2C3C4C5-D6D7-7890-C2C3-C4C5D6D77890")!,
            name: "Gemini 2.5 Flash",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "thinkingBudget": "12288", "tools": "[{ \"google_search\": {} }]"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // DeepSeek V3 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "D2D2D2D2-E5E6-7890-D1D2-D3D4E5E67890")!),
            modelId: UUID(uuidString: "D2D2D2D2-E5E6-7890-D1D2-D3D4E5E67890")!,
            name: "DeepSeek V3",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // DeepSeek R1 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "D2D2D2D2-E6E7-7890-D2D3-D4D5E6E77890")!),
            modelId: UUID(uuidString: "D2D2D2D2-E6E7-7890-D2D3-D4D5E6E77890")!,
            name: "DeepSeek R1",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // xAI Grok 3 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "E1E2E3E4-F5F6-7890-E1E2-E3E4F5F67890")!),
            modelId: UUID(uuidString: "E1E2E3E4-F5F6-7890-E1E2-E3E4F5F67890")!,
            name: "Grok 3",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "search_parameters": "{ \"mode\": \"auto\" }"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // xAI Grok 3 Mini instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "E2E3E4E5-F6F7-7890-E2E3-E4E5F6F77890")!),
            modelId: UUID(uuidString: "E2E3E4E5-F6F7-7890-E2E3-E4E5F6F77890")!,
            name: "Grok 3 Mini",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "reasoning_effort": "low", "search_parameters": "{ \"mode\": \"auto\" }"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Qwen 32B instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "F1F2F3F4-A5A6-7890-F1F2-F3F4A5A67890")!),
            modelId: UUID(uuidString: "F1F2F3F4-A5A6-7890-F1F2-F3F4A5A67890")!,
            name: "Qwen 32B",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Nvidia DeepSeek R1 instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A3A3A3A3-B4B4-7890-A3A3-A3A3B4B47890")!),
            modelId: UUID(uuidString: "A3A3A3A3-B4B4-7890-A3A3-A3A3B4B47890")!,
            name: "DeepSeek R1 0528",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // SiliconFlow DeepSeek R1 Qwen3-8B instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A4A4A4A4-B5B5-7890-A4A4-A4A4B5B57890")!),
            modelId: UUID(uuidString: "A4A4A4A4-B5B5-7890-A4A4-A4A4B5B57890")!,
            name: "DeepSeek R1 0528 Qwen3-8B",
            systemPrompt: "You are a helpful assistant.",
            defaultParameters: ["temperature": "0.7", "thinking_budget": "16384"],
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: false,
            metadata: nil
        ),

        // Custom Model instance
        LLMInstance(
            id: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C9C9C9C9-D9D9-7890-C9C9-C9C9D9D97890")!),
            modelId: UUID(uuidString: "C9C9C9C9-D9D9-7890-C9C9-C9C9D9D97890")!,
            name: "Custom Model 1 (instance)",
            customLogoData: nil,
            systemPrompt: "You are a specialized custom assistant.",
            defaultParameters: ["temperature": "0.6"],
            totalPromptTokensUsed: 200,
            totalCompletionTokensUsed: 100,
            createdAt: Date().addingTimeInterval(-86400 * 1), // 1 day ago
            lastUsedAt: Date().addingTimeInterval(-1800), // 30 minutes ago
            isFavorited: true,
            isUserModified: true,
            metadata: nil
        )
    ]
    
    private static var groups: [LLMInstanceGroup] = [
        LLMInstanceGroup(
            id: UUID(uuidString: "E1E2E3E4-F5F6-7890-E1E2-E3E4F5F67890")!,
            name: "My AI Team",
            isFavorited: true,
            customLogoData: nil,
            instanceIds: [
                BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!), // GPT-4.1
                BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!), // Claude Sonnet 4
                BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!), // Gemini 2.5 Pro
                BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "D2D2D2D2-E6E7-7890-D2D3-D4D5E6E77890")!)  // DeepSeek R1
            ],
            createdAt: Date().addingTimeInterval(-86400 * 2), // 2 days ago
            metadata: nil
        )
    ]
    
    // Error simulation flags
    private var shouldThrowError = false
    private var errorMessage = "Mock repository error"
    
    // MARK: - Initialization
    
    init(shouldThrowError: Bool = false) {
        self.shouldThrowError = shouldThrowError
        // No need to copy static data to instance variables anymore
    }

    // MARK: - Batch Operations Control
    
    func beginBatchUpdate() async {
        print("MockLLMInstanceRepository: beginBatchUpdate() called.")
        // For the mock, usually no specific state change is needed unless testing interactions with this flag.
    }
    
    func endBatchUpdateAndPublish() async throws {
        try await simulateAsyncOperation(returning: () ) // Use existing helper for error simulation
        print("MockLLMInstanceRepository: endBatchUpdateAndPublish() called, triggering data did change.")
        triggerDataDidChange() // Simulate the single publish after batch operation
    }
    
    // MARK: - Helper Methods
    
    private func simulateAsyncOperation<T>(returning value: T) async throws -> T {
        if shouldThrowError {
            throw ModelManagementError.persistenceError(NSError(domain: "MockRepository", code: 100, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
        }
        
        // Optional: add a tiny delay to simulate network/disk operations
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        return value
    }
    
    // MARK: - LLMProvider Operations
    
    func createProvider(_ provider: LLMProvider) async throws {
        try await simulateAsyncOperation(returning: ())
        MockLLMInstanceRepository.providers.append(provider)
        triggerDataDidChange()
    }
    
    func getProvider(byId id: UUID) async throws -> LLMProvider? {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.providers.first(where: { $0.id == id }))
    }
    
    func getAllProviders() async throws -> [LLMProvider] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.providers)
    }
    
    func getProviders(isUserCreated: Bool) async throws -> [LLMProvider] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.providers.filter { $0.isUserCreated == isUserCreated })
    }
    
    func updateProvider(_ provider: LLMProvider) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.providers.firstIndex(where: { $0.id == provider.id }) {
            MockLLMInstanceRepository.providers[index] = provider
            triggerDataDidChange()
        } else {
            throw ModelManagementError.providerNotFound
        }
    }
    
    func deleteProvider(byId id: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.providers.firstIndex(where: { $0.id == id }) {
            let provider = MockLLMInstanceRepository.providers[index]
            if !provider.isUserCreated {
                throw ModelManagementError.cannotDeleteBuiltInProvider
            }
            MockLLMInstanceRepository.providers.remove(at: index)
            triggerDataDidChange()
        }
    }
    
    // MARK: - LLMModel Operations
    
    func createModel(_ model: LLMModel) async throws {
        try await simulateAsyncOperation(returning: ())
        MockLLMInstanceRepository.models.append(model)
        triggerDataDidChange()
    }
    
    func getModel(byId id: UUID) async throws -> LLMModel? {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.models.first(where: { $0.id == id }))
    }

    func getAllModels() async throws -> [LLMModel] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.models)
    }
    
    func getAllModels(for providerId: UUID) async throws -> [LLMModel] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.models.filter { $0.providerId == providerId })
    }
    
    func findModel(identifier: String, providerId: UUID) async throws -> LLMModel? {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.models.first(where: { 
            $0.modelIdentifier == identifier && $0.providerId == providerId
        }))
    }
    
    func updateModel(_ model: LLMModel) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.models.firstIndex(where: { $0.id == model.id }) {
            MockLLMInstanceRepository.models[index] = model
            triggerDataDidChange()
        } else {
            throw ModelManagementError.modelNotFound
        }
    }
    
    func deleteModel(byId id: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        if MockLLMInstanceRepository.instances.contains(where: { $0.modelId == id }) {
            throw ModelManagementError.cannotDeleteModelUsedByInstances
        }
        if let index = MockLLMInstanceRepository.models.firstIndex(where: { $0.id == id }) {
            MockLLMInstanceRepository.models.remove(at: index)
            triggerDataDidChange()
        }
    }
    
    // MARK: - LLMInstance Operations
    
    func createInstance(_ instance: LLMInstance) async throws {
        try await simulateAsyncOperation(returning: ())
        MockLLMInstanceRepository.instances.append(instance)
        triggerDataDidChange()
    }
    
    func getInstance(byId id: UUID) async throws -> LLMInstance? {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.instances.first(where: { $0.id == id }))
    }
    
    func getAllInstances() async throws -> [LLMInstance] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.instances)
    }
    
    func getAllInstances(for modelId: UUID) async throws -> [LLMInstance] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.instances.filter { $0.modelId == modelId })
    }

    func updateInstance(_ instance: LLMInstance) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.instances.firstIndex(where: { $0.id == instance.id }) {
            MockLLMInstanceRepository.instances[index] = instance
            triggerDataDidChange()
        } else {
            throw ModelManagementError.instanceNotFound
        }
    }
    
    func deleteInstance(byId id: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.instances.firstIndex(where: { $0.id == id }) {
            MockLLMInstanceRepository.instances.remove(at: index)
            triggerDataDidChange()
        }
    }
    
    func updateInstanceTokenUsage(instanceId: UUID, promptTokens: Int, completionTokens: Int) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.instances.firstIndex(where: { $0.id == instanceId }) {
            var updatedInstance = MockLLMInstanceRepository.instances[index]
            updatedInstance.totalPromptTokensUsed += Int64(promptTokens)
            updatedInstance.totalCompletionTokensUsed += Int64(completionTokens)
            updatedInstance.lastUsedAt = Date()
            MockLLMInstanceRepository.instances[index] = updatedInstance
            triggerDataDidChange()
        } else {
            throw ModelManagementError.instanceNotFound
        }
    }
    
    // MARK: - LLMInstanceGroup Operations
    
    func createGroup(_ group: LLMInstanceGroup) async throws {
        try await simulateAsyncOperation(returning: ())
        MockLLMInstanceRepository.groups.append(group)
        triggerDataDidChange()
    }
    
    func getGroup(byId id: UUID) async throws -> LLMInstanceGroup? {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.groups.first(where: { $0.id == id }))
    }
    
    func getAllGroups() async throws -> [LLMInstanceGroup] {
        try await simulateAsyncOperation(returning: MockLLMInstanceRepository.groups)
    }
    
    func updateGroup(_ group: LLMInstanceGroup) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.groups.firstIndex(where: { $0.id == group.id }) {
            MockLLMInstanceRepository.groups[index] = group
            triggerDataDidChange()
        } else {
            throw ModelManagementError.groupNotFound
        }
    }
    
    func deleteGroup(byId id: UUID) async throws {
        try await simulateAsyncOperation(returning: ())
        if let index = MockLLMInstanceRepository.groups.firstIndex(where: { $0.id == id }) {
            MockLLMInstanceRepository.groups.remove(at: index)
            triggerDataDidChange()
        }
    }

    func getInstancesWithRelatedEntities(instanceIds: [UUID]) async throws -> [LLMInstanceContext] {
        try await simulateAsyncOperation(returning: {
            guard !instanceIds.isEmpty else {
                return []
            }
            
            var instanceContexts: [LLMInstanceContext] = []
            
            for instanceId in instanceIds {
                guard let instance = MockLLMInstanceRepository.instances.first(where: { $0.id == instanceId }) else {
                    print("Warning: Instance \(instanceId) not found in mock data")
                    continue
                }
                
                guard let model = MockLLMInstanceRepository.models.first(where: { $0.id == instance.modelId }) else {
                    print("Warning: Model \(instance.modelId) not found for instance \(instanceId)")
                    continue
                }
                
                guard let provider = MockLLMInstanceRepository.providers.first(where: { $0.id == model.providerId }) else {
                    print("Warning: Provider \(model.providerId) not found for model \(model.id)")
                    continue
                }
                
                let instanceContext = LLMInstanceContext(
                    instance: instance,
                    model: model,
                    provider: provider
                )
                instanceContexts.append(instanceContext)
            }
            
            return instanceContexts
        }())
    }
}
#endif
