#if DEBUG
import Foundation
import Combine

/// Mock implementation of UserSettingsRepository for testing and previews.
final class MockUserSettingsRepository: UserSettingsRepository {
    
    // MARK: - Mock Data Storage
    
    private var users: [User] = []
    private var currentUserId: UUID?
    private let dataChangeSubject = PassthroughSubject<Void, Never>()
    
    // MARK: - Initialization
    
    init() {
        loadMockData()
    }
    
    // MARK: - User CRUD Operations
    
    func createUser(_ user: User) async throws -> User {
        // Check if user with this ID already exists
        if users.contains(where: { $0.id == user.id }) {
            throw NSError(domain: "MockUserRepository", code: 409, userInfo: [NSLocalizedDescriptionKey: "User with ID \(user.id) already exists"])
        }
        
        users.append(user)
        publishDataChange()
        return user
    }
    
    func getUser(byId id: UUID) async throws -> User? {
        return users.first { $0.id == id }
    }
    
    func getAllUsers() async throws -> [User] {
        return users.sorted { $0.registrationDate < $1.registrationDate }
    }
    
    func updateUser(_ user: User) async throws -> User {
        guard let index = users.firstIndex(where: { $0.id == user.id }) else {
            throw NSError(domain: "MockUserRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "User with ID \(user.id) not found"])
        }
        
        users[index] = user
        publishDataChange()
        return user
    }
    
    func deleteUser(byId id: UUID) async throws {
        guard let index = users.firstIndex(where: { $0.id == id }) else {
            throw NSError(domain: "MockUserRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "User with ID \(id) not found"])
        }
        
        // Clear current user if this is the current user
        if currentUserId == id {
            currentUserId = nil
        }
        
        users.remove(at: index)
        publishDataChange()
    }
    
    func saveUser(_ user: User) async throws {
        if let index = users.firstIndex(where: { $0.id == user.id }) {
            // Update existing user
            users[index] = user
        } else {
            // Create new user
            users.append(user)
        }
        publishDataChange()
    }
    
    // MARK: - Current User Management
    
    func getCurrentUser() async throws -> User? {
        guard let currentUserId = currentUserId else { return nil }
        return try await getUser(byId: currentUserId)
    }
    
    func setCurrentUser(userId: UUID) async throws {
        // Verify the user exists
        guard let _ = try await getUser(byId: userId) else {
            throw NSError(domain: "MockUserRepository", code: 404, userInfo: [NSLocalizedDescriptionKey: "User with ID \(userId) not found"])
        }
        
        currentUserId = userId
        publishDataChange()
    }
    
    // MARK: - Subscription Management (Not Implemented)
    
    func getSubscriptionStatus(for userId: UUID) async throws -> SubscriptionStatus? {
        throw NSError(domain: "MockUserRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription status not implemented in mock."])
    }
    
    func saveSubscriptionStatus(_ status: SubscriptionStatus) async throws {
        throw NSError(domain: "MockUserRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription status not implemented in mock."])
    }
    
    func getAllSubscriptionPlans() async throws -> [SubscriptionPlan] {
        throw NSError(domain: "MockUserRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription plans not implemented in mock."])
    }
    
    func getSubscriptionPlan(by productId: String) async throws -> SubscriptionPlan? {
        throw NSError(domain: "MockUserRepository", code: 501, userInfo: [NSLocalizedDescriptionKey: "Subscription plans not implemented in mock."])
    }
    
    // MARK: - Mock Data Loading
    
    private func loadMockData() {
        let mockUser1 = User(
            id: UUID(uuidString: "11111111-1111-1111-1111-111111111111")!,
            nickname: "John Doe",
            avatarIdentifier: "person.circle",
            registrationDate: Date().addingTimeInterval(-86400 * 30), // 30 days ago
            lastSeenAt: Date().addingTimeInterval(-3600), // 1 hour ago
            appSettings: AppSettings(shouldRestoreLastChat: true),
            defaultChatSettingsId: nil,
            systemUtilityLLMInstanceId: nil,
            syncStatus: .synced,
            lastSyncTimestamp: Date(),
            memoryFileBookmarkData: nil,
            metadata: ["source": "mock"]
        )
        
        let mockUser2 = User(
            id: UUID(uuidString: "*************-2222-2222-************")!,
            nickname: "Jane Smith",
            avatarIdentifier: "person.circle.fill",
            registrationDate: Date().addingTimeInterval(-86400 * 15), // 15 days ago
            lastSeenAt: Date().addingTimeInterval(-1800), // 30 minutes ago
            appSettings: AppSettings(shouldRestoreLastChat: false),
            defaultChatSettingsId: nil,
            systemUtilityLLMInstanceId: nil,
            syncStatus: .syncing,
            lastSyncTimestamp: Date().addingTimeInterval(-300), // 5 minutes ago
            memoryFileBookmarkData: nil,
            metadata: ["source": "mock", "role": "admin"]
        )
        
        users = [mockUser1, mockUser2]
        currentUserId = mockUser1.id // Set first user as current
    }
    
    // MARK: - Helper Methods
    
    private func publishDataChange() {
        DispatchQueue.main.async { [weak self] in
            self?.dataChangeSubject.send()
        }
    }
    
    // MARK: - Test Helpers
    
    /// Resets all data to initial mock state (useful for testing)
    func resetToMockData() {
        users.removeAll()
        currentUserId = nil
        loadMockData()
        publishDataChange()
    }
    
    /// Gets current data counts for testing
    var userCount: Int { users.count }
    var hasCurrentUser: Bool { currentUserId != nil }
}

#endif
