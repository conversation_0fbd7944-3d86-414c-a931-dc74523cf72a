#if DEBUG
import Foundation
import Combine

// MARK: - Mock Data for Previews

/// Provides mock data for chat previews.
class MockChatPreviewData {

    // System default user ID
    public static let systemDefaultUserId = UUID(uuidString: "C142A374-0001-0001-0001-000000000001")!

    // Assuming these are the default instance IDs from MockLLMInstanceRepository.
    public static let claudeInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!) // Claude 3.7 Sonnet
    public static let gpt4InstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!) // GPT-4.1
    public static let customInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!) // Custom Model 1 (instance)
    public static let gemini25FlashInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C2C3C4C5-D6D7-7890-C2C3-C4C5D6D77890")!) // Gemini 2.5 Flash (instance)
    public static let ORDSV30324FreeInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A9A9A9A9-B9B9-7890-A9A9-A9A9B9B97890")!) // OpenRouter DeepSeek V3 0324 Free (instance)
    public static let ORDSR10528FreeInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A9A9A9A9-B8B8-7890-A9A9-A9A9B8B87890")!) // OpenRouter DeepSeek R1 0528 Free (instance)
    public static let NVDeepSeekR10528FreeInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A3A3A3A3-B4B4-7890-A3A3-A3A3B4B47890")!) // Nvidia DeepSeek R1 0528 Free (instance)
    public static let SFDeepSeekR10528Qwen38BInstanceId: UUID = BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A4A4A4A4-B5B5-7890-A4A4-A4A4B5B57890")!) // SiliconFlow DeepSeek R1 0528 Qwen3-8B (instance)

    // Mock built-in prompt segments (same as BuiltinChatInitializer)
    private static let mockPromptSegments: [SavedPromptSegment] = [
        SavedPromptSegment(
            id: UUID(uuidString: "F1F1F1F1-F1F1-F1F1-F1F1-F1F1F1F1F1F1")!,
            content: "Summarize this in 3 key points",
            usageCount: 50,
            createdAt: Date().addingTimeInterval(-30 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-1 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F2F2F2F2-F2F2-F2F2-F2F2-F2F2F2F2F2F2")!,
            content: "Explain this like I'm 5 years old",
            usageCount: 45,
            createdAt: Date().addingTimeInterval(-25 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-2 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F3F3F3F3-F3F3-F3F3-F3F3-F3F3F3F3F3F3")!,
            content: "What are the pros and cons?",
            usageCount: 40,
            createdAt: Date().addingTimeInterval(-20 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-3 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F4F4F4F4-F4F4-F4F4-F4F4-F4F4F4F4F4F4")!,
            content: "Provide a step-by-step guide",
            usageCount: 35,
            createdAt: Date().addingTimeInterval(-18 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-4 * 24 * 3600)
        ),
        SavedPromptSegment(
            id: UUID(uuidString: "F5F5F5F5-F5F5-F5F5-F5F5-F5F5F5F5F5F5")!,
            content: "Find potential issues or problems",
            usageCount: 30,
            createdAt: Date().addingTimeInterval(-15 * 24 * 3600),
            lastUsedAt: Date().addingTimeInterval(-5 * 24 * 3600)
        )
    ]

    // Default system chat session setting
    public static let systemDefaultChatSessionSetting = ChatSessionSetting(
        id: UUID(uuidString: "C142A374-5536-7778-C142-A37455367778")!,
        name: "System Default",
        isSystemDefault: true,
        createdAt: Date(),
        lastModifiedAt: Date(),
        llmParameterOverrides: nil,
        defaultContextServerIds: nil,
        shouldExpandThinking: true,
        uiThemeSettings: UIThemeSettings(
            themeIdentifier: "system",
            customColors: nil,
            backgroundImage: nil,
            fontSize: .medium
        ),
        messageActionSettings: MessageActionSettings(
            actionPanelActions: [SystemMessageActions.camera.id, SystemMessageActions.photo.id, SystemMessageActions.file.id],
            userMessageActions: [SystemMessageActions.copy.id, SystemMessageActions.edit.id],
            assistantMessageCardActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id],
            assistantMessageMenuActions: [SystemMessageActions.copy.id, SystemMessageActions.regenerate.id, SystemMessageActions.like.id, SystemMessageActions.dislike.id, SystemMessageActions.select.id]
        ),
        savedPromptSegments: mockPromptSegments,
        auxiliaryLLMInstanceId: nil,
        shouldAutoGenerateTitle: false,
        contextMessageCount: Int64.max,
        metadata: nil
    )

    // Example session 1: Basic chat introduction
    public static let exampleSession1 = ChatSession(
        id: UUID(uuidString: "C142A374-1001-1001-1001-000000001001")!,
        title: "Welcome to LavaChat",
        createdAt: Date().addingTimeInterval(-2000),
        lastModifiedAt: Date().addingTimeInterval(-1650),
        activeMessageId: UUID(uuidString: "C142A374-1014-1014-1014-000000001014")!,
        activeLLMInstanceIds: [claudeInstanceId],
        usedLLMInstanceIds: [claudeInstanceId],
        activeContextServerIds: [],
        settingsId: systemDefaultChatSessionSetting.id,
        userId: systemDefaultUserId,
        instanceSettings: [
            claudeInstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false)
        ]
    )

    // Example session 2: Demonstrating complex interactions
    public static let exampleSession2 = ChatSession(
        id: UUID(uuidString: "C142A374-**************-000000002001")!,
        title: "Multi Instance Interaction",
        createdAt: Date().addingTimeInterval(-3 * 24 * 3600 - 2 * 3600),
        lastModifiedAt: Date().addingTimeInterval(-2 * 3600),
        activeMessageId: UUID(uuidString: "C142A374-**************-000000002023")!,
        activeLLMInstanceIds: [claudeInstanceId, gpt4InstanceId, customInstanceId],
        usedLLMInstanceIds: [claudeInstanceId, gpt4InstanceId, customInstanceId],
        activeContextServerIds: [],
        settingsId: systemDefaultChatSessionSetting.id,
        userId: systemDefaultUserId,
        instanceSettings: [
            claudeInstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false),
            gpt4InstanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false),
            customInstanceId: SessionInstanceSetting(thinkingEnabled: false, networkEnabled: false)
        ]
    )

    // Example session 3: File interaction placeholder
    public static let exampleSession3 = ChatSession(
        id: UUID(uuidString: "C142A374-**************-000000003001")!,
        title: "FileSession Demo",
        createdAt: Date().addingTimeInterval(-3 * 24 * 3600 - 2 * 3600),
        lastModifiedAt: Date().addingTimeInterval(-1 * 24 * 3600 - 2 * 3600),
        activeMessageId: UUID(uuidString: "C142A374-**************-000000003011")!,
        activeLLMInstanceIds: [claudeInstanceId],
        usedLLMInstanceIds: [claudeInstanceId],
        activeContextServerIds: [],
        settingsId: systemDefaultChatSessionSetting.id,
        userId: systemDefaultUserId,
        instanceSettings: [
            claudeInstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false)
        ]
    )
    
    // Example session 4: Basic chat introduction
    public static let exampleSession4 = ChatSession(
        id: UUID(uuidString: "C142A374-**************-000000004001")!,
        title: "Test",
        createdAt: Date().addingTimeInterval(-5 * 24 * 3600 - 2 * 3600),
        lastModifiedAt: Date().addingTimeInterval(-1650),
        activeMessageId: nil,
        activeLLMInstanceIds: [claudeInstanceId],
        usedLLMInstanceIds: [claudeInstanceId],
        activeContextServerIds: [],
        settingsId: systemDefaultChatSessionSetting.id,
        userId: systemDefaultUserId,
        instanceSettings: [
            claudeInstanceId: SessionInstanceSetting(thinkingEnabled: true, networkEnabled: false)
        ]
    )

    // Get messages for example session 1
    public static let messagesForExampleSession1: [Message] = {        
        // Root user message (depth 0)
        let message1 = Message(
            id: UUID(uuidString: "C142A374-1011-1011-1011-000000001011")!,
            sessionId: exampleSession1.id,
            parentId: nil,
            timestamp: Date(timeIntervalSinceNow: -60),
            role: .user,
            content: [.text("Hello, please introduce yourself.\n- List your features in a table.\n- Include a code example.")],
            depth: 0,
            userId: systemDefaultUserId,
            status: .received
        )
        
        // Assistant reply (depth 1)
        let message2 = Message(
            id: UUID(uuidString: "C142A374-1012-1012-1012-000000001012")!,
            sessionId: exampleSession1.id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: -30),
            role: .assistant,
            content: [.text("Hello! I am LavaChat, your AI hub. I can help you interact with multiple large language models, providing conversation, file interaction, and management functions.\n\nMy main features include:\n\n| Feature | Description |\n|---------|-------------|\n| Unified Management | Manage multiple AI models; @mention different instances for different answers |\n| Tree Conversations | Edit, regenerate, merge and backtrack conversations easily |\n| File Interaction | Reference local files in your conversations |\n| File Access | Convenient file management and access |\n\nHow can I help you today?\n\n```swift\n// Example code for using LavaChat\nfunc askQuestion(question: String) {\n    let lavaChat = LavaChat()\n    lavaChat.send(question: question) { response in\n        print(\"AI Response: \\(response)\")\n    }\n}\n```")],
            depth: 1,
            llmInstanceId: claudeInstanceId,
            promptTokens: 64,
            completionTokens: 256,
            status: .received,
            isReplied: true
        )
        
        // Second user message (depth 2)
        let message3 = Message(
            id: UUID(uuidString: "C142A374-1013-1013-1013-000000001013")!,
            sessionId: exampleSession1.id,
            parentId: message2.id,
            timestamp: Date(timeIntervalSinceNow: -29),
            role: .user,
            content: [.text("How do I use the @mention feature?")],
            depth: 2,
            userId: systemDefaultUserId,
            status: .received
        )
        
        // Second assistant reply (depth 3)
        let message4 = Message(
            id: UUID(uuidString: "C142A374-1014-1014-1014-000000001014")!,
            sessionId: exampleSession1.id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: -28),
            role: .assistant,
            content: [.text("Using the @mention feature is very simple:\n\n1. **Mention AI Model Instance or Group**:\n   - Type \"@\" in the input box, then select the \"@mention instance/group\" option\n   - Enter the instance name or group name (e.g., \"@Claude\" or \"@ThinkingGroup\")\n   - After sending the message, the specified instance or all instances within the group will receive your message and respond\n\n2. **Mention File or Folder**:\n   - Type \"@\" in the input box, then select the \"@mention file/folder\" option\n   - The system will open a file picker, allowing you to select one or more files/folders\n   - After selection, the file content will be embedded into your message, and the AI can access this content\n\nThis way, you can easily direct specific AI models to answer your questions or have them analyze the content of files you select. Give it a try!")],
            depth: 3,
            llmInstanceId: claudeInstanceId,
            promptTokens: 128,
            completionTokens: 352,
            status: .received
        )
        
        return [message1, message2, message3, message4]
    }()

    // Get messages for example session 2
    public static let messagesForExampleSession2: [Message] = {
        // First round user message (root - depth 0)
        let message1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002011")!,
            sessionId: exampleSession2.id,
            parentId: nil,
            timestamp: Date(),
            role: .user,
            content: [.text("What is LavaChat?")],
            depth: 0,
            userId: systemDefaultUserId,
            status: .received
        )
        
        // First round assistant replies (three parallel - depth 1)
        let message2_claude = Message(
            id: UUID(uuidString: "C142A374-**************-000000002012")!,
            sessionId: exampleSession2.id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 1),
            role: .assistant,
            content: [.text("LavaChat is an AI hub designed to centralize and manage interactions with multiple large language models (LLMs). It provides a unified interface where users can communicate with different AI models, compare their responses, and leverage their unique capabilities. The application focuses on providing advanced conversation management, file interactions, and a streamlined user experience for AI-assisted tasks.")],
            depth: 1,
            llmInstanceId: claudeInstanceId,
            promptTokens: 32,
            completionTokens: 128,
            status: .received,
            userFeedback: .liked
        )
        
        let message2_gpt4 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002013")!,
            sessionId: exampleSession2.id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 2),
            role: .assistant,
            content: [.text("LavaChat is an iOS application that serves as a centralized hub for interacting with multiple AI language models. It allows users to manage different AI instances, conduct structured conversations with tree-based history, and seamlessly work with local files. The platform supports both subscription-based models and custom API key configurations, making it versatile for researchers, developers, content creators, and anyone who needs sophisticated AI assistance.")],
            depth: 1,
            llmInstanceId: gpt4InstanceId,
            promptTokens: 32,
            completionTokens: 148,
            status: .received,
            userFeedback: .disliked
        )
        
        let message2_gemini = Message(
            id: UUID(uuidString: "C142A374-**************-000000002014")!,
            sessionId: exampleSession2.id,
            parentId: message1.id,
            timestamp: Date(timeIntervalSinceNow: 3),
            role: .assistant,
            content: [.text("LavaChat is a multi-model AI communication platform initially launched for iOS with cross-platform expansion plans. It integrates various Large Language Models (LLMs) into a single interface, allowing users to interact with different AI systems simultaneously. The application features advanced conversation management with branching dialogues, AI-assisted file editing capabilities, and local file knowledge base integration. Users can either subscribe to use built-in API keys or connect their own API keys to access various AI models.")],
            depth: 1,
            llmInstanceId: customInstanceId,
            promptTokens: 32,
            completionTokens: 168,
            status: .received
        )
        
        // Second round user message (edited version - depth 0)
        let message3 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002015")!,
            sessionId: exampleSession2.id,
            parentId: nil,
            timestamp: Date(timeIntervalSinceNow: 60),
            role: .user,
            content: [.text("What is LavaChat and what are its features?")],
            depth: 0,
            userId: systemDefaultUserId,
            status: .received,
            metadata: [Message.MetadataKeys.isEditOf: "C142A374-**************-000000002011"]
        )
        
        // Second round assistant replies (three parallel - depth 1)
        let message4_claude = Message(
            id: UUID(uuidString: "C142A374-**************-000000002016")!,
            sessionId: exampleSession2.id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 61),
            role: .assistant,
            content: [.text("LavaChat is an AI hub platform that centralizes interactions with multiple large language models (LLMs). \n\nKey features include:\n\n1. **Unified Model Management**: Access multiple AI models through a single interface\n2. **Tree-Based Conversations**: Non-linear conversation structure with branching, editing, and message fusion\n3. **Multi-Model Responses**: Compare different AI models' answers to the same question simultaneously\n4. **@Mention System**: Tag specific models or file content in your messages\n5. **File Intelligence**: Import, edit and interact with documents while conversing with AI\n6. **Local Knowledge Base**: Reference your own files and folders during conversations\n7. **Flexible API Access**: Use subscription-provided models or connect your own API keys\n8. **Cross-Platform Vision**: Starting with iOS and expanding to other platforms")],
            depth: 1,
            llmInstanceId: claudeInstanceId,
            promptTokens: 48,
            completionTokens: 192,
            status: .received,
            userFeedback: .liked,
            isReplied: true
        )
        
        let message4_gpt4 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002017")!,
            sessionId: exampleSession2.id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 62),
            role: .assistant,
            content: [.text("LavaChat is an AI communication hub that brings together multiple language models in one application. \n\nIts key features include:\n\n1. **Multi-Model Integration**: Access to various LLMs including OpenAI, Anthropic, Google, and others\n2. **Advanced Conversation Structure**: Tree-based history with branching, editing, and merging capabilities\n3. **File Interaction**: AI-assisted document editing and analysis directly within conversations\n4. **API Key Management**: Secure storage and management of your own API keys\n5. **Mention System**: Tag specific AI instances or reference files using @mentions\n6. **Message Operations**: Like/dislike, favorite, regenerate, and other feedback mechanisms\n7. **Theme Customization**: Personalize the UI appearance\n8. **Custom AI Actions**: Create specialized operations for selected messages\n9. **Local Knowledge Base**: Use local files as reference material for AI")],
            depth: 1,
            llmInstanceId: gpt4InstanceId,
            promptTokens: 48,
            completionTokens: 212,
            status: .received,
            userFeedback: .liked
        )
        
        let message4_gemini = Message(
            id: UUID(uuidString: "C142A374-**************-000000002018")!,
            sessionId: exampleSession2.id,
            parentId: message3.id,
            timestamp: Date(timeIntervalSinceNow: 63),
            role: .assistant,
            content: [.text("LavaChat is an AI integration platform primarily for iOS (with plans for cross-platform expansion). It combines multiple language models into a unified interface.\n\nFeatures:\n• Multiple AI model access through one interface\n• Tree-structured conversations with non-linear paths\n• File editing capabilities with AI assistance\n• @mention system for AI instances or files\n• Subscription options or use your own API keys\n• Local document referencing within chats\n• Theme and UI customization\n• Message feedback system\n• Custom AI action buttons\n• Conversation export and sharing\n\nThese features make it useful for developers, researchers, content creators, students, and anyone needing sophisticated AI assistance.")],
            depth: 1,
            llmInstanceId: customInstanceId,
            promptTokens: 48,
            completionTokens: 156,
            status: .received,
            userFeedback: .disliked
        )
        
        // Third round user message (depth 2)
        let message5 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002019")!,
            sessionId: exampleSession2.id,
            parentId: message4_claude.id, // Parent is the selected assistant message
            timestamp: Date(timeIntervalSinceNow: 120),
            role: .user,
            content: [.text("Explain the multi-assistant feature in detail.")],
            depth: 2,
            userId: systemDefaultUserId,
            status: .received
        )
        
        // Third round assistant replies (original - depth 3)
        let message6_claude = Message(
            id: UUID(uuidString: "C142A374-**************-000000002020")!,
            sessionId: exampleSession2.id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 121),
            role: .assistant,
            content: [.text("LavaChat's multi-assistant feature allows you to interact with several AI models simultaneously within a single conversation. Here's a detailed explanation:\n\n**Core Functionality:**\n- When you ask a question, you can receive answers from multiple AI instances in parallel\n- Each model applies its unique capabilities and perspective to your query\n- Responses appear as separate message bubbles, clearly labeled with the model name\n\n**How to Use:**\n1. **Direct Method**: Type @ followed by an instance name or group name (e.g., \"@Claude\" or \"@Research Group\")\n2. **Selection Method**: Use the model selection toolbar to pick one or multiple models\n\n**Instance Groups:**\n- Create custom groups of AI models for specific tasks (e.g., \"Creative Writing\", \"Code Review\")\n- When mentioning a group, all instances in that group respond\n\n**Message Operations:**\n- Like/dislike individual responses to provide feedback\n- Mark specific responses as \"selected\" to continue the conversation based on that reply\n- Merge insights from multiple responses into a unified answer\n\n**Benefits:**\n- Compare different approaches to the same problem\n- Get specialized responses for different aspects of complex questions\n- Identify consensus and divergence in AI responses\n- Select the most helpful response to continue your conversation\n\nThis feature is particularly valuable for research, decision-making, creative brainstorming, and learning about AI differences.")],
            depth: 3,
            llmInstanceId: claudeInstanceId,
            promptTokens: 64,
            completionTokens: 256,
            status: .received
        )
        
        let message6_gpt4 = Message(
            id: UUID(uuidString: "C142A374-**************-000000002021")!,
            sessionId: exampleSession2.id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 122),
            role: .assistant,
            content: [.text("# LavaChat's Multi-Assistant Feature\n\nThe multi-assistant feature in LavaChat enables users to interact with multiple AI models simultaneously, creating a rich, diverse conversation environment. Here's a comprehensive breakdown:\n\n## Core Mechanics\n\n1. **Parallel Querying**: Send the same prompt to multiple AI instances at once\n2. **Selective Interaction**: Choose which models to include in each exchange\n3. **Comparative Analysis**: View different perspectives side-by-side\n\n## Implementation Methods\n\n### Method 1: @ Mention System\n- Type \"@\" in the input box\n- Select \"@mention instance/group\"\n- Enter model name(s) or group name\n- Your message is sent to all mentioned models/groups\n\n### Method 2: Instance Selection UI\n- Use the model selection interface at the bottom of the chat\n- Toggle multiple models on/off\n- Send your message to all selected models\n\n## Advanced Features\n\n### Response Management\n- **Feedback System**: Like/dislike individual responses\n- **Selection Mechanism**: Mark a specific response as the chosen path for continuing the conversation\n- **Fusion Capability**: Combine insights from multiple AI responses into a consolidated answer\n\n### Instance Grouping\n- Create logical groupings of models (e.g., \"Creative\", \"Analytical\", \"Technical\")\n- Assign models to multiple groups based on capabilities\n- Mention groups to engage all contained models simultaneously\n\n## Practical Applications\n\n1. **Research**: Compare methodologies and perspectives across models\n2. **Decision-making**: Gather diverse viewpoints on complex problems\n3. **Learning**: Understand how different AI architectures approach the same query\n4. **Content Creation**: Generate varied creative inputs\n5. **Technical Problem-solving**: Get multiple solution approaches\n\nThis feature fundamentally transforms the AI interaction paradigm from a one-to-one conversation to a dynamic, multi-faceted exchange.")],
            depth: 3,
            llmInstanceId: gpt4InstanceId,
            promptTokens: 64,
            completionTokens: 298,
            status: .received
        )
        
        let message6_gemini = Message(
            id: UUID(uuidString: "C142A374-**************-000000002022")!,
            sessionId: exampleSession2.id,
            parentId: message5.id,
            timestamp: Date(timeIntervalSinceNow: 123),
            role: .assistant,
            content: [.text("# Multi-Assistant Feature in LavaChat\n\nThe multi-assistant feature is one of LavaChat's core capabilities that sets it apart from single-model chat applications. Here's a detailed explanation:\n\n## What It Is\nThe multi-assistant feature allows you to interact with multiple AI models simultaneously within a single chat interface. Instead of switching between different AI applications or conversations, you can send one message and receive responses from several AI models at once.\n\n## How It Works\n\n### Message Routing\n- When you send a message, LavaChat can route it to multiple AI instances based on your selection\n- Each AI model processes your query independently\n- Responses appear as separate message bubbles in the conversation, clearly labeled with the source model\n\n### Interaction Methods\n\n1. **@mention System**\n   - Type \"@\" in the message input\n   - Choose between \"@mention instance/group\" \n   - Type the name of the model instance (e.g., \"@Claude\") or group (e.g., \"@CreativeTeam\")\n   - Your message is sent to all specified models/groups\n\n2. **Model Selection Interface**\n   - Use the model selection toolbar in the chat interface\n   - Toggle which models should receive your message\n   - Send to all selected models with one click\n\n### Response Management\n- **Feedback**: Provide thumbs up/down on individual responses\n- **Selection**: Click to mark a specific response as the one you want to build upon\n- **Branching**: Continue the conversation from any model's response\n- **Regeneration**: Ask any specific model to try again with a different response\n- **Merging**: Combine insights from multiple responses (in advanced usage)\n\n## Use Cases\n\n- **Comparison**: Directly compare how different AI models approach the same question\n- **Specialization**: Leverage each model's unique strengths for different aspects of a problem\n- **Verification**: Check for consensus or disagreement across models\n- **Learning**: Understand the differences in AI capabilities and approaches\n\nThe multi-assistant feature essentially transforms LavaChat from a simple chat application into a collaborative AI workspace where multiple artificial intelligence systems can contribute to solving your problems or answering your questions.")],
            depth: 3,
            llmInstanceId: customInstanceId,
            promptTokens: 64,
            completionTokens: 322,
            status: .received
        )
        
        // Third round assistant reply (regeneration - depth 3)
        let message7_claude_regen = Message(
            id: UUID(uuidString: "C142A374-**************-000000002023")!,
            sessionId: exampleSession2.id,
            parentId: message5.id, // Same parent as the original
            timestamp: Date(timeIntervalSinceNow: 180),
            role: .assistant,
            content: [.text("# LavaChat's Multi-Assistant Feature\n\nThe multi-assistant feature is a cornerstone capability of LavaChat that fundamentally changes how you interact with AI systems. Unlike traditional single-AI applications, LavaChat enables simultaneous interaction with multiple AI models in a unified conversation thread.\n\n## Key Components\n\n### 1. Parallel Querying\nWhen you ask a question in LavaChat, you can receive responses from multiple AI models simultaneously. This creates a panel of AI advisors, each bringing their unique training, capabilities, and perspectives.\n\n### 2. @Mention System\nLavaChat implements an intuitive @mention system:\n- Type @ in your message\n- Choose \"@mention instance/group\"\n- Specify model names (\"@Claude\") or custom groups (\"@Research\")\n- Your query routes to all mentioned entities\n\n### 3. Response Interface\n- Each AI response appears in its own message bubble\n- Clear labeling shows which model generated each response\n- Visual differentiation helps track multiple parallel conversations\n\n### 4. Response Management\n- **Feedback mechanisms**: Like/dislike buttons for each response\n- **Branch selection**: Mark specific responses to continue that conversation path\n- **Regeneration**: Request new responses from specific models\n- **Response fusion**: Combine insights from multiple models (premium feature)\n\n### 5. Instance Grouping\n- Create logical collections of models for specific purposes\n- Example groups: \"Creative Writing,\" \"Technical Analysis,\" \"Research\"\n- @mention a group to engage all contained models at once\n\n## Benefits\n\n1. **Comparative Intelligence**: Directly observe how different AI architectures approach the same problem\n2. **Confidence through Consensus**: When multiple models agree, you gain higher confidence in the information\n3. **Divergent Thinking**: When models disagree, you gain exposure to multiple perspectives\n4. **Specialized Applications**: Use different models for their specific strengths\n5. **Educational Value**: Learn about AI differences and capabilities firsthand\n\nThe multi-assistant feature essentially transforms your AI interactions from one-dimensional conversations into rich, multi-perspective exchanges that more closely resemble consulting a diverse panel of experts.")],
            depth: 3,
            llmInstanceId: claudeInstanceId,
            promptTokens: 64,
            completionTokens: 312,
            status: .received,
            metadata: [Message.MetadataKeys.isRegenerationOf: "C142A374-**************-000000002020"]
        )
        
        return [
            message1, 
            message2_claude, message2_gpt4, message2_gemini, 
            message3, 
            message4_claude, message4_gpt4, message4_gemini,
            message5, 
            message6_claude, message6_gpt4, message6_gemini,
            message7_claude_regen
        ]
    }()

    // Get messages for example session 3
    public static let messagesForExampleSession3: [Message] = {        
        // System message explaining the file session (depth 0)
        let message1 = Message(
            id: UUID(uuidString: "C142A374-**************-000000003011")!,
            sessionId: exampleSession3.id,
            parentId: nil,
            timestamp: Date(),
            role: .system,
            content: [.text("Welcome to LavaChat! I'm your AI hub for managing and interacting with multiple LLMs. Try asking me about the content of 'Project_Report.md' or other files in your knowledge base by using the @mention feature to reference files.")],
            depth: 0,
            llmInstanceId: claudeInstanceId,
            status: .received
        )
        
        return [message1]
    }()

    // Get all built-in example sessions
    public static func getAllExampleSessions() -> [ChatSession] {
        return [exampleSession1, exampleSession2, exampleSession3, exampleSession4]
    }
    
    // Get all messages for a given session ID
    public static func getMessagesForSession(sessionId: UUID) -> [Message] {
        if sessionId == exampleSession1.id {
            return messagesForExampleSession1
        } else if sessionId == exampleSession2.id {
            return messagesForExampleSession2
        } else if sessionId == exampleSession3.id {
            return messagesForExampleSession3
        }
        return []
    }
}
#endif
