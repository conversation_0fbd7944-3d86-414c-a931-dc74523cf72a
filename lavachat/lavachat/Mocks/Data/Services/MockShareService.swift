import Foundation
import UIKit

/// Mock implementation of ShareServiceProtocol for testing and previews
final class MockShareService: ShareServiceProtocol, @unchecked Sendable {
    
    // MARK: - Mock Configuration
    
    var shouldSucceed: Bool = true
    var simulatedDelay: TimeInterval = 0.1
    var mockFileURL: URL = URL(fileURLWithPath: "/tmp/mock_share.lavachat")
    var mockICloudURL: URL = URL(string: "https://icloud.com/mock-share")!
    var mockQRCodeData: Data = Data([0x89, 0x50, 0x4E, 0x47]) // Mock PNG header
    
    // MARK: - State Tracking
    
    private var activeOperations: [UUID: MockShareOperation] = [:]
    private let operationQueue = DispatchQueue(label: "com.lavachat.mock.share", qos: .userInitiated)
    
    // MARK: - ShareServiceProtocol Implementation
    
    func shareItem<T: ShareableItem>(
        _ item: T,
        configuration: ShareConfiguration
    ) async -> ShareResult {
        let operationId = UUID()
        let operation = MockShareOperation(id: operationId, item: item, configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        // Simulate processing delay
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            return .failure(.unknownError("Mock share service configured to fail"))
        }
        
        let successInfo: ShareSuccessInfo
        
        switch configuration.format {
        case .file:
            successInfo = ShareSuccessInfo(
                format: .file,
                contentType: item.shareContentType,
                fileURL: mockFileURL
            )
        case .icloud:
            successInfo = ShareSuccessInfo(
                format: .icloud,
                contentType: item.shareContentType,
                icloudURL: mockICloudURL
            )
        case .qrCode:
            successInfo = ShareSuccessInfo(
                format: .qrCode,
                contentType: item.shareContentType,
                icloudURL: mockICloudURL,
                qrCodeImage: mockQRCodeData
            )
        }
        
        return .success(successInfo)
    }
    
    func shareItems<T: ShareableItem>(
        _ items: [T],
        configuration: ShareConfiguration
    ) async -> ShareResult {
        // For mock, just use the first item
        guard let firstItem = items.first else {
            return .failure(.invalidContent)
        }
        return await shareItem(firstItem, configuration: configuration)
    }
    
    func shareAsFile<T: ShareableItem>(
        _ item: T,
        fileName: String?
    ) async throws -> URL {
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            throw ShareError.fileCreationFailed("Mock service configured to fail")
        }
        
        return mockFileURL
    }
    
    func shareViaICloud<T: ShareableItem>(
        _ item: T,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            throw ShareError.icloudShareFailed("Mock service configured to fail")
        }
        
        return mockICloudURL
    }
    
    func generateQRCode<T: ShareableItem>(
        for item: T,
        size: CGSize
    ) async throws -> Data {
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            throw ShareError.qrCodeGenerationFailed
        }
        
        return mockQRCodeData
    }
    
    func isFormatAvailable(_ format: ShareFormat) async -> Bool {
        return true // Mock always supports all formats
    }
    
    func getSupportedFormats() async -> [ShareFormat] {
        return ShareFormat.allCases
    }
    
    func validateItem<T: ShareableItem>(_ item: T) throws {
        // Mock validation always passes
    }
    
    func getDefaultFileName<T: ShareableItem>(for item: T) -> String {
        return "mock_\(item.shareContentType.rawValue).lavachat"
    }
    
    func cancelSharingOperation(_ operationId: UUID) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations.removeValue(forKey: operationId)
                continuation.resume()
            }
        }
    }
    
    func getSharingProgress(_ operationId: UUID) -> Double? {
        return operationQueue.sync {
            return activeOperations[operationId]?.progress
        }
    }
}

// MARK: - Mock Operation

private struct MockShareOperation: Sendable {
    let id: UUID
    let item: any ShareableItem
    let configuration: ShareConfiguration
    var progress: Double = 1.0
}
