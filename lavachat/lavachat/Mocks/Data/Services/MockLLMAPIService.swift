import Foundation
import Combine

/// Mock implementation of LLMAPIServiceProtocol for testing and development
final class MockLLMAPIService: LLMAPIServiceProtocol {
    
    // MARK: - Configuration
    
    /// Configuration for mock behavior
    struct Configuration {
        var simulateNetworkDelay: Bool = true
        var baseDelay: TimeInterval = 0.5
        var chunkDelay: TimeInterval = 0.1
        var errorRate: Double = 0.0
        var maxTokensPerChunk: Int = 20
        var failureInstanceIds: Set<UUID> = []
        var slowInstanceIds: Set<UUID> = []
        
        static let `default` = Configuration()
        static let fast = Configuration(simulateNetworkDelay: false, baseDelay: 0.0, chunkDelay: 0.0)
        static let unreliable = Configuration(errorRate: 0.3)
    }
    
    // MARK: - Properties
    
    private(set) var configuration: Configuration
    private var activeRequests: [UUID: Task<Void, Never>] = [:]
    private let requestsQueue = DispatchQueue(label: "MockLLMAPIService.requests", attributes: .concurrent)
    private let responseGenerator = MockStreamingResponseGenerator()
    
    // MARK: - Initialization
    
    init(configuration: Configuration = .default) {
        self.configuration = configuration
    }
    
    // MARK: - LLMAPIServiceProtocol Implementation
    
    func sendMessage(request: LLMStreamingRequest) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        return AsyncThrowingStream { continuation in
            let task = Task {
                do {
                    // Simulate initial delay
                    if configuration.simulateNetworkDelay {
                        try await Task.sleep(nanoseconds: UInt64(configuration.baseDelay * 1_000_000_000))
                    }
                    
                    // Check for forced failures
                    if configuration.failureInstanceIds.contains(request.instanceId) {
                        throw APIError.networkError(code: 503)
                    }
                    
                    // Check for random failures
                    if configuration.errorRate > 0 && Double.random(in: 0...1) < configuration.errorRate {
                        throw APIError.apiResponseError(
                            statusCode: Int.random(in: 500...599),
                            message: "Simulated server error"
                        )
                    }
                    
                    // Calculate response delay factor
                    let delayFactor: Double = configuration.slowInstanceIds.contains(request.instanceId) ? 2.0 : 1.0
                    
                    // Generate response stream
                    let responseStream = responseGenerator.generateResponse(
                        for: request,
                        chunkDelay: configuration.chunkDelay * delayFactor,
                        maxTokensPerChunk: configuration.maxTokensPerChunk
                    )
                    
                    for try await response in responseStream {
                        continuation.yield(response)
                    }
                    
                    continuation.finish()
                } catch {
                    continuation.finish(throwing: error)
                }
                
                // Clean up tracking
                await removeActiveRequest(request.id)
            }
            
            // Track active request
            Task {
                await addActiveRequest(request.id, task: task)
            }
            
            // Handle cancellation
            continuation.onTermination = { @Sendable _ in
                task.cancel()
            }
        }
    }
    
    func sendMessageToMultipleInstances(requests: [LLMStreamingRequest]) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        return AsyncThrowingStream { continuation in
            let groupTask = Task {
                await withTaskGroup(of: Void.self) { group in
                    for request in requests {
                        group.addTask {
                            do {
                                let responseStream = self.sendMessage(request: request)
                                for try await response in responseStream {
                                    continuation.yield(response)
                                }
                            } catch {
                                let errorResponse = LLMStreamingResponse.error(
                                    requestId: request.id,
                                    instanceId: request.instanceId,
                                    error: APIError.unknown(code: -1, message: error.localizedDescription)
                                )
                                continuation.yield(errorResponse)
                            }
                        }
                    }
                }
                
                continuation.finish()
            }
            
            // Handle cancellation
            continuation.onTermination = { @Sendable _ in
                groupTask.cancel()
            }
        }
    }
    
    func cancelRequest(requestId: UUID) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                if let task = self.activeRequests[requestId] {
                    task.cancel()
                    self.activeRequests.removeValue(forKey: requestId)
                }
                continuation.resume()
            }
        }
    }
    
    func cancelInstanceRequests(instanceId: UUID) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                let requestsToCancel = self.activeRequests.filter { _, task in
                    // In mock, we cancel all requests for simplicity
                    return true
                }
                
                for (requestId, task) in requestsToCancel {
                    task.cancel()
                    self.activeRequests.removeValue(forKey: requestId)
                }
                
                continuation.resume()
            }
        }
    }
    
    func cancelAllRequests() async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                for (_, task) in self.activeRequests {
                    task.cancel()
                }
                self.activeRequests.removeAll()
                continuation.resume()
            }
        }
    }
    
    func getActiveRequests() -> [UUID] {
        return requestsQueue.sync {
            return Array(activeRequests.keys)
        }
    }
    
    func getActiveRequests(for instanceId: UUID) -> [UUID] {
        return requestsQueue.sync {
            // In mock, return all for simplicity
            return Array(activeRequests.keys)
        }
    }
    
    func isRequestActive(requestId: UUID) -> Bool {
        return requestsQueue.sync {
            return activeRequests[requestId] != nil
        }
    }
    
    var isServiceAvailable: Bool {
        return true // Mock is always available
    }
    
    func getServiceStatus() -> [String: Any] {
        let activeRequestCount = getActiveRequests().count
        
        return [
            "activeRequestCount": activeRequestCount,
            "serviceName": "MockLLMAPIService",
            "configuration": [
                "simulateNetworkDelay": configuration.simulateNetworkDelay,
                "baseDelay": configuration.baseDelay,
                "errorRate": configuration.errorRate
            ],
            "serviceVersion": "1.0.0-Mock"
        ]
    }
    
    // MARK: - Mock Configuration
    
    func updateConfiguration(_ newConfiguration: Configuration) {
        self.configuration = newConfiguration
    }
    
    func addFailureInstance(_ instanceId: UUID) {
        configuration.failureInstanceIds.insert(instanceId)
    }
    
    func removeFailureInstance(_ instanceId: UUID) {
        configuration.failureInstanceIds.remove(instanceId)
    }
    
    func addSlowInstance(_ instanceId: UUID) {
        configuration.slowInstanceIds.insert(instanceId)
    }
    
    func removeSlowInstance(_ instanceId: UUID) {
        configuration.slowInstanceIds.remove(instanceId)
    }
    
    // MARK: - Private Methods
    
    private func addActiveRequest(_ requestId: UUID, task: Task<Void, Never>) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                self.activeRequests[requestId] = task
                continuation.resume()
            }
        }
    }
    
    private func removeActiveRequest(_ requestId: UUID) async {
        await withCheckedContinuation { continuation in
            requestsQueue.async(flags: .barrier) {
                self.activeRequests.removeValue(forKey: requestId)
                continuation.resume()
            }
        }
    }
}

// MARK: - Convenience Initializers

extension MockLLMAPIService {
    
    static func fastResponseService() -> MockLLMAPIService {
        return MockLLMAPIService(configuration: .fast)
    }
    
    static func unreliableService() -> MockLLMAPIService {
        return MockLLMAPIService(configuration: .unreliable)
    }
    
    static func customService(
        delay: TimeInterval = 0.5,
        errorRate: Double = 0.0,
        maxTokensPerChunk: Int = 20
    ) -> MockLLMAPIService {
        let config = Configuration(
            simulateNetworkDelay: delay > 0,
            baseDelay: delay,
            chunkDelay: delay / 10,
            errorRate: errorRate,
            maxTokensPerChunk: maxTokensPerChunk
        )
        return MockLLMAPIService(configuration: config)
    }
} 
