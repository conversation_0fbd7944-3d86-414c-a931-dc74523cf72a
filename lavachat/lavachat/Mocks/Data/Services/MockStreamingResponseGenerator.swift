import Foundation

/// Generates realistic mock streaming responses for LLM interactions
final class MockStreamingResponseGenerator {
    
    // MARK: - Response Templates
    
    private let responseTemplates: [String: [String]] = [
        "welcome": [
            "Hello! I'm your AI assistant.",
            " I'm here to help you with any questions or tasks you might have.",
            " Feel free to ask me anything about LavaChat or how I can assist you today.",
            " Is there something specific you'd like to know?"
        ],
        "explanation": [
            "LavaChat is an innovative AI hub application",
            " that allows you to interact with multiple large language models",
            " in a unified interface.",
            " You can chat with different AI assistants,",
            " manage your conversations,",
            " and even work with files and documents.",
            " The app supports both built-in AI models",
            " and allows you to configure your own API keys",
            " for personalized AI experiences."
        ],
        "features": [
            "LavaChat offers several key features:",
            "\n\n**Multi-Assistant Conversations**",
            "\n- Chat with multiple AI models simultaneously",
            "\n- Compare responses from different assistants",
            "\n- Switch between models seamlessly",
            "\n\n**Advanced Message Management**",
            "\n- Edit your messages and create conversation branches",
            "\n- Regenerate AI responses if you're not satisfied",
            "\n- Organize conversations with favorites and feedback",
            "\n\n**File Integration**",
            "\n- Upload and discuss documents with AI",
            "\n- Edit files collaboratively with AI assistance",
            "\n- Reference files in your conversations"
        ],
        "help": [
            "I can help you with various tasks:",
            "\n• Answer questions on any topic",
            "\n• Help with writing and editing",
            "\n• Explain complex concepts",
            "\n• Assist with problem-solving",
            "\n• Provide creative ideas",
            "\n• Code review and programming help",
            "\n\nWhat would you like assistance with today?"
        ],
        "code": [
            "Here's a simple example in Swift:",
            "\n\n```swift",
            "\nfunc greetUser(name: String) -> String {",
            "\n    return \"Hello, \\(name)! Welcome to LavaChat.\"",
            "\n}",
            "\n",
            "\nlet message = greetUser(name: \"Developer\")",
            "\nprint(message)",
            "\n```",
            "\n\nThis function demonstrates basic string interpolation",
            " and function definition in Swift."
        ]
    ]
    
    private let fallbackResponse = [
        "I understand your question.",
        " Let me think about this for a moment.",
        " Based on the information you've provided,",
        " I believe the best approach would be to",
        " consider multiple factors and provide you",
        " with a comprehensive response that addresses",
        " your specific needs and requirements.",
        " Is there anything particular you'd like me to focus on?"
    ]
    
    // MARK: - Generation Methods
    
    func generateResponse(
        for request: LLMStreamingRequest,
        chunkDelay: TimeInterval = 0.1,
        maxTokensPerChunk: Int = 20
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        
        return AsyncThrowingStream { continuation in
            Task {
                do {
                    // Select response template based on message content
                    let template = selectTemplate(for: request)
                    let chunks = createChunks(from: template, maxTokensPerChunk: maxTokensPerChunk)
                    
                    var totalPromptTokens = 0
                    var totalCompletionTokens = 0
                    
                    // Calculate token counts
                    for message in request.messageHistory {
                        totalPromptTokens += estimateTokens(in: message.content)
                    }
                    
                    // Send streaming chunks
                    for (index, chunk) in chunks.enumerated() {
                        // Simulate processing delay
                        if chunkDelay > 0 {
                            try await Task.sleep(nanoseconds: UInt64(chunkDelay * 1_000_000_000))
                        }
                        
                        totalCompletionTokens += estimateTokens(in: chunk)
                        
                        let response = LLMStreamingResponse.contentDelta(
                            requestId: request.id,
                            instanceId: request.instanceId,
                            content: chunk,
                            metadata: createMetadata(
                                isComplete: index == chunks.count - 1,
                                promptTokens: totalPromptTokens,
                                completionTokens: totalCompletionTokens
                            )
                        )
                        
                        continuation.yield(response)
                    }
                    
                    // Send completion
                    let completionInfo = CompletionInfo(
                        promptTokens: Int64(totalPromptTokens),
                        completionTokens: Int64(totalCompletionTokens),
                        totalTokens: Int64(totalPromptTokens + totalCompletionTokens),
                        finishReason: "stop",
                        modelUsed: "mock-gpt-4"
                    )
                    
                    let completionResponse = LLMStreamingResponse.completion(
                        requestId: request.id,
                        instanceId: request.instanceId,
                        completionInfo: completionInfo,
                        metadata: createMetadata(
                            isComplete: true,
                            promptTokens: totalPromptTokens,
                            completionTokens: totalCompletionTokens
                        )
                    )
                    
                    continuation.yield(completionResponse)
                    continuation.finish()
                    
                } catch {
                    continuation.finish(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func selectTemplate(for request: LLMStreamingRequest) -> [String] {
        guard let lastMessage = request.messageHistory.last else {
            return fallbackResponse
        }
        
        let content = extractTextContent(from: lastMessage.content).lowercased()
        
        // Simple keyword matching
        if content.contains("lavachat") || content.contains("what is") || content.contains("introduce") {
            return responseTemplates["explanation"] ?? fallbackResponse
        } else if content.contains("feature") || content.contains("capability") || content.contains("what can") {
            return responseTemplates["features"] ?? fallbackResponse
        } else if content.contains("help") || content.contains("assist") || content.contains("support") {
            return responseTemplates["help"] ?? fallbackResponse
        } else if content.contains("code") || content.contains("swift") || content.contains("programming") {
            return responseTemplates["code"] ?? fallbackResponse
        } else if content.contains("hello") || content.contains("hi") || content.contains("welcome") {
            return responseTemplates["welcome"] ?? fallbackResponse
        } else {
            return fallbackResponse
        }
    }
    
    private func extractTextContent(from contentBlocks: [ContentBlock]) -> String {
        return contentBlocks
            .compactMap { block in
                if case .text(let text) = block {
                    return text
                } else {
                    return nil
                }
            }
            .joined(separator: " ")
    }
    
    private func createChunks(from template: [String], maxTokensPerChunk: Int) -> [String] {
        var chunks: [String] = []
        var currentChunk = ""
        var currentTokens = 0
        
        for templatePart in template {
            let partTokens = estimateTokens(in: templatePart)
            
            if currentTokens + partTokens > maxTokensPerChunk && !currentChunk.isEmpty {
                chunks.append(currentChunk)
                currentChunk = templatePart
                currentTokens = partTokens
            } else {
                currentChunk += templatePart
                currentTokens += partTokens
            }
        }
        
        if !currentChunk.isEmpty {
            chunks.append(currentChunk)
        }
        
        return chunks
    }
    
    private func estimateTokens(in contentBlocks: [ContentBlock]) -> Int {
        let text = extractTextContent(from: contentBlocks)
        return estimateTokens(in: text)
    }
    
    private func estimateTokens(in text: String) -> Int {
        // Simple token estimation: roughly 4 characters per token
        return max(1, text.count / 4)
    }
    
    private func createMetadata(
        isComplete: Bool,
        promptTokens: Int,
        completionTokens: Int
    ) -> ResponseMetadata {
        return ResponseMetadata(
            modelIdentifier: "mock-gpt-4",
            providerId: nil, // Could be set to a mock provider ID if needed
            processingTimeMs: Int64.random(in: 50...500),
            sequence: nil,
            additionalInfo: isComplete ? [
                "promptTokens": String(promptTokens),
                "completionTokens": String(completionTokens),
                "totalTokens": String(promptTokens + completionTokens),
                "timestamp": String(Date().timeIntervalSince1970),
                "provider": "mock-openai"
            ] : [
                "timestamp": String(Date().timeIntervalSince1970),
                "provider": "mock-openai"
            ]
        )
    }
}

// MARK: - Specialized Response Generation

extension MockStreamingResponseGenerator {
    
    /// Generate a response that simulates thinking/reasoning
    func generateThinkingResponse(
        for request: LLMStreamingRequest,
        chunkDelay: TimeInterval = 0.05
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        
        let thinkingTemplate = [
            "Let me think about this carefully.",
            " I need to consider several aspects:",
            "\n\n1. Understanding the context of your question",
            "\n2. Identifying the key information you need",
            "\n3. Structuring a helpful response",
            "\n\nBased on my analysis, here's what I think..."
        ]
        
        return generateCustomResponse(
            for: request,
            template: thinkingTemplate,
            chunkDelay: chunkDelay
        )
    }
    
    /// Generate a response with a custom template
    func generateCustomResponse(
        for request: LLMStreamingRequest,
        template: [String],
        chunkDelay: TimeInterval = 0.1,
        maxTokensPerChunk: Int = 20
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        
        return AsyncThrowingStream { continuation in
            Task {
                do {
                    let chunks = createChunks(from: template, maxTokensPerChunk: maxTokensPerChunk)
                    
                    var totalPromptTokens = 0
                    var totalCompletionTokens = 0
                    
                    // Calculate prompt tokens
                    for message in request.messageHistory {
                        totalPromptTokens += estimateTokens(in: extractTextContent(from: message.content))
                    }
                    
                    // Send chunks
                    for (index, chunk) in chunks.enumerated() {
                        if chunkDelay > 0 {
                            try await Task.sleep(nanoseconds: UInt64(chunkDelay * 1_000_000_000))
                        }
                        
                        totalCompletionTokens += estimateTokens(in: chunk)
                        
                        let response = LLMStreamingResponse.contentDelta(
                            requestId: request.id,
                            instanceId: request.instanceId,
                            content: chunk,
                            metadata: createMetadata(
                                isComplete: index == chunks.count - 1,
                                promptTokens: totalPromptTokens,
                                completionTokens: totalCompletionTokens
                            )
                        )
                        
                        continuation.yield(response)
                    }
                    
                    // Send completion
                    let completionInfo = CompletionInfo(
                        promptTokens: Int64(totalPromptTokens),
                        completionTokens: Int64(totalCompletionTokens),
                        totalTokens: Int64(totalPromptTokens + totalCompletionTokens),
                        finishReason: "stop",
                        modelUsed: "mock-gpt-4"
                    )
                    
                    let completionResponse = LLMStreamingResponse.completion(
                        requestId: request.id,
                        instanceId: request.instanceId,
                        completionInfo: completionInfo,
                        metadata: createMetadata(
                            isComplete: true,
                            promptTokens: totalPromptTokens,
                            completionTokens: totalCompletionTokens
                        )
                    )
                    
                    continuation.yield(completionResponse)
                    continuation.finish()
                    
                } catch {
                    continuation.finish(throwing: error)
                }
            }
        }
    }
} 