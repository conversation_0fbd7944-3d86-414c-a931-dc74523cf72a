import Foundation

/// Mock implementation of ImportServiceProtocol for testing and previews
final class MockImportService: ImportServiceProtocol, @unchecked Sendable {
    
    // MARK: - Mock Configuration
    
    var shouldSucceed: Bool = true
    var simulatedDelay: TimeInterval = 0.1
    var mockImportResult: ImportSuccessInfo = ImportSuccessInfo(
        contentType: .llmInstance,
        importedItemIds: [UUID()],
        conflictsResolved: [],
        skippedItemIds: []
    )
    
    // MARK: - State Tracking
    
    private var activeOperations: [UUID: MockImportOperation] = [:]
    private let operationQueue = DispatchQueue(label: "com.lavachat.mock.import", qos: .userInitiated)
    
    // MARK: - ImportServiceProtocol Implementation
    
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = MockImportOperation(id: operationId, source: .file(fileURL), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        // Simulate processing delay
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            return .failure(.unknownError("Mock import service configured to fail"))
        }
        
        return .success(mockImportResult)
    }
    
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = MockImportOperation(id: operationId, source: .icloudShare(shareURL), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        // Simulate processing delay
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            return .failure(.networkError("Mock iCloud share not found"))
        }
        
        return .success(mockImportResult)
    }
    
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = MockImportOperation(id: operationId, source: .qrCode(qrCodeData), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        // Simulate processing delay
        try? await Task.sleep(nanoseconds: UInt64(simulatedDelay * 1_000_000_000))
        
        guard shouldSucceed else {
            return .failure(.invalidFileFormat)
        }
        
        return .success(mockImportResult)
    }
    
    func canImportFile(_ fileURL: URL) async -> Bool {
        // Mock validation - check if file extension is .lavachat
        return fileURL.pathExtension.lowercased() == "lavachat"
    }
    
    func getImportPreview(_ fileURL: URL) async -> ImportPreview? {
        guard await canImportFile(fileURL) else {
            return nil
        }
        
        return ImportPreview(
            contentType: .llmInstance,
            itemCount: 1,
            itemNames: ["Mock LLM Instance"],
            formatVersion: "1.0",
            exportedAt: Date(),
            metadata: ShareableMetadata()
        )
    }
    
    func canImportFromQRCode(_ qrCodeData: String) async -> Bool {
        // Mock validation - check if QR code contains a URL
        return URL(string: qrCodeData) != nil
    }
    
    func cancelImportOperation(_ operationId: UUID) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations.removeValue(forKey: operationId)
                continuation.resume()
            }
        }
    }
    
    func getImportProgress(_ operationId: UUID) -> Double? {
        return operationQueue.sync {
            return activeOperations[operationId]?.progress
        }
    }
}

// MARK: - Mock Operation

private struct MockImportOperation: Sendable {
    let id: UUID
    let source: ImportSource
    let configuration: ImportConfiguration
    var progress: Double = 1.0
}

private enum ImportSource: Sendable {
    case file(URL)
    case icloudShare(URL)
    case qrCode(String)
}