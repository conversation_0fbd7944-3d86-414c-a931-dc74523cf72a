enum ModelManagementError: Error {
    // Provider related errors
    case providerNotFound
    case cannotDeleteBuiltInProvider
    case providerDoesNotRequireApiKey
    case providerHasActiveInstances
    case cannotCreateProviderDoesNotRequireApiKey
    
    // API key related errors
    case apiKeyNotStored
    case emptyApiKey
    
    // Model related errors
    case modelNotFound
    case cannotDeleteModelUsedByInstances
    case duplicateModelIdentifier
    case cannotModifyBuiltInModelCore
    case cannotDeleteBuiltInModel
    case missingProviderIdForNewModel
    case cannotFindModelForProvider
    
    // Instance related errors
    case instanceNotFound
    case cannotModifyBuiltInInstance
    case cannotDuplicateInstance
    case cannotUpdateInstanceModelAssociation
    case invalidInstanceParameters
    case instanceGroupAssociationError
    case instanceNameAlreadyExists
    
    // Group related errors
    case groupNotFound
    case instanceAlreadyInGroup
    case instanceNotInGroup
    case emptyGroupName
    case groupNameAlreadyExists
    case cannotRemoveLastInstanceFromGroup
    
    // General errors
    case validationError
    case duplicateEntityName
    
    // Core data related errors
    case persistenceError(Error)
}
