import Foundation

/// Errors related to file and image processing for multimodal content
enum FileProcessingError: Error, LocalizedError, Equatable {
    
    // MARK: - Format Validation Errors
    
    /// Unsupported file format for the specified API style
    case unsupportedFormat(mimeType: String, apiStyle: APIStyle)
    
    /// Invalid or unrecognized MIME type
    case invalidMimeType(String)
    
    // MARK: - Size Validation Errors
    
    /// File size exceeds the maximum allowed limit
    case fileTooLarge(actualSize: Int64, maxSize: Int64)
    
    /// File is too small to process effectively
    case fileTooSmall(actualSize: Int64, minSize: Int64)
    
    // MARK: - Processing Errors
    
    /// Failed to compress image to required size
    case compressionFailed(reason: String?)
    
    /// Failed to encode file data to base64
    case encodingFailed(reason: String?)
    
    /// Failed to decode base64 data
    case decodingFailed(reason: String?)
    
    /// Failed to read file data
    case fileReadFailed(reason: String?)
    
    /// Invalid image data or corrupted file
    case invalidFileData(reason: String?)
    
    // MARK: - Localized Error Descriptions
    
    var errorDescription: String? {
        switch self {
        case .unsupportedFormat(let mimeType, let apiStyle):
            return String(localized: "Format '\(mimeType)' is not supported by \(apiStyle.displayName)", 
                         comment: "Unsupported file format error")
        case .invalidMimeType(let mimeType):
            return String(localized: "Invalid or unrecognized file type: \(mimeType)", 
                         comment: "Invalid MIME type error")
        case .fileTooLarge(let actualSize, let maxSize):
            let actualMB = Double(actualSize) / (1024 * 1024)
            let maxMB = Double(maxSize) / (1024 * 1024)
            return String(localized: "File size \(String(format: "%.1f", actualMB))MB exceeds maximum \(String(format: "%.1f", maxMB))MB", 
                         comment: "File too large error")
        case .fileTooSmall(let actualSize, let minSize):
            return String(localized: "File size \(actualSize) bytes is too small (minimum: \(minSize) bytes)", 
                         comment: "File too small error")
        case .compressionFailed(let reason):
            if let reason = reason {
                return String(localized: "Failed to compress image: \(reason)", 
                             comment: "Image compression failed with reason")
            } else {
                return String(localized: "Failed to compress image", 
                             comment: "Image compression failed error")
            }
        case .encodingFailed(let reason):
            if let reason = reason {
                return String(localized: "Failed to encode file to base64: \(reason)", 
                             comment: "Base64 encoding failed with reason")
            } else {
                return String(localized: "Failed to encode file to base64", 
                             comment: "Base64 encoding failed error")
            }
        case .decodingFailed(let reason):
            if let reason = reason {
                return String(localized: "Failed to decode base64 data: \(reason)", 
                             comment: "Base64 decoding failed with reason")
            } else {
                return String(localized: "Failed to decode base64 data", 
                             comment: "Base64 decoding failed error")
            }
        case .fileReadFailed(let reason):
            if let reason = reason {
                return String(localized: "Failed to read file: \(reason)", 
                             comment: "File read failed with reason")
            } else {
                return String(localized: "Failed to read file", 
                             comment: "File read failed error")
            }
        case .invalidFileData(let reason):
            if let reason = reason {
                return String(localized: "Invalid or corrupted file data: \(reason)", 
                             comment: "Invalid file data with reason")
            } else {
                return String(localized: "Invalid or corrupted file data", 
                             comment: "Invalid file data error")
            }
        }
    }
    
    // MARK: - Recovery Suggestions
    
    var recoverySuggestion: String? {
        switch self {
        case .unsupportedFormat:
            return String(localized: "Please use a supported file format", 
                         comment: "Unsupported format recovery suggestion")
        case .invalidMimeType:
            return String(localized: "Please check the file type and try again", 
                         comment: "Invalid MIME type recovery suggestion")
        case .fileTooLarge:
            return String(localized: "Please use a smaller file or compress the image", 
                         comment: "File too large recovery suggestion")
        case .fileTooSmall:
            return String(localized: "Please use a larger file", 
                         comment: "File too small recovery suggestion")
        case .compressionFailed:
            return String(localized: "Try using a different image or reduce the image size manually", 
                         comment: "Compression failed recovery suggestion")
        case .encodingFailed, .decodingFailed:
            return String(localized: "Please try again or use a different file", 
                         comment: "Encoding/decoding failed recovery suggestion")
        case .fileReadFailed:
            return String(localized: "Please check file permissions and try again", 
                         comment: "File read failed recovery suggestion")
        case .invalidFileData:
            return String(localized: "Please use a valid, non-corrupted file", 
                         comment: "Invalid file data recovery suggestion")
        }
    }
    
    // MARK: - Error Properties
    
    /// Whether this error is recoverable by user action
    var isRecoverable: Bool {
        switch self {
        case .unsupportedFormat, .fileTooLarge, .fileTooSmall, .invalidMimeType:
            return true
        case .compressionFailed, .encodingFailed, .decodingFailed, .fileReadFailed, .invalidFileData:
            return false
        }
    }
    
    /// Whether the operation should be retried
    var isRetryable: Bool {
        switch self {
        case .compressionFailed, .encodingFailed, .decodingFailed, .fileReadFailed:
            return true
        case .unsupportedFormat, .fileTooLarge, .fileTooSmall, .invalidMimeType, .invalidFileData:
            return false
        }
    }
}
