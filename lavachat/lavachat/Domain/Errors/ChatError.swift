import Foundation

/// Chat-specific errors that wrap and extend APIError for enhanced context.
enum ChatError: Error, LocalizedError, Equatable {
    
    // MARK: - Chat-Specific Errors
    
    /// Chat session not found
    case sessionNotFound
    
    /// Message sending failed with optional underlying error
    case messageSendFailed(underlying: Error? = nil)

    /// Message not found
    case messageNotFound
    
    /// Chat session setting not found
    case settingNotFound

    /// User not found
    case userNotFound
    
    /// Invalid message tree structure
    case invalidMessageTree

    /// Invalid message
    case invalidMessageNavigation
    
    /// Duplicate session creation failed
    case duplicateSessionCreationFailed
    
    /// Message editing failed
    case messageEditFailed
    
    /// Message regeneration failed
    case regenerationFailed
    
    /// Streaming response was interrupted for a specific instance
    case streamingInterrupted(instanceId: UUID)
    
    /// Batch save operation failed
    case batchSaveFailed
    
    /// Placeholder message creation failed
    case placeholderMessageCreationFailed
    
    /// Message ordering failed during sorting
    case messageOrderingFailed
    
    /// Tree update operation failed
    case treeUpdateFailed

    /// System action permission denied
    case systemActionPermissionDenied

    /// System setting permission denied (cannot delete system default settings)
    case systemSettingPermissionDenied

    /// File processing failed (images, documents, etc.)
    case fileProcessingFailed(FileProcessingError)

    // MARK: - API Error Wrappers
    
    /// Wraps APIError with optional instance context
    case apiError(APIError, instanceId: UUID? = nil)
    
    /// Multiple instances failed with different API errors
    case multiInstancePartialFailure([UUID: APIError])
    
    // MARK: - Static Helper Methods
    
    /// Creates a ChatError from an APIError with optional instance context
    static func from(_ apiError: APIError, instanceId: UUID? = nil) -> ChatError {
        return .apiError(apiError, instanceId: instanceId)
    }
    
    /// Creates a ChatError from any Error with optional instance context
    static func from(_ error: Error, instanceId: UUID? = nil) -> ChatError {
        if let chatError = error as? ChatError {
            return chatError
        } else if let apiError = error as? APIError {
            return .apiError(apiError, instanceId: instanceId)
        } else if let fileError = error as? FileProcessingError {
            return .fileProcessingFailed(fileError)
        } else {
            return .messageSendFailed(underlying: error)
        }
    }
    
    // MARK: - Error Properties
    
    /// User-friendly localized error description
    var errorDescription: String? {
        switch self {
        case .sessionNotFound:
            return String(localized: "Chat session not found", comment: "Chat session not found error")
        case .messageSendFailed(let underlying):
            if let underlying = underlying {
                return String(localized: "Failed to send message: \(underlying.localizedDescription)", comment: "Message send failed with underlying error")
            } else {
                return String(localized: "Failed to send message", comment: "Message send failed error")
            }
        case .messageNotFound:
            return String(localized: "Message not found", comment: "Message not found error")
        case .settingNotFound:
            return String(localized: "Chat settings not found", comment: "Chat settings not found error")
        case .userNotFound:
            return String(localized: "User not found", comment: "User not found error")
        case .invalidMessageTree:
            return String(localized: "Invalid message structure", comment: "Invalid message tree error")
        case .invalidMessageNavigation:
            return String(localized: "Tried to navigate to a invalid message", comment: "Invalid message navigation error")
        case .duplicateSessionCreationFailed:
            return String(localized: "Failed to duplicate chat session", comment: "Duplicate session creation failed error")
        case .messageEditFailed:
            return String(localized: "Failed to edit message", comment: "Message edit failed error")
        case .regenerationFailed:
            return String(localized: "Failed to regenerate message", comment: "Message regeneration failed error")
        case .streamingInterrupted(let instanceId):
            return String(localized: "Streaming was interrupted for instance: \(instanceId.uuidString)", comment: "Streaming interrupted error")
        case .batchSaveFailed:
            return String(localized: "Failed to save messages", comment: "Batch save failed error")
        case .placeholderMessageCreationFailed:
            return String(localized: "Failed to create placeholder message", comment: "Placeholder message creation failed error")
        case .messageOrderingFailed:
            return String(localized: "Failed to order messages correctly", comment: "Message ordering failed error")
        case .treeUpdateFailed:
            return String(localized: "Failed to update message tree", comment: "Tree update failed error")
        case .apiError(let apiError, let instanceId):
            if let instanceId = instanceId {
                return String(localized: "API error for instance \(instanceId.uuidString): \(apiError.localizedDescription)", comment: "API error with instance context")
            } else {
                return apiError.localizedDescription
            }
        case .multiInstancePartialFailure(let failures):
            let failureCount = failures.count
            return String(localized: "Failed to send to \(failureCount) instances", comment: "Multi-instance partial failure error")
        case .systemActionPermissionDenied:
            return String(localized: "System action permission denied", comment: "System action permission denied error")
        case .systemSettingPermissionDenied:
            return String(localized: "Cannot delete system default setting", comment: "System setting permission denied error")
        case .fileProcessingFailed(let fileError):
            return fileError.localizedDescription
        }
    }
    
    /// Indicates whether this error condition is retryable
    var isRetryable: Bool {
        switch self {
        case .sessionNotFound, .settingNotFound, .invalidMessageTree, .messageNotFound, .userNotFound:
            return false
        case .messageSendFailed, .duplicateSessionCreationFailed, .messageEditFailed, .regenerationFailed, .batchSaveFailed, .invalidMessageNavigation:
            return true
        case .streamingInterrupted:
            return true
        case .placeholderMessageCreationFailed, .messageOrderingFailed, .treeUpdateFailed:
            return true
        case .apiError(let apiError, _):
            return apiError.isRetryable
        case .multiInstancePartialFailure:
            return true
        case .systemActionPermissionDenied:
            return false
        case .systemSettingPermissionDenied:
            return false
        case .fileProcessingFailed(let fileError):
            return fileError.isRetryable
        }
    }
    
    // MARK: - Equatable
    
    static func == (lhs: ChatError, rhs: ChatError) -> Bool {
        switch (lhs, rhs) {
        case (.sessionNotFound, .sessionNotFound):
            return true
        case (.messageSendFailed(let lhsError), .messageSendFailed(let rhsError)):
            return lhsError?.localizedDescription == rhsError?.localizedDescription
        case (.messageNotFound, .messageNotFound):
            return true
        case (.settingNotFound, .settingNotFound):
            return true
        case (.userNotFound, .userNotFound):
            return true
        case (.invalidMessageTree, .invalidMessageTree):
            return true
        case (.invalidMessageNavigation, .invalidMessageNavigation):
            return true
        case (.duplicateSessionCreationFailed, .duplicateSessionCreationFailed):
            return true
        case (.messageEditFailed, .messageEditFailed):
            return true
        case (.regenerationFailed, .regenerationFailed):
            return true
        case (.streamingInterrupted(let lhsId), .streamingInterrupted(let rhsId)):
            return lhsId == rhsId
        case (.batchSaveFailed, .batchSaveFailed):
            return true
        case (.placeholderMessageCreationFailed, .placeholderMessageCreationFailed):
            return true
        case (.messageOrderingFailed, .messageOrderingFailed):
            return true
        case (.treeUpdateFailed, .treeUpdateFailed):
            return true
        case (.apiError(let lhsApi, let lhsId), .apiError(let rhsApi, let rhsId)):
            return lhsApi == rhsApi && lhsId == rhsId
        case (.multiInstancePartialFailure(let lhsFailures), .multiInstancePartialFailure(let rhsFailures)):
            return lhsFailures == rhsFailures
        case (.systemActionPermissionDenied, .systemActionPermissionDenied):
            return true
        case (.systemSettingPermissionDenied, .systemSettingPermissionDenied):
            return true
        case (.fileProcessingFailed(let lhsError), .fileProcessingFailed(let rhsError)):
            return lhsError == rhsError
        default:
            return false
        }
    }
}
