import Foundation

enum APIError: <PERSON>rro<PERSON>, Equatable, Has<PERSON>le, Codable {
    case networkError(code: Int?) // URLError.Code.rawValue
    case authenticationFailed(String?)
    case apiResponseError(statusCode: Int, message: String?)
    case parseError(String?)
    case streamConnectionError(String?)
    case rateLimitExceeded(retryAfter: TimeInterval?)
    case invalidRequest(String?)
    case notImplemented(String?)
    case serverUnavailable
    case timeout
    case cancelled
    case unknown(code: Int, message: String?)
    
    // MARK: - Localized Description
    
    var localizedDescription: String {
        switch self {
        case .networkError(let code):
            if let code = code {
                return String(localized: "Network connection error occurred: \(code)")
            }
            return String(localized: "Network connection error occurred")
        case .authenticationFailed(let message):
            if let message = message {
                return String(localized: "Authentication failed: \(message)")
            }
            return String(localized: "Authentication failed")
        case .apiResponseError(let statusCode, let message):
            if let message = message {
                return String(localized: "API error (status: \(statusCode), message: \(message))")
            }
            return String(localized: "API error (status: \(statusCode))")
        case .parseError(let message):
            if let message = message {
                return String(localized: "Failed to parse response: \(message)")
            }
            return String(localized: "Failed to parse response")
        case .streamConnectionError(let message):
            if let message = message {
                return String(localized: "Stream connection error: \(message)")
            }
            return String(localized: "Stream connection error")
        case .rateLimitExceeded(let retryAfter):
            if let retryAfter = retryAfter {
                return String(localized: "Rate limit exceeded. Please wait \(Int(retryAfter)) seconds")
            }
            return String(localized: "Rate limit exceeded. Please wait and try again")
        case .invalidRequest(let message):
            if let message = message {
                return String(localized: "Invalid request: \(message)")
            }
            return String(localized: "Invalid request")
        case .notImplemented(let message):
            if let message = message {
                return String(localized: "This feature is not implemented: \(message)")
            }
            return String(localized: "This feature is not implemented")
        case .serverUnavailable:
            return String(localized: "Server unavailable")
        case .timeout:
            return String(localized: "Request timeout")
        case .cancelled:
            return String(localized: "Request cancelled")
        case .unknown(let code, let message):
            if let message = message, !message.isEmpty {
                return String(localized: "An unexpected error occurred with code \(code): \(message)")
            } else {
                return String(localized: "An unexpected error occurred with code \(code)")
            }
        }
    }
    
    // MARK: - Recovery Suggestions
    
    var isRecoverable: Bool {
        switch self {
        case .networkError, .timeout, .serverUnavailable, .rateLimitExceeded:
            return true
        case .authenticationFailed, .invalidRequest, .parseError, .notImplemented:
            return false
        case .apiResponseError(let statusCode, _):
            return statusCode >= 500 || statusCode == 429
        case .streamConnectionError:
            return true
        case .cancelled:
            return true
        case .unknown:
            return false
        }
    }
    
    var isRetryable: Bool {
        switch self {
        case .networkError, .timeout, .serverUnavailable:
            return true
        case .rateLimitExceeded:
            return true
        case .streamConnectionError:
            return true
        case .apiResponseError(let statusCode, _):
            return statusCode >= 500
        default:
            return false
        }
    }
    
    var requiresUserIntervention: Bool {
        switch self {
        case .authenticationFailed, .invalidRequest:
            return true
        case .apiResponseError(let statusCode, _):
            return statusCode == 401 || statusCode == 403 || statusCode == 400
        default:
            return false
        }
    }
    
    // MARK: - Suggested Action
    
    var suggestedAction: String {
        switch self {
        case .networkError:
            return String(localized: "Check internet connection")
        case .authenticationFailed:
            return String(localized: "Verify API key")
        case .rateLimitExceeded:
            return String(localized: "Wait and retry")
        case .timeout, .serverUnavailable:
            return String(localized: "Retry later")
        case .streamConnectionError:
            return String(localized: "Restart conversation")
        case .invalidRequest:
            return String(localized: "Modify input")
        case .notImplemented:
            return String(localized: "Contact support")
        case .parseError:
            return String(localized: "Report issue")
        case .cancelled:
            return String(localized: "Retry if needed")
        case .apiResponseError(let statusCode, _):
            switch statusCode {
            case 401, 403:
                return String(localized: "Check permissions")
            case 429:
                return String(localized: "Wait and retry")
            case 500...599:
                return String(localized: "Retry later")
            default:
                return String(localized: "Check input")
            }
        case .unknown:
            return String(localized: "Contact support")
        }
    }
} 
