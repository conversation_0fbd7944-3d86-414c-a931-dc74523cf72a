import Foundation

/// Cache structure for efficient message tree management and query operations
final class MessageTreeCache {
    
    // MARK: - Properties
    
    /// The session ID this cache belongs to
    let sessionId: UUID
    
    /// The chat session reference
    var chatSession: ChatSession?
    
    /// All messages in the session, keyed by message ID
    private(set) var allMessages: [UUID: Message] = [:]
    
    /// Messages organized by their parent ID for efficient child lookup (UUID only)
    private(set) var messagesByParentId: [UUID?: [UUID]] = [:]
    
    /// Active message path from root to current active message
    private(set) var activeMessagePath: [UUID] = []
    
    /// Siblings at each depth level for the active path (UUID only)
    private(set) var activePathSiblingIds: [Int64: [UUID]] = [:]
    
    /// Current active message ID
    private(set) var activeMessageId: UUID?
    
    // MARK: - Initialization
    
    init(sessionId: UUID, chatSession: ChatSession? = nil) {
        self.sessionId = sessionId
        self.chatSession = chatSession
    }
    
    // MARK: - Message CRUD Operations
    
    /// Add a message to the cache
    func addMessage(_ message: Message) {
        allMessages[message.id] = message
        
        // Update parent-child relationships (UUID only)
        if messagesByParentId[message.parentId] == nil {
            messagesByParentId[message.parentId] = []
        }
        messagesByParentId[message.parentId]?.append(message.id)
        
        // Sort by timestamp using message data
        messagesByParentId[message.parentId]?.sort { messageId1, messageId2 in
            guard let msg1 = allMessages[messageId1],
                  let msg2 = allMessages[messageId2] else {
                return false
            }
            return msg1.timestamp < msg2.timestamp
        }
    }
    
    /// Update an existing message in the cache
    func updateMessage(_ message: Message) {
        guard allMessages[message.id] != nil else { return }
        
        // Remove from old parent relationship if parent changed
        let oldMessage = allMessages[message.id]!
        if oldMessage.parentId != message.parentId {
            removeMessageFromParentIndex(oldMessage)
        }
        
        // Update the message
        allMessages[message.id] = message
        
        // Update parent-child relationships
        if messagesByParentId[message.parentId] == nil {
            messagesByParentId[message.parentId] = []
        }
        
        // Add to new parent if not already there
        if !messagesByParentId[message.parentId]!.contains(message.id) {
            messagesByParentId[message.parentId]?.append(message.id)
            messagesByParentId[message.parentId]?.sort { messageId1, messageId2 in
                guard let msg1 = allMessages[messageId1],
                      let msg2 = allMessages[messageId2] else {
                    return false
                }
                return msg1.timestamp < msg2.timestamp
            }
        }
    }
    
    /// Remove a message from the cache
    func removeMessage(_ messageId: UUID) {
        guard let message = allMessages[messageId] else { return }
        
        // Remove from all messages
        allMessages.removeValue(forKey: messageId)
        
        // Remove from parent-child relationships
        removeMessageFromParentIndex(message)
        
        messagesByParentId.removeValue(forKey: messageId)
    }
    
    // MARK: - Query Operations
    
    /// Get message by ID
    func getMessage(by id: UUID) -> Message? {
        return allMessages[id]
    }
    
    /// Get message history for the active message (root to active)
    func getActivePathMessage() -> [Message] {
        return activeMessagePath.compactMap { allMessages[$0] }
    }
    
    /// Get siblings at a specific depth level (returns Message objects)
    func getSiblingsAt(depth: Int64) -> [Message] {
        guard let siblingIds = activePathSiblingIds[depth] else { return [] }
        return siblingIds.compactMap { allMessages[$0] }
    }
    
    /// Get children of a specific message (returns Message objects)
    func getChildren(of messageId: UUID) -> [Message] {
        guard let childIds = messagesByParentId[messageId] else { return [] }
        return childIds.compactMap { allMessages[$0] }
    }
    
    /// Get root messages (messages with no parent)
    func getRootMessages() -> [Message] {
        guard let rootIds = messagesByParentId[nil] else { return [] }
        return rootIds.compactMap { allMessages[$0] }
    }
    
    /// Get current active message ID
    func getCurrentActiveMessageId() -> UUID? {
        return activeMessageId
    }
    
    // MARK: - Message Sorting and Selection
    
    /// Sort messages for display using intelligent ordering based on ChatSession configuration
    static func sortMessagesInALayerForDisplay(
        messages: [Message],
        chatSession: ChatSession? = nil
    ) -> [Message] {
        guard !messages.isEmpty else { return [] }
        
        // Get chat session for ordering configuration
        guard let chatSession = chatSession else {
            // Fallback to timestamp sorting
            return messages.sorted { $0.timestamp < $1.timestamp }
        }
        
        // For assistant messages, use multi-level sorting
        if messages.first?.role == .assistant || messages.first?.role == .mergedAssistant {
            return messages.sorted { message1, message2 in
                // Priority 1: calculateMessagePriority (user interactions)
                let priority1 = calculateMessagePriority(message1)
                let priority2 = calculateMessagePriority(message2)
                
                if priority1 != priority2 {
                    return priority1 > priority2 // Higher priority first
                }
                
                // Priority 2: activeLLMInstanceIds order
                if let instanceId1 = message1.llmInstanceId,
                   let instanceId2 = message2.llmInstanceId {
                    
                    let activeIndex1 = chatSession.activeLLMInstanceIds.firstIndex(of: instanceId1)
                    let activeIndex2 = chatSession.activeLLMInstanceIds.firstIndex(of: instanceId2)
                    
                    switch (activeIndex1, activeIndex2) {
                    case (.some(let idx1), .some(let idx2)):
                        if idx1 != idx2 {
                            return idx1 < idx2 // Earlier in active list wins
                        }
                        // If indices are equal (same instanceId/regeneration), continue to next level
                    case (.some(_), .none):
                        return true // In active list beats not in active list
                    case (.none, .some(_)):
                        return false // Not in active list loses to in active list
                    case (.none, .none):
                        break // Continue to next priority level
                    }
                    
                    // Priority 3: usedLLMInstanceIds order
                    let usedIndex1 = chatSession.usedLLMInstanceIds.firstIndex(of: instanceId1)
                    let usedIndex2 = chatSession.usedLLMInstanceIds.firstIndex(of: instanceId2)
                    
                    switch (usedIndex1, usedIndex2) {
                    case (.some(let idx1), .some(let idx2)):
                        if idx1 != idx2 {
                            return idx1 < idx2 // Earlier in used list wins
                        }
                        // If indices are equal (same instanceId/regeneration), continue to timestamp fallback
                    case (.some(_), .none):
                        return true // In used list beats not in used list
                    case (.none, .some(_)):
                        return false // Not in used list loses to in used list
                    case (.none, .none):
                        break // Continue to timestamp fallback
                    }
                }
                
                // Priority 4: Timestamp fallback (latest first for assistant messages)
                return message1.timestamp > message2.timestamp
            }
        } else {
            // For user messages, simple timestamp sorting, latest at the last
            return messages.sorted { $0.timestamp < $1.timestamp }
        }
    }
    
    /// Select preferred assistant messages by filtering out disliked and applying priority sorting
    func selectPreferredAssistantMessages(from messages: [Message], filterDisliked: Bool = false) -> [Message] {
        var filteredMessages: [Message]
        
        if filterDisliked {
            // Filter out disliked messages completely
            filteredMessages = messages.filter { $0.userFeedback != .disliked }
        } else {
            filteredMessages = messages
        }
        
        if filteredMessages.isEmpty {
            return []
        }
        
        // Apply intelligent sorting
        return MessageTreeCache.sortMessagesInALayerForDisplay(messages: filteredMessages, chatSession: self.chatSession)
    }
    
    // MARK: - Active Path Management
    
    /// Set the active message with full rebuild (for initialization and complete reconstruction)
    func setActiveMessageWithFullRebuild(_ messageId: UUID?) {
        activeMessageId = messageId
        rebuildActivePath()
    }
    
    /// Build active path from a specific message
    func buildActivePathFromMessage(startMessage: Message) {
        print("🌳 Building active path from message: \(startMessage.id) at depth: \(startMessage.depth)")
        
        // Clear data after the start message's depth
        clearSubtreeAfter(depth: startMessage.depth - 1)
        
        // Build path from start message downward
        buildChildPathFromMessage(startMessage)
        
        print("✅ Active path building complete, final active message: \(activeMessageId ?? UUID())")
    }
    
    /// Update tree from a specific message, optimized for incremental changes
    func updateFromMessage(messageId: UUID, newActiveMessageId: UUID?) {
        guard let newActiveMessageId = newActiveMessageId,
              let newActiveMessage = allMessages[newActiveMessageId] else { return }
        
        buildActivePathFromMessage(startMessage: newActiveMessage)
    }
    
    // MARK: - Cache Cleanup Operations
    
    /// Clear subtree cache after a specific depth
    func clearSubtreeAfter(depth: Int64) {
        let keysToRemove = activePathSiblingIds.keys.filter { $0 > depth }
        for key in keysToRemove {
            activePathSiblingIds.removeValue(forKey: key)
        }
        
        // Trim active message path if necessary
        if activeMessagePath.count > depth + 1 {
            activeMessagePath = Array(activeMessagePath.prefix(Int(depth) + 1))
        }
    }
    
    /// Clear entire subtree starting from a specific message
    func clearSubtreeFrom(messageId: UUID) {
        // Find all descendant messages by traversing the tree
        guard allMessages[messageId] != nil else { return }
        
        // Collect all descendants
        var toRemove: [UUID] = []
        var queue: [UUID] = [messageId]
        
        while !queue.isEmpty {
            let currentId = queue.removeFirst()
            toRemove.append(currentId)
            
            // Find children of current message
            for (parentId, childIds) in messagesByParentId {
                if parentId == currentId {
                    queue.append(contentsOf: childIds)
                }
            }
        }
        
        // Remove all collected messages
        for msgId in toRemove {
            removeMessage(msgId)
        }
    }
    
    /// Invalidate and clear the cache
    func invalidateCache() {
        allMessages.removeAll()
        messagesByParentId.removeAll()
        activeMessagePath.removeAll()
        activePathSiblingIds.removeAll()
        activeMessageId = nil
    }
    
    /// Rebuild the cache from a fresh set of messages
    func rebuildFromMessages(_ messages: [Message], activeMessageId: UUID? = nil) {
        invalidateCache()
        
        // Add all messages
        for message in messages {
            addMessage(message)
        }
        
        // Set active message with full rebuild
        setActiveMessageWithFullRebuild(activeMessageId)
    }
    
    // MARK: - Private Helper Methods
    
    /// Remove a message from the parent-child index
    private func removeMessageFromParentIndex(_ message: Message) {
        if var siblings = messagesByParentId[message.parentId] {
            siblings.removeAll { $0 == message.id }
            if siblings.isEmpty {
                messagesByParentId.removeValue(forKey: message.parentId)
            } else {
                messagesByParentId[message.parentId] = siblings
            }
        }
    }
    
    /// Calculate message priority score for sorting
    private static func calculateMessagePriority(_ message: Message) -> Int {
        var score = 0
        
        // isReplied has highest priority
        if message.isReplied {
            score += 100
        }
        
        // userFeedback priority
        switch message.userFeedback {
        case .liked:
            score += 10
        case .none:
            score += 5
        case .disliked:
            score += 0 // Should already be filtered out
        }
        
        return score
    }
    
    /// Rebuild the active path from root to active message (full rebuild)
    private func rebuildActivePath() {
        activeMessagePath.removeAll()
        activePathSiblingIds.removeAll()
        
        guard let activeId = activeMessageId,
              let activeMessage = allMessages[activeId] else { return }
        
        // Build path from active message to root
        var currentMessage: Message? = activeMessage
        var pathMessages: [Message] = []
        
        while let message = currentMessage {
            pathMessages.insert(message, at: 0)
            currentMessage = message.parentId != nil ? allMessages[message.parentId!] : nil
        }
        
        // Store the path (UUID only)
        activeMessagePath = pathMessages.map { $0.id }
        
        // Build siblings at each depth using message.depth (UUID only)
        for message in pathMessages {
            let depth = message.depth
            let siblingIds = messagesByParentId[message.parentId] ?? []
            activePathSiblingIds[depth] = siblingIds
        }
    }
    
    /// Recursively build child path from a message
    private func buildChildPathFromMessage(_ message: Message) {
        // Add current message to path
        activeMessagePath.append(message.id)
        
        // Add siblings for this depth level
        let siblingIds = messagesByParentId[message.parentId] ?? []
        activePathSiblingIds[message.depth] = siblingIds
        
        // Get children of current message
        let children = getChildren(of: message.id)
        
        if children.isEmpty {
            // This is a leaf message, set it as active
            activeMessageId = message.id
            print("🍃 Leaf message reached, setting as active: \(message.id)")
            return
        }
        
        // Group children by role
        var messagesByRole: [MessageRole: [Message]] = [:]
        for child in children {
            let groupRole: MessageRole
            if child.role == .assistant || child.role == .mergedAssistant {
                groupRole = .assistant // Group both types under .assistant
            } else {
                groupRole = child.role
            }
            
            if messagesByRole[groupRole] == nil {
                messagesByRole[groupRole] = []
            }
            messagesByRole[groupRole]?.append(child)
        }
        
        // Process roles in order: user first, then assistant, then system
        let roleOrder: [MessageRole] = [.user, .assistant, .system]
        
        for role in roleOrder {
            guard let roleMessages = messagesByRole[role] else { continue }
            
            // Apply role-specific processing
            let processedMessages: [Message]
            if role == .assistant {
                processedMessages = selectPreferredAssistantMessages(from: roleMessages)
                if processedMessages.isEmpty {
                    continue // Skip if no assistant messages remain after filtering
                }
            } else {
                processedMessages = MessageTreeCache.sortMessagesInALayerForDisplay(messages: roleMessages, chatSession: self.chatSession)
            }
            
            // Select the appropriate message to continue the path
            let nextMessage: Message?
            if role == .user {
                // For user messages, take the last (most recent) message
                nextMessage = processedMessages.last
            } else {
                // For assistant and system messages, take the first (preferred) message
                nextMessage = processedMessages.first
            }
            
            if let selectedMessage = nextMessage {
                print("🎯 Selected next message: \(selectedMessage.id) with role: \(selectedMessage.role)")
                buildChildPathFromMessage(selectedMessage)
                return // Only follow one path
            }
        }
        
        // If no suitable child found, current message becomes active
        activeMessageId = message.id
        print("🏁 No suitable child found, setting current as active: \(message.id)")
    }
}
