import Foundation

/// Defines specific types of changes that are relevant to ChatView
enum ChatViewRelevantChange {
    case sessionTitleChanged(String?)
    case sessionSettingsChanged(UUID?)
    case sessionDeleted
    case settingUIThemeChanged(UIThemeSettings?)
    case settingMessageActionsChanged(MessageActionSettings?)
    case settingPromptSegmentsChanged([SavedPromptSegment]?)
    case settingMessageActionContentChanged(MessageAction)
    case settingShouldExpandThinkingChanged(Bool)
    case settingAuxiliaryLLMInstanceIdChanged(UUID?)
    case settingShouldAutoGenerateTitleChanged(Bool)
    case settingContextMessageCountChanged(Int64)
}