import Foundation

/// Represents the result of handling an incoming URL
enum URLHandlingResult {
    case success(ImportSuccessInfo)
    case failure(ImportError)
    case unsupported
}

/// Extension to provide user-friendly descriptions
extension URLHandlingResult {
    
    /// Whether the URL handling was successful
    var isSuccess: Bool {
        switch self {
        case .success:
            return true
        case .failure, .unsupported:
            return false
        }
    }
    
    /// User-friendly title for the result
    var title: String {
        switch self {
        case .success:
            return "Import Successful"
        case .failure:
            return "Import Failed"
        case .unsupported:
            return "Unsupported Link"
        }
    }
    
    /// User-friendly description for the result
    var description: String {
        switch self {
        case .success(let info):
            let totalItems = info.importedItemIds.count + info.skippedItemIds.count
            if totalItems == 0 {
                return "No items found to import"
            } else if info.importedItemIds.count > 0 && info.skippedItemIds.count > 0 {
                return "Imported \(info.importedItemIds.count) new \(info.contentType.displayName.lowercased())(s), \(info.skippedItemIds.count) already existed"
            } else if info.importedItemIds.count > 0 {
                return "Successfully imported \(info.importedItemIds.count) \(info.contentType.displayName.lowercased())(s)"
            } else {
                return "All \(info.skippedItemIds.count) \(info.contentType.displayName.lowercased())(s) already existed"
            }
        case .failure(let error):
            return error.localizedDescription
        case .unsupported:
            return "This link or file type is not supported for import."
        }
    }
    
    /// System image name for the result
    var systemImageName: String {
        switch self {
        case .success:
            return "checkmark.circle.fill"
        case .failure:
            return "exclamationmark.triangle.fill"
        case .unsupported:
            return "questionmark.circle.fill"
        }
    }
    
    /// Color for the result icon
    var iconColor: String {
        switch self {
        case .success:
            return "green"
        case .failure:
            return "red"
        case .unsupported:
            return "orange"
        }
    }
    
    /// Navigation hint for where to find imported content
    func navigationHint() -> String? {
        switch self {
        case .success(let info):
            return navigationHint(for: info.contentType)
        case .failure, .unsupported:
            return nil
        }
    }
    
    private func navigationHint(for contentType: ShareContentType) -> String {
        switch contentType {
        case .llmInstance:
            return "Models Tab → Your imported instances"
        case .chatSession:
            return "Chats Tab → Your imported conversations"
        case .messageAction:
            return "Settings Tab → My Actions"
        case .chatSessionSetting:
            return "Settings Tab → My Chat Settings"
        }
    }
}
