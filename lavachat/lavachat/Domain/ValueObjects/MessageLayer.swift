import Foundation

/// Represents a layer of messages at a specific depth in the conversation tree
struct MessageLayer {
    let siblingMessages: [Message]
    let activeMessageId: UUID
    let depth: Int64
    let role: MessageRole

    var activeMessage: Message? {
        return siblingMessages.first(where: { $0.id == activeMessageId })
    }

    var activeMessageIndex: Int? {
        return siblingMessages.firstIndex(where: { $0.id == activeMessageId })
    }
} 
