enum MessageActionCategory: String, CaseIterable {
    case actionPanel = "Input Actions"
    case userMessage = "User Message"
    case assistantCard = "Assistant Card"
    case assistantMenu = "Assistant Menu"
    
    var suitabilityCheck: (MessageAction) -> Bool {
        switch self {
        case .actionPanel:
            return { $0.suitableForActionPanel }
        case .userMessage:
            return { $0.suitableForUserMessage }
        case .assistantCard:
            return { $0.suitableForAssistantMessage }
        case .assistantMenu:
            return { $0.suitableForAssistantMessage }
        }
    }

    var locationDescription: String {
        switch self {
        case .actionPanel:
            return "Actions shown in the panel that appears when tapping the '+' button next to the input field."
        case .userMessage:
            return "Actions shown in the context menu when long-pressing on user messages."
        case .assistantCard:
            return "Actions shown as buttons at the bottom of assistant reply cards."
        case .assistantMenu:
            return "Actions shown in the context menu when long-pressing on assistant reply cards."
        }
    }
}