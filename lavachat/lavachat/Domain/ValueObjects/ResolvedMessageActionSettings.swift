import Foundation

/// A value object that holds fully resolved `MessageAction` objects,
/// converted from the UUIDs stored in `MessageActionSettings`.
struct ResolvedMessageActionSettings {
    /// Actions available for action panel.
    let actionPanelActions: [MessageAction]
    
    /// Actions available for user-initiated messages.
    let userMessageActions: [MessageAction]
    
    /// Actions available on the assistant's message card UI.
    let assistantMessageCardActions: [MessageAction]
    
    /// Actions available in the assistant's message context menu (e.g., on long press).
    let assistantMessageMenuActions: [MessageAction]

    // MARK: - Computed Properties

    /// Valid actions for action panel, filtered by suitableForActionPanel
    var validActionPanelActions: [MessageAction] {
        return actionPanelActions.filter { $0.suitableForActionPanel }
    }

    /// Valid actions for user message, filtered by suitableForUserMessage
    var validUserMessageActions: [MessageAction] {
        return userMessageActions.filter { $0.suitableForUserMessage }
    }

    /// Valid actions for assistant message card, filtered by suitableForAssistantMessage
    var validAssistantMessageCardActions: [MessageAction] {
        return assistantMessageCardActions.filter { $0.suitableForAssistantMessage }
    }

    /// Valid actions for assistant message menu, filtered by suitableForAssistantMessage
    var validAssistantMessageMenuActions: [MessageAction] {
        return assistantMessageMenuActions.filter { $0.suitableForAssistantMessage }
    }

    /// Initializes with empty arrays for a default state.
    init(
        actionPanelActions: [MessageAction] = [],
        userMessageActions: [MessageAction] = [],
        assistantMessageCardActions: [MessageAction] = [],
        assistantMessageMenuActions: [MessageAction] = []
    ) {
        self.actionPanelActions = actionPanelActions
        self.userMessageActions = userMessageActions
        self.assistantMessageCardActions = assistantMessageCardActions
        self.assistantMessageMenuActions = assistantMessageMenuActions
    }

    // MARK: - Update Methods

    /// Updates an existing action across all action arrays if it exists.
    /// Returns a new ResolvedMessageActionSettings instance with the updated action,
    /// or the original instance if the action was not found.
    ///
    /// - Parameter updatedAction: The MessageAction with updated content
    /// - Returns: A new ResolvedMessageActionSettings instance with the action updated if found
    func updatingActionContent(with updatedAction: MessageAction) -> ResolvedMessageActionSettings {
        // Check if the action exists in any of the arrays
        let actionExists = containsAction(with: updatedAction.id)

        guard actionExists else {
            // Action not found, return self unchanged
            return self
        }

        // Update all arrays that contain this action
        let updatedActionPanelActions = updateActionInArray(actionPanelActions, with: updatedAction)
        let updatedUserMessageActions = updateActionInArray(userMessageActions, with: updatedAction)
        let updatedAssistantMessageCardActions = updateActionInArray(assistantMessageCardActions, with: updatedAction)
        let updatedAssistantMessageMenuActions = updateActionInArray(assistantMessageMenuActions, with: updatedAction)

        return ResolvedMessageActionSettings(
            actionPanelActions: updatedActionPanelActions,
            userMessageActions: updatedUserMessageActions,
            assistantMessageCardActions: updatedAssistantMessageCardActions,
            assistantMessageMenuActions: updatedAssistantMessageMenuActions
        )
    }

    /// Checks if an action with the given ID exists in any of the action arrays.
    ///
    /// - Parameter actionId: The UUID of the action to check for
    /// - Returns: true if the action exists in any array, false otherwise
    func containsAction(with actionId: UUID) -> Bool {
        return actionPanelActions.contains { $0.id == actionId } ||
               userMessageActions.contains { $0.id == actionId } ||
               assistantMessageCardActions.contains { $0.id == actionId } ||
               assistantMessageMenuActions.contains { $0.id == actionId }
    }

    // MARK: - Private Helper Methods

    /// Helper method to update an action in an array if it exists.
    ///
    /// - Parameters:
    ///   - actions: The array of MessageActions to update
    ///   - updatedAction: The updated MessageAction
    /// - Returns: A new array with the action updated if found, or the original array if not found
    private func updateActionInArray(_ actions: [MessageAction], with updatedAction: MessageAction) -> [MessageAction] {
        return actions.map { action in
            if action.id == updatedAction.id {
                return updatedAction
            } else {
                return action
            }
        }
    }
}
