import Foundation

/// State object for managing message editing mode
struct EditingState {
    let messageId: UUID
    let originalText: String
    let originalMessage: Message
    let sourceRowViewModelId: UUID
    
    init(message: Message, sourceRowViewModelId: UUID) {
        self.messageId = message.id
        self.originalMessage = message
        self.sourceRowViewModelId = sourceRowViewModelId
        self.originalText = message.textContent
    }
}
