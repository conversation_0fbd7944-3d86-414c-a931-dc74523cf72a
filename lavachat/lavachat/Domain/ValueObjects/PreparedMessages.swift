import Foundation

/// Prepared messages for immediate UI display and subsequent streaming processing
struct PreparedMessages {
    /// The user message to be sent
    let userMessage: Message
    
    /// Placeholder assistant messages for immediate UI display
    let placeholderAssistantMessages: [Message]
    
    /// New assistant message layer for UI display (nil for regenerateSingleMessage)
    let newAssistantMessageLayer: MessageLayer?
    
    /// The operation type for this message preparation
    let operationType: MessageOperationType
    
    /// Override system prompt for the LLM
    let overrideSystemPrompt: String?
    
    /// Thinking controls for each instance
    let thinkingControls: [UUID: Bool]
    
    /// Searching controls for each instance
    let searchingControls: [UUID: Bool]

    /// Context message count for history truncation
    let contextMessageCount: Int64

    /// Asynchronous persistence task for repository and cache updates
    let persistenceTask: Task<Void, Error>
    
    init(
        userMessage: Message,
        placeholderAssistantMessages: [Message],
        newAssistantMessageLayer: MessageLayer?,
        operationType: MessageOperationType,
        overrideSystemPrompt: String?,
        thinkingControls: [UUID: Bool],
        searchingControls: [UUID: Bool],
        contextMessageCount: Int64,
        persistenceTask: Task<Void, Error>
    ) {
        self.userMessage = userMessage
        self.placeholderAssistantMessages = placeholderAssistantMessages
        self.newAssistantMessageLayer = newAssistantMessageLayer
        self.operationType = operationType
        self.overrideSystemPrompt = overrideSystemPrompt
        self.thinkingControls = thinkingControls
        self.searchingControls = searchingControls
        self.contextMessageCount = contextMessageCount
        self.persistenceTask = persistenceTask
    }
} 