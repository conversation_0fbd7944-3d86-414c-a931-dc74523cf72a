import Foundation

struct ModelAPIConfig: Codable, Hashable, Equatable {
    var apiStyle: APIStyle
    var apiEndpointPath: String
}

enum APIStyle: String, Codable, CaseIterable, Equatable {
    case openaiCompatible = "openai_compatible"
    case anthropic = "anthropic"
    case google = "google"
    case openaiImageGeneration = "openai_image_generation"
    case openaiImageEdit = "openai_image_edit"
    // Add other styles as needed

    var displayName: String {
        switch self {
        case .openaiCompatible:
            return "OpenAI Compatible"
        case .anthropic:
            return "Anthropic"
        case .google:
            return "Google"
        case .openaiImageGeneration:
            return "OpenAI Image Generation"
        case .openaiImageEdit:
            return "OpenAI Image Edit"
        }
    }

    var description: String? {
        switch self {
        case .openaiCompatible:
            return "Standard chat completion API, usually supports text input and output. Check model modalities."
        case .anthropic:
            return "Anthropic Messages API, supports text and often image/PDF input, text output."
        case .google:
            return "Google Gemini API, supports various multimodal inputs (text, image, audio, video), text output."
        case .openaiImageGeneration:
            return "OpenAI Image Generation API (GPT image 1). **Requires** text prompt, **outputs** image."
        case .openaiImageEdit:
            return "OpenAI Image Editing API (GPT image 1). **Requires** text prompt and image, **outputs** edited image."
        }
    }

    // MARK: - Multimodal Support

    /// Supported image formats for this API style
    var supportedImageFormats: Set<String> {
        switch self {
        case .google:
            return ["image/png", "image/jpeg", "image/webp", "image/heic", "image/heif"]
        case .openaiCompatible:
            return ["image/png", "image/jpeg", "image/webp", "image/gif"]
        case .anthropic:
            return ["image/jpeg", "image/png", "image/gif", "image/webp"]
        case .openaiImageGeneration, .openaiImageEdit:
            return ["image/png", "image/jpeg", "image/webp"]
        }
    }

    /// Supported document formats for this API style
    var supportedDocumentFormats: Set<String> {
        switch self {
        case .google:
            return ["application/pdf", "application/msword",
                   "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                   "text/plain", "text/markdown"]
        case .openaiCompatible:
            return ["application/pdf"]
        case .anthropic:
            return ["application/pdf"]
        case .openaiImageGeneration, .openaiImageEdit:
            return []
        }
    }

    /// Maximum file size limit in bytes for base64 uploads
    var maxFileSizeBytes: Int64 {
        switch self {
        case .google:
            return 20 * 1024 * 1024 // 20MB for base64
        case .openaiCompatible:
            return 20 * 1024 * 1024 // 20MB
        case .anthropic:
            return 5 * 1024 * 1024  // 5MB
        case .openaiImageGeneration, .openaiImageEdit:
            return 4 * 1024 * 1024  // 4MB
        }
    }

    /// Whether this API style supports multimodal input
    var supportsMultimodal: Bool {
        switch self {
        case .google, .openaiCompatible, .anthropic:
            return true
        case .openaiImageGeneration, .openaiImageEdit:
            return false
        }
    }
}