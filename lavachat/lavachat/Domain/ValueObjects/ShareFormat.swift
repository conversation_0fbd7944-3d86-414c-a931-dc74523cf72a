import Foundation

/// Represents different sharing formats and methods
enum ShareFormat: String, CaseIterable, Codable {
    case file = "file"
    case icloud = "icloud"
    case qrCode = "qr_code"
    
    var displayName: String {
        switch self {
        case .file:
            return "Save as File"
        case .icloud:
            return "Share via iCloud"
        case .qrCode:
            return "Generate QR Code"
        }
    }
    
    var systemImageName: String {
        switch self {
        case .file:
            return "doc.badge.plus"
        case .icloud:
            return "icloud.and.arrow.up"
        case .qrCode:
            return "qrcode"
        }
    }
    
    var description: String {
        switch self {
        case .file:
            return "Export as .lavachat file for offline sharing"
        case .icloud:
            return "Share via iCloud link with other LavaChat users"
        case .qrCode:
            return "Generate QR code for quick sharing"
        }
    }
}

/// Represents the type of content being shared
enum ShareContentType: String, Codable {
    case llmInstance = "llm_instance"
    case chatSession = "chat_session"
    case messageAction = "message_action"
    case chatSessionSetting = "chat_session_setting"
    
    var displayName: String {
        switch self {
        case .llmInstance:
            return "LLM Instance"
        case .chatSession:
            return "Chat Session"
        case .messageAction:
            return "Message Action"
        case .chatSessionSetting:
            return "Chat Setting"
        }
    }
}

/// Represents the result of a sharing operation
enum ShareResult {
    case success(ShareSuccessInfo)
    case failure(ShareError)
}

/// Information about a successful sharing operation
struct ShareSuccessInfo {
    let format: ShareFormat
    let contentType: ShareContentType
    let fileURL: URL?
    let icloudURL: URL?
    let qrCodeImage: Data?
    let metadata: [String: String]?
    
    init(
        format: ShareFormat,
        contentType: ShareContentType,
        fileURL: URL? = nil,
        icloudURL: URL? = nil,
        qrCodeImage: Data? = nil,
        metadata: [String: String]? = nil
    ) {
        self.format = format
        self.contentType = contentType
        self.fileURL = fileURL
        self.icloudURL = icloudURL
        self.qrCodeImage = qrCodeImage
        self.metadata = metadata
    }
}

/// Errors that can occur during sharing operations
enum ShareError: Error, LocalizedError {
    case invalidContent
    case missingDependencies(String)
    case fileCreationFailed(String)
    case icloudUnavailable
    case icloudShareFailed(String)
    case qrCodeGenerationFailed
    case permissionDenied
    case networkError(String)
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidContent:
            return "The content cannot be shared"
        case .missingDependencies(let details):
            return "Missing required dependencies: \(details)"
        case .fileCreationFailed(let details):
            return "Failed to create share file: \(details)"
        case .icloudUnavailable:
            return "iCloud is not available"
        case .icloudShareFailed(let details):
            return "iCloud sharing failed: \(details)"
        case .qrCodeGenerationFailed:
            return "Failed to generate QR code"
        case .permissionDenied:
            return "Permission denied"
        case .networkError(let details):
            return "Network error: \(details)"
        case .unknownError(let details):
            return "Unknown error: \(details)"
        }
    }
}

/// Represents the result of an import operation
enum ImportResult {
    case success(ImportSuccessInfo)
    case failure(ImportError)
}

/// Information about a successful import operation
struct ImportSuccessInfo {
    let contentType: ShareContentType
    let importedItemIds: [UUID]
    let conflictsResolved: [String]
    let skippedItemIds: [UUID]
    let metadata: [String: String]?

    init(
        contentType: ShareContentType,
        importedItemIds: [UUID],
        conflictsResolved: [String] = [],
        skippedItemIds: [UUID] = [],
        metadata: [String: String]? = nil
    ) {
        self.contentType = contentType
        self.importedItemIds = importedItemIds
        self.conflictsResolved = conflictsResolved
        self.skippedItemIds = skippedItemIds
        self.metadata = metadata
    }
}

/// Errors that can occur during import operations
enum ImportError: Error, LocalizedError {
    case invalidFileFormat
    case unsupportedVersion(String)
    case corruptedData
    case missingRequiredFields(String)
    case dependencyResolutionFailed(String)
    case duplicateContent
    case permissionDenied
    case networkError(String)
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidFileFormat:
            return "Invalid file format"
        case .unsupportedVersion(let version):
            return "Unsupported format version: \(version)"
        case .corruptedData:
            return "The file appears to be corrupted"
        case .missingRequiredFields(let fields):
            return "Missing required fields: \(fields)"
        case .dependencyResolutionFailed(let details):
            return "Failed to resolve dependencies: \(details)"
        case .duplicateContent:
            return "This content has already been imported"
        case .permissionDenied:
            return "Permission denied"
        case .networkError(let details):
            return "Network error: \(details)"
        case .unknownError(let details):
            return "Unknown error: \(details)"
        }
    }
}
