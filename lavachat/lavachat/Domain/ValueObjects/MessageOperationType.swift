import Foundation

/// Defines different types of message operations for unified SendMessageUseCase processing
enum MessageOperationType {
    /// Regular new message from user
    case newMessage

    /// Creating an edited version of an existing message
    case editMessage

    /// Regenerating an assistant's response
    case regenerateSingleMessage

    /// Regenerating an assistant's response with a custom prompt
    case regenerateSingleMessageWithPrompt

    /// Regenerating all assistant responses for a user message
    case regenerateAllMessages

    /// Rewrite prompt with LLM-processed prompt (internal operation, no persistence)
    case rewritePromptWithPrompt

    /// Generate chat title (internal operation, no persistence)
    case generateChatTitle
}

/// Extension to provide additional operation context
extension MessageOperationType {
    /// Returns true if this operation creates a new user message
    var createsUserMessage: Bool {
        switch self {
        case .newMessage, .editMessage, .regenerateSingleMessageWithPrompt, .rewritePromptWithPrompt, .generateChatTitle:
            return true
        case .regenerateSingleMessage, .regenerateAllMessages:
            return false
        }
    }

    /// Returns true if this operation should persist the user message to database
    var persistUserMessage: Bool {
        switch self {
        case .newMessage, .editMessage:
            return true
        case .regenerateSingleMessage, .regenerateSingleMessageWithPrompt, .regenerateAllMessages, .rewritePromptWithPrompt, .generateChatTitle:
            return false
        }
    }

    /// Returns true if this operation should drop the last message before sending
    // For regenerateSingleMessageWithPrompt and rewritePromptWithPrompt, we keep the original assistant message in history to be modified
    var dropLastMessageBeforeSend: Bool {
        switch self {
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateAllMessages:
            return true
        case .regenerateSingleMessageWithPrompt, .rewritePromptWithPrompt, .generateChatTitle:
            return false
        }
    }

    /// Returns true if this operation should skip user message creation
    var skipsUserMessageCreation: Bool {
        return self == .regenerateSingleMessage || self == .regenerateAllMessages
    }

    /// Returns true if this operation should delay adding placeholder messages to cache
    /// This is useful for regenerate operations where we need to get the original messageHistory first
    var delayAddPlaceholderMessagesToCache: Bool {
        switch self {
        case .regenerateSingleMessage, .regenerateSingleMessageWithPrompt:
            return true
        case .newMessage, .editMessage, .regenerateAllMessages, .rewritePromptWithPrompt, .generateChatTitle:
            return false
        }
    }

    /// Returns true if this operation should add the custom user message to the history before sending
    var addCustomUserMessageToHistoryBeforeSend: Bool {
        switch self {
        case .regenerateSingleMessageWithPrompt, .rewritePromptWithPrompt:
            return true
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateAllMessages, .generateChatTitle:
            return false
        }
    }

    /// Returns true if this operation should only take custom user message for the history before sending
    var onlyTakeCustomUserMessageForHistoryBeforeSend: Bool {
        switch self {
        case .generateChatTitle:
            return true
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateAllMessages, .regenerateSingleMessageWithPrompt, .rewritePromptWithPrompt:
            return false
        }
    }

    /// Returns true if this operation should skip all persistence operations (cache and repository)
    /// This is useful for internal operations that don't need to be saved
    var skipsPersistence: Bool {
        switch self {
        case .rewritePromptWithPrompt, .generateChatTitle:
            return true
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateSingleMessageWithPrompt, .regenerateAllMessages:
            return false
        }
    }

    /// Returns the number of additional messages to preserve beyond contextMessageCount
    /// This accounts for operation-specific messages that should always be kept
    var additionalMessagesToPreserve: Int {
        switch self {
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateAllMessages, .rewritePromptWithPrompt, .generateChatTitle:
            // These operations have messageHistory = real history + 1 operation-related user message
            // contextMessageCount applies to real history, preserve the 1 operation-related message
            return 1

        case .regenerateSingleMessageWithPrompt:
            // This operation has messageHistory = real history + regeneration-related messages + custom prompt
            // contextMessageCount applies to real history, preserve the 3 regeneration-related messages
            return 3
        }
    }

    /// Whether to force disable thinking for this operation type
    var forceDisableThinking: Bool {
        switch self {
        case .generateChatTitle:
            return true
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateSingleMessageWithPrompt, .regenerateAllMessages, .rewritePromptWithPrompt:
            return false
        }
    }

    /// Whether to force disable searching for this operation type
    var forceDisableSearching: Bool {
        switch self {
        case .generateChatTitle:
            return true
        case .newMessage, .editMessage, .regenerateSingleMessage, .regenerateSingleMessageWithPrompt, .regenerateAllMessages, .rewritePromptWithPrompt:
            return false
        }
    }
}