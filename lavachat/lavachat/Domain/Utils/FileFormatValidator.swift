import Foundation

/// Validates file formats and MIME types for multimodal content
struct FileFormatValidator {
    
    // MARK: - Format Validation
    
    /// Validates if an image format is supported by the specified API style
    /// - Parameters:
    ///   - mimeType: The MIME type to validate (e.g., "image/jpeg")
    ///   - apiStyle: The API style to check compatibility against
    /// - Returns: True if the format is supported, false otherwise
    static func isValidImageFormat(_ mimeType: String, for apiStyle: APIStyle) -> Bool {
        return apiStyle.supportedImageFormats.contains(mimeType.lowercased())
    }
    
    /// Validates if a document format is supported by the specified API style
    /// - Parameters:
    ///   - mimeType: The MIME type to validate (e.g., "application/pdf")
    ///   - apiStyle: The API style to check compatibility against
    /// - Returns: True if the format is supported, false otherwise
    static func isValidDocumentFormat(_ mimeType: String, for apiStyle: APIStyle) -> <PERSON><PERSON> {
        return apiStyle.supportedDocumentFormats.contains(mimeType.lowercased())
    }
    
    /// Validates if any file format (image or document) is supported
    /// - Parameters:
    ///   - mimeType: The MIME type to validate
    ///   - apiStyle: The API style to check compatibility against
    /// - Returns: True if the format is supported, false otherwise
    static func isValidFileFormat(_ mimeType: String, for apiStyle: APIStyle) -> Bool {
        return isValidImageFormat(mimeType, for: apiStyle) || 
               isValidDocumentFormat(mimeType, for: apiStyle)
    }
    
    // MARK: - MIME Type Utilities
    
    /// Determines the file type category from a MIME type
    /// - Parameter mimeType: The MIME type to categorize
    /// - Returns: The file type category
    static func fileType(from mimeType: String) -> FileType {
        let lowercasedType = mimeType.lowercased()
        
        if lowercasedType.hasPrefix("image/") {
            return .image
        } else if isDocumentMimeType(lowercasedType) {
            return .document
        } else {
            return .other
        }
    }
    
    /// Checks if a MIME type represents a document
    /// - Parameter mimeType: The MIME type to check
    /// - Returns: True if it's a document type, false otherwise
    static func isDocumentMimeType(_ mimeType: String) -> Bool {
        let documentTypes = [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
            "text/markdown",
            "text/csv",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ]
        
        return documentTypes.contains(mimeType.lowercased())
    }
    
    /// Validates if a MIME type string is well-formed
    /// - Parameter mimeType: The MIME type to validate
    /// - Returns: True if the MIME type is well-formed, false otherwise
    static func isValidMimeType(_ mimeType: String) -> Bool {
        // Basic MIME type pattern: type/subtype
        // Allow letters, numbers, and common special characters
        let mimeTypePattern = #"^[a-zA-Z][a-zA-Z0-9][a-zA-Z0-9\!\#\$\&\-\^\_\+\.]*\/[a-zA-Z0-9][a-zA-Z0-9\!\#\$\&\-\^\_\+\.]*$"#

        guard let regex = try? NSRegularExpression(pattern: mimeTypePattern) else {
            return false
        }

        let range = NSRange(location: 0, length: mimeType.utf16.count)
        return regex.firstMatch(in: mimeType, options: [], range: range) != nil
    }
    
    // MARK: - File Extension Utilities
    
    /// Gets the expected file extension for a MIME type
    /// - Parameter mimeType: The MIME type
    /// - Returns: The expected file extension (without dot) or nil if unknown
    static func fileExtension(for mimeType: String) -> String? {
        let mimeToExtension: [String: String] = [
            // Images
            "image/jpeg": "jpg",
            "image/jpg": "jpg",
            "image/png": "png",
            "image/gif": "gif",
            "image/webp": "webp",
            "image/heic": "heic",
            "image/heif": "heif",
            
            // Documents
            "application/pdf": "pdf",
            "application/msword": "doc",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
            "text/plain": "txt",
            "text/markdown": "md",
            "text/csv": "csv",
            "application/vnd.ms-excel": "xls",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx"
        ]
        
        return mimeToExtension[mimeType.lowercased()]
    }
    
    /// Gets the MIME type for a file extension
    /// - Parameter fileExtension: The file extension (without dot)
    /// - Returns: The MIME type or nil if unknown
    static func mimeType(for fileExtension: String) -> String? {
        let extensionToMime: [String: String] = [
            // Images
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "webp": "image/webp",
            "heic": "image/heic",
            "heif": "image/heif",
            
            // Documents
            "pdf": "application/pdf",
            "doc": "application/msword",
            "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "txt": "text/plain",
            "md": "text/markdown",
            "csv": "text/csv",
            "xls": "application/vnd.ms-excel",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ]
        
        return extensionToMime[fileExtension.lowercased()]
    }
    
    // MARK: - Validation with Error Results
    
    /// Validates file format and returns detailed error if invalid
    /// - Parameters:
    ///   - mimeType: The MIME type to validate
    ///   - apiStyle: The API style to check compatibility against
    /// - Returns: A FileProcessingError if validation fails, nil if valid
    static func validateFileFormat(_ mimeType: String, for apiStyle: APIStyle) -> FileProcessingError? {
        // First check if MIME type is well-formed
        guard isValidMimeType(mimeType) else {
            return .invalidMimeType(mimeType)
        }
        
        // Check if format is supported
        guard isValidFileFormat(mimeType, for: apiStyle) else {
            return .unsupportedFormat(mimeType: mimeType, apiStyle: apiStyle)
        }
        
        return nil
    }
}
