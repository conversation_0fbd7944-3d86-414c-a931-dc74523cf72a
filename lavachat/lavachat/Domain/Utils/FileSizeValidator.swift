import Foundation

/// Validates file sizes for multimodal content uploads
struct FileSizeValidator {
    
    // MARK: - Size Constants
    
    /// Minimum file size in bytes (1KB)
    static let minimumFileSizeBytes: Int64 = 1024
    
    /// Size threshold for compression consideration (1MB)
    static let compressionThresholdBytes: Int64 = 1024 * 1024
    
    // MARK: - Size Validation
    
    /// Checks if file size is within the allowed limit for the API style
    /// - Parameters:
    ///   - sizeBytes: The file size in bytes
    ///   - apiStyle: The API style to check limits against
    /// - Returns: True if within limit, false otherwise
    static func isWithinSizeLimit(_ sizeBytes: Int64, for apiStyle: APIStyle) -> Bool {
        return sizeBytes <= apiStyle.maxFileSizeBytes
    }
    
    /// Checks if file size meets the minimum requirement
    /// - Parameter sizeBytes: The file size in bytes
    /// - Returns: True if meets minimum, false otherwise
    static func meetsMinimumSize(_ sizeBytes: Int64) -> Bool {
        return sizeBytes >= minimumFileSizeBytes
    }
    
    /// Determines if a file needs compression based on size and API limits
    /// - Parameters:
    ///   - sizeBytes: The file size in bytes
    ///   - apiStyle: The API style to check limits against
    /// - Returns: True if compression is needed, false otherwise
    static func needsCompression(_ sizeBytes: Int64, for apiStyle: APIStyle) -> Bool {
        return sizeBytes > apiStyle.maxFileSizeBytes
    }
    
    /// Determines if a file should be compressed for optimization
    /// - Parameters:
    ///   - sizeBytes: The file size in bytes
    ///   - apiStyle: The API style to check limits against
    /// - Returns: True if compression is recommended, false otherwise
    static func shouldCompress(_ sizeBytes: Int64, for apiStyle: APIStyle) -> Bool {
        // Compress if file is larger than threshold and close to the limit
        let threshold = min(compressionThresholdBytes, apiStyle.maxFileSizeBytes / 2)
        return sizeBytes > threshold
    }
    
    // MARK: - Size Calculations
    
    /// Calculates the target size for compression
    /// - Parameters:
    ///   - originalSize: The original file size in bytes
    ///   - apiStyle: The API style to target
    /// - Returns: The target size in bytes (80% of max limit)
    static func targetCompressionSize(for apiStyle: APIStyle) -> Int64 {
        return Int64(Double(apiStyle.maxFileSizeBytes) * 0.8)
    }
    
    /// Estimates compression ratio needed to meet size requirements
    /// - Parameters:
    ///   - originalSize: The original file size in bytes
    ///   - apiStyle: The API style to target
    /// - Returns: The compression ratio (0.0 to 1.0) needed
    static func requiredCompressionRatio(originalSize: Int64, for apiStyle: APIStyle) -> Double {
        let targetSize = targetCompressionSize(for: apiStyle)
        return Double(targetSize) / Double(originalSize)
    }
    
    // MARK: - Human Readable Sizes
    
    /// Converts bytes to human-readable format
    /// - Parameter bytes: The size in bytes
    /// - Returns: Human-readable size string (e.g., "1.5 MB")
    static func humanReadableSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: bytes)
    }
    
    /// Converts bytes to megabytes with decimal precision
    /// - Parameter bytes: The size in bytes
    /// - Returns: Size in megabytes as a formatted string
    static func megabytes(_ bytes: Int64) -> String {
        let mb = Double(bytes) / (1024.0 * 1024.0)
        return String(format: "%.1f MB", mb)
    }
    
    // MARK: - Validation with Error Results
    
    /// Validates file size and returns detailed error if invalid
    /// - Parameters:
    ///   - sizeBytes: The file size in bytes
    ///   - apiStyle: The API style to check limits against
    /// - Returns: A FileProcessingError if validation fails, nil if valid
    static func validateFileSize(_ sizeBytes: Int64, for apiStyle: APIStyle) -> FileProcessingError? {
        // Check minimum size
        guard meetsMinimumSize(sizeBytes) else {
            return .fileTooSmall(actualSize: sizeBytes, minSize: minimumFileSizeBytes)
        }
        
        // Check maximum size
        guard isWithinSizeLimit(sizeBytes, for: apiStyle) else {
            return .fileTooLarge(actualSize: sizeBytes, maxSize: apiStyle.maxFileSizeBytes)
        }
        
        return nil
    }
    
    /// Comprehensive size validation with detailed feedback
    /// - Parameters:
    ///   - sizeBytes: The file size in bytes
    ///   - apiStyle: The API style to check limits against
    /// - Returns: Validation result with recommendations
    static func validateSizeWithRecommendations(_ sizeBytes: Int64, for apiStyle: APIStyle) -> SizeValidationResult {
        if let error = validateFileSize(sizeBytes, for: apiStyle) {
            return SizeValidationResult(
                isValid: false,
                error: error,
                needsCompression: needsCompression(sizeBytes, for: apiStyle),
                shouldCompress: false,
                targetSize: nil
            )
        }
        
        let shouldCompress = shouldCompress(sizeBytes, for: apiStyle)
        let targetSize = shouldCompress ? targetCompressionSize(for: apiStyle) : nil
        
        return SizeValidationResult(
            isValid: true,
            error: nil,
            needsCompression: false,
            shouldCompress: shouldCompress,
            targetSize: targetSize
        )
    }
}

// MARK: - Supporting Types

/// Result of size validation with recommendations
struct SizeValidationResult {
    let isValid: Bool
    let error: FileProcessingError?
    let needsCompression: Bool
    let shouldCompress: Bool
    let targetSize: Int64?
    
    /// Human-readable recommendation message
    var recommendation: String? {
        if let error = error {
            return error.recoverySuggestion
        } else if shouldCompress {
            if let targetSize = targetSize {
                return "Consider compressing to \(FileSizeValidator.humanReadableSize(targetSize)) for better performance"
            } else {
                return "Consider compressing for better performance"
            }
        }
        return nil
    }
}
