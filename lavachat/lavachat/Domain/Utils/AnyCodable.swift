import Foundation

/// A type-erased wrapper for `Codable` values.
/// This allows storing and encoding/decoding values of different types in a uniform way.
struct AnyCodable: Codable, Equatable, Hashable {
    let value: Any

    // MARK: - Initialization
    
    init<T>(_ value: T?) {
        self.value = value ?? ()
    }
    
    init<T: Codable>(_ value: T) {
        self.value = value
    }
    
    // MARK: - Codable Implementation
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        // Try to decode as different types in order of specificity
        if container.decodeNil() {
            self.value = ()
        } else if let bool = try? container.decode(Bool.self) {
            self.value = bool
        } else if let int = try? container.decode(Int.self) {
            self.value = int
        } else if let int8 = try? container.decode(Int8.self) {
            self.value = int8
        } else if let int16 = try? container.decode(Int16.self) {
            self.value = int16
        } else if let int32 = try? container.decode(Int32.self) {
            self.value = int32
        } else if let int64 = try? container.decode(Int64.self) {
            self.value = int64
        } else if let uint = try? container.decode(UInt.self) {
            self.value = uint
        } else if let uint8 = try? container.decode(UInt8.self) {
            self.value = uint8
        } else if let uint16 = try? container.decode(UInt16.self) {
            self.value = uint16
        } else if let uint32 = try? container.decode(UInt32.self) {
            self.value = uint32
        } else if let uint64 = try? container.decode(UInt64.self) {
            self.value = uint64
        } else if let float = try? container.decode(Float.self) {
            self.value = float
        } else if let double = try? container.decode(Double.self) {
            self.value = double
        } else if let string = try? container.decode(String.self) {
            self.value = string
        } else if let array = try? container.decode([AnyCodable].self) {
            self.value = array.map(\.value)
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            self.value = dictionary.mapValues(\.value)
        } else {
            throw DecodingError.dataCorruptedError(
                in: container,
                debugDescription: "AnyCodable value cannot be decoded"
            )
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case is Void:
            try container.encodeNil()
        case let bool as Bool:
            try container.encode(bool)
        case let int as Int:
            try container.encode(int)
        case let int8 as Int8:
            try container.encode(int8)
        case let int16 as Int16:
            try container.encode(int16)
        case let int32 as Int32:
            try container.encode(int32)
        case let int64 as Int64:
            try container.encode(int64)
        case let uint as UInt:
            try container.encode(uint)
        case let uint8 as UInt8:
            try container.encode(uint8)
        case let uint16 as UInt16:
            try container.encode(uint16)
        case let uint32 as UInt32:
            try container.encode(uint32)
        case let uint64 as UInt64:
            try container.encode(uint64)
        case let float as Float:
            try container.encode(float)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let array as [Any]:
            let anyCodableArray = array.map(AnyCodable.init)
            try container.encode(anyCodableArray)
        case let dictionary as [String: Any]:
            let anyCodableDictionary = dictionary.mapValues(AnyCodable.init)
            try container.encode(anyCodableDictionary)
        case let codable as Codable:
            try codable.encode(to: encoder)
        default:
            let context = EncodingError.Context(
                codingPath: container.codingPath,
                debugDescription: "AnyCodable value cannot be encoded"
            )
            throw EncodingError.invalidValue(value, context)
        }
    }
    
    // MARK: - Equatable Implementation
    
    static func == (lhs: AnyCodable, rhs: AnyCodable) -> Bool {
        switch (lhs.value, rhs.value) {
        case is (Void, Void):
            return true
        case let (lhs as Bool, rhs as Bool):
            return lhs == rhs
        case let (lhs as Int, rhs as Int):
            return lhs == rhs
        case let (lhs as Int8, rhs as Int8):
            return lhs == rhs
        case let (lhs as Int16, rhs as Int16):
            return lhs == rhs
        case let (lhs as Int32, rhs as Int32):
            return lhs == rhs
        case let (lhs as Int64, rhs as Int64):
            return lhs == rhs
        case let (lhs as UInt, rhs as UInt):
            return lhs == rhs
        case let (lhs as UInt8, rhs as UInt8):
            return lhs == rhs
        case let (lhs as UInt16, rhs as UInt16):
            return lhs == rhs
        case let (lhs as UInt32, rhs as UInt32):
            return lhs == rhs
        case let (lhs as UInt64, rhs as UInt64):
            return lhs == rhs
        case let (lhs as Float, rhs as Float):
            return lhs == rhs
        case let (lhs as Double, rhs as Double):
            return lhs == rhs
        case let (lhs as String, rhs as String):
            return lhs == rhs
        case let (lhs as [String: Any], rhs as [String: Any]):
            return NSDictionary(dictionary: lhs).isEqual(to: rhs)
        case let (lhs as [Any], rhs as [Any]):
            return NSArray(array: lhs).isEqual(NSArray(array: rhs))
        default:
            return false
        }
    }
    
    // MARK: - Hashable Implementation
    
    func hash(into hasher: inout Hasher) {
        switch value {
        case let bool as Bool:
            hasher.combine(bool)
        case let int as Int:
            hasher.combine(int)
        case let int8 as Int8:
            hasher.combine(int8)
        case let int16 as Int16:
            hasher.combine(int16)
        case let int32 as Int32:
            hasher.combine(int32)
        case let int64 as Int64:
            hasher.combine(int64)
        case let uint as UInt:
            hasher.combine(uint)
        case let uint8 as UInt8:
            hasher.combine(uint8)
        case let uint16 as UInt16:
            hasher.combine(uint16)
        case let uint32 as UInt32:
            hasher.combine(uint32)
        case let uint64 as UInt64:
            hasher.combine(uint64)
        case let float as Float:
            hasher.combine(float)
        case let double as Double:
            hasher.combine(double)
        case let string as String:
            hasher.combine(string)
        case let array as [AnyHashable]:
            hasher.combine(array)
        case let dictionary as [String: AnyHashable]:
            hasher.combine(dictionary)
        default:
            hasher.combine(0) // Fallback for non-hashable types
        }
    }
}

// MARK: - Convenience Extensions

extension AnyCodable {
    /// Returns the wrapped value as the specified type, or nil if the cast fails
    func value<T>(as type: T.Type) -> T? {
        return value as? T
    }
    
    /// Returns the wrapped value as a String, or nil if it's not a string
    var stringValue: String? {
        return value as? String
    }
    
    /// Returns the wrapped value as an Int, attempting conversion from various numeric types
    var intValue: Int? {
        switch value {
        case let int as Int:
            return int
        case let int8 as Int8:
            return Int(int8)
        case let int16 as Int16:
            return Int(int16)
        case let int32 as Int32:
            return Int(int32)
        case let int64 as Int64:
            return Int(exactly: int64)
        case let uint as UInt:
            return Int(exactly: uint)
        case let uint8 as UInt8:
            return Int(uint8)
        case let uint16 as UInt16:
            return Int(uint16)
        case let uint32 as UInt32:
            return Int(exactly: uint32)
        case let uint64 as UInt64:
            return Int(exactly: uint64)
        case let float as Float:
            // Only convert if it's a whole number within Int range
            guard float.isFinite && float == Float(Int(float)) else { return nil }
            return Int(float)
        case let double as Double:
            // Only convert if it's a whole number within Int range
            guard double.isFinite && double == Double(Int(double)) else { return nil }
            return Int(double)
        case let string as String:
            return Int(string)
        default:
            return nil
        }
    }
    
    /// Returns the wrapped value as an Int64, attempting conversion from various numeric types
    var int64Value: Int64? {
        switch value {
        case let int as Int:
            return Int64(int)
        case let int8 as Int8:
            return Int64(int8)
        case let int16 as Int16:
            return Int64(int16)
        case let int32 as Int32:
            return Int64(int32)
        case let int64 as Int64:
            return int64
        case let uint as UInt:
            return Int64(exactly: uint)
        case let uint8 as UInt8:
            return Int64(uint8)
        case let uint16 as UInt16:
            return Int64(uint16)
        case let uint32 as UInt32:
            return Int64(uint32)
        case let uint64 as UInt64:
            return Int64(exactly: uint64)
        case let float as Float:
            // Only convert if it's a whole number within Int64 range
            guard float.isFinite && float == Float(Int64(float)) else { return nil }
            return Int64(float)
        case let double as Double:
            // Only convert if it's a whole number within Int64 range
            guard double.isFinite && double == Double(Int64(double)) else { return nil }
            return Int64(double)
        case let string as String:
            return Int64(string)
        default:
            return nil
        }
    }
    
    /// Returns the wrapped value as a Double, attempting conversion from various numeric types
    var doubleValue: Double? {
        switch value {
        case let int as Int:
            return Double(int)
        case let int8 as Int8:
            return Double(int8)
        case let int16 as Int16:
            return Double(int16)
        case let int32 as Int32:
            return Double(int32)
        case let int64 as Int64:
            return Double(int64)
        case let uint as UInt:
            return Double(uint)
        case let uint8 as UInt8:
            return Double(uint8)
        case let uint16 as UInt16:
            return Double(uint16)
        case let uint32 as UInt32:
            return Double(uint32)
        case let uint64 as UInt64:
            return Double(uint64)
        case let float as Float:
            return Double(float)
        case let double as Double:
            return double
        case let string as String:
            return Double(string)
        default:
            return nil
        }
    }
    
    /// Returns the wrapped value as a Bool, attempting conversion from various types
    var boolValue: Bool? {
        switch value {
        case let bool as Bool:
            return bool
        case let int as Int:
            return int != 0
        case let int8 as Int8:
            return int8 != 0
        case let int16 as Int16:
            return int16 != 0
        case let int32 as Int32:
            return int32 != 0
        case let int64 as Int64:
            return int64 != 0
        case let uint as UInt:
            return uint != 0
        case let uint8 as UInt8:
            return uint8 != 0
        case let uint16 as UInt16:
            return uint16 != 0
        case let uint32 as UInt32:
            return uint32 != 0
        case let uint64 as UInt64:
            return uint64 != 0
        case let float as Float:
            return float != 0.0
        case let double as Double:
            return double != 0.0
        case let string as String:
            let lowercased = string.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
            if lowercased == "true" || lowercased == "1" || lowercased == "yes" {
                return true
            } else if lowercased == "false" || lowercased == "0" || lowercased == "no" {
                return false
            } else {
                return nil
            }
        default:
            return nil
        }
    }
    
    /// Returns the wrapped value as an array, or nil if it's not an array
    var arrayValue: [Any]? {
        return value as? [Any]
    }
    
    /// Returns the wrapped value as a dictionary, or nil if it's not a dictionary
    var dictionaryValue: [String: Any]? {
        return value as? [String: Any]
    }
    
    /// Returns true if the wrapped value is nil (Void)
    var isNil: Bool {
        return value is Void
    }
    
    /// Returns a safe Int value with fallback to default
    /// - Parameter defaultValue: The default value to return if conversion fails
    /// - Returns: The converted Int value or the default value
    func safeIntValue(default defaultValue: Int = 0) -> Int {
        return intValue ?? defaultValue
    }
    
    /// Returns a safe Double value with fallback to default
    /// - Parameter defaultValue: The default value to return if conversion fails
    /// - Returns: The converted Double value or the default value
    func safeDoubleValue(default defaultValue: Double = 0.0) -> Double {
        return doubleValue ?? defaultValue
    }
    
    /// Returns a safe Bool value with fallback to default
    /// - Parameter defaultValue: The default value to return if conversion fails
    /// - Returns: The converted Bool value or the default value
    func safeBoolValue(default defaultValue: Bool = false) -> Bool {
        return boolValue ?? defaultValue
    }
    
    /// Returns a safe String value with fallback to default
    /// - Parameter defaultValue: The default value to return if conversion fails
    /// - Returns: The converted String value or the default value
    func safeStringValue(default defaultValue: String = "") -> String {
        return stringValue ?? String(describing: value)
    }
}

// MARK: - ExpressibleBy Literals

extension AnyCodable: ExpressibleByNilLiteral {
    init(nilLiteral: ()) {
        self.value = ()
    }
}

extension AnyCodable: ExpressibleByBooleanLiteral {
    init(booleanLiteral value: Bool) {
        self.value = value
    }
}

extension AnyCodable: ExpressibleByIntegerLiteral {
    init(integerLiteral value: Int) {
        self.value = value
    }
}

extension AnyCodable: ExpressibleByFloatLiteral {
    init(floatLiteral value: Double) {
        self.value = value
    }
}

extension AnyCodable: ExpressibleByStringLiteral {
    init(stringLiteral value: String) {
        self.value = value
    }
}

extension AnyCodable: ExpressibleByArrayLiteral {
    init(arrayLiteral elements: AnyCodable...) {
        self.value = elements.map(\.value)
    }
}

extension AnyCodable: ExpressibleByDictionaryLiteral {
    init(dictionaryLiteral elements: (String, AnyCodable)...) {
        self.value = Dictionary(elements.map { ($0.0, $0.1.value) }, uniquingKeysWith: { first, _ in first })
    }
}

// MARK: - Smart String Parsing

extension AnyCodable {
    
    /// Intelligently parse a string value and convert it to the most appropriate AnyCodable type
    /// - Parameter string: The string value to parse
    /// - Returns: AnyCodable with the most appropriate type (Bool, Int64, Double, JSON Object/Array, or String)
    ///
    /// Type inference rules (in order of priority):
    /// 1. Empty/whitespace → String
    /// 2. JSON Object/Array → Dictionary/Array (if valid JSON)
    /// 3. "true"/"false" (case-insensitive) → Bool
    /// 4. "null"/"nil" (case-insensitive) → nil
    /// 5. Pure integer format → Int64
    /// 6. Floating point format → Double
    /// 7. Everything else → String
    static func fromString(_ string: String) -> AnyCodable {
        let trimmed = string.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Handle empty or whitespace-only strings
        if trimmed.isEmpty {
            return AnyCodable(string)
        }
        
        // Handle JSON objects and arrays (new functionality)
        if isJSONString(trimmed) {
            if let jsonValue = parseJSONString(trimmed) {
                return AnyCodable(jsonValue)
            }
            // If JSON parsing fails, continue with other type checks
        }
        
        // Handle boolean values (case-insensitive)
        if isBool(trimmed) {
            return AnyCodable(trimmed.lowercased() == "true")
        }
        
        // Handle null/nil values (case-insensitive)
        let lowercased = trimmed.lowercased()
        if lowercased == "null" || lowercased == "nil" {
            return AnyCodable(())
        }
        
        // Handle integer values
        if isInteger(trimmed) {
            if let intValue = Int64(trimmed) {
                return AnyCodable(intValue)
            }
        }
        
        // Handle double values
        if isDouble(trimmed) {
            if let doubleValue = Double(trimmed) {
                // Check for special values
                if doubleValue.isNaN || doubleValue.isInfinite {
                    return AnyCodable(string) // Keep as string for special values
                }
                return AnyCodable(doubleValue)
            }
        }
        
        // Default to string
        return AnyCodable(string)
    }
    
    /// Convert a dictionary of string values to AnyCodable values using intelligent parsing
    /// - Parameter dict: Dictionary with string keys and string values
    /// - Returns: Dictionary with string keys and AnyCodable values
    static func fromStringDictionary(_ dict: [String: String]) -> [String: AnyCodable] {
        return dict.mapValues { fromString($0) }
    }
    
    // MARK: - JSON Detection and Parsing
    
    /// Check if a string represents a JSON object or array
    /// - Parameter string: The string to check
    /// - Returns: True if the string appears to be JSON format
    private static func isJSONString(_ string: String) -> Bool {
        // Basic format check: must start and end with proper JSON delimiters
        let hasObjectDelimiters = string.hasPrefix("{") && string.hasSuffix("}")
        let hasArrayDelimiters = string.hasPrefix("[") && string.hasSuffix("]")
        
        guard hasObjectDelimiters || hasArrayDelimiters else {
            return false
        }
        
        // For JSON objects: empty object or should contain quotes and colons
        if hasObjectDelimiters {
            // Empty object is valid JSON
            if string == "{}" {
                return true
            }
            // Non-empty objects should contain quotes and colons
            return string.contains("\"") && string.contains(":")
        }
        
        // For JSON arrays: be more permissive - arrays can contain various valid JSON elements
        if hasArrayDelimiters {
            // Empty array is valid JSON
            if string == "[]" {
                return true
            }
            
            // If it contains quotes, commas, or JSON-like content, consider it JSON
            if string.contains("\"") || string.contains(",") {
                return true
            }
            
            // Check for numeric arrays like [1, 2, 3] or boolean arrays [true, false]
            let inner = String(string.dropFirst().dropLast()).trimmingCharacters(in: .whitespacesAndNewlines)
            if !inner.isEmpty {
                // Split by comma and check if elements look like JSON values
                let elements = inner.split(separator: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                return elements.allSatisfy { element in
                    // Check if element looks like a valid JSON primitive
                    return element == "true" || element == "false" || element == "null" ||
                           Double(element) != nil || Int(element) != nil ||
                           (element.hasPrefix("\"") && element.hasSuffix("\"")) ||
                           element.hasPrefix("{") || element.hasPrefix("[")
                }
            }
        }
        
        return false
    }
    
    /// Parse a JSON string into appropriate Swift types
    /// - Parameter string: The JSON string to parse
    /// - Returns: Parsed value (Dictionary, Array, or nil if parsing fails)
    private static func parseJSONString(_ string: String) -> Any? {
        guard let data = string.data(using: .utf8) else {
            return nil
        }
        
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            return jsonObject
        } catch {
            // JSON parsing failed - this is expected for invalid JSON strings
            return nil
        }
    }
    
    // MARK: - Private Helper Methods
    
    /// Check if a string represents a boolean value
    private static func isBool(_ string: String) -> Bool {
        let lowercased = string.lowercased()
        return lowercased == "true" || lowercased == "false"
    }
    
    /// Check if a string represents an integer value
    private static func isInteger(_ string: String) -> Bool {
        // Handle negative numbers
        let cleanString = string.hasPrefix("-") ? String(string.dropFirst()) : string
        
        // Check if all remaining characters are digits
        return !cleanString.isEmpty && cleanString.allSatisfy { $0.isNumber }
    }
    
    /// Check if a string represents a double value
    private static func isDouble(_ string: String) -> Bool {
        // Use Double's built-in parsing as the most reliable check
        // But exclude pure integers (they should be handled as Int64)
        guard !isInteger(string) else { return false }
        
        // Check for basic floating point patterns
        let hasDecimalPoint = string.contains(".")
        let hasExponential = string.lowercased().contains("e")
        
        // Must have either decimal point or exponential notation
        guard hasDecimalPoint || hasExponential else { return false }
        
        // Verify it can actually be parsed as Double
        return Double(string) != nil
    }
}

// MARK: - Dynamic Coding Key

/// Dynamic coding key for encoding/decoding arbitrary parameter names
struct DynamicCodingKey: CodingKey {
    let stringValue: String
    let intValue: Int?
    
    init?(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }
    
    init?(intValue: Int) {
        self.stringValue = "\(intValue)"
        self.intValue = intValue
    }
}