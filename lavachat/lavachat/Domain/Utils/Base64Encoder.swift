import Foundation

/// Handles base64 encoding and decoding for file data
struct Base64Encoder {
    
    // MARK: - Encoding
    
    /// Encodes data to base64 string
    /// - Parameter data: The data to encode
    /// - Returns: Base64 encoded string
    /// - Throws: FileProcessingError if encoding fails
    static func encode(_ data: Data) throws -> String {
        guard !data.isEmpty else {
            throw FileProcessingError.encodingFailed(reason: "Data is empty")
        }
        
        return data.base64EncodedString()
    }
    
    /// Encodes data to base64 string with MIME type prefix for data URLs
    /// - Parameters:
    ///   - data: The data to encode
    ///   - mimeType: The MIME type for the data URL prefix
    /// - Returns: Data URL string (e.g., "data:image/jpeg;base64,...")
    /// - Throws: FileProcessingError if encoding fails
    static func encodeAsDataURL(_ data: Data, mimeType: String) throws -> String {
        let base64String = try encode(data)
        return "data:\(mimeType);base64,\(base64String)"
    }
    
    /// Encodes data with chunking for large files
    /// - Parameters:
    ///   - data: The data to encode
    ///   - chunkSize: The size of each chunk (default: 1MB)
    /// - Returns: Base64 encoded string
    /// - Throws: FileProcessingError if encoding fails
    static func encodeChunked(_ data: Data, chunkSize: Int = 1024 * 1024) throws -> String {
        guard !data.isEmpty else {
            throw FileProcessingError.encodingFailed(reason: "Data is empty")
        }

        guard chunkSize > 0 else {
            throw FileProcessingError.encodingFailed(reason: "Chunk size must be positive")
        }

        // For proper base64 encoding, we need to ensure chunks are multiples of 3 bytes
        // to avoid padding issues in the middle of the encoding
        let adjustedChunkSize = (chunkSize / 3) * 3
        let finalChunkSize = adjustedChunkSize > 0 ? adjustedChunkSize : 3

        var result = ""
        var offset = 0

        while offset < data.count {
            let endIndex = min(offset + finalChunkSize, data.count)
            let chunk = data.subdata(in: offset..<endIndex)
            result += chunk.base64EncodedString()
            offset = endIndex
        }

        return result
    }
    
    // MARK: - Decoding
    
    /// Decodes base64 string to data
    /// - Parameter base64String: The base64 string to decode
    /// - Returns: Decoded data
    /// - Throws: FileProcessingError if decoding fails
    static func decode(_ base64String: String) throws -> Data {
        guard !base64String.isEmpty else {
            throw FileProcessingError.decodingFailed(reason: "Base64 string is empty")
        }
        
        // Clean the string (remove whitespace and newlines)
        let cleanedString = base64String.replacingOccurrences(of: "\\s", with: "", options: .regularExpression)
        
        guard let data = Data(base64Encoded: cleanedString) else {
            throw FileProcessingError.decodingFailed(reason: "Invalid base64 format")
        }
        
        return data
    }
    
    /// Decodes data URL to data and extracts MIME type
    /// - Parameter dataURL: The data URL string (e.g., "data:image/jpeg;base64,...")
    /// - Returns: Tuple containing the decoded data and MIME type
    /// - Throws: FileProcessingError if decoding fails
    static func decodeDataURL(_ dataURL: String) throws -> (data: Data, mimeType: String) {
        guard dataURL.hasPrefix("data:") else {
            throw FileProcessingError.decodingFailed(reason: "Not a valid data URL")
        }
        
        let components = dataURL.components(separatedBy: ",")
        guard components.count == 2 else {
            throw FileProcessingError.decodingFailed(reason: "Invalid data URL format")
        }
        
        let header = components[0]
        let base64Data = components[1]
        
        // Extract MIME type from header (e.g., "data:image/jpeg;base64")
        let headerParts = header.components(separatedBy: ";")
        guard headerParts.count >= 2,
              headerParts[0].hasPrefix("data:"),
              headerParts[1] == "base64" else {
            throw FileProcessingError.decodingFailed(reason: "Invalid data URL header")
        }
        
        let mimeType = String(headerParts[0].dropFirst(5)) // Remove "data:" prefix
        
        guard FileFormatValidator.isValidMimeType(mimeType) else {
            throw FileProcessingError.invalidMimeType(mimeType)
        }
        
        let data = try decode(base64Data)
        
        return (data: data, mimeType: mimeType)
    }
    
    // MARK: - Validation
    
    /// Validates if a string is valid base64
    /// - Parameter string: The string to validate
    /// - Returns: True if valid base64, false otherwise
    static func isValidBase64(_ string: String) -> Bool {
        guard !string.isEmpty else { return false }
        
        let cleanedString = string.replacingOccurrences(of: "\\s", with: "", options: .regularExpression)
        return Data(base64Encoded: cleanedString) != nil
    }
    
    /// Validates if a string is a valid data URL
    /// - Parameter string: The string to validate
    /// - Returns: True if valid data URL, false otherwise
    static func isValidDataURL(_ string: String) -> Bool {
        do {
            _ = try decodeDataURL(string)
            return true
        } catch {
            return false
        }
    }
    
    // MARK: - Size Estimation
    
    /// Estimates the base64 encoded size for given data size
    /// - Parameter dataSize: The original data size in bytes
    /// - Returns: Estimated base64 encoded size in bytes
    static func estimateBase64Size(for dataSize: Int64) -> Int64 {
        // Base64 encoding increases size by approximately 33% (4/3 ratio)
        // Plus padding and potential line breaks
        return Int64(ceil(Double(dataSize) * 4.0 / 3.0)) + 4
    }
    
    /// Estimates the original data size from base64 string length
    /// - Parameter base64Length: The length of the base64 string
    /// - Returns: Estimated original data size in bytes
    static func estimateOriginalSize(from base64Length: Int) -> Int64 {
        // Reverse calculation: base64 to original size
        return Int64(Double(base64Length) * 3.0 / 4.0)
    }
    
    // MARK: - Utility Methods
    
    /// Creates an ImageInfo with base64 data from raw data
    /// - Parameters:
    ///   - data: The image data
    ///   - mimeType: The MIME type of the image
    ///   - caption: Optional caption for the image
    /// - Returns: ImageInfo with base64 data populated
    /// - Throws: FileProcessingError if encoding fails
    static func createImageInfo(from data: Data, mimeType: String, caption: String? = nil) throws -> ImageInfo {
        let base64Data = try encode(data)
        
        return ImageInfo(
            url: nil,
            caption: caption,
            data: data,
            mimeType: mimeType,
            base64Data: base64Data,
            fileSizeBytes: Int64(data.count)
        )
    }
    
    /// Creates a FileInfo with base64 data from raw data
    /// - Parameters:
    ///   - data: The file data
    ///   - fileName: The name of the file
    ///   - mimeType: The MIME type of the file
    /// - Returns: FileInfo with base64 data populated
    /// - Throws: FileProcessingError if encoding fails
    static func createFileInfo(from data: Data, fileName: String, mimeType: String) throws -> FileInfo {
        let base64Data = try encode(data)
        let fileType = FileFormatValidator.fileType(from: mimeType)
        
        return FileInfo(
            fileName: fileName,
            mimeType: mimeType,
            base64Data: base64Data,
            fileSizeBytes: Int64(data.count),
            fileType: fileType
        )
    }
}
