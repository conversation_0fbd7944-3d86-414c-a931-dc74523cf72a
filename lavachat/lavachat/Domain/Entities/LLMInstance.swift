import Foundation

struct LLMInstance: Identifiable, Codable, Equatable, Hashable {
    let id: UUID
    var modelId: UUID // Reference to the base LLMModel
    var name: String // User-defined name for this instance
    var customLogoData: Data? // Custom logo image data
    var systemPrompt: String? // System prompt for this instance
    var defaultParameters: [String: String]? // Default prompt, temp, etc. Values as Strings for now
    var totalPromptTokensUsed: Int64 = 0
    var totalCompletionTokensUsed: Int64 = 0
    let createdAt: Date
    var lastUsedAt: Date?
    var isFavorited: Bool = false // Marked as favorite by user
    var isUserModified: Bool // True if the user has modified this instance
    var metadata: [String: String]? // Custom icon, tags, future sharing info

    init(
        id: UUID = UUID(),
        modelId: UUID,
        name: String,
        customLogoData: Data? = nil,
        systemPrompt: String? = nil,
        defaultParameters: [String : String]? = nil,
        totalPromptTokensUsed: Int64 = 0,
        totalCompletionTokensUsed: Int64 = 0,
        createdAt: Date = Date(),
        lastUsedAt: Date? = nil,
        isFavorited: Bool = false,
        isUserModified: Bool = false,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.modelId = modelId
        self.name = name
        self.customLogoData = customLogoData
        self.systemPrompt = systemPrompt
        self.defaultParameters = defaultParameters
        self.totalPromptTokensUsed = totalPromptTokensUsed
        self.totalCompletionTokensUsed = totalCompletionTokensUsed
        self.createdAt = createdAt
        self.lastUsedAt = lastUsedAt
        self.isFavorited = isFavorited
        self.isUserModified = isUserModified
        self.metadata = metadata
    }

    // Hashable conformance needed if used in Sets or Dictionary keys
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: LLMInstance, rhs: LLMInstance) -> Bool {
        lhs.id == rhs.id &&
        lhs.modelId == rhs.modelId &&
        lhs.name == rhs.name &&
        lhs.customLogoData == rhs.customLogoData &&
        lhs.systemPrompt == rhs.systemPrompt &&
        lhs.defaultParameters == rhs.defaultParameters &&
        lhs.isFavorited == rhs.isFavorited &&
        lhs.isUserModified == rhs.isUserModified &&
        lhs.metadata == rhs.metadata
        // Note: We exclude usage stats (totalPromptTokensUsed, totalCompletionTokensUsed)
        // and timestamps (createdAt, lastUsedAt) from equality comparison
        // as they represent state rather than configuration
    }

    // MARK: - Factory Methods

    /// Creates a default instance for a given model with appropriate default parameters
    /// This method contains the shared logic from BuiltinLLMInitializer.getBuiltinInstanceForModel
    /// but excludes the deterministic UUID generation part
    static func createDefaultInstance(
        for model: LLMModel,
        provider: LLMProvider? = nil,
        instanceId: UUID = UUID(),
        isUserModified: Bool = true
    ) -> LLMInstance {
        // Default system prompt based on model
        let systemPrompt = "You are a helpful assistant."

        // Default parameters based on the model type
        var defaultParameters: [String: String] = [:]

        // Set default temperature depending on the model style
        defaultParameters["temperature"] = "0.7"

        // Set thinking-related parameters for models with thinking capabilities
        if let thinkingCapabilities = model.thinkingCapabilities,
            thinkingCapabilities.controlType != ThinkingCapabilities.ControlType.none {
            switch thinkingCapabilities.controlType {
            case .reasoningEffort:
                if let paramName = thinkingCapabilities.parameterName,
                let options = thinkingCapabilities.parameterOptions,
                !options.isEmpty {
                    // Use the highest reasoning effort by default
                    defaultParameters[paramName] = options.first
                }
            case .thinkingBudget:
                if let paramName = thinkingCapabilities.parameterName,
                let maxBudget = thinkingCapabilities.maxBudget {
                    // Set to half of max budget by default
                    defaultParameters[paramName] = "\(maxBudget / 2)"
                }
            case .parameterBased:
                if let paramName = thinkingCapabilities.parameterName {
                    // For models using parameter-based thinking
                    switch paramName {
                    case "thinking":
                        defaultParameters[paramName] = "{ \"type\": \"enabled\", \"budget_tokens\": 10000 }"
                    case "enable_thinking":
                        defaultParameters[paramName] = "true"
                    default:
                        defaultParameters[paramName] = "{ \"type\": \"enabled\" }"
                    }
                }
            case .promptBased:
                if let paramName = thinkingCapabilities.parameterName {
                    // For models using prompt-based thinking
                    defaultParameters[paramName] = "Think step by step."
                }
            default:
                break
            }
        }

         // Set searching-related parameters for models with searching capabilities
         if let searchingCapabilities = model.searchingCapabilities,
            searchingCapabilities.controlType != SearchingCapabilities.ControlType.none {
            switch searchingCapabilities.controlType {
            case .parameterBased:
                if let paramName = searchingCapabilities.parameterName {
                    if let providerName = provider?.name {
                        switch providerName {
                        case "Anthropic":
                            defaultParameters[paramName] = "[{ \"type\": \"web_search_20250305\", \"name\": \"web_search\", \"max_uses\": 5 }]"
                        case "Google":
                            defaultParameters[paramName] = "[{ \"google_search\": {} }]"
                        case "xAI":
                            defaultParameters[paramName] = "{ \"mode\": \"auto\" }"
                        case "OpenRouter":
                            defaultParameters[paramName] = "[{ \"id\": \"web\" }]"
                        default:
                            defaultParameters[paramName] = "{}"
                        }
                    } else {
                        defaultParameters[paramName] = "{}"
                    }
                }
            case .appApi:
                break
            default:
                break
            }
        }

        return LLMInstance(
            id: instanceId,
            modelId: model.id,
            name: "\(model.name)",
            systemPrompt: systemPrompt,
            defaultParameters: defaultParameters,
            totalPromptTokensUsed: 0,
            totalCompletionTokensUsed: 0,
            createdAt: Date(),
            lastUsedAt: nil,
            isFavorited: false,
            isUserModified: isUserModified,
            metadata: nil
        )
    }

}

// MARK: - ShareableItem Conformance

extension LLMInstance: ShareableItem {
    var shareContentType: ShareContentType {
        return .llmInstance
    }

    var shareDisplayName: String {
        return name
    }

    func toShareableData() async throws -> ShareableData {
        // Note: This method should be implemented with repository access
        // For now, we'll create a basic structure that will be completed in the service layer
        let content = ShareableDataContent(instance: self)
        return ShareableData(shareType: .llmInstance, data: content)
    }

    func validateForSharing() throws {
        // Validate that the instance has required data for sharing
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ShareError.invalidContent
        }

        // Check if this is a valid instance (has model reference)
        if modelId.uuidString.isEmpty {
            throw ShareError.missingDependencies("Model reference is required")
        }
    }
}
