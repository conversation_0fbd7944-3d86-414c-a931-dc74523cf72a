import Foundation

struct LLMProvider: Identifiable, Codable, Hashable, Equatable {
    let id: UUID
    var name: String
    var logoImageName: String?
    var customLogoData: Data?
    var websiteUrl: String?
    var apiDocumentationUrl: String?
    var apiBaseUrl: String? // Base URL for the provider's API
    var providerType: LLMProviderType
    var apiKeyStored: Bool // True if the API key is stored in the keychain
    var apiStyle: APIStyle // API style (e.g., OpenAI compatible)
    var apiEndpointPath: String? // Specific endpoint path (e.g., "/chat/completions")
    var isUserCreated: Bool // True if added by the user
    var isUserModified: Bool // True if the user has modified this provider
    var metadata: [String: String]?

    enum LLMProviderType: String, Codable, CaseIterable, Equatable {
        case subscriptionBased = "subscription_based"   // no user key needed
        case userApiKey = "user_api_key"                // user key
        
        var displayName: String {
            switch self {
            case .subscriptionBased:
                return "Subscription Based"
            case .userApiKey:
                return "User API Key"
            }
        }
    }

    init(
        id: UUID = UUID(),
        name: String,
        logoImageName: String? = nil,
        customLogoData: Data? = nil,
        websiteUrl: String? = nil,
        apiDocumentationUrl: String? = nil,
        apiBaseUrl: String? = nil,
        providerType: LLMProviderType = .userApiKey,
        apiKeyStored: Bool = false,
        apiStyle: APIStyle = .openaiCompatible,
        apiEndpointPath: String? = nil,
        isUserCreated: Bool = false,
        isUserModified: Bool = false,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.name = name
        self.logoImageName = logoImageName
        self.customLogoData = customLogoData
        self.websiteUrl = websiteUrl
        self.apiDocumentationUrl = apiDocumentationUrl
        self.apiBaseUrl = apiBaseUrl
        self.providerType = providerType
        self.apiKeyStored = (providerType == .subscriptionBased) ? false : apiKeyStored
        self.apiStyle = apiStyle
        self.apiEndpointPath = apiEndpointPath
        self.isUserCreated = isUserCreated
        self.isUserModified = isUserModified
        self.metadata = metadata
    }
}

// MARK: - ShareableItem Conformance

extension LLMProvider: ShareableItem {
    var shareContentType: ShareContentType {
        return .llmInstance // Providers are typically shared as part of instances
    }

    var shareDisplayName: String {
        return name
    }

    func toShareableData() async throws -> ShareableData {
        // Note: This method should be implemented with repository access
        // For now, we'll create a basic structure that will be completed in the service layer
        let content = ShareableDataContent(provider: self)
        return ShareableData(shareType: .llmInstance, data: content)
    }

    func validateForSharing() throws {
        // Validate that the provider has required data for sharing
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ShareError.invalidContent
        }

        // Ensure we don't share sensitive information
        if apiKeyStored && providerType == .userApiKey {
            // This is handled by sanitization in the service layer
            // We just validate that the provider can be shared
        }
    }
}