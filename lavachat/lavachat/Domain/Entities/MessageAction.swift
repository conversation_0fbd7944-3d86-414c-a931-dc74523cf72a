import Foundation

struct MessageAction: Identifiable, Equatable, Hashable, Codable {
    let id: UUID
    var name: String // Button text
    var icon: String? // SF Symbol name or custom icon identifier
    var actionType: ActionType
    var prompts: [String] // Used for assistantRegenerate, actionPanelPromptInsert, and actionPanelPromptRewrite
    var targetLLMInstanceId: UUID? // LLM instance to use, nil means use same as message or first one in session activeLLMInstanceIds
    var metadata: [String: String]?

    enum ActionType: Equatable, Hashable, Codable {
        case system(SystemActionKind)
        case assistantRegenerate  // Prompt-based regeneration for assistant messages
        case actionPanelPromptInsert  // Insert prompt into input field at cursor position
        case actionPanelPromptRewrite  // Rewrite input field content with prompt

        enum SystemActionKind: String, Codable, CaseIterable {
            case copy, edit, share, like, dislike, regenerate, select
            case camera, photo, file
        }

        var promptSelectionDescription: String {
            switch self {
            case .system:
                return "Choose an option to continue"
            case .assistantRegenerate:
                return "Choose a prompt to regenerate the message"
            case .actionPanelPromptInsert:
                return "Choose a prompt to insert at end of input"
            case .actionPanelPromptRewrite:
                return "Choose a prompt to rewrite input content"
            }
        }
    }

    // Computed properties: derive suitability from actionType
    var suitableForUserMessage: Bool {
        switch actionType {
        case .system(let kind):
            switch kind {
            case .copy, .edit:
                return true
            case .share, .like, .dislike, .regenerate, .select:
                return false
            case .camera, .photo, .file:
                return false
            }
        case .assistantRegenerate:
            return false
        case .actionPanelPromptInsert, .actionPanelPromptRewrite:
            return false
        }
    }

    var suitableForAssistantMessage: Bool {
        switch actionType {
        case .system(let kind):
            switch kind {
            case .copy, .share, .like, .dislike, .regenerate, .select:
                return true
            case .edit:
                return true  // Assistant messages can also be edited
            case .camera, .photo, .file:
                return false
            }
        case .assistantRegenerate:
            return true
        case .actionPanelPromptInsert, .actionPanelPromptRewrite:
            return false
        }
    }

    var suitableForActionPanel: Bool {
        switch actionType {
        case .system(let kind):
            switch kind {
            case .camera, .photo, .file:
                return true
            case .copy, .edit, .share, .like, .dislike, .regenerate, .select:
                return false
            }
        case .assistantRegenerate:
            return false
        case .actionPanelPromptInsert, .actionPanelPromptRewrite:
            return true
        }
    }

    /// Determines the default category for this action based on its type
    var defaultCategory: MessageActionCategory {
        switch actionType {
        case .actionPanelPromptInsert, .actionPanelPromptRewrite:
            return .actionPanel
        case .assistantRegenerate:
            return .assistantCard // Default to assistantCard for regenerate actions
        case .system(let kind):
            switch kind {
            case .camera, .photo, .file:
                return .actionPanel
            case .copy, .edit:
                return .userMessage
            case .share, .like, .dislike, .regenerate, .select:
                return .assistantCard
            }
        }
    }

    init(
        id: UUID = UUID(),
        name: String,
        icon: String? = nil,
        actionType: ActionType,
        prompts: [String] = [],
        targetLLMInstanceId: UUID? = nil,
        metadata: [String: String]? = [:]
    ) {
        self.id = id
        self.name = name
        self.icon = icon
        self.actionType = actionType
        self.prompts = prompts
        self.targetLLMInstanceId = targetLLMInstanceId
        self.metadata = metadata
    }
}

struct SystemMessageActions: Equatable, Hashable, Codable {
    static let copy = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0001-AC71-000000000001")!,
        name: "Copy",
        icon: "square.on.square",
        actionType: .system(.copy)
    )
    static let edit = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0002-AC71-000000000002")!,
        name: "Edit",
        icon: "pencil",
        actionType: .system(.edit)
    )

    static let share = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0003-AC71-000000000003")!,
        name: "Share",
        icon: "square.and.arrow.up",
        actionType: .system(.share)
    )

    static let like = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0004-AC71-000000000004")!,
        name: "Like",
        icon: "hand.thumbsup",
        actionType: .system(.like)
    )

    static let dislike = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0005-AC71-000000000005")!,
        name: "Dislike",
        icon: "hand.thumbsdown",
        actionType: .system(.dislike)
    )

    static let regenerate = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0006-AC71-000000000006")!,
        name: "Regenerate",
        icon: "arrow.clockwise",
        actionType: .system(.regenerate)
    )

    static let select = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0007-AC71-000000000007")!,
        name: "Select",
        icon: "selection.pin.in.out",
        actionType: .system(.select)
    )

    static let camera = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0008-AC71-000000000008")!,
        name: "Camera",
        icon: "camera",
        actionType: .system(.camera)
    )

    static let photo = MessageAction(
        id: UUID(uuidString: "AC710000-0000-0009-AC71-000000000009")!,
        name: "Photo",
        icon: "photo",
        actionType: .system(.photo)
    )

    static let file = MessageAction(
        id: UUID(uuidString: "AC710000-0000-000A-AC71-00000000000A")!,
        name: "File",
        icon: "doc",
        actionType: .system(.file)
    )

    static let actions = [copy, edit, share, like, dislike, regenerate, select, camera, photo, file]

    static let defaultActionPanelActions = [camera, photo, file]
    static let defaultUserMessageActions = [copy, edit]
    static let defaultAssistantMessageCardActions = [copy, regenerate, like, dislike]
    static let defaultAssistantMessageMenuActions = [copy, regenerate, like, dislike, select]
}

// MARK: - ShareableItem Conformance

extension MessageAction: ShareableItem {
    var shareContentType: ShareContentType {
        return .messageAction
    }

    var shareDisplayName: String {
        return name
    }

    func toShareableData() async throws -> ShareableData {
        // Note: This method should be implemented with repository access
        // For now, we'll create a basic structure that will be completed in the service layer
        let content = ShareableDataContent(messageAction: self)
        return ShareableData(shareType: .messageAction, data: content)
    }

    func validateForSharing() throws {
        // Validate that the action has required data for sharing
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ShareError.invalidContent
        }

        // Validate prompts if this action uses them
        switch actionType {
        case .assistantRegenerate, .actionPanelPromptInsert, .actionPanelPromptRewrite:
            if prompts.isEmpty {
                throw ShareError.invalidContent
            }
            // Check that prompts are not empty
            for prompt in prompts {
                if prompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    throw ShareError.invalidContent
                }
            }
        case .system:
            // System actions don't require prompts
            break
        }
    }
}
