import Foundation

// Represents operations AI might request on files
enum FileOperation: Codable, Equatable, Hashable {
    case readFile(path: String, range: RangeInfo?) // Use RangeInfo
    case applyDiff(diff: DiffBlock)
    case searchFile(query: String, path: String?)
    // Add other file operations as needed
}

// Represents content within a message
enum ContentBlock: Codable, Equatable, Hashable {
    case text(String)
    case image(ImageInfo) // Image content with metadata
    case file(FileInfo) // Non-image file content
    case attachedFileContent(fileName: String, content: String) // For @file mentions
    case thinking(String) // AI thinking process
    case fileReadRequest(path: String, range: RangeInfo?) // Use RangeInfo
    case diffBlock(DiffBlock) // AI-proposed diff
    case fileWriteResult(success: Bool, message: String?) // Result of a write operation
    case fileSearchRequest(query: String, path: String?) // AI requests search
    case fileSearchResult(results: [SearchResultInfo]) // AI returns search results

    // Placeholders for future MCP integration
    // case mcpToolCall(/* ... */)
    // case mcpTool<PERSON>esult(/* ... */)

    // Allow easy checking of the type
    var typeDescription: String {
        switch self {
        case .text: return "text"
        case .image: return "image"
        case .file: return "file"
        case .attachedFileContent: return "attachedFileContent"
        case .thinking: return "thinking"
        case .fileReadRequest: return "fileReadRequest"
        case .diffBlock: return "diffBlock"
        case .fileWriteResult: return "fileWriteResult"
        case .fileSearchRequest: return "fileSearchRequest"
        case .fileSearchResult: return "fileSearchResult"
        }
    }

    /// Whether this content block contains multimodal content
    var isMultimodal: Bool {
        switch self {
        case .image, .file:
            return true
        default:
            return false
        }
    }
}

// Placeholder for range information (line/column)
struct RangeInfo: Codable, Equatable, Hashable {
    // Define properties like startLine, startColumn, endLine, endColumn as needed
    var startLine: Int?
    var endLine: Int?
    // Could also use simple start/end indices if sufficient
    var startIndex: Int?
    var length: Int?
}

// Image information for multimodal content
struct ImageInfo: Codable, Equatable, Hashable {
    var url: String? // URL or local identifier
    var caption: String?
    var data: Data? // Optional image data
    var mimeType: String? // MIME type (e.g., "image/jpeg")
    var base64Data: String? // Base64 encoded data for API transmission
    var fileSizeBytes: Int64? // File size in bytes

    init(url: String? = nil, caption: String? = nil, data: Data? = nil,
         mimeType: String? = nil, base64Data: String? = nil, fileSizeBytes: Int64? = nil) {
        self.url = url
        self.caption = caption
        self.data = data
        self.mimeType = mimeType
        self.base64Data = base64Data
        self.fileSizeBytes = fileSizeBytes
    }
}

// File information for non-image attachments
struct FileInfo: Codable, Equatable, Hashable {
    var fileName: String
    var mimeType: String
    var base64Data: String
    var fileSizeBytes: Int64
    var fileType: FileType

    init(fileName: String, mimeType: String, base64Data: String,
         fileSizeBytes: Int64, fileType: FileType) {
        self.fileName = fileName
        self.mimeType = mimeType
        self.base64Data = base64Data
        self.fileSizeBytes = fileSizeBytes
        self.fileType = fileType
    }
}

// File type classification
enum FileType: String, Codable, CaseIterable {
    case image = "image"
    case document = "document"
    case other = "other"

    var displayName: String {
        switch self {
        case .image:
            return "Image"
        case .document:
            return "Document"
        case .other:
            return "Other"
        }
    }
}

// Placeholder for search result information
struct SearchResultInfo: Codable, Equatable, Hashable {
    var snippet: String
    var range: RangeInfo? // Position of the snippet
    var score: Double? // Relevance score
}


// Represents a proposed change to a file
struct DiffBlock: Codable, Identifiable, Equatable, Hashable {
    let id: UUID // Unique ID for this specific diff suggestion
    var originalContent: String
    var replacementContent: String
    var filePath: String // Path to the target file (relative or absolute as appropriate)
    var rangeInOriginal: RangeInfo // Location in the original file content
    var explanation: String? // AI's explanation for the change

    init(
        id: UUID = UUID(),
        originalContent: String,
        replacementContent: String,
        filePath: String,
        rangeInOriginal: RangeInfo,
        explanation: String? = nil
    ) {
        self.id = id
        self.originalContent = originalContent
        self.replacementContent = replacementContent
        self.filePath = filePath
        self.rangeInOriginal = rangeInOriginal
        self.explanation = explanation
    }
}

// Represents the role of the message sender
enum MessageRole: String, Codable, Equatable {
    case user = "user"
    case assistant
    case system
    case mergedAssistant = "merged_assistant" // Use raw value for JSON key matching
    case tool // Reserved for future non-MCP tool use
}

// Represents the status of a message
enum MessageStatus: String, Codable, Equatable {
    case sending
    case received = "received"
    case error
    case generating
    case cancelled
    case merged // For merged_assistant role
}

// Represents user feedback on a message
enum MessageFeedback: String, Codable, Equatable {
    case none = "none"
    case liked
    case disliked
} 
