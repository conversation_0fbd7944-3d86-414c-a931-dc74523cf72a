import Foundation

struct Message: Identifiable, Codable, Equatable, Hashable {
    let id: UUID
    let sessionId: UUID // Belongs to ChatSession or FileSession
    let parentId: UUID? // UUID of the parent message in the tree, nil for root
    let timestamp: Date
    let role: MessageRole
    var content: [ContentBlock]
    let depth: Int64 // Distance from root message, 0 for root messages
    var llmInstanceId: UUID? // ID of the LLMInstance that generated this (if assistant/merged)
    var promptTokens: Int64? // Tokens used for the prompt generating this message
    var completionTokens: Int64? // Tokens used for this message's content
    var userId: UUID? // User ID if role is 'user'
    var status: MessageStatus
    var userFeedback: MessageFeedback = .none // User's like/dislike feedback
    var isReplied: Bool = false // is this the message used to branch from for subsequent interaction
    var isFavorited: Bool = false // Is this message bookmarked by the user
    var metadata: [String: String]? // Flexible metadata for relationships, UI hints, etc.

    // Optional fields based on original markdown design
    var rawResponseText: String? // Raw text from LLM before parsing into ContentBlocks
    var parsedFileOperations: [FileOperation]? // File operations requested by the AI

    // MARK: - Computed Properties
    
    /// Returns true if this is a root message (depth 0)
    var isRootMessage: Bool { depth == 0 }

    // MARK: - Metadata Keys (Constants for consistency)
    struct MetadataKeys {
        static let isEditOf = "metadata_is_edit_of" // Value: UUID String of original message
        static let isRegenerationOf = "metadata_is_regeneration_of" // Value: UUID String of original message
        static let mergedFrom = "metadata_merged_from" // Value: Comma-separated UUID Strings
        static let referencedMessage = "metadata_referenced_message" // Value: UUID String of referenced message
        static let uiHidden = "metadata_ui_hidden" // Value: "true" or "false"
        static let requestedMode = "metadata_requested_mode" // Value: "chat" or "edit" (FileSession)
        static let inputFileCheckpointId = "metadata_input_file_checkpoint_id" // Value: UUID String (FileSession)
        static let outputFileCheckpointId = "metadata_output_file_checkpoint_id" // Value: UUID String (FileSession)
        static let referencesDiffBlockId = "metadata_references_diff_block_id" // Value: UUID String (FileSession)
        // Add other keys as needed
    }

    // Initializer with defaults (depth is required)
    init(
        id: UUID = UUID(),
        sessionId: UUID,
        parentId: UUID? = nil,
        timestamp: Date = Date(),
        role: MessageRole,
        content: [ContentBlock],
        depth: Int64,
        rawResponseText: String? = nil,
        llmInstanceId: UUID? = nil,
        promptTokens: Int64? = nil,
        completionTokens: Int64? = nil,
        userId: UUID? = nil,
        status: MessageStatus = .received, // Default to received for simplicity, adjust as needed
        userFeedback: MessageFeedback = .none,
        isReplied: Bool = false,
        isFavorited: Bool = false,
        parsedFileOperations: [FileOperation]? = nil,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.sessionId = sessionId
        self.parentId = parentId
        self.timestamp = timestamp
        self.role = role
        self.content = content
        self.depth = depth
        self.rawResponseText = rawResponseText
        self.llmInstanceId = llmInstanceId
        self.promptTokens = promptTokens
        self.completionTokens = completionTokens
        // Assign userId only if the role is user
        self.userId = (role == .user) ? userId : nil
        self.status = status
        self.userFeedback = userFeedback
        self.isReplied = isReplied
        self.isFavorited = isFavorited
        self.parsedFileOperations = parsedFileOperations
        self.metadata = metadata

        // Ensure userId is set if role is user, and nil otherwise during init
        if role == .user && userId == nil {
             print("Warning: User message created without userId.")
             // Decide on handling: assert, throw, fallback? For now, just a warning.
        } else if role != .user && userId != nil {
            print("Warning: Non-user message created with userId. Clearing userId.")
            self.userId = nil
        }
    }

    // Equatable conformance based on ID
    static func == (lhs: Message, rhs: Message) -> Bool {
        lhs.id == rhs.id
    }

}

// Helper extension for accessing typed metadata (example)
extension Message {
    var isEditOf: UUID? {
        guard let idString = metadata?[MetadataKeys.isEditOf] else { return nil }
        return UUID(uuidString: idString)
    }

    var isRegenerationOf: UUID? {
        guard let idString = metadata?[MetadataKeys.isRegenerationOf] else { return nil }
        return UUID(uuidString: idString)
     }

    var mergedFrom: [UUID]? {
        guard let idsString = metadata?[MetadataKeys.mergedFrom] else { return nil }
        return idsString.split(separator: ",").compactMap { UUID(uuidString: String($0)) }
    }

    // Add similar computed properties for other metadata keys...
}

// Helper extension for accessing text content
extension Message {
    var textContent: String {
        content.compactMap { block in
            if case .text(let text) = block {
                if !text.isEmpty {
                    return text
                }
            }
            return nil
        }.joined(separator: "\n\n")
    }

    var textContentCleaned: String {
        content.compactMap { block in
            if case .text(let text) = block {
                return text.trimmingCharacters(in: .whitespacesAndNewlines)
            }
            return nil
        }.joined(separator: " ").trimmingCharacters(in: .whitespacesAndNewlines)
    }
}