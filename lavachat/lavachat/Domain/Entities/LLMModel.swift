import Foundation

struct LLMModel: Identifiable, Codable, Hashable, Equatable {
    let id: UUID
    let providerId: UUID
    var modelIdentifier: String // Identifier used in API calls (e.g., "gpt-4-turbo")
    var name: String // User-friendly name (e.g., "GPT-4 Turbo")
    var modelDescription: String?
    var logoImageName: String?
    var customLogoData: Data?
    var contextWindowSize: Int64?
    var inputModalities: [ModelModality]
    var outputModalities: [ModelModality]
    var thinkingCapabilities: ThinkingCapabilities?
    var searchingCapabilities: SearchingCapabilities?
    var maxOutputTokens: Int64? // Maximum number of output tokens
    var pricingInfo: String? // Simple string or JSON string
    var group: String? // Group name for UI organization and backend routing
    var availabilityStatus: ModelAvailabilityStatus?
    var isDefaultRecommendation: Bool = false // Suggested default model
    var isUserCreated: Bool = false // True if the model is user-created
    var isUserModified: Bool // True if the user has modified this model
    var apiConfigsOverride: [ModelAPIConfig]? // API configuration overrides
    var metadata: [String: String]? // e.g., feature tags like "code", "creative"

    enum ModelAvailabilityStatus: String, Codable, Equatable {
        case available
        case beta
        case preview
        case deprecated
        case restricted
        case unavailable // Added for clarity when seeding
    }

    init(
        id: UUID = UUID(),
        providerId: UUID,
        modelIdentifier: String,
        name: String,
        modelDescription: String? = nil,
        logoImageName: String? = nil,
        customLogoData: Data? = nil,
        contextWindowSize: Int64? = nil,
        inputModalities: [ModelModality] = [.text],
        outputModalities: [ModelModality] = [.text],
        thinkingCapabilities: ThinkingCapabilities? = nil,
        searchingCapabilities: SearchingCapabilities? = nil,
        maxOutputTokens: Int64? = nil,
        pricingInfo: String? = nil,
        group: String? = nil,
        availabilityStatus: ModelAvailabilityStatus? = .available,
        isDefaultRecommendation: Bool = false,
        isUserCreated: Bool = false,
        isUserModified: Bool = false,
        apiConfigsOverride: [ModelAPIConfig]? = nil,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.providerId = providerId
        self.modelIdentifier = modelIdentifier
        self.name = name
        self.modelDescription = modelDescription
        self.logoImageName = logoImageName
        self.customLogoData = customLogoData
        self.contextWindowSize = contextWindowSize
        self.inputModalities = inputModalities
        self.outputModalities = outputModalities
        self.thinkingCapabilities = thinkingCapabilities
        self.searchingCapabilities = searchingCapabilities
        self.maxOutputTokens = maxOutputTokens
        self.pricingInfo = pricingInfo
        self.group = group
        self.availabilityStatus = availabilityStatus
        self.isDefaultRecommendation = isDefaultRecommendation
        self.isUserCreated = isUserCreated
        self.isUserModified = isUserModified
        self.apiConfigsOverride = apiConfigsOverride
        self.metadata = metadata
    }
}

enum ModelModality: String, Codable, CaseIterable {
    case text
    case image
    case audio
    case video
    case pdf
}

struct ThinkingCapabilities: Codable, Hashable, Equatable {
    enum ControlType: String, Codable, CaseIterable {
        case none
        case defaultOn
        case reasoningEffort // like OpenAI's reasoning_effort
        case thinkingBudget // like Google's thinkingBudget
        case parameterBased // like Anthropic's extended thinking
        case promptBased // like Anthropic's "think more"
    }

    var controlType: ControlType?
    var parameterName: String?
    var parameterOptions: [String]?
    var maxBudget: Int64?

    init(
        controlType: ControlType,
        parameterName: String? = nil,
        parameterOptions: [String]? = nil,
        maxBudget: Int64? = nil
    ) {
        self.controlType = controlType
        self.parameterName = parameterName
        self.parameterOptions = parameterOptions
        self.maxBudget = maxBudget

        // clean up unused fields
        switch controlType {
        case .none, .defaultOn:
            self.parameterName = nil
            self.parameterOptions = nil
            self.maxBudget = nil
        case .promptBased, .parameterBased:
            self.parameterOptions = nil
            self.maxBudget = nil
        case .reasoningEffort:
            self.maxBudget = nil
        case .thinkingBudget:
            self.parameterOptions = nil
        }
    }
}

struct SearchingCapabilities: Codable, Hashable, Equatable {
    enum ControlType: String, Codable, CaseIterable {
        case none
        case parameterBased
        case appApi
    }

    var controlType: ControlType?
    var parameterName: String?

    init(
        controlType: ControlType,
        parameterName: String? = nil,
    ) {
        self.controlType = controlType
        self.parameterName = parameterName

        // clean up unused fields
        switch controlType {
        case .none:
            self.parameterName = nil
        case .parameterBased:
            break
        case .appApi:
            break
        }
    }
}

// MARK: - ShareableItem Conformance

extension LLMModel: ShareableItem {
    var shareContentType: ShareContentType {
        return .llmInstance // Models are typically shared as part of instances
    }

    var shareDisplayName: String {
        return name
    }

    func toShareableData() async throws -> ShareableData {
        // Note: This method should be implemented with repository access
        // For now, we'll create a basic structure that will be completed in the service layer
        let content = ShareableDataContent(model: self)
        return ShareableData(shareType: .llmInstance, data: content)
    }

    func validateForSharing() throws {
        // Validate that the model has required data for sharing
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ShareError.invalidContent
        }

        if modelIdentifier.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ShareError.invalidContent
        }

        // Check if this model has a valid provider reference
        if providerId.uuidString.isEmpty {
            throw ShareError.missingDependencies("Provider reference is required")
        }
    }
}