import Foundation

struct SubscriptionStatus: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    var activePlanProductId: String? // Apple Product ID of the current plan
    var status: SubscriptionStatusValue
    var expiryDate: Date?
    var purchaseDate: Date? // Last purchase/renewal date
    var startDate: Date? // Original purchase date
    var autoRenewEnabled: Bool = false
    var lastVerificationDate: Date?
    var tokensUsedThisCycle: Int = 0 // Weighted tokens used in current cycle
    var lastCycleResetDate: Date? // Start date of the current billing cycle
    var originalTransactionId: String?
    var latestTransactionId: String?
    var metadata: [String: String]? // e.g., isTrialEligible, cancellationReason

    enum SubscriptionStatusValue: String, Codable {
        case active
        case expired
        case inBillingRetryPeriod = "in_billing_retry_period"
        case inGracePeriod = "in_grace_period"
        case revoked
        case cancelled // Auto-renew off, but still valid
        case freeTrial = "free_trial"
        case inactive // No subscription ever
    }

    init(
        id: UUID = UUID(),
        userId: UUID,
        activePlanProductId: String? = nil,
        status: SubscriptionStatusValue = .inactive,
        expiryDate: Date? = nil,
        purchaseDate: Date? = nil,
        startDate: Date? = nil,
        autoRenewEnabled: Bool = false,
        lastVerificationDate: Date? = nil,
        tokensUsedThisCycle: Int = 0,
        lastCycleResetDate: Date? = nil,
        originalTransactionId: String? = nil,
        latestTransactionId: String? = nil,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.userId = userId
        self.activePlanProductId = activePlanProductId
        self.status = status
        self.expiryDate = expiryDate
        self.purchaseDate = purchaseDate
        self.startDate = startDate
        self.autoRenewEnabled = autoRenewEnabled
        self.lastVerificationDate = lastVerificationDate
        self.tokensUsedThisCycle = tokensUsedThisCycle
        self.lastCycleResetDate = lastCycleResetDate
        self.originalTransactionId = originalTransactionId
        self.latestTransactionId = latestTransactionId
        self.metadata = metadata
    }
} 