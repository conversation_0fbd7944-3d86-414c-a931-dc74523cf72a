import Foundation

struct User: Identifiable, Codable {
    let id: UUID
    var nickname: String?
    var avatarIdentifier: String?
    let registrationDate: Date
    var lastSeenAt: Date?
    var appSettings: AppSettings? // Direct embedding of app settings
    var defaultChatSettingsId: UUID?
    var systemUtilityLLMInstanceId: UUID?
    var syncStatus: SyncStatus? // Potentially store locally, CloudKit state might differ
    var lastSyncTimestamp: Date?
    var memoryFileBookmarkData: Data? // Placeholder for future use
    var metadata: [String: String]? // Flexible metadata

    enum SyncStatus: String, Codable {
        case synced
        case syncing
        case error
        case disabled
    }

    // Initializer with default values
    init(
        id: UUID = UUID(),
        nickname: String? = nil,
        avatarIdentifier: String? = nil,
        registrationDate: Date = Date(),
        lastSeenAt: Date? = Date(),
        appSettings: AppSettings? = nil,
        defaultChatSettingsId: UUID? = nil,
        systemUtilityLLMInstanceId: UUID? = nil,
        syncStatus: SyncStatus? = nil,
        lastSyncTimestamp: Date? = nil,
        memoryFileBookmarkData: Data? = nil,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.nickname = nickname
        self.avatarIdentifier = avatarIdentifier
        self.registrationDate = registrationDate
        self.lastSeenAt = lastSeenAt
        self.appSettings = appSettings
        self.defaultChatSettingsId = defaultChatSettingsId
        self.systemUtilityLLMInstanceId = systemUtilityLLMInstanceId
        self.syncStatus = syncStatus
        self.lastSyncTimestamp = lastSyncTimestamp
        self.memoryFileBookmarkData = memoryFileBookmarkData
        self.metadata = metadata
    }
}

// MARK: - AppSettings

struct AppSettings: Codable, Equatable {
    var shouldRestoreLastChat: Bool

    init(shouldRestoreLastChat: Bool = true) {
        self.shouldRestoreLastChat = shouldRestoreLastChat
    }
}