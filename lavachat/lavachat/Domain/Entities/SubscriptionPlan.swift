import Foundation

struct SubscriptionPlan: Identifiable, Codable, Equatable {
    let id: String // Use Apple's Product ID
    var name: String
    var description: String
    var priceLocaleIdentifier: String? // e.g., "USD"
    var price: String // Formatted price string
    var billingCycle: BillingCycle
    var featureList: [String]?
    var tokenAllowancePerCycle: Int? // Nil means unlimited or not applicable
    var promptTokenCostFactor: Double = 1.0
    var completionTokenCostFactor: Double = 1.0
    var concurrentRequestLimit: Int?
    var prioritySupport: Bool = false
    var metadata: [String: String]? // e.g., highlight color, isMostPopular

    enum BillingCycle: String, Codable {
        case monthly
        case yearly
        case lifetime // If offered
    }

    // Need an initializer as default one might conflict with Identifiable 'id'
    init(
        id: String,
        name: String,
        description: String,
        priceLocaleIdentifier: String? = nil,
        price: String,
        billingCycle: BillingCycle,
        featureList: [String]? = nil,
        tokenAllowancePerCycle: Int? = nil,
        promptTokenCostFactor: Double = 1.0,
        completionTokenCostFactor: Double = 1.0,
        concurrentRequestLimit: Int? = nil,
        prioritySupport: Bool = false,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.priceLocaleIdentifier = priceLocaleIdentifier
        self.price = price
        self.billingCycle = billingCycle
        self.featureList = featureList
        self.tokenAllowancePerCycle = tokenAllowancePerCycle
        self.promptTokenCostFactor = promptTokenCostFactor
        self.completionTokenCostFactor = completionTokenCostFactor
        self.concurrentRequestLimit = concurrentRequestLimit
        self.prioritySupport = prioritySupport
        self.metadata = metadata
    }
} 