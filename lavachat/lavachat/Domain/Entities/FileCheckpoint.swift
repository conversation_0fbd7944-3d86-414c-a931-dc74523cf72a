import Foundation

struct FileCheckpoint: Identifiable, Codable, Equatable {
    let id: UUID
    let fileSessionId: UUID
    let timestamp: Date
    let basedOnCheckpointId: UUID? // Previous checkpoint ID, nil for initial
    let triggeringMessageId: UUID? // Message that triggered this checkpoint
    var fileContent: String // Full content of the managed Markdown file at this point
    var pendingEditStates: [PendingEditInfo]? // Edits not yet accepted/rejected
    var metadata: [String: String]?

    init(
        id: UUID = UUID(),
        fileSessionId: UUID,
        timestamp: Date = Date(),
        basedOnCheckpointId: UUID? = nil,
        triggeringMessageId: UUID? = nil,
        fileContent: String,
        pendingEditStates: [PendingEditInfo]? = nil,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.fileSessionId = fileSessionId
        self.timestamp = timestamp
        self.basedOnCheckpointId = basedOnCheckpointId
        self.triggeringMessageId = triggeringMessageId
        self.fileContent = fileContent
        self.pendingEditStates = pendingEditStates
        self.metadata = metadata
    }
}


struct PendingEditInfo: Codable, Equatable, Hashable {
    let diffBlockId: UUID // Associates with a DiffBlock.id or a generated ID for manual edits
    var sourceType: EditSourceType
    var status: EditStatus
    var manualEditDiff: String? // Stores user modifications within this pending region
    var rangeInFileContent: RangeInfo // Exact location in the FileCheckpoint.fileContent

    enum EditSourceType: String, Codable {
        case aiGenerated = "ai_generated"
        case manualEdit = "manual_edit"
    }

    enum EditStatus: String, Codable {
        case pending // Waiting for user action
        case accepted // User accepted the change
        case rejected // User rejected the change
        case modifiedPending = "modified_pending" // User edited AI suggestion, needs final confirm
    }

    init(
        diffBlockId: UUID = UUID(), // Auto-generate if manual edit
        sourceType: EditSourceType,
        status: EditStatus = .pending,
        manualEditDiff: String? = nil,
        rangeInFileContent: RangeInfo
    ) {
        self.diffBlockId = diffBlockId
        self.sourceType = sourceType
        self.status = status
        self.manualEditDiff = manualEditDiff
        self.rangeInFileContent = rangeInFileContent
    }
} 