import Foundation

struct ChatSession: Identifiable, Codable, Hashable {
    let id: UUID
    var title: String?
    let createdAt: Date
    var lastModifiedAt: Date
    var activeMessageId: UUID? // track current active message
    var activeLLMInstanceIds: [UUID]
    var usedLLMInstanceIds: [UUID] // Historical record of all instances used in this session
    var activeContextServerIds: [UUID] // Placeholder for future MCP
    var settingsId: UUID? // Link to ChatSessionSetting
    let userId: UUID // Owning user
    var instanceSettings: [UUID: SessionInstanceSetting]? // Per-instance settings for this session
    var metadata: [String: String]?

    // Initializer with default values
    init(
        id: UUID = UUID(),
        title: String? = nil,
        createdAt: Date = Date(),
        lastModifiedAt: Date = Date(),
        activeMessageId: UUID? = nil,
        activeLLMInstanceIds: [UUID] = [],
        usedLLMInstanceIds: [UUID] = [],
        activeContextServerIds: [UUID] = [], // Placeholder for future MCP
        settingsId: UUID? = nil,
        userId: UUID,
        instanceSettings: [UUID: SessionInstanceSetting]? = [:],
        metadata: [String : String]? = [:]
    ) {
        self.id = id
        self.title = title
        self.createdAt = createdAt
        self.lastModifiedAt = lastModifiedAt
        self.activeMessageId = activeMessageId
        self.activeLLMInstanceIds = activeLLMInstanceIds
        self.usedLLMInstanceIds = usedLLMInstanceIds
        self.activeContextServerIds = activeContextServerIds
        self.settingsId = settingsId
        self.userId = userId
        self.instanceSettings = instanceSettings
        self.metadata = metadata
    }

    static let defaultSessionName = "New Chat"
}

// MARK: - ShareableItem Conformance

extension ChatSession: ShareableItem {
    var shareContentType: ShareContentType {
        return .chatSession
    }

    var shareDisplayName: String {
        return title ?? ChatSession.defaultSessionName
    }

    func toShareableData() async throws -> ShareableData {
        // Note: This method should be implemented with repository access
        // For now, we'll create a basic structure that will be completed in the service layer
        let content = ShareableDataContent(chatSession: self)
        return ShareableData(shareType: .chatSession, data: content)
    }

    func validateForSharing() throws {
        // Validate that the session has required data for sharing
        let displayName = title ?? ChatSession.defaultSessionName
        if displayName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ShareError.invalidContent
        }

        // Check if session has any LLM instances
        if activeLLMInstanceIds.isEmpty && usedLLMInstanceIds.isEmpty {
            throw ShareError.missingDependencies("Session must have at least one LLM instance")
        }
    }
}

/// Configuration settings for a specific LLM instance within a chat session
struct SessionInstanceSetting: Codable, Hashable, Equatable {
    var thinkingEnabled: Bool?
    var networkEnabled: Bool?
    
    init(
        thinkingEnabled: Bool? = nil,
        networkEnabled: Bool? = nil
    ) {
        self.thinkingEnabled = thinkingEnabled
        self.networkEnabled = networkEnabled
    }
} 
