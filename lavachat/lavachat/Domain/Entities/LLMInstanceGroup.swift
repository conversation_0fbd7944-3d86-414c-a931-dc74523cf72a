import Foundation

struct LLMInstanceGroup: Identifiable, Codable, Hashable {
    let id: UUID
    var name: String // Must be unique across user's groups and instances for @mention
    var description: String?
    var isFavorited: Bool = false
    var customLogoData: Data? // Custom logo image data
    var instanceIds: [UUID] // List of LLMInstance IDs in this group
    let createdAt: Date
    var lastModifiedAt: Date
    var metadata: [String: String]? // Custom icon, tags

    init(
        id: UUID = UUID(),
        name: String,
        description: String? = nil,
        isFavorited: Bool = false,
        customLogoData: Data? = nil,
        instanceIds: [UUID] = [],
        createdAt: Date = Date(),
        lastModifiedAt: Date = Date(),
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.isFavorited = isFavorited
        self.customLogoData = customLogoData
        self.instanceIds = instanceIds
        self.createdAt = createdAt
        self.lastModifiedAt = lastModifiedAt
        self.metadata = metadata
    }
} 