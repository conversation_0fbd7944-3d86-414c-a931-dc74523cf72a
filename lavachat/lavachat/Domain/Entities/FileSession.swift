import Foundation

struct FileSession: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    var originalSourceInfo: OriginalSourceInfo? // Metadata about the original file
    var title: String?
    let createdAt: Date
    var lastModifiedAt: Date
    var currentFileCheckpointId: UUID? // ID of the latest FileCheckpoint
    var rootMessageId: UUID? // Root of the message tree for this session
    var activeLLMInstanceId: UUID? // Optional: AI primarily used for editing
    var settingsId: UUID? // Reuses ChatSessionSetting
    var bookmarkData: Data? // Security-Scoped Bookmark for the managed Markdown file
    var metadata: [String: String]?

    init(
        id: UUID = UUID(),
        userId: UUID,
        originalSourceInfo: OriginalSourceInfo? = nil,
        title: String? = nil,
        createdAt: Date = Date(),
        lastModifiedAt: Date = Date(),
        currentFileCheckpointId: UUID? = nil,
        rootMessageId: UUID? = nil,
        activeLLMInstanceId: UUID? = nil,
        settingsId: UUID? = nil,
        bookmarkData: Data? = nil,
        metadata: [String : String]? = nil
    ) {
        self.id = id
        self.userId = userId
        self.originalSourceInfo = originalSourceInfo
        // Default title from original file name if available
        self.title = title ?? originalSourceInfo?.originalFileName
        self.createdAt = createdAt
        self.lastModifiedAt = lastModifiedAt
        self.currentFileCheckpointId = currentFileCheckpointId
        self.rootMessageId = rootMessageId
        self.activeLLMInstanceId = activeLLMInstanceId
        self.settingsId = settingsId
        self.bookmarkData = bookmarkData
        self.metadata = metadata
    }
}

struct OriginalSourceInfo: Codable {
    var originalFileName: String?
    var originalFileType: String? // e.g., "pdf", "docx", "md"
    // Add other relevant original info if needed
} 