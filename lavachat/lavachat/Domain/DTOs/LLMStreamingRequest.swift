import Foundation

struct LLMStreamingRequest: Codable, Equatable, Hashable {
    let id: UUID
    let instanceId: UUID
    let messageHistory: [Message]
    let overrideModelParameters: [String: AnyCodable]
    let overrideSystemPrompt: String?
    let thinkingControls: [UUID: Bool] // Control thinking capabilities per instance
    let searchingControls: [UUID: Bool] // Control searching capabilities per instance
    let timestamp: Date
    
    // MARK: - Initialization
    
    init(
        id: UUID = UUID(),
        instanceId: UUID,
        messageHistory: [Message],
        overrideModelParameters: [String: AnyCodable] = [:],
        overrideSystemPrompt: String? = nil,
        thinkingControls: [UUID: Bool] = [:],
        searchingControls: [UUID: Bool] = [:],
        timestamp: Date = Date()
    ) {
        self.id = id
        self.instanceId = instanceId
        self.messageHistory = messageHistory
        self.overrideModelParameters = overrideModelParameters
        self.overrideSystemPrompt = overrideSystemPrompt
        self.thinkingControls = thinkingControls
        self.searchingControls = searchingControls
        self.timestamp = timestamp
    }
}

// MARK: - Helper Extensions

extension LLMStreamingRequest {
    /// Get the last user message in the history
    var lastUserMessage: Message? {
        return messageHistory.last { $0.role == .user }
    }
    
    /// Get all messages sorted by timestamp
    var sortedMessageHistory: [Message] {
        return messageHistory.sorted { $0.timestamp < $1.timestamp }
    }
    
    /// Check if the request contains any file content
    var containsFileContent: Bool {
        return messageHistory.contains { message in
            message.content.contains { content in
                if case .attachedFileContent = content {
                    return true
                }
                return false
            }
        }
    }
} 