import Foundation

struct LLMStreamingResponse: Codable, Equatable, Hashable {
    let requestId: UUID
    let instanceId: UUID
    let responseType: ResponseType
    let timestamp: Date
    let metadata: ResponseMetadata?
    
    // MARK: - Response Types
    
    enum ResponseType: Codable, Equatable, Hashable {
        case contentDelta(String)
        case thinkingDelta(String)
        case completionMarker(CompletionInfo)
        case error(APIError)
        case statusUpdate(String)
        
        var isCompletion: Bool {
            switch self {
            case .completionMarker:
                return true
            default:
                return false
            }
        }
        
        var isError: Bool {
            switch self {
            case .error:
                return true
            default:
                return false
            }
        }
        
        var content: String? {
            switch self {
            case .contentDelta(let text), .thinkingDelta(let text), .statusUpdate(let text):
                return text
            default:
                return nil
            }
        }
    }
    
    // MARK: - Initialization
    
    init(
        requestId: UUID,
        instanceId: UUID,
        responseType: ResponseType,
        timestamp: Date = Date(),
        metadata: ResponseMetadata? = nil
    ) {
        self.requestId = requestId
        self.instanceId = instanceId
        self.responseType = responseType
        self.timestamp = timestamp
        self.metadata = metadata
    }
    
    // MARK: - Factory Methods
    
    static func contentDelta(
        requestId: UUID,
        instanceId: UUID,
        content: String,
        metadata: ResponseMetadata? = nil
    ) -> LLMStreamingResponse {
        return LLMStreamingResponse(
            requestId: requestId,
            instanceId: instanceId,
            responseType: .contentDelta(content),
            metadata: metadata
        )
    }
    
    static func completion(
        requestId: UUID,
        instanceId: UUID,
        completionInfo: CompletionInfo,
        metadata: ResponseMetadata? = nil
    ) -> LLMStreamingResponse {
        return LLMStreamingResponse(
            requestId: requestId,
            instanceId: instanceId,
            responseType: .completionMarker(completionInfo),
            metadata: metadata
        )
    }
    
    static func error(
        requestId: UUID,
        instanceId: UUID,
        error: APIError,
        metadata: ResponseMetadata? = nil
    ) -> LLMStreamingResponse {
        return LLMStreamingResponse(
            requestId: requestId,
            instanceId: instanceId,
            responseType: .error(error),
            metadata: metadata
        )
    }
    
    static func thinkingDelta(
        requestId: UUID,
        instanceId: UUID,
        content: String,
        metadata: ResponseMetadata? = nil
    ) -> LLMStreamingResponse {
        return LLMStreamingResponse(
            requestId: requestId,
            instanceId: instanceId,
            responseType: .thinkingDelta(content),
            metadata: metadata
        )
    }
    
    static func status(
        requestId: UUID,
        instanceId: UUID,
        status: String,
        metadata: ResponseMetadata? = nil
    ) -> LLMStreamingResponse {
        return LLMStreamingResponse(
            requestId: requestId,
            instanceId: instanceId,
            responseType: .statusUpdate(status),
            metadata: metadata
        )
    }
}

// MARK: - Supporting Types

struct CompletionInfo: Codable, Equatable, Hashable {
    let promptTokens: Int64?
    let completionTokens: Int64?
    let totalTokens: Int64?
    let finishReason: String?
    let modelUsed: String?
    
    init(
        promptTokens: Int64? = nil,
        completionTokens: Int64? = nil,
        totalTokens: Int64? = nil,
        finishReason: String? = nil,
        modelUsed: String? = nil
    ) {
        self.promptTokens = promptTokens
        self.completionTokens = completionTokens
        self.totalTokens = totalTokens ?? ((promptTokens ?? 0) + (completionTokens ?? 0))
        self.finishReason = finishReason
        self.modelUsed = modelUsed
    }
}

struct ResponseMetadata: Codable, Equatable, Hashable {
    let modelIdentifier: String?
    let providerId: UUID?
    let processingTimeMs: Int64?
    let sequence: Int64? // For ordering multiple responses
    let additionalInfo: [String: String]?
    
    init(
        modelIdentifier: String? = nil,
        providerId: UUID? = nil,
        processingTimeMs: Int64? = nil,
        sequence: Int64? = nil,
        additionalInfo: [String: String]? = nil
    ) {
        self.modelIdentifier = modelIdentifier
        self.providerId = providerId
        self.processingTimeMs = processingTimeMs
        self.sequence = sequence
        self.additionalInfo = additionalInfo
    }
}

// MARK: - Convenience Extensions

extension LLMStreamingResponse {
    /// Check if this response indicates the stream is complete
    var isStreamComplete: Bool {
        return responseType.isCompletion || responseType.isError
    }
    
    /// Get the total tokens used if available
    var totalTokens: Int64? {
        switch responseType {
        case .completionMarker(let info):
            return info.totalTokens
        default:
            return nil
        }
    }
    
    /// Get the error if this is an error response
    var error: APIError? {
        switch responseType {
        case .error(let apiError):
            return apiError
        default:
            return nil
        }
    }
} 