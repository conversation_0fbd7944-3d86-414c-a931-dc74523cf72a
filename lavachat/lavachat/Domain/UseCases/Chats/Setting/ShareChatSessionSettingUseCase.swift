import Foundation

/// Use case for sharing chat session settings using various formats
final class ShareChatSessionSettingUseCase: ShareChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let shareService: ShareServiceProtocol
    
    // MARK: - Initialization
    
    init(shareService: ShareServiceProtocol) {
        self.shareService = shareService
    }
    
    // MARK: - ShareChatSessionSettingUseCaseProtocol Implementation
    
    func execute(
        _ setting: ChatSessionSetting,
        configuration: ShareConfiguration
    ) async -> ShareResult {
        return await shareService.shareItem(setting, configuration: configuration)
    }
    
    func shareAsFile(
        _ setting: ChatSessionSetting,
        fileName: String?
    ) async throws -> URL {
        return try await shareService.shareAsFile(setting, fileName: fileName)
    }
    
    func shareViaICloud(
        _ setting: ChatSessionSetting,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        return try await shareService.shareViaICloud(setting, permissions: permissions)
    }
    
    func generateQRCode(
        for setting: ChatSessionSetting,
        size: CGSize
    ) async throws -> Data {
        return try await shareService.generateQRCode(for: setting, size: size)
    }
}
