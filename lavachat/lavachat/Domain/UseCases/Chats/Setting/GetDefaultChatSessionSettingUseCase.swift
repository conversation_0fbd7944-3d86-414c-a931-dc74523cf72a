import Foundation

/// Retrieves the system default chat session setting.
final class GetDefaultChatSessionSettingUseCase: GetDefaultChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute() async throws -> ChatSessionSetting {
        guard let defaultSetting = try await chatRepository.getSystemDefaultSetting() else {
            throw ChatError.settingNotFound
        }
        
        return defaultSetting
    }
} 