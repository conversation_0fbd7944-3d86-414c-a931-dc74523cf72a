import Foundation
import Combine

// MARK: - ChatSessionSetting UseCase Protocols

/// Retrieves a specific chat session setting by ID.
protocol GetChatSessionSettingUseCaseProtocol {
    func execute(settingId: UUID) async throws -> ChatSessionSetting?
    func executeAndResolveActions(settingId: UUID) async throws -> (setting: ChatSessionSetting, resolvedActions: ResolvedMessageActionSettings)?
}

/// Retrieves all chat session settings.
protocol GetAllChatSessionSettingsUseCaseProtocol {
    func execute() async throws -> [ChatSessionSetting]
}

/// Retrieves the system default chat session setting.
protocol GetDefaultChatSessionSettingUseCaseProtocol {
    func execute() async throws -> ChatSessionSetting
}

/// Creates a new chat session setting using direct entity approach.
protocol CreateChatSessionSettingUseCaseProtocol {
    func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting
}

/// Updates an existing chat session setting using direct entity approach.
protocol UpdateChatSessionSettingUseCaseProtocol {
    func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting
}

/// Updates the user's default chat session setting.
protocol UpdateUserDefaultChatSettingUseCaseProtocol {
    func execute(settingId: UUID) async throws -> User
}

/// Deletes a chat session setting.
protocol DeleteChatSessionSettingUseCaseProtocol {
    func execute(settingId: UUID) async throws
}

// MARK: - Share and Import UseCase Protocols

/// Shares a chat session setting using various formats
protocol ShareChatSessionSettingUseCaseProtocol {
    /// Shares a chat session setting with the specified configuration
    /// - Parameters:
    ///   - setting: The chat session setting to share
    ///   - configuration: Configuration for the sharing operation
    /// - Returns: Result of the sharing operation
    func execute(
        _ setting: ChatSessionSetting,
        configuration: ShareConfiguration
    ) async -> ShareResult

    /// Shares a chat session setting as a file
    /// - Parameters:
    ///   - setting: The chat session setting to share
    ///   - fileName: Optional custom file name
    /// - Returns: URL of the created file
    /// - Throws: ShareError if the operation fails
    func shareAsFile(
        _ setting: ChatSessionSetting,
        fileName: String?
    ) async throws -> URL

    /// Shares a chat session setting via iCloud
    /// - Parameters:
    ///   - setting: The chat session setting to share
    ///   - permissions: iCloud sharing permissions
    /// - Returns: iCloud share URL
    /// - Throws: ShareError if the operation fails
    func shareViaICloud(
        _ setting: ChatSessionSetting,
        permissions: ICloudSharePermissions
    ) async throws -> URL

    /// Generates QR code for sharing a chat session setting
    /// - Parameters:
    ///   - setting: The chat session setting to share
    ///   - size: Size of the QR code image
    /// - Returns: QR code image data
    /// - Throws: ShareError if the operation fails
    func generateQRCode(
        for setting: ChatSessionSetting,
        size: CGSize
    ) async throws -> Data
}

/// Imports chat session settings from various sources
protocol ImportChatSessionSettingUseCaseProtocol {
    /// Imports a chat session setting from a file
    /// - Parameters:
    ///   - fileURL: URL of the file to import
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports a chat session setting from iCloud share URL
    /// - Parameters:
    ///   - shareURL: iCloud share URL
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports a chat session setting from QR code data
    /// - Parameters:
    ///   - qrCodeData: Data extracted from QR code
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult
}