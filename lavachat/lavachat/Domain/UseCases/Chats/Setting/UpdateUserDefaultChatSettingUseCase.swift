import Foundation

/// Updates the user's default chat session setting.
final class UpdateUserDefaultChatSettingUseCase: UpdateUserDefaultChatSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let getCurrentUserUseCase: GetCurrentUserUseCaseProtocol
    private let updateUserUseCase: UpdateUserUseCaseProtocol
    
    // MARK: - Initialization
    
    init(
        getCurrentUserUseCase: GetCurrentUserUseCaseProtocol,
        updateUserUseCase: UpdateUserUseCaseProtocol
    ) {
        self.getCurrentUserUseCase = getCurrentUserUseCase
        self.updateUserUseCase = updateUserUseCase
    }
    
    // MARK: - UseCase Execution
    
    func execute(settingId: UUID) async throws -> User {
        guard let currentUser = try await getCurrentUserUseCase.execute() else {
            throw ChatError.userNotFound
        }
        
        var updatedUser = currentUser
        updatedUser.defaultChatSettingsId = settingId
        
        return try await updateUserUseCase.execute(updatedUser)
    }
}
