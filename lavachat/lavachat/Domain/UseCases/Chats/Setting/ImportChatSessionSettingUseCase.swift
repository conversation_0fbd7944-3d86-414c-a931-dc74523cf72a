import Foundation

/// Use case for importing chat session settings from various sources
final class ImportChatSessionSettingUseCase: ImportChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let importService: ImportServiceProtocol
    
    // MARK: - Initialization
    
    init(importService: ImportServiceProtocol) {
        self.importService = importService
    }
    
    // MARK: - ImportChatSessionSettingUseCaseProtocol Implementation
    
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromFile(fileURL, configuration: configuration)
    }
    
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromICloudShare(shareURL, configuration: configuration)
    }
    
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromQRCode(qrCodeData, configuration: configuration)
    }
}
