import Foundation

/// Updates an existing chat session setting using the direct entity approach.
final class UpdateChatSessionSettingUseCase: UpdateChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting {
        // Verify the setting exists
        guard let existingSetting = try await chatRepository.getSetting(byId: setting.id) else {
            throw ChatError.settingNotFound
        }
        
        // Check if this is a system default setting and handle accordingly
        if existingSetting.isSystemDefault {
            // For system default settings, check if it has been modified by the user
            let isUnmodified = existingSetting.createdAt == existingSetting.lastModifiedAt
            
            if !isUnmodified {
                // User has modified the system default setting before, allow update
                var updatedSetting = setting
                updatedSetting.lastModifiedAt = Date()
                try await chatRepository.updateSetting(updatedSetting)
                return updatedSetting
            } else {
                // First time modification of system default, update timestamps
                var updatedSetting = setting
                updatedSetting.lastModifiedAt = Date()
                try await chatRepository.updateSetting(updatedSetting)
                return updatedSetting
            }
        } else {
            // Regular user setting, update normally
            var updatedSetting = setting
            updatedSetting.lastModifiedAt = Date()
            try await chatRepository.updateSetting(updatedSetting)
            return updatedSetting
        }
    }
} 