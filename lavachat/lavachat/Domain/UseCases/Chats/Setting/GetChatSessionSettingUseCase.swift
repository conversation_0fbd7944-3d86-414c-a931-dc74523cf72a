import Foundation

/// Retrieves a specific chat session setting by its ID.
final class GetChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(settingId: UUID) async throws -> ChatSessionSetting? {
        // Try to get the requested setting first
        if let setting = try await chatRepository.getSetting(byId: settingId) {
            return setting
        }

        // If the requested setting is not found, fallback to system default
        // This ensures we always return a valid setting for the app to function
        return try await chatRepository.getSetting(byId: BuiltinConstants.systemDefaultChatSessionSettingId)
    }
    
    func executeAndResolveActions(settingId: UUID) async throws -> (setting: ChatSessionSetting, resolvedActions: ResolvedMessageActionSettings)? {
        // Try to get the requested setting first, fallback to system default if not found
        let setting: ChatSessionSetting
        if let requestedSetting = try await chatRepository.getSetting(byId: settingId) {
            setting = requestedSetting
        } else {
            // Fallback to system default setting
            guard let systemDefaultSetting = try await chatRepository.getSetting(byId: BuiltinConstants.systemDefaultChatSessionSettingId) else {
                return nil
            }
            setting = systemDefaultSetting
        }
        
        let allActionIDs = setting.messageActionSettings?.uniqueActionIds ?? []
        
        // Fetch all custom actions by their IDs
        let customActions = try await chatRepository.getMessageActions(for: allActionIDs)
        let customActionsDict = Dictionary(uniqueKeysWithValues: customActions.map { ($0.id, $0) })
        
        // Resolve system and custom actions
        let resolveActions = { (actionIDs: [UUID]?) -> [MessageAction] in
            guard let actionIDs = actionIDs else { return [] }
            return actionIDs.compactMap { id in
                if let systemAction = SystemMessageActions.actions.first(where: { $0.id == id }) {
                    return systemAction
                } else if let customAction = customActionsDict[id] {
                    return customAction
                } else {
                    // Handle case where action is not found (e.g., deleted custom action)
                    print("Warning: MessageAction with ID \(id.uuidString) not found.")
                    return nil
                }
            }
        }
        
        let resolvedActionPanelActions = resolveActions(setting.messageActionSettings?.actionPanelActions)
        let resolvedUserActions = resolveActions(setting.messageActionSettings?.userMessageActions)
        let resolvedAssistantCardActions = resolveActions(setting.messageActionSettings?.assistantMessageCardActions)
        let resolvedAssistantMenuActions = resolveActions(setting.messageActionSettings?.assistantMessageMenuActions)
        
        let resolvedMessageActionSettings = ResolvedMessageActionSettings(
            actionPanelActions: resolvedActionPanelActions,
            userMessageActions: resolvedUserActions,
            assistantMessageCardActions: resolvedAssistantCardActions,
            assistantMessageMenuActions: resolvedAssistantMenuActions
        )
        
        return (setting, resolvedMessageActionSettings)
    }
} 
