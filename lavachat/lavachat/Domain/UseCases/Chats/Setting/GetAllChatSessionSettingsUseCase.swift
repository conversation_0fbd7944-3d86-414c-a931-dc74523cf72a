import Foundation

/// Retrieves all chat session settings.
final class GetAllChatSessionSettingsUseCase: GetAllChatSessionSettingsUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute() async throws -> [ChatSessionSetting] {
        return try await chatRepository.getAllSettings()
    }
} 