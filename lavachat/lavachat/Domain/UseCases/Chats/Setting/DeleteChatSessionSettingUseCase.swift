import Foundation

/// Deletes a chat session setting with proper validation and business logic.
final class DeleteChatSessionSettingUseCase: DeleteChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(settingId: UUID) async throws {
        // Verify the setting exists
        guard let existingSetting = try await chatRepository.getSetting(byId: settingId) else {
            throw ChatError.settingNotFound
        }
        
        // Check if this is a system default setting (cannot be deleted)
        if existingSetting.isSystemDefault {
            throw ChatError.systemSettingPermissionDenied
        }
        
        // Check if this setting is used by any chat sessions
        // The repository layer already handles this check, but we can add additional validation here if needed
        
        // Perform the deletion
        // The repository will handle:
        // 1. Checking if setting is used by chat sessions
        // 2. Updating users who have this as their default setting to use system default
        // 3. Actual deletion from storage
        try await chatRepository.deleteSetting(byId: settingId)
    }
}
