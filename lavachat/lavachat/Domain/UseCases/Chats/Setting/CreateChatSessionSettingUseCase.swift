import Foundation

/// Creates a new chat session setting using the direct entity approach.
final class CreateChatSessionSettingUseCase: CreateChatSessionSettingUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting {
        // Ensure that non-system settings cannot be marked as system default
        if setting.isSystemDefault {
            throw ChatError.settingNotFound // Invalid configuration
        }
        
        // Create the setting
        try await chatRepository.createSetting(setting)
        
        return setting
    }
} 
