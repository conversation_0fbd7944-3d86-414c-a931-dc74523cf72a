import Foundation

// MARK: - MessageAction UseCase Protocols

/// Creates a new message action.
protocol CreateMessageActionUseCaseProtocol {
    func execute(_ action: MessageAction) async throws -> MessageAction
}

/// Retrieves a specific message action by its ID.
protocol GetMessageActionUseCaseProtocol {
    func execute(actionId: UUID) async throws -> MessageAction?
}

/// Retrieves all message actions.
protocol GetAllMessageActionsUseCaseProtocol {
    func execute() async throws -> [MessageAction] // Only user-created actions
    func executeIncludingSystemActions() async throws -> [MessageAction]
}

/// Updates an existing message action.
protocol UpdateMessageActionUseCaseProtocol {
    func execute(_ action: MessageAction) async throws -> MessageAction
}

/// Deletes a message action by its ID.
protocol DeleteMessageActionUseCaseProtocol {
    func execute(actionId: UUID) async throws
}

/// Checks which chat session settings are using a specific message action.
protocol GetMessageActionUsageUseCaseProtocol {
    func execute(actionId: UUID) async throws -> [ChatSessionSetting]
}

// MARK: - Share and Import UseCase Protocols

/// Shares a message action using various formats
protocol ShareMessageActionUseCaseProtocol {
    /// Shares a message action with the specified configuration
    /// - Parameters:
    ///   - action: The message action to share
    ///   - configuration: Configuration for the sharing operation
    /// - Returns: Result of the sharing operation
    func execute(
        _ action: MessageAction,
        configuration: ShareConfiguration
    ) async -> ShareResult

    /// Shares a message action as a file
    /// - Parameters:
    ///   - action: The message action to share
    ///   - fileName: Optional custom file name
    /// - Returns: URL of the created file
    /// - Throws: ShareError if the operation fails
    func shareAsFile(
        _ action: MessageAction,
        fileName: String?
    ) async throws -> URL

    /// Shares a message action via iCloud
    /// - Parameters:
    ///   - action: The message action to share
    ///   - permissions: iCloud sharing permissions
    /// - Returns: iCloud share URL
    /// - Throws: ShareError if the operation fails
    func shareViaICloud(
        _ action: MessageAction,
        permissions: ICloudSharePermissions
    ) async throws -> URL

    /// Generates QR code for sharing a message action
    /// - Parameters:
    ///   - action: The message action to share
    ///   - size: Size of the QR code image
    /// - Returns: QR code image data
    /// - Throws: ShareError if the operation fails
    func generateQRCode(
        for action: MessageAction,
        size: CGSize
    ) async throws -> Data
}

/// Imports message actions from various sources
protocol ImportMessageActionUseCaseProtocol {
    /// Imports a message action from a file
    /// - Parameters:
    ///   - fileURL: URL of the file to import
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports a message action from iCloud share URL
    /// - Parameters:
    ///   - shareURL: iCloud share URL
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports a message action from QR code data
    /// - Parameters:
    ///   - qrCodeData: Data extracted from QR code
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult
}
