import Foundation

/// Use case for retrieving a specific message action.
final class GetMessageActionUseCase: GetMessageActionUseCaseProtocol {
    private let chatRepository: ChatRepository

    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }

    func execute(actionId: UUID) async throws -> MessageAction? {
        return try await chatRepository.getMessageAction(byId: actionId)
    }
}
