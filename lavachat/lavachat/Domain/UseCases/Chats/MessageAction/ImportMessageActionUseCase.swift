import Foundation

/// Use case for importing message actions from various sources
final class ImportMessageActionUseCase: ImportMessageActionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let importService: ImportServiceProtocol
    
    // MARK: - Initialization
    
    init(importService: ImportServiceProtocol) {
        self.importService = importService
    }
    
    // MARK: - ImportMessageActionUseCaseProtocol Implementation
    
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromFile(fileURL, configuration: configuration)
    }
    
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromICloudShare(shareURL, configuration: configuration)
    }
    
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromQRCode(qrCodeData, configuration: configuration)
    }
}
