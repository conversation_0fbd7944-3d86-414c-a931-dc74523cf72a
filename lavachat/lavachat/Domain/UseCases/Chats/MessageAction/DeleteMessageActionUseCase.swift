import Foundation

/// Use case for deleting a message action.
final class DeleteMessageActionUseCase: DeleteMessageActionUseCaseProtocol {
    private let chatRepository: ChatRepository

    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }

    func execute(actionId: UUID) async throws {
        // Optional: Check if the action is a system action before attempting to delete.
        if SystemMessageActions.actions.contains(where: { $0.id == actionId }) {
            throw ChatError.systemActionPermissionDenied
        }
        try await chatRepository.deleteMessageAction(byId: actionId)
    }
}
