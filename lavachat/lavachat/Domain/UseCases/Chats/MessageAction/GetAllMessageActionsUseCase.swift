import Foundation

/// Use case for retrieving all user-created message actions.
final class GetAllMessageActionsUseCase: GetAllMessageActionsUseCaseProtocol {
    private let chatRepository: ChatRepository

    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }

    func execute() async throws -> [MessageAction] {
        return try await chatRepository.getAllMessageActions()
    }

    func executeIncludingSystemActions() async throws -> [MessageAction] {
        let userActions = try await chatRepository.getAllMessageActions()
        return userActions + SystemMessageActions.actions
    }
}
