import Foundation

/// Use case for checking which chat session settings are using a specific message action.
final class GetMessageActionUsageUseCase: GetMessageActionUsageUseCaseProtocol {
    private let chatRepository: ChatRepository
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    func execute(actionId: UUID) async throws -> [ChatSessionSetting] {
        // Get all chat session settings
        let allSettings = try await chatRepository.getAllSettings()
        
        // Filter settings that use this action
        let usingSettings = allSettings.filter { setting in
            guard let messageActionSettings = setting.messageActionSettings else {
                return false
            }
            
            // Check if the action ID exists in any of the action arrays
            return messageActionSettings.uniqueActionIds.contains(actionId)
        }
        
        return usingSettings
    }
}
