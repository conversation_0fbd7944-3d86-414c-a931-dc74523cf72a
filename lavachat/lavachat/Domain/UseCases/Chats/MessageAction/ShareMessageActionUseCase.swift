import Foundation

/// Use case for sharing message actions using various formats
final class ShareMessageActionUseCase: ShareMessageActionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let shareService: ShareServiceProtocol
    
    // MARK: - Initialization
    
    init(shareService: ShareServiceProtocol) {
        self.shareService = shareService
    }
    
    // MARK: - ShareMessageActionUseCaseProtocol Implementation
    
    func execute(
        _ action: MessageAction,
        configuration: ShareConfiguration
    ) async -> ShareResult {
        return await shareService.shareItem(action, configuration: configuration)
    }
    
    func shareAsFile(
        _ action: MessageAction,
        fileName: String?
    ) async throws -> URL {
        return try await shareService.shareAsFile(action, fileName: fileName)
    }
    
    func shareViaICloud(
        _ action: MessageAction,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        return try await shareService.shareViaICloud(action, permissions: permissions)
    }
    
    func generateQRCode(
        for action: MessageAction,
        size: CGSize
    ) async throws -> Data {
        return try await shareService.generateQRCode(for: action, size: size)
    }
}
