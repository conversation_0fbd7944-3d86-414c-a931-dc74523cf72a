import Foundation

/// Use case for updating an existing message action.
final class UpdateMessageActionUseCase: UpdateMessageActionUseCaseProtocol {
    private let chatRepository: ChatRepository

    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }

    func execute(_ action: MessageAction) async throws -> MessageAction {
        // Ensure we are not trying to update a system action.
        if case .system = action.actionType {
            throw ChatError.systemActionPermissionDenied
        }
        try await chatRepository.updateMessageAction(action)
        return action
    }
}
