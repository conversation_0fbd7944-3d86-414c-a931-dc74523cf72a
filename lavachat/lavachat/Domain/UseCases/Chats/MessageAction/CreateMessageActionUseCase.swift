import Foundation

/// Use case for creating a new message action.
final class CreateMessageActionUseCase: CreateMessageActionUseCaseProtocol {
    private let chatRepository: ChatRepository

    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }

    func execute(_ action: MessageAction) async throws -> MessageAction {
        // Ensure the action being created is not marked as a system action.
        let newAction = action
        if case .system = newAction.actionType {
            throw ChatError.systemActionPermissionDenied
        }
        try await chatRepository.createMessageAction(newAction)
        return newAction
    }
}
