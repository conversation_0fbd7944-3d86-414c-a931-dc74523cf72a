import Foundation

/// Deletes a specific message and handles message tree structure implications.
final class DeleteMessageUseCase: DeleteMessageUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(messageId: UUID) async throws {
        // Verify the message exists
        guard let messageToDelete = try await chatRepository.getMessage(byId: messageId) else {
            return // Message doesn't exist, nothing to delete
        }
        
        // Get all child messages that depend on this message
        let childMessages = try await chatRepository.getChildMessages(for: messageId)
        
        // Handle child message dependencies
        if !childMessages.isEmpty {
            // Option 1: Delete all child messages (cascade delete)
            // Option 2: Orphan child messages by setting their parentId to the deleted message's parent
            // For this implementation, we'll use cascade delete for simplicity
            
            for childMessage in childMessages {
                try await execute(messageId: childMessage.id) // Recursive deletion
            }
        }
        
        // Update session's activeMessageId if it points to this message
        let session = try await chatRepository.getChatSession(byId: messageToDelete.sessionId)
        if let session = session, session.activeMessageId == messageId {
            var updatedSession = session
            updatedSession.activeMessageId = messageToDelete.parentId // Set to parent or nil
            updatedSession.lastModifiedAt = Date()
            try await chatRepository.updateChatSession(updatedSession)
        }
        
        // Update session's rootMessageIds if this is a root message
        if let session = session, messageToDelete.parentId == nil {
            var updatedSession = session
            updatedSession.lastModifiedAt = Date()
            try await chatRepository.updateChatSession(updatedSession)
        }
        
        // Delete the message
        try await chatRepository.deleteMessage(byId: messageId)
    }
} 
