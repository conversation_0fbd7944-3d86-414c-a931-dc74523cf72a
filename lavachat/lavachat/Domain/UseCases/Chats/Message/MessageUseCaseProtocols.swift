import Foundation
import Combine

// MARK: - Message UseCase Protocols

/// Prepares messages for immediate UI display and subsequent streaming
protocol PrepareMessagesUseCaseProtocol {
    func execute(
        chatSession: ChatSession,
        chatSessionSetting: ChatSessionSetting,
        content: [ContentBlock],
        instanceIds: [UUID],
        operationType: MessageOperationType,
        parentMessageId: UUID?,
        originalMessageId: UUID?,
        userMessageDepth: Int64
    ) async throws -> PreparedMessages
}

/// Unified message sending protocol accepting prepared messages
protocol SendMessageUseCaseProtocol {
    func execute(preparedMessages: PreparedMessages) -> AsyncThrowingStream<LLMStreamingResponse, Error>
    func stopSending(sessionId: UUID) async
}

/// Retrieves all messages for a specific chat session.
protocol GetMessagesForSessionUseCaseProtocol {
    func execute(sessionId: UUID) async throws -> [Message]
}

/// Retrieves messages by their IDs.
protocol GetMessagesUseCaseProtocol {
    func execute(messageIds: [UUID]) async throws -> [Message]
}

/// Creates a new message using direct entity approach.
protocol CreateMessageUseCaseProtocol {
    func execute(_ message: Message) async throws -> Message
}

/// Updates an existing message using direct entity approach.
protocol UpdateMessageUseCaseProtocol {
    func execute(_ message: Message) async throws -> Message
}

/// Deletes a specific message by ID.
protocol DeleteMessageUseCaseProtocol {
    func execute(messageId: UUID) async throws
}

/// Regenerates an assistant message (now returns streaming response and placeholder message)
protocol RegenerateSingleMessageUseCaseProtocol {
    func execute(message: Message, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> (stream: AsyncThrowingStream<LLMStreamingResponse, Error>, placeholderMessage: Message)
}

/// Regenerates an assistant message with a custom prompt
protocol RegenerateSingleMessageWithPromptUseCaseProtocol {
    func execute(message: Message, prompt: String, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> (stream: AsyncThrowingStream<LLMStreamingResponse, Error>, placeholderMessage: Message)
}

/// Rewrites user input with LLM-processed prompt
protocol RewritePromptWithPromptUseCaseProtocol {
    func execute(originalInput: String, prompt: String, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> AsyncThrowingStream<LLMStreamingResponse, Error>
}

/// Generates chat title using auxiliary LLM instance
protocol GenerateChatTitleUseCaseProtocol {
    func execute(userInput: String, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> String
}

/// Regenerates all assistant messages for a user message (returns streaming response and new message layer)
protocol RegenerateAllMessagesUseCaseProtocol {
    func execute(userMessage: Message, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> (stream: AsyncThrowingStream<LLMStreamingResponse, Error>, newAssistantMessageLayer: MessageLayer)
}

/// Creates an edited version of a message (now returns streaming response)
protocol EditMessageUseCaseProtocol {
    func execute(originalMessageId: UUID, newContent: [ContentBlock]) -> AsyncThrowingStream<LLMStreamingResponse, Error>
}

/// Manages message tree caching and structure with efficient incremental updates
protocol MessageTreeManagerUseCaseProtocol {
    // MARK: - Initialization
    /// Initialize tree from repository data
    func initializeTreeFromRepository(sessionId: UUID) async throws
    
    // MARK: - Incremental Updates
    /// Add a message and update active message
    /// This will update the active message in the cache and the repository
    func addMessageAndUpdateTree(sessionId: UUID, message: Message) async
    
    /// Switch active message and return updated message layers
    /// This will update the active message in the cache and the repository
    func switchActiveMessage(sessionId: UUID, fromMessageId: UUID, newActiveMessageId: UUID) async throws -> [MessageLayer]
    
    /// Add multiple messages in batch with intelligent ordering
    /// This will update the active message in the cache and the repository
    func addMultipleMessagesAndUpdateTree(sessionId: UUID, messages: [Message]) async
    
    // MARK: - Cache Management
    /// Update cache when a message is modified
    func updateMessageInCache(sessionId: UUID, message: Message)
    
    /// Remove message from cache
    func removeMessageFromCache(sessionId: UUID, messageId: UUID)
    
    /// Updates chat session in both cache and repository, with validation
    func updateChatSessionInCacheAndRepository(chatSession: ChatSession) async throws
    
    /// Updates message in both cache and repository
    func updateMessageInCacheAndRepository(sessionId: UUID, message: Message) async throws
    
    // MARK: - Query Interface
    /// Get ordered message layers for display
    func getMessageLayersForDisplay(sessionId: UUID) async -> [MessageLayer]
    
    /// Get active path message (root to active)
    func getActivePathMessage(for sessionId: UUID) -> [Message]
    
    /// Get active path message as UUIDs
    func getActivePathId(for sessionId: UUID) -> [UUID]
    
    /// Create a message layer from messages without cache operations (zero-delay)
    func createMessageLayerFromMessages(
        chatSession: ChatSession,
        messages: [Message]
    ) -> MessageLayer
}