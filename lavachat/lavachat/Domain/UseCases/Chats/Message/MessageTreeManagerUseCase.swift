import Foundation

/// Central message tree management use case providing caching and tree structure computation
final class MessageTreeManagerUseCase: MessageTreeManagerUseCaseProtocol {
    
    // MARK: - Properties
    
    private let chatRepository: ChatRepository
    private var treeCache: [UUID: MessageTreeCache] = [:]
    private let cacheQueue = DispatchQueue(label: "message-tree-cache", qos: .userInitiated)
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - Cache Initialization & Management
    
    func initializeTreeFromRepository(sessionId: UUID) async throws {
        // Get all messages for the session
        let messages = try await chatRepository.getMessages(for: sessionId)
        print("📝 Retrieved \(messages.count) messages from repository")
        
        let chatSession = try await chatRepository.getChatSession(byId: sessionId)
        print("📋 Chat session: \(chatSession != nil ? "found" : "not found")")
        if chatSession == nil {
            throw ChatError.sessionNotFound
        }
        
        // Create new cache
        let cache = MessageTreeCache(sessionId: sessionId, chatSession: chatSession)
        
        // Rebuild from messages (this is initialization, so use full rebuild)
        cache.rebuildFromMessages(messages, activeMessageId: chatSession?.activeMessageId)
        print("🌳 Cache rebuilt with \(messages.count) messages")
        
        // Store in cache
        cacheQueue.sync {
            treeCache[sessionId] = cache
        }
        print("✅ Cache stored for session: \(sessionId)")
    }
    
    /// Clear cache for a specific session
    func clearCache(for sessionId: UUID) {
        cacheQueue.sync {
            _ = treeCache.removeValue(forKey: sessionId)
        }
    }
    
    /// Clear all caches
    func clearAllCaches() {
        cacheQueue.sync {
            treeCache.removeAll()
        }
    }
    
    // MARK: - Message Operations
    
    func addMessageAndUpdateTree(sessionId: UUID, message: Message) async {
        cacheQueue.sync {
            guard let cache = treeCache[sessionId] else {
                print("⚠️ Warning: No cache found for session \(sessionId)")
                return
            }
            
            // Add message to cache
            cache.addMessage(message)
            
            // Update active message using new method
            cache.buildActivePathFromMessage(startMessage: message)
        }
        
        // Update repository with new active message
        await updateChatSessionActiveMessage(sessionId: sessionId)
    }
    
    func addMultipleMessagesAndUpdateTree(sessionId: UUID, messages: [Message]) async {
        cacheQueue.sync {
            guard let cache = treeCache[sessionId] else {
                print("⚠️ Warning: No cache found for session \(sessionId)")
                return
            }
            
            // Sort messages intelligently before adding
            let sortedMessages = MessageTreeCache.sortMessagesInALayerForDisplay(messages: messages, chatSession: cache.chatSession)
            
            // Add all messages to cache
            for message in sortedMessages {
                cache.addMessage(message)
            }
            
            // Set the most preferred message as active using new method
            if let preferredMessage = sortedMessages.first,
               preferredMessage.role == .assistant || preferredMessage.role == .mergedAssistant {
                // For assistant messages, use the first (highest priority) message after sorting
                cache.buildActivePathFromMessage(startMessage: preferredMessage)
            } else if let lastMessage = sortedMessages.last {
                // For other message types, use the last message
                cache.buildActivePathFromMessage(startMessage: lastMessage)
            }
        }
        
        // Update repository with new active message
        await updateChatSessionActiveMessage(sessionId: sessionId)
    }
    
    func updateMessageInCache(sessionId: UUID, message: Message) {
        cacheQueue.sync {
            treeCache[sessionId]?.updateMessage(message)
        }
    }
    
    func removeMessageFromCache(sessionId: UUID, messageId: UUID) {
        cacheQueue.sync {
            treeCache[sessionId]?.removeMessage(messageId)
        }
    }
    
    func updateChatSessionInCacheAndRepository(chatSession: ChatSession) async throws {
        // Update cache first (synchronous operation)
        let shouldUpdateRepository = cacheQueue.sync { () -> Bool in
            // Check if session exists in cache
            guard let cache = treeCache[chatSession.id] else {
                print("⚠️ Warning: Session \(chatSession.id) not found in cache, cannot update")
                return false
            }
            
            // Check if activeMessageId was updated
            let currentActiveMessageId = cache.chatSession?.activeMessageId
            let newActiveMessageId = chatSession.activeMessageId
            
            if currentActiveMessageId != newActiveMessageId {
                print("⚠️ Warning: Cannot update activeMessageId through this method. Use specific navigation methods instead.")
                return false
            }
            
            // Update cache with new chatSession
            cache.chatSession = chatSession
            print("✅ Updated chatSession in cache for session: \(chatSession.id)")
            return true
        }
        
        // Update repository if cache update was successful
        if shouldUpdateRepository {
            try await chatRepository.updateChatSession(chatSession)
            print("✅ Updated chatSession in repository for session: \(chatSession.id)")
        }
    }
    
    func updateMessageInCacheAndRepository(sessionId: UUID, message: Message) async throws {
        // Update cache first (synchronous operation)
        let shouldUpdateRepository = cacheQueue.sync { () -> Bool in
            // Check if session exists in cache
            guard let cache = treeCache[sessionId] else {
                print("⚠️ Warning: Session \(sessionId) not found in cache, cannot update message")
                return false
            }
            
            // Update message in cache
            cache.updateMessage(message)
            print("✅ Updated message \(message.id) in cache for session: \(sessionId)")
            return true
        }
        
        // Update repository if cache update was successful
        if shouldUpdateRepository {
            try await chatRepository.updateMessage(message)
            print("✅ Updated message \(message.id) in repository for session: \(sessionId)")
        }
    }
    
    // MARK: - Navigation Operations
    
    func switchActiveMessage(sessionId: UUID, fromMessageId: UUID, newActiveMessageId: UUID) async throws -> [MessageLayer] {
        print("🎯 switchActiveMessage: \(sessionId) fromMessageId: \(fromMessageId) newActiveMessageId: \(newActiveMessageId)")
        try await withCheckedThrowingContinuation { continuation in
            cacheQueue.sync {
                do {
                    guard let cache = treeCache[sessionId] else {
                        throw ChatError.sessionNotFound
                    }
                    
                    guard let fromMessage = cache.getMessage(by: fromMessageId) else {
                        throw ChatError.invalidMessageNavigation
                    }
                    
                    guard let newActiveMessage = cache.getMessage(by: newActiveMessageId), newActiveMessage.userFeedback != .disliked else {
                        throw ChatError.invalidMessageNavigation
                    }

                    guard newActiveMessage.parentId == fromMessage.parentId else {
                        throw ChatError.invalidMessageNavigation
                    }
                    
                    // Use new optimized update method
                    cache.updateFromMessage(messageId: fromMessageId, newActiveMessageId: newActiveMessageId)
                    
                    continuation.resume(returning: ())
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
        
        // Update repository with new active message
        await updateChatSessionActiveMessage(sessionId: sessionId)
        
        // Get updated message layers after the switch
        return await getMessageLayersForDisplay(sessionId: sessionId)
    }
    
    // MARK: - Query Operations
    
    func getMessageLayersForDisplay(sessionId: UUID) async -> [MessageLayer] {
        print("🎯 getMessageLayersForDisplay for session: \(sessionId)")
        
        // Get cache
        guard let cache = getCachedTree(sessionId: sessionId) else {
            print("❌ No cache found for session: \(sessionId)")
            return []
        }
        print("📦 Cache found for session: \(sessionId)")
        
        var layers: [MessageLayer] = []
        
        // Build layers from active path
        let activeHistory = cache.getActivePathMessage()
        var processedMessageIds = Set<UUID>()
        
        for message in activeHistory {
            guard !processedMessageIds.contains(message.id) else { continue }
            
            var layerMessages: [Message] = []
            
            if message.role == .user {
                // For user message, find all messages with the same parent (siblings including edits)
                let siblings = cache.getSiblingsAt(depth: message.depth).filter { $0.role == .user }
                layerMessages = siblings
                
                // Mark all user messages in this layer as processed
                siblings.forEach { processedMessageIds.insert($0.id) }

                // Sort by timestamp
                layerMessages.sort { $0.timestamp < $1.timestamp }
                
            } else if message.role == .assistant || message.role == .mergedAssistant {
                // For assistant message, find all responses to the same parent
                let siblings = cache.getSiblingsAt(depth: message.depth).filter { 
                    $0.role == .assistant || $0.role == .mergedAssistant
                }
                
                // Apply filtering for assistant messages
                layerMessages = cache.selectPreferredAssistantMessages(from: siblings)
                
                // Skip if no messages remain after filtering
                if layerMessages.isEmpty {
                    continue
                }
                
                // Mark all assistant messages in this layer as processed
                siblings.forEach { processedMessageIds.insert($0.id) }
            }
            
            // Skip if no messages found for this layer
            if layerMessages.isEmpty {
                continue
            }
            
            // Determine active message for this layer
            let activeMessageInLayer: UUID
            if message.role == .user {
                if activeHistory.contains(where: { $0.id == message.id }) {
                    activeMessageInLayer = message.id
                } else if let lastMessage = layerMessages.last {
                    activeMessageInLayer = lastMessage.id
                } else {
                    // Fallback to the first message in the layer
                    activeMessageInLayer = layerMessages.first!.id
                }
            } else {
                // For assistant messages, use priority-based selection
                if activeHistory.contains(where: { $0.id == message.id }) && layerMessages.contains(where: { $0.id == message.id }) {
                    activeMessageInLayer = message.id
                } else if let firstMessage = layerMessages.first {
                    activeMessageInLayer = firstMessage.id
                } else {
                    // This should not happen since we checked layerMessages.isEmpty above
                    fatalError("No messages found in layer after checking isEmpty")
                }
            }
            
            // Create layer
            let layer = MessageLayer(
                siblingMessages: layerMessages,
                activeMessageId: activeMessageInLayer,
                depth: message.depth,
                role: message.role
            )
            
            layers.append(layer)
        }
        print("🎯 getMessageLayersForDisplay with layers count: \(layers.count)")
        let textContent = layers.map { $0.siblingMessages.map { $0.content.compactMap { content in
            if case .text(let text) = content { return text.prefix(50) } else { return nil }
        } } }
        print("🎯 getMessageLayersForDisplay with layers text content: \(textContent)")
        let activeMessageTextContent = layers.map { $0.activeMessage?.content.compactMap { content in
            if case .text(let text) = content { return text.prefix(50) } else { return nil }
        } }
        print("🎯 getMessageLayersForDisplay with layers activeMessage text content: \(activeMessageTextContent)")
        print("🎯 getMessageLayersForDisplay with layers activeMessageIndex: \(layers.map { $0.activeMessageIndex })")
        
        return layers
    }
    
    func getActivePathMessage(for sessionId: UUID) -> [Message] {
        guard let cache = getCachedTree(sessionId: sessionId) else {
            return []
        }
        
        return cache.getActivePathMessage()
    }
    
    func getActivePathId(for sessionId: UUID) -> [UUID] {
        guard let cache = getCachedTree(sessionId: sessionId) else {
            return []
        }
        
        return cache.activeMessagePath
    }
    
    // MARK: - Private Helper Methods
    
    private func getCachedTree(sessionId: UUID) -> MessageTreeCache? {
        return cacheQueue.sync {
            return treeCache[sessionId]
        }
    }
    
    /// Create a message layer from messages without cache operations (zero-delay)
    func createMessageLayerFromMessages(
        chatSession: ChatSession,
        messages: [Message]
    ) -> MessageLayer {
        guard !messages.isEmpty else {
            // Return empty layer if no messages
            return MessageLayer(
                siblingMessages: [],
                activeMessageId: UUID(), // Placeholder ID
                depth: 0,
                role: .user
            )
        }
        
        // Sort messages using static method for zero-delay performance
        let sortedMessages = MessageTreeCache.sortMessagesInALayerForDisplay(
            messages: messages,
            chatSession: chatSession
        )
        
        // Determine active message (first in sorted list)
        let activeMessageId = sortedMessages.first?.id ?? UUID()
        
        // Get common properties from first message
        let firstMessage = sortedMessages.first!
        
        return MessageLayer(
            siblingMessages: sortedMessages,
            activeMessageId: activeMessageId,
            depth: firstMessage.depth,
            role: firstMessage.role
        )
    }
    
    /// Update the chat session's active message ID in repository
    private func updateChatSessionActiveMessage(sessionId: UUID) async {
        do {
            guard let cache = getCachedTree(sessionId: sessionId),
                  let currentActiveMessageId = cache.getCurrentActiveMessageId(),
                  var chatSession = cache.chatSession else {
                print("⚠️ Warning: Cannot update active message - missing cache or active message")
                return
            }
            
            // Update chat session's active message ID
            chatSession.activeMessageId = currentActiveMessageId
            cache.chatSession = chatSession
            
            // Persist to repository
            try await chatRepository.updateChatSession(chatSession)
            print("✅ Updated chat session active message: \(currentActiveMessageId)")
            
        } catch {
            print("❌ Failed to update chat session active message: \(error)")
        }
    }
}
