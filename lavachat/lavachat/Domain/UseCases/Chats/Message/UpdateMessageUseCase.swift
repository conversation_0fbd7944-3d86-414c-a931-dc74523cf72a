import Foundation

/// Updates an existing message using the direct entity approach.
final class UpdateMessageUseCase: UpdateMessageUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ message: Message) async throws -> Message {
        // Verify the message exists
        guard let _ = try await chatRepository.getMessage(byId: message.id) else {
            throw ChatError.messageSendFailed(underlying: nil)
        }
        
        // Verify the session still exists
        guard let _ = try await chatRepository.getChatSession(byId: message.sessionId) else {
            throw ChatError.sessionNotFound
        }
        
        // Update the message
        try await chatRepository.updateMessage(message)
        
        return message
    }
} 