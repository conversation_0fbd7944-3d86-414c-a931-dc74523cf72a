import Foundation

/// Use case for replacing user input with LLM-processed prompt
final class RewritePromptWithPromptUseCase: RewritePromptWithPromptUseCaseProtocol {

    // MARK: - Dependencies

    private let prepareMessagesUseCase: PrepareMessagesUseCaseProtocol
    private let sendMessageUseCase: SendMessageUseCaseProtocol

    // MARK: - Initialization

    init(
        prepareMessagesUseCase: PrepareMessagesUseCaseProtocol,
        sendMessageUseCase: SendMessageUseCaseProtocol
    ) {
        self.prepareMessagesUseCase = prepareMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
    }
    
    // MARK: - RewritePromptWithPromptUseCaseProtocol
    
    func execute(originalInput: String, prompt: String, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        print("🔄 [RewritePromptWithPromptUseCase] Starting prompt rewrite")
        print("📝 Original input: '\(originalInput)'")
        print("🎯 Rewrite with prompt: '\(prompt)'")

        // Step 1: Replace template variables in the prompt
        let processedPrompt = replaceTemplateVariables(in: prompt, originalInput: originalInput)
        print("✅ Processed prompt with template variables")

        // Step 2: Use the first active LLM instance for processing
        guard let instanceId = chatSession.activeLLMInstanceIds.first else {
            throw ChatError.messageSendFailed(underlying: nil)
        }

        // Step 3: Prepare messages for LLM processing using internal operation type
        let preparedMessages = try await prepareMessagesUseCase.execute(
            chatSession: chatSession,
            chatSessionSetting: chatSessionSetting,
            content: [ContentBlock.text(processedPrompt)],
            instanceIds: [instanceId],
            operationType: MessageOperationType.rewritePromptWithPrompt, // Use internal operation type to skip persistence
            parentMessageId: nil as UUID?, // No parent needed for internal operation
            originalMessageId: nil as UUID?,
            userMessageDepth: 0 // Temporary depth for internal processing
        )

        // Step 4: Return the streaming response directly
        let stream = sendMessageUseCase.execute(preparedMessages: preparedMessages)
        return stream
    }
    
    // MARK: - Private Helper Methods
    
    /// Replace template variables in the prompt
    private func replaceTemplateVariables(in prompt: String, originalInput: String) -> String {
        // Replace {{user_original_input}} with the actual user input
        return prompt.replacingOccurrences(of: "{{user_original_input}}", with: originalInput)
    }
}
