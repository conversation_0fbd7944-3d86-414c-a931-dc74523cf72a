import Foundation

/// Creates an edited version of a message as a lightweight wrapper around SendMessageUseCase
final class EditMessageUseCase: EditMessageUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    private let prepareMessagesUseCase: PrepareMessagesUseCaseProtocol
    private let sendMessageUseCase: SendMessageUseCaseProtocol
    
    // MARK: - Initialization
    
    init(
        chatRepository: ChatRepository,
        prepareMessagesUseCase: PrepareMessagesUseCaseProtocol,
        sendMessageUseCase: SendMessageUseCaseProtocol
    ) {
        self.chatRepository = chatRepository
        self.prepareMessagesUseCase = prepareMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
    }
    
    // MARK: - Protocol Implementation
    
    func execute(originalMessageId: UUID, newContent: [ContentBlock]) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        return AsyncThrowingStream { continuation in
            Task {
                do {
                    // Get the original message to extract session info
                    guard let originalMessage = try await chatRepository.getMessage(byId: originalMessageId) else {
                        continuation.finish(throwing: ChatError.messageEditFailed)
                        return
                    }
                    
                    // Ensure it's a user message (only user messages can be edited)
                    guard originalMessage.role == .user else {
                        continuation.finish(throwing: ChatError.messageEditFailed)
                        return
                    }
                    
                    // Validate new content is not empty
                    guard !newContent.isEmpty else {
                        continuation.finish(throwing: ChatError.messageEditFailed)
                        return
                    }
                    
                    // Get the chat session to determine active instances
                    guard let chatSession = try await chatRepository.getChatSession(byId: originalMessage.sessionId) else {
                        continuation.finish(throwing: ChatError.sessionNotFound)
                        return
                    }

                    guard let settingsId = chatSession.settingsId else {
                        continuation.finish(throwing: ChatError.sessionNotFound)
                        return
                    }
                    
                    // Get the chat session setting
                    guard let chatSessionSetting = try await chatRepository.getSetting(byId: settingsId) else {
                        continuation.finish(throwing: ChatError.sessionNotFound)
                        return
                    }
                    
                    // Use active LLM instances from the session
                    let instanceIds = chatSession.activeLLMInstanceIds
                    guard !instanceIds.isEmpty else {
                        continuation.finish(throwing: ChatError.messageEditFailed)
                        return
                    }
                    
                    // Step 1: Prepare messages using PrepareMessagesUseCase
                    let preparedMessages = try await prepareMessagesUseCase.execute(
                        chatSession: chatSession,
                        chatSessionSetting: chatSessionSetting,
                        content: newContent,
                        instanceIds: instanceIds,
                        operationType: .editMessage,
                        parentMessageId: originalMessage.parentId,
                        originalMessageId: originalMessageId,
                        userMessageDepth: originalMessage.depth
                    )
                    
                    // Step 2: Send messages using SendMessageUseCase
                    let editStream = sendMessageUseCase.execute(preparedMessages: preparedMessages)
                    
                    // Forward all responses from SendMessageUseCase
                    for try await response in editStream {
                        continuation.yield(response)
                    }
                    
                    // Complete the stream
                    continuation.finish()
                    
                } catch {
                    continuation.finish(throwing: ChatError.messageEditFailed)
                }
            }
        }
    }
} 
