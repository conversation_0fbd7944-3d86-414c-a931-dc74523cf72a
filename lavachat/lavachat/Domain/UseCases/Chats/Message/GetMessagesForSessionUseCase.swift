import Foundation

/// Retrieves all messages for a specific chat session, sorted by creation date.
final class GetMessagesForSessionUseCase: GetMessagesForSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(sessionId: UUID) async throws -> [Message] {
        // Verify the session exists
        guard let _ = try await chatRepository.getChatSession(byId: sessionId) else {
            throw ChatError.sessionNotFound
        }
        
        // Get messages for the session
        let messages = try await chatRepository.getMessages(for: sessionId)
        
        // Sort by timestamp ascending (oldest to newest)
        return messages.sorted { $0.timestamp < $1.timestamp }
    }
} 