import Foundation

/// Regenerates all assistant messages for a user message as a lightweight wrapper around SendMessageUseCase
final class RegenerateAllMessagesUseCase: RegenerateAllMessagesUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let prepareMessagesUseCase: PrepareMessagesUseCaseProtocol
    private let sendMessageUseCase: SendMessageUseCaseProtocol
    
    // MARK: - Initialization
    
    init(
        prepareMessagesUseCase: PrepareMessagesUseCaseProtocol,
        sendMessageUseCase: SendMessageUseCaseProtocol
    ) {
        self.prepareMessagesUseCase = prepareMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
    }
    
    // MARK: - Protocol Implementation
    
    func execute(userMessage: Message, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> (stream: AsyncThrowingStream<LLMStreamingResponse, Error>, newAssistantMessageLayer: MessageLayer) {
        // Ensure it's a user message
        guard userMessage.role == .user else {
            throw ChatError.regenerationFailed
        }
        
        // Get all active LLM instance IDs from the chat session
        let instanceIds = chatSession.activeLLMInstanceIds
        
        // Ensure we have at least one instance to regenerate for
        guard !instanceIds.isEmpty else {
            throw ChatError.regenerationFailed
        }
        
        // Step 1: Prepare messages using PrepareMessagesUseCase
        let preparedMessages = try await prepareMessagesUseCase.execute(
            chatSession: chatSession,
            chatSessionSetting: chatSessionSetting,
            content: [], // No new content for regeneration
            instanceIds: instanceIds, // Use all active instances
            operationType: .regenerateAllMessages,
            parentMessageId: userMessage.id, // User message as parent
            originalMessageId: nil, // No original message ID needed for regenerate all
            userMessageDepth: userMessage.depth // Use user message depth for calculations
        )

        // Get the new assistant message layer for UI
        guard let newAssistantMessageLayer = preparedMessages.newAssistantMessageLayer else {
            throw ChatError.placeholderMessageCreationFailed
        }
        
        // Step 2: Create streaming response
        let stream = AsyncThrowingStream<LLMStreamingResponse, Error> { continuation in
            Task {
                do {
                    // Send messages using SendMessageUseCase
                    let regenerationStream = sendMessageUseCase.execute(preparedMessages: preparedMessages)
                    
                    // Forward all responses from SendMessageUseCase
                    for try await response in regenerationStream {
                        continuation.yield(response)
                    }
                    
                    // Complete the stream
                    continuation.finish()
                    
                } catch {
                    continuation.finish(throwing: ChatError.regenerationFailed)
                }
            }
        }
        
        return (stream: stream, newAssistantMessageLayer: newAssistantMessageLayer)
    }
} 