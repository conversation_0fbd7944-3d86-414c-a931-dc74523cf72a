import Foundation

/// Regenerates an assistant message as a lightweight wrapper around SendMessageUseCase
final class RegenerateSingleMessageUseCase: RegenerateSingleMessageUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let prepareMessagesUseCase: PrepareMessagesUseCaseProtocol
    private let sendMessageUseCase: SendMessageUseCaseProtocol
    
    // MARK: - Initialization
    
    init(
        prepareMessagesUseCase: PrepareMessagesUseCaseProtocol,
        sendMessageUseCase: SendMessageUseCaseProtocol
    ) {
        self.prepareMessagesUseCase = prepareMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
    }
    
    // MARK: - Protocol Implementation
    
    func execute(message: Message, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> (stream: AsyncThrowingStream<LLMStreamingResponse, Error>, placeholderMessage: Message) {
        // Ensure it's an assistant message
        guard message.role == .assistant || message.role == .mergedAssistant else {
            throw ChatError.regenerationFailed
        }
        
        // Ensure it has an associated LLM instance
        guard let instanceId = message.llmInstanceId else {
            throw ChatError.regenerationFailed
        }
        
        // Step 1: Prepare messages using PrepareMessagesUseCase
        let preparedMessages = try await prepareMessagesUseCase.execute(
            chatSession: chatSession,
            chatSessionSetting: chatSessionSetting,
            content: [], // No new content for regeneration
            instanceIds: [instanceId], // Use same instance as original
            operationType: .regenerateSingleMessage,
            parentMessageId: message.parentId,
            originalMessageId: message.id,
            userMessageDepth: message.depth - 1 // userMessageDepth is the regenerated depth - 1
        )

        // Get the placeholder message for UI
        guard let placeholderMessage = preparedMessages.placeholderAssistantMessages.first else {
            throw ChatError.placeholderMessageCreationFailed
        }
        
        // Step 2: Create streaming response
        let stream = AsyncThrowingStream<LLMStreamingResponse, Error> { continuation in
            Task {
                do {
                    // Send messages using SendMessageUseCase
                    let regenerationStream = sendMessageUseCase.execute(preparedMessages: preparedMessages)
                    
                    // Forward all responses from SendMessageUseCase
                    for try await response in regenerationStream {
                        continuation.yield(response)
                    }
                    
                    // Complete the stream
                    continuation.finish()
                    
                } catch {
                    continuation.finish(throwing: ChatError.regenerationFailed)
                }
            }
        }
        
        return (stream: stream, placeholderMessage: placeholderMessage)
    }
} 