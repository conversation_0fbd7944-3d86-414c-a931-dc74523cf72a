import Foundation

/// Use case for generating chat title using auxiliary LLM instance
final class GenerateChatTitleUseCase: GenerateChatTitleUseCaseProtocol {

    // MARK: - Dependencies

    private let prepareMessagesUseCase: PrepareMessagesUseCaseProtocol
    private let sendMessageUseCase: SendMessageUseCaseProtocol

    // MARK: - Initialization

    init(
        prepareMessagesUseCase: PrepareMessagesUseCaseProtocol,
        sendMessageUseCase: SendMessageUseCaseProtocol
    ) {
        self.prepareMessagesUseCase = prepareMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
    }
    
    // MARK: - GenerateChatTitleUseCaseProtocol
    
    func execute(userInput: String, chatSession: ChatSession, chatSessionSetting: ChatSessionSetting) async throws -> String {
        print("🏷️ [GenerateChatTitleUseCase] Starting title generation")
        print("📝 User input: '\(userInput)'")

        // Verify auxiliary LLM instance is configured
        guard let auxiliaryInstanceId = chatSessionSetting.auxiliaryLLMInstanceId else {
            throw ChatError.messageSendFailed(underlying: NSError(domain: "GenerateChatTitleUseCase", code: 1, userInfo: [NSLocalizedDescriptionKey: "No auxiliary LLM instance configured"]))
        }

        // Step 1: Replace template variables in the prompt
        let processedPrompt = replaceTemplateVariables(in: BuiltinPrompt.builtinChatTitleGeneratorPrompt, userInput: userInput)
        print("✅ Processed prompt with template variables")

        // Step 2: Prepare messages for LLM processing using internal operation type
        let preparedMessages = try await prepareMessagesUseCase.execute(
            chatSession: chatSession,
            chatSessionSetting: chatSessionSetting,
            content: [ContentBlock.text(processedPrompt)],
            instanceIds: [auxiliaryInstanceId],
            operationType: MessageOperationType.generateChatTitle, // Use internal operation type to skip persistence
            parentMessageId: nil as UUID?, // No parent needed for internal operation
            originalMessageId: nil as UUID?,
            userMessageDepth: 0 // Temporary depth for internal processing
        )

        // Step 3: Collect streaming response and extract title
        let stream = sendMessageUseCase.execute(preparedMessages: preparedMessages)
        var accumulatedContent = ""
        
        do {
            for try await response in stream {
                switch response.responseType {
                case .contentDelta(let content):
                    accumulatedContent += content
                    
                case .completionMarker(_):
                    // Title generation completed successfully
                    let generatedTitle = extractAndCleanTitle(from: accumulatedContent)
                    print("✅ Generated title: '\(generatedTitle)'")
                    return generatedTitle
                    
                case .error(let error):
                    print("❌ Error during title generation: \(error)")
                    throw ChatError.messageSendFailed(underlying: error)
                    
                case .thinkingDelta(_), .statusUpdate(_):
                    // Ignore thinking and status updates for title generation
                    break
                }
            }
        } catch {
            print("❌ Failed to generate title: \(error)")
            throw ChatError.messageSendFailed(underlying: error)
        }
        
        // If we reach here, the stream ended without completion marker
        // Return whatever content we accumulated, cleaned up
        let fallbackTitle = extractAndCleanTitle(from: accumulatedContent)
        print("⚠️ Stream ended without completion marker, using fallback title: '\(fallbackTitle)'")
        return fallbackTitle
    }
    
    // MARK: - Private Helper Methods
    
    /// Replace template variables in the prompt
    private func replaceTemplateVariables(in prompt: String, userInput: String) -> String {
        // Replace {{user_original_input}} with the actual user input
        return prompt.replacingOccurrences(of: "{{user_original_input}}", with: userInput)
    }
    
    /// Extract and clean the generated title
    private func extractAndCleanTitle(from content: String) -> String {
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // If content is empty, return default title
        guard !trimmedContent.isEmpty else {
            return ChatSession.defaultSessionName
        }
        
        // Take only the first line to avoid multi-line titles
        let firstLine = trimmedContent.components(separatedBy: .newlines).first ?? trimmedContent
        
        // Remove common prefixes and suffixes that might be added by the LLM
        var cleanedTitle = firstLine
            .replacingOccurrences(of: "Title:", with: "", options: .caseInsensitive)
            .replacingOccurrences(of: "Chat Title:", with: "", options: .caseInsensitive)
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove quotes if the title is wrapped in them
        if (cleanedTitle.hasPrefix("\"") && cleanedTitle.hasSuffix("\"")) ||
           (cleanedTitle.hasPrefix("'") && cleanedTitle.hasSuffix("'")) {
            cleanedTitle = String(cleanedTitle.dropFirst().dropLast())
        }
        
        // Limit title length to reasonable bounds (e.g., 50 characters)
        if cleanedTitle.count > 50 {
            cleanedTitle = String(cleanedTitle.prefix(47)) + "..."
        }
        
        // If after cleaning the title is empty, return default
        if cleanedTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return ChatSession.defaultSessionName
        }
        
        return cleanedTitle.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}
