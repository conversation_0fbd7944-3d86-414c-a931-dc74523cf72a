import Foundation

/// Unified message sending use case that handles all types of message operations with streaming support
final class SendMessageUseCase: SendMessageUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    private let llmAPIService: LLMAPIServiceProtocol
    private let messageTreeManager: MessageTreeManagerUseCaseProtocol
    
    // MARK: - Session Tracking
    
    private var activeSessionTrackers: [UUID: ActiveMessagesTracker] = [:]
    private let trackersQueue = DispatchQueue(label: "session-trackers", attributes: .concurrent)
    
    // MARK: - Initialization
    
    init(
        chatRepository: ChatRepository,
        llmAPIService: LLMAPIServiceProtocol,
        messageTreeManager: MessageTreeManagerUseCaseProtocol
    ) {
        self.chatRepository = chatRepository
        self.llmAPIService = llmAPIService
        self.messageTreeManager = messageTreeManager
    }
    
    // MARK: - Protocol Implementation
    
    func execute(preparedMessages: PreparedMessages) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        let sessionId = preparedMessages.userMessage.sessionId
        let operation = preparedMessages.operationType
        
        var log = """
        [SendMessageUseCase] execute with preparedMessages for session: \(sessionId)
        [SendMessageUseCase] operationType: \(operation)
        [SendMessageUseCase] placeholderMessages count: \(preparedMessages.placeholderAssistantMessages.count)
        [SendMessageUseCase] thinkingControls: \(preparedMessages.thinkingControls)
        [SendMessageUseCase] searchingControls: \(preparedMessages.searchingControls)
        """
        print(log)
        
        return AsyncThrowingStream { continuation in
            Task {
                do {
                    // Wait for persistence task to complete before proceeding
                    try await preparedMessages.persistenceTask.value
                    
                    // Get message history for API context
                    var messageHistory = messageTreeManager.getActivePathMessage(for: sessionId)

                    // For operations that delay adding placeholder messages to cache, add them now
                    // This ensures we get the correct messageHistory first, then update the cache
                    if operation.delayAddPlaceholderMessagesToCache {
                        await messageTreeManager.addMultipleMessagesAndUpdateTree(
                            sessionId: sessionId,
                            messages: preparedMessages.placeholderAssistantMessages
                        )
                    }

                    // Drop the placeholder or original assistant messages from the history
                    if operation.dropLastMessageBeforeSend {
                        messageHistory = messageHistory.dropLast()
                    }

                    // Add the custom user message with special prompt designed for certain operations
                    if operation.addCustomUserMessageToHistoryBeforeSend {
                        // Add the custom prompt user message to the end of history
                        messageHistory.append(preparedMessages.userMessage)
                    }

                    // For operations that only take custom user message for the history before sending
                    if operation.onlyTakeCustomUserMessageForHistoryBeforeSend {
                        messageHistory = [preparedMessages.userMessage]
                    }

                    // Apply context message count truncation
                    if preparedMessages.contextMessageCount != Int64.max {
                        messageHistory = applyContextMessageCountTruncation(
                            messageHistory: messageHistory,
                            contextMessageCount: preparedMessages.contextMessageCount,
                            operationType: operation
                        )
                    }

                    // Extract instance IDs from placeholder messages
                    let instanceIds = preparedMessages.placeholderAssistantMessages.compactMap { $0.llmInstanceId }

                    let messageHistoryTextContent = messageHistory.map { $0.content.compactMap { content in
                        if case .text(let text) = content { return text.prefix(50) }
                        else if case .image(_) = content { return "**image**" }
                        else if case .file(_) = content { return "**file**" }
                        else { return nil }
                    } }
                    
                    log = """
                    [SendMessageUseCase] messageHistory count: \(messageHistory.count)
                    [SendMessageUseCase] messageHistory: \(messageHistoryTextContent)
                    [SendMessageUseCase] instanceIds: \(instanceIds)
                    """
                    print(log)
                    
                    // Send to LLM API service with control settings
                    let apiStream = llmAPIService.sendMessageToInstances(
                        instanceIds: instanceIds,
                        messageHistory: messageHistory,
                        overrideModelParameters: nil,
                        overrideSystemPrompt: preparedMessages.overrideSystemPrompt,
                        thinkingControls: preparedMessages.thinkingControls,
                        searchingControls: preparedMessages.searchingControls
                    )
                    
                    // Process streaming responses
                    try await processStreamingResponses(
                        stream: apiStream,
                        sessionId: sessionId,
                        operation: operation,
                        preparedMessages: preparedMessages,
                        continuation: continuation
                    )
                    
                } catch {
                    continuation.finish(throwing: ChatError.messageSendFailed(underlying: error))
                }
            }
        }
    }
    
    func stopSending(sessionId: UUID) async {
        print("[SendMessageUseCase] stopSending called for session: \(sessionId)")

        // Cancel all requests first (this is always needed)
        await llmAPIService.cancelAllRequests()
        print("[SendMessageUseCase] Cancelled all API requests")

        // Get the tracker for this session (only for persistent operations)
        let tracker = trackersQueue.sync {
            return activeSessionTrackers[sessionId]
        }

        guard let activeMessages = tracker else {
            print("[SendMessageUseCase] No active tracker found for session: \(sessionId) (may be non-persistent operation)")
            return
        }

        // Get incomplete instances
        let incompleteInstances = activeMessages.getIncompleteInstances()
        print("[SendMessageUseCase] Found \(incompleteInstances.count) incomplete instances: \(incompleteInstances)")

        // Finalize incomplete messages (only for persistent operations)
        for instanceId in incompleteInstances {
            do {
                try await finalizeIncompleteMessage(
                    instanceId: instanceId,
                    sessionId: sessionId,
                    activeMessages: activeMessages
                )
                print("[SendMessageUseCase] Finalized incomplete message for instance: \(instanceId)")
            } catch {
                print("[SendMessageUseCase] Failed to finalize message for instance \(instanceId): \(error)")
            }
        }
    }
    
    // MARK: - Private Methods

    /// Apply context message count truncation based on operation type
    private func applyContextMessageCountTruncation(
        messageHistory: [Message],
        contextMessageCount: Int64,
        operationType: MessageOperationType
    ) -> [Message] {
        // Calculate total messages to keep: contextMessageCount + operation-specific additional messages
        let totalToKeep = min(messageHistory.count, Int(contextMessageCount) + operationType.additionalMessagesToPreserve)
        let startIndex = messageHistory.count - totalToKeep
        let truncatedHistory = Array(messageHistory.suffix(from: startIndex))

        print("[SendMessageUseCase] Message history truncated from \(messageHistory.count) to \(truncatedHistory.count) messages (contextMessageCount: \(contextMessageCount), additionalMessages: \(operationType.additionalMessagesToPreserve), operationType: \(operationType))")

        return truncatedHistory
    }

    /// Process streaming responses and handle real-time updates
    private func processStreamingResponses(
        stream: AsyncThrowingStream<LLMStreamingResponse, Error>,
        sessionId: UUID,
        operation: MessageOperationType,
        preparedMessages: PreparedMessages,
        continuation: AsyncThrowingStream<LLMStreamingResponse, Error>.Continuation
    ) async throws {

        // For operations that skip persistence, we still need a tracker but don't need to store it globally
        let activeMessages = ActiveMessagesTracker()
        let shouldPersist = !operation.skipsPersistence

        // Initialize tracker with placeholder messages (only if we have persistent messages)
        if shouldPersist {
            for placeholderMessage in preparedMessages.placeholderAssistantMessages {
                if let instanceId = placeholderMessage.llmInstanceId {
                    activeMessages.setMessage(placeholderMessage, for: instanceId)
                }
            }

            // Store tracker for this session (only for persistent operations)
            trackersQueue.async(flags: .barrier) {
                self.activeSessionTrackers[sessionId] = activeMessages
                print("[SendMessageUseCase] Stored session tracker for: \(sessionId)")
            }
        } else {
            print("[SendMessageUseCase] Skipping session tracker storage for non-persistent operation: \(operation)")
        }
        
        for try await response in stream {
            // Forward response to UI immediately for real-time display
            continuation.yield(response)

            // Process response for database operations asynchronously (non-blocking)
            // Skip database processing for non-persistent operations
            if shouldPersist {
                Task.detached { [weak self] in
                    guard let self = self else { return }
                    do {
                        try await self.processResponseForDatabase(
                            response: response,
                            sessionId: sessionId,
                            activeMessages: activeMessages
                        )
                    } catch {
                        print("Database processing error: \(error)")
                    }
                }
            }
        }
        
        // Update tree cache after completion (only for persistent operations)
        if shouldPersist {
            try await updateTreeCacheAfterCompletion(sessionId: sessionId)
        }

        // Complete the stream
        continuation.finish()

        // Clean up session tracker after completion (only for persistent operations)
        if shouldPersist {
            trackersQueue.async(flags: .barrier) {
                self.activeSessionTrackers.removeValue(forKey: sessionId)
                print("[SendMessageUseCase] Cleaned up session tracker for: \(sessionId)")
            }
        }
    }
    
    /// Process individual streaming response for database operations
    private func processResponseForDatabase(
        response: LLMStreamingResponse,
        sessionId: UUID,
        activeMessages: ActiveMessagesTracker
    ) async throws {
        
        let instanceId = response.instanceId
        
        switch response.responseType {
        case .contentDelta(let content):
            // Simply accumulate content in memory, no database operations during streaming
            activeMessages.appendContentText(to: instanceId, content: content)

        case .thinkingDelta(let content):
            // Simply accumulate thinking text in memory, no database operations during streaming
            activeMessages.appendThinkingText(to: instanceId, content: content)
            
        case .completionMarker(let completionInfo):
            // Update the existing placeholder message with final content
            try await updatePlaceholderMessage(
                instanceId: instanceId,
                sessionId: sessionId,
                completionInfo: completionInfo,
                activeMessages: activeMessages
            )
            // Mark instance as completed
            activeMessages.markInstanceCompleted(instanceId)
            
        case .error(let error):
            // Update placeholder message status to error
            try await handleStreamingError(
                instanceId: instanceId,
                sessionId: sessionId,
                error: error,
                activeMessages: activeMessages
            )
            // Mark instance as completed
            activeMessages.markInstanceCompleted(instanceId)
            
        case .statusUpdate:
            // Status updates don't require database operations
            break
        }
    }
    
    /// Update placeholder message with final content
    private func updatePlaceholderMessage(
        instanceId: UUID,
        sessionId: UUID,
        completionInfo: CompletionInfo?,
        activeMessages: ActiveMessagesTracker
    ) async throws {
        
        // Get the placeholder message for this instance
        guard let placeholderMessageFromActiveMessages = activeMessages.getMessage(for: instanceId) else {
            throw ChatError.placeholderMessageCreationFailed
        }
        
        // Get all accumulated content for this instance
        let accumulatedThinkingText = activeMessages.getAccumulatedThinkingText(for: instanceId)
        let accumulatedContentText = activeMessages.getAccumulatedContentText(for: instanceId)

        // Get placeholder message from repository
        guard let placeholderMessage = try await chatRepository.getMessage(byId: placeholderMessageFromActiveMessages.id) else {
            throw ChatError.placeholderMessageCreationFailed
        }

        // Determine token counts and status based on completion info
        let promptTokens: Int64?
        let completionTokens: Int64?
        let status: MessageStatus
        
        if let completionInfo = completionInfo {
            // Normal completion with valid completion info
            promptTokens = completionInfo.promptTokens.map { Int64($0) }
            completionTokens = completionInfo.completionTokens.map { Int64($0) }
            status = .received
        } else {
            // Cancelled or stopped completion
            promptTokens = 0
            completionTokens = 0
            status = .cancelled
        }
        
        // Create updated message with final content
        let updatedMessage = Message(
            id: placeholderMessage.id, // Keep same ID
            sessionId: sessionId,
            parentId: placeholderMessage.parentId,
            timestamp: placeholderMessage.timestamp, // Keep original timestamp
            role: .assistant,
            content: [ContentBlock.thinking(accumulatedThinkingText), ContentBlock.text(accumulatedContentText)],
            depth: placeholderMessage.depth, // Keep same depth as placeholder
            llmInstanceId: instanceId,
            promptTokens: promptTokens,
            completionTokens: completionTokens,
            status: status,
            userFeedback: placeholderMessage.userFeedback,
            isReplied: placeholderMessage.isReplied,
            isFavorited: placeholderMessage.isFavorited,
            parsedFileOperations: placeholderMessage.parsedFileOperations,
            metadata: placeholderMessage.metadata // Keep existing metadata from placeholder
        )
        
        // Update message in database
        try await chatRepository.updateMessage(updatedMessage)
        
        // Update message in tree cache
        messageTreeManager.updateMessageInCache(sessionId: sessionId, message: updatedMessage)
        
        // Clear accumulated content for this instance
        activeMessages.clearData(for: instanceId)
        
        print("✅ Updated placeholder message \(placeholderMessage.id) for instance \(instanceId)")
    }
    
    /// Finalize incomplete message that was stopped
    private func finalizeIncompleteMessage(
        instanceId: UUID,
        sessionId: UUID,
        activeMessages: ActiveMessagesTracker
    ) async throws {
        
        // Update placeholder message with nil completion info (will mark as cancelled)
        try await updatePlaceholderMessage(
            instanceId: instanceId,
            sessionId: sessionId,
            completionInfo: nil,
            activeMessages: activeMessages
        )
        
        // Mark instance as completed
        activeMessages.markInstanceCompleted(instanceId)
        
        print("🛑 Finalized incomplete message for instance \(instanceId)")
    }
    
    /// Handle streaming error by updating placeholder message status
    private func handleStreamingError(
        instanceId: UUID,
        sessionId: UUID,
        error: APIError,
        activeMessages: ActiveMessagesTracker
    ) async throws {
        
        guard let placeholderMessageFromActiveMessages = activeMessages.getMessage(for: instanceId) else {
            return
        }

        // Get placeholder message from repository
        guard let placeholderMessage = try await chatRepository.getMessage(byId: placeholderMessageFromActiveMessages.id) else {
            throw ChatError.placeholderMessageCreationFailed
        }

        // Create updated message with error status
        let errorMessage = Message(
            id: placeholderMessage.id, // Keep same ID
            sessionId: sessionId,
            parentId: placeholderMessage.parentId,
            timestamp: placeholderMessage.timestamp,
            role: .assistant,
            content: [ContentBlock.text("Error: \(error.localizedDescription)")],
            depth: placeholderMessage.depth, // Keep same depth as placeholder
            llmInstanceId: instanceId,
            status: .error, // Set to error status
            userFeedback: placeholderMessage.userFeedback,
            isReplied: placeholderMessage.isReplied,
            isFavorited: placeholderMessage.isFavorited,
            parsedFileOperations: placeholderMessage.parsedFileOperations,
            metadata: placeholderMessage.metadata // Keep existing metadata from placeholder
        )
        
        // Update message in database
        try await chatRepository.updateMessage(errorMessage)
        
        // Update message in tree cache
        messageTreeManager.updateMessageInCache(sessionId: sessionId, message: errorMessage)
        
        // clear the active messages
        activeMessages.clearData(for: instanceId)
        
        print("❌ Updated placeholder message \(placeholderMessage.id) with error status")
    }
    
    /// Update tree cache after completion (minimal operation now)
    private func updateTreeCacheAfterCompletion(sessionId: UUID) async throws {
        // With placeholder messages, we only need to ensure the cache is consistent
        // Most updates were already handled during streaming
        print("✅ Stream processing completed for session: \(sessionId)")
    }
}

// MARK: - Supporting Types

/// Thread-safe tracker for active assistant messages and their accumulated content
private class ActiveMessagesTracker {
    private let queue = DispatchQueue(label: "active-messages", qos: .utility)
    private var messages: [UUID: Message] = [:]
    private var accumulatedContentText: [UUID: String] = [:]
    private var accumulatedThinkingText: [UUID: String] = [:]
    private var completedInstances: Set<UUID> = []
    
    func setMessage(_ message: Message, for instanceId: UUID) {
        queue.sync {
            messages[instanceId] = message
        }
    }
    
    func getMessage(for instanceId: UUID) -> Message? {
        queue.sync {
            return messages[instanceId]
        }
    }
    
    func getAllMessages() -> [UUID: Message] {
        queue.sync {
            return messages
        }
    }
    
    func appendContentText(to instanceId: UUID, content: String) {
        queue.sync {
            if let existing = accumulatedContentText[instanceId] {
                accumulatedContentText[instanceId] = existing + content
            } else {
                accumulatedContentText[instanceId] = content
            }
        }
    }

    func appendThinkingText(to instanceId: UUID, content: String) {
        queue.sync {
            if let existing = accumulatedThinkingText[instanceId] {
                accumulatedThinkingText[instanceId] = existing + content
            } else {
                accumulatedThinkingText[instanceId] = content
            }
        }
    }
    
    func getAccumulatedContentText(for instanceId: UUID) -> String {
        queue.sync {
            return accumulatedContentText[instanceId] ?? ""
        }
    }
    
    func getAccumulatedThinkingText(for instanceId: UUID) -> String {
        queue.sync {
            return accumulatedThinkingText[instanceId] ?? ""
        }
    }
    
    func markInstanceCompleted(_ instanceId: UUID) {
        queue.sync {
            completedInstances.insert(instanceId)
        }
    }
    
    func isInstanceCompleted(_ instanceId: UUID) -> Bool {
        queue.sync {
            return completedInstances.contains(instanceId)
        }
    }
    
    func getIncompleteInstances() -> [UUID] {
        queue.sync {
            let allInstances = Set(messages.keys)
            return Array(allInstances.subtracting(completedInstances))
        }
    }
    
    func clearData(for instanceId: UUID) {
        queue.sync {
            _ = accumulatedContentText.removeValue(forKey: instanceId)
            _ = accumulatedThinkingText.removeValue(forKey: instanceId)
            _ = messages.removeValue(forKey: instanceId)
            completedInstances.remove(instanceId)
        }
    }
}

 
