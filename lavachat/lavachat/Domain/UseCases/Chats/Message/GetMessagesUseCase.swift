import Foundation

/// Retrieves messages by their IDs.
final class GetMessagesUseCase: GetMessagesUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(messageIds: [UUID]) async throws -> [Message] {
        return try await chatRepository.getMessages(for: messageIds)
    }
} 