import Foundation

/// Creates a new message using the direct entity approach.
final class CreateMessageUseCase: CreateMessageUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ message: Message) async throws -> Message {
        // Verify the session exists
        guard let _ = try await chatRepository.getChatSession(byId: message.sessionId) else {
            throw ChatError.sessionNotFound
        }
        
        // If the message has a parent, verify it exists
        if let parentId = message.parentId {
            guard let _ = try await chatRepository.getMessage(byId: parentId) else {
                throw ChatError.invalidMessageTree
            }
        }
        
        // Create the message
        try await chatRepository.createMessage(message)
        
        return message
    }
} 