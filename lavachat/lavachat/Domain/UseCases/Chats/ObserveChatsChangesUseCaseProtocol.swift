import Foundation
import Combine

/// Protocol for a use case that observes changes in the chat domain.
protocol ObserveChatsChangesUseCaseProtocol {
    /// Executes the use case and returns a publisher that emits void when data changes.
    func execute() -> AnyPublisher<Void, Never>
    
    /// Executes the use case for a specific session and returns a publisher that emits relevant changes for ChatView.
    func execute(forSpecificSessionId sessionId: UUID) -> AnyPublisher<ChatViewRelevantChange, Never>
    
    /// Executes the use case and returns a publisher that emits relevant changes for ChatsView (session creation/deletion only).
    func executeForChatsView() -> AnyPublisher<ChatsViewRelevantChange, Never>
}
