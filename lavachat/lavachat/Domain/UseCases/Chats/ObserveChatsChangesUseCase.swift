import Foundation
import Combine

/// Use case for observing changes in the chat domain.
final class ObserveChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol {
    
    // MARK: - Dependencies
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Protocol Conformance
    func execute() -> AnyPublisher<Void, Never> {
        return chatRepository.dataDidChangePublisher
    }
    
    func execute(forSpecificSessionId sessionId: UUID) -> AnyPublisher<ChatViewRelevantChange, Never> {
        return chatRepository.observeChatViewRelevantChanges(for: sessionId)
    }
    
    func executeForChatsView() -> AnyPublisher<ChatsViewRelevantChange, Never> {
        return chatRepository.observeChatsViewRelevantChanges()
    }
} 