import Foundation
import Combine

// MARK: - ChatSession UseCase Protocols

/// Creates a new chat session with optional title and LLM instance.
protocol CreateChatSessionUseCaseProtocol {
    func execute(title: String?, llmInstanceId: UUID?, instanceSettings: [UUID: SessionInstanceSetting]?) async throws -> ChatSession
}

/// Retrieves a specific chat session by ID.
protocol GetChatSessionUseCaseProtocol {
    func execute(sessionId: UUID) async throws -> ChatSession?
}

/// Retrieves all chat sessions for the current user.
protocol GetAllChatSessionsUseCaseProtocol {
    func execute() async throws -> [ChatSession]
}

/// Updates an existing chat session using direct entity approach.
protocol UpdateChatSessionUseCaseProtocol {
    func execute(_ session: ChatSession) async throws -> ChatSession
}

/// Deletes a chat session and its associated messages.
protocol DeleteChatSessionUseCaseProtocol {
    func execute(sessionId: UUID) async throws
}

/// Creates a duplicate of an existing chat session.
protocol DuplicateChatSessionUseCaseProtocol {
    func execute(sourceSession: ChatSession) async throws -> ChatSession
}

// MARK: - Share and Import UseCase Protocols

/// Shares a chat session using various formats
protocol ShareChatSessionUseCaseProtocol {
    /// Shares a chat session with the specified configuration
    /// - Parameters:
    ///   - session: The chat session to share
    ///   - configuration: Configuration for the sharing operation
    /// - Returns: Result of the sharing operation
    func execute(
        _ session: ChatSession,
        configuration: ShareConfiguration
    ) async -> ShareResult

    /// Shares a chat session as a file
    /// - Parameters:
    ///   - session: The chat session to share
    ///   - fileName: Optional custom file name
    /// - Returns: URL of the created file
    /// - Throws: ShareError if the operation fails
    func shareAsFile(
        _ session: ChatSession,
        fileName: String?
    ) async throws -> URL

    /// Shares a chat session via iCloud
    /// - Parameters:
    ///   - session: The chat session to share
    ///   - permissions: iCloud sharing permissions
    /// - Returns: iCloud share URL
    /// - Throws: ShareError if the operation fails
    func shareViaICloud(
        _ session: ChatSession,
        permissions: ICloudSharePermissions
    ) async throws -> URL

    /// Generates QR code for sharing a chat session
    /// - Parameters:
    ///   - session: The chat session to share
    ///   - size: Size of the QR code image
    /// - Returns: QR code image data
    /// - Throws: ShareError if the operation fails
    func generateQRCode(
        for session: ChatSession,
        size: CGSize
    ) async throws -> Data
}

/// Imports chat sessions from various sources
protocol ImportChatSessionUseCaseProtocol {
    /// Imports a chat session from a file
    /// - Parameters:
    ///   - fileURL: URL of the file to import
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports a chat session from iCloud share URL
    /// - Parameters:
    ///   - shareURL: iCloud share URL
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports a chat session from QR code data
    /// - Parameters:
    ///   - qrCodeData: Data extracted from QR code
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult
}