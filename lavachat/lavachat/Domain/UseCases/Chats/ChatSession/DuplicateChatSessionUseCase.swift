import Foundation

/// Creates a duplicate of an existing chat session with new IDs and timestamps.
final class DuplicateChatSessionUseCase: DuplicateChatSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(
        chatRepository: ChatRepository,
    ) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(sourceSession: ChatSession) async throws -> ChatSession {

        // Create the duplicated session
        let duplicatedSession = ChatSession(
            id: UUID(),
            title: ChatSession.defaultSessionName,
            createdAt: Date(),
            lastModifiedAt: Date(),
            activeMessageId: nil,
            activeLLMInstanceIds: sourceSession.activeLLMInstanceIds,
            usedLLMInstanceIds: sourceSession.usedLLMInstanceIds,
            activeContextServerIds: sourceSession.activeContextServerIds,
            settingsId: sourceSession.settingsId,
            userId: sourceSession.userId,
            instanceSettings: sourceSession.instanceSettings,
            metadata: sourceSession.metadata
        )
        
        // Save the duplicated session
        try await chatRepository.createChatSession(duplicatedSession)
        
        return duplicatedSession
    }
} 
