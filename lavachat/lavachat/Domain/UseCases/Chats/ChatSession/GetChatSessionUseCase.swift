import Foundation

/// Retrieves a specific chat session by its ID.
final class GetChatSessionUseCase: GetChatSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(sessionId: UUID) async throws -> ChatSession? {
        return try await chatRepository.getChatSession(byId: sessionId)
    }
} 