import Foundation

/// Deletes a chat session and its associated messages.
final class DeleteChatSessionUseCase: DeleteChatSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(sessionId: UUID) async throws {
        // Verify the session exists
        guard let _ = try await chatRepository.getChatSession(byId: sessionId) else {
            throw ChatError.sessionNotFound
        }
        
        // The repository should handle cascade deletion of associated messages
        try await chatRepository.deleteChatSession(byId: sessionId)
    }
} 