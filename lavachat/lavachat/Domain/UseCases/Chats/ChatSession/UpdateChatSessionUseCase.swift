import Foundation

/// Updates an existing chat session using the direct entity approach.
final class UpdateChatSessionUseCase: UpdateChatSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    
    // MARK: - Initialization
    
    init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ session: ChatSession) async throws -> ChatSession {
        // Verify the session exists
        guard let _ = try await chatRepository.getChatSession(byId: session.id) else {
            throw ChatError.sessionNotFound
        }
        
        // Update the lastModifiedAt timestamp
        var updatedSession = session
        updatedSession.lastModifiedAt = Date()
        
        // Save the updated session
        try await chatRepository.updateChatSession(updatedSession)
        
        return updatedSession
    }
} 