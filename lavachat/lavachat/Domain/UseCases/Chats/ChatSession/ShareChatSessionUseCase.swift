import Foundation

/// Use case for sharing chat sessions using various formats
final class ShareChatSessionUseCase: ShareChatSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let shareService: ShareServiceProtocol
    
    // MARK: - Initialization
    
    init(shareService: ShareServiceProtocol) {
        self.shareService = shareService
    }
    
    // MARK: - ShareChatSessionUseCaseProtocol Implementation
    
    func execute(
        _ session: ChatSession,
        configuration: ShareConfiguration
    ) async -> ShareResult {
        return await shareService.shareItem(session, configuration: configuration)
    }
    
    func shareAsFile(
        _ session: ChatSession,
        fileName: String?
    ) async throws -> URL {
        return try await shareService.shareAsFile(session, fileName: fileName)
    }
    
    func shareViaICloud(
        _ session: ChatSession,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        return try await shareService.shareViaICloud(session, permissions: permissions)
    }
    
    func generateQRCode(
        for session: ChatSession,
        size: CGSize
    ) async throws -> Data {
        return try await shareService.generateQRCode(for: session, size: size)
    }
}
