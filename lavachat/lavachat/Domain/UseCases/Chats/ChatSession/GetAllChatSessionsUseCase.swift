import Foundation

/// Retrieves all chat sessions for the current user, sorted by last modified date.
final class GetAllChatSessionsUseCase: GetAllChatSessionsUseCaseProtocol {
    
    // MARK: - Dependencies

    private let chatRepository: ChatRepository
    private let appSettingsRepository: AppSettingsRepository

    // MARK: - Initialization

    init(
        chatRepository: ChatRepository,
        appSettingsRepository: AppSettingsRepository
    ) {
        self.chatRepository = chatRepository
        self.appSettingsRepository = appSettingsRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute() async throws -> [ChatSession] {
        // Get current user ID
        guard let userId = getCurrentUserId() else {
            throw ChatError.userNotFound
        }

        // Create sort descriptor for lastModifiedAt descending
        let sortDescriptor = NSSortDescriptor(key: "lastModifiedAt", ascending: false)

        let sessions = try await chatRepository.getAllChatSessions(
            for: userId,
            sortBy: [sortDescriptor]
        )

        return sessions
    }
    
    // MARK: - Private Helpers
    
    private func getCurrentUserId() -> UUID? {
        return appSettingsRepository.getCurrentUserId()
    }
} 