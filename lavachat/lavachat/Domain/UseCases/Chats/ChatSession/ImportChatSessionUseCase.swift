import Foundation

/// Use case for importing chat sessions from various sources
final class ImportChatSessionUseCase: ImportChatSessionUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let importService: ImportServiceProtocol
    
    // MARK: - Initialization
    
    init(importService: ImportServiceProtocol) {
        self.importService = importService
    }
    
    // MARK: - ImportChatSessionUseCaseProtocol Implementation
    
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromFile(fileURL, configuration: configuration)
    }
    
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromICloudShare(shareURL, configuration: configuration)
    }
    
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        return await importService.importFromQRCode(qrCodeData, configuration: configuration)
    }
}
