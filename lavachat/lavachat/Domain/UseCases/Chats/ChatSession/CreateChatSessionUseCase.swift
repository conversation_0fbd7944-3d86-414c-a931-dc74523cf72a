import Foundation

/// Creates a new chat session with optional title and LLM instance configuration.
final class CreateChatSessionUseCase: CreateChatSessionUseCaseProtocol {

    // MARK: - Dependencies

    private let chatRepository: ChatRepository
    private let llmInstanceRepository: LLMInstanceRepository
    private let userRepository: UserSettingsRepository

    // MARK: - Initialization

    init(
        chatRepository: ChatRepository,
        llmInstanceRepository: LLMInstanceRepository,
        userRepository: UserSettingsRepository
    ) {
        self.chatRepository = chatRepository
        self.llmInstanceRepository = llmInstanceRepository
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(title: String?, llmInstanceId: UUID?, instanceSettings: [UUID: SessionInstanceSetting]?) async throws -> ChatSession {
        // Determine the LLM instance to use
        let resolvedInstanceId: UUID
        if let providedInstanceId = llmInstanceId {
            // Verify the provided instance exists
            guard let _ = try await llmInstanceRepository.getInstance(byId: providedInstanceId) else {
                throw ChatError.messageSendFailed(underlying: ModelManagementError.instanceNotFound)
            }
            resolvedInstanceId = providedInstanceId
        } else {
            throw ChatError.messageSendFailed(underlying: ModelManagementError.instanceNotFound)
        }

        // Get current user and extract required information
        guard let currentUser = try await userRepository.getCurrentUser() else {
            throw ChatError.userNotFound
        }

        // Use user's default chat settings ID, fallback to system default if not set
        let settingsId = currentUser.defaultChatSettingsId ?? BuiltinConstants.systemDefaultChatSessionSettingId

        // Generate title if not provided
        let sessionTitle = title ?? generateDefaultTitle()

        // Create the chat session entity
        let chatSession = ChatSession(
            id: UUID(),
            title: sessionTitle,
            createdAt: Date(),
            lastModifiedAt: Date(),
            activeMessageId: nil,
            activeLLMInstanceIds: [resolvedInstanceId],
            usedLLMInstanceIds: [],
            activeContextServerIds: [],
            settingsId: settingsId, // Use user's default or system default
            userId: currentUser.id,
            instanceSettings: instanceSettings,
            metadata: [:]
        )

        // Save to repository
        try await chatRepository.createChatSession(chatSession)

        return chatSession
    }
    
    // MARK: - Private Helpers

    private func generateDefaultTitle() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return "Chat \(formatter.string(from: Date()))"
    }
} 