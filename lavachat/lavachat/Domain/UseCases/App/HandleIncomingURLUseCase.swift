import Foundation
import os.log

/// Use case for handling incoming URLs to the app
final class HandleIncomingURLUseCase: HandleIncomingURLUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let importService: ImportServiceProtocol
    private let logger = Logger(subsystem: "io.github.alwayskeepcoding.lavachat", category: "URLHandling")
    
    // MARK: - Initialization
    
    init(importService: ImportServiceProtocol) {
        self.importService = importService
    }
    
    // MARK: - HandleIncomingURLUseCaseProtocol Implementation
    
    func handleURL(_ url: URL) async -> URLHandlingResult {
        logger.info("Handling incoming URL: \(url.absoluteString)")
        
        // Determine URL type and handle accordingly
        if url.scheme == "lavachat" {
            return await handleLavaChatURL(url)
        } else if isICloudShareURL(url) {
            return await handleICloudURL(url)
        } else if isLavaChatFile(url) {
            return await handleFileURL(url)
        } else {
            logger.warning("Unsupported URL scheme or format: \(url.absoluteString)")
            return .unsupported
        }
    }
    
    // MARK: - Private Methods
    
    /// Handles custom lavachat:// scheme URLs
    private func handleLavaChatURL(_ url: URL) async -> URLHandlingResult {
        logger.info("Handling LavaChat custom scheme URL")
        
        // For now, custom scheme URLs are not implemented
        // This could be used for deep linking in the future
        logger.warning("Custom LavaChat scheme URLs not yet implemented")
        return .unsupported
    }
    
    /// Handles iCloud share URLs
    private func handleICloudURL(_ url: URL) async -> URLHandlingResult {
        logger.info("Handling iCloud share URL")
        
        let config = ImportConfiguration()
        let result = await importService.importFromICloudShare(url, configuration: config)
        
        switch result {
        case .success(let info):
            logger.info("Successfully imported from iCloud: \(info.importedItemIds.count) items")
            return .success(info)
        case .failure(let error):
            logger.error("Failed to import from iCloud: \(error.localizedDescription)")
            return .failure(error)
        }
    }
    
    /// Handles .lavachat file URLs
    private func handleFileURL(_ url: URL) async -> URLHandlingResult {
        logger.info("Handling LavaChat file URL: \(url.lastPathComponent)")
        
        let config = ImportConfiguration()
        let result = await importService.importFromFile(url, configuration: config)
        
        switch result {
        case .success(let info):
            logger.info("Successfully imported from file: \(info.importedItemIds.count) items")
            return .success(info)
        case .failure(let error):
            logger.error("Failed to import from file: \(error.localizedDescription)")
            return .failure(error)
        }
    }
    
    /// Checks if the URL is an iCloud share URL
    private func isICloudShareURL(_ url: URL) -> Bool {
        guard let host = url.host?.lowercased() else { return false }
        return host.contains("icloud.com")
    }
    
    /// Checks if the URL points to a .lavachat file
    private func isLavaChatFile(_ url: URL) -> Bool {
        return url.pathExtension.lowercased() == "lavachat"
    }
}
