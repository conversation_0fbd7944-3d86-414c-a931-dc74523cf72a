import Foundation

// MARK: - User UseCase Protocols

/// Creates a new user in the system.
protocol CreateUserUseCaseProtocol {
    func execute(_ user: User) async throws -> User
}

/// Retrieves a specific user by its ID.
protocol GetUserUseCaseProtocol {
    func execute(userId: UUID) async throws -> User?
}

/// Retrieves all users in the system, sorted by registration date.
protocol GetAllUsersUseCaseProtocol {
    func execute() async throws -> [User]
}

/// Updates an existing user.
protocol UpdateUserUseCaseProtocol {
    func execute(_ user: User) async throws -> User
}

/// Deletes a user and handles cleanup.
protocol DeleteUserUseCaseProtocol {
    func execute(userId: UUID) async throws
}

/// Gets the current active user.
protocol GetCurrentUserUseCaseProtocol {
    func execute() async throws -> User?
}

/// Sets the current active user.
protocol SetCurrentUserUseCaseProtocol {
    func execute(userId: UUID) async throws
}
