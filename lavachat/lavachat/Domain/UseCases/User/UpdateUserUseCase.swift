import Foundation

/// Updates an existing user.
final class UpdateUserUseCase: UpdateUserUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ user: User) async throws -> User {
        return try await userRepository.updateUser(user)
    }
}
