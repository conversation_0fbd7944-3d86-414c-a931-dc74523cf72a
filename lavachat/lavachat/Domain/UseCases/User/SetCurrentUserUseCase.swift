import Foundation

/// Sets the current active user.
final class SetCurrentUserUseCase: SetCurrentUserUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(userId: UUID) async throws {
        try await userRepository.setCurrentUser(userId: userId)
    }
}
