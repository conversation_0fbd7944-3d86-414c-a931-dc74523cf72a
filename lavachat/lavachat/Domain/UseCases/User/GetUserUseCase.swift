import Foundation

/// Retrieves a specific user by its ID.
final class GetUserUseCase: GetUserUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(userId: UUID) async throws -> User? {
        return try await userRepository.getUser(byId: userId)
    }
}
