import Foundation

/// Deletes a user and handles cleanup.
final class DeleteUserUseCase: DeleteUserUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(userId: UUID) async throws {
        try await userRepository.deleteUser(byId: userId)
    }
}
