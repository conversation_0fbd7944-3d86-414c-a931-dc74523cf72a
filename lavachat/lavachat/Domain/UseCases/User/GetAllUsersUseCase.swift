import Foundation

/// Retrieves all users in the system, sorted by registration date.
final class GetAllUsersUseCase: GetAllUsersUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute() async throws -> [User] {
        return try await userRepository.getAllUsers()
    }
}
