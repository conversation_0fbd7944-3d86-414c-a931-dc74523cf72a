import Foundation

/// Creates a new user in the system.
final class CreateUserUseCase: CreateUserUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute(_ user: User) async throws -> User {
        return try await userRepository.createUser(user)
    }
}
