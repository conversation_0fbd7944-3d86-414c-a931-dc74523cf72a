import Foundation

/// Gets the current active user.
final class GetCurrentUserUseCase: GetCurrentUserUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let userRepository: UserSettingsRepository
    
    // MARK: - Initialization
    
    init(userRepository: UserSettingsRepository) {
        self.userRepository = userRepository
    }
    
    // MARK: - UseCase Execution
    
    func execute() async throws -> User? {
        return try await userRepository.getCurrentUser()
    }
}
