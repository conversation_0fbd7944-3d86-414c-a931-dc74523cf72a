import Foundation

final class DeleteInstanceGroupUseCase: DeleteInstanceGroupUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(id: UUID) async throws {
        // Check if the group exists
        guard let _ = try await repository.getGroup(byId: id) else {
            throw ModelManagementError.groupNotFound
        }
        
        // Delete the group
        try await repository.deleteGroup(byId: id)
    }
} 
