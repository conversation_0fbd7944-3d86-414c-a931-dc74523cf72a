import Foundation

final class RemoveInstanceFromGroupUseCase: RemoveInstanceFromGroupUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(groupId: UUID, instanceId: UUID) async throws -> LLMInstanceGroup {
        // Verify the group exists
        guard var group = try await repository.getGroup(byId: groupId) else {
            throw ModelManagementError.groupNotFound
        }
        
        // Check if instance is in the group
        if !group.instanceIds.contains(instanceId) {
            throw ModelManagementError.instanceNotInGroup
        }
        
        // Remove instance from the group
        group.instanceIds.removeAll { $0 == instanceId }
        group.lastModifiedAt = Date()
        
        // Update the group
        try await repository.updateGroup(group)
        
        return group
    }
} 