import Foundation

final class AddInstanceToGroupUseCase: AddInstanceToGroupUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(groupId: UUID, instanceId: UUID) async throws -> LLMInstanceGroup {
        // Verify the instance exists
        guard let _ = try await repository.getInstance(byId: instanceId) else {
            throw ModelManagementError.instanceNotFound
        }
        
        // Verify the group exists
        guard var group = try await repository.getGroup(byId: groupId) else {
            throw ModelManagementError.groupNotFound
        }
        
        // Check if instance is already in the group
        if group.instanceIds.contains(instanceId) {
            throw ModelManagementError.instanceAlreadyInGroup
        }
        
        // Add instance to the group
        group.instanceIds.append(instanceId)
        group.lastModifiedAt = Date()
        
        // Update the group
        try await repository.updateGroup(group)
        
        return group
    }
} 