import Foundation

/// Creates a new LLM instance group
protocol CreateInstanceGroupUseCaseProtocol {
    func execute(group: LLMInstanceGroup) async throws -> LLMInstanceGroup
}

/// Retrieves a specific LLM instance group by ID
protocol GetInstanceGroupUseCaseProtocol {
    func execute(id: UUID) async throws -> LLMInstanceGroup
}

/// Retrieves all LLM instance groups
protocol GetAllInstanceGroupsUseCaseProtocol {
    func execute() async throws -> [LLMInstanceGroup]
}

/// Updates an existing LLM instance group
protocol UpdateInstanceGroupUseCaseProtocol {
    func execute(group: LLMInstanceGroup) async throws -> LLMInstanceGroup
}

/// Deletes an LLM instance group
protocol DeleteInstanceGroupUseCaseProtocol {
    func execute(id: UUID) async throws
}

/// Adds an LLM instance to a group
protocol AddInstanceToGroupUseCaseProtocol {
    func execute(groupId: UUID, instanceId: UUID) async throws -> LLMInstanceGroup
}

/// Removes an LLM instance from a group
protocol RemoveInstanceFromGroupUseCaseProtocol {
    func execute(groupId: UUID, instanceId: UUID) async throws -> LLMInstanceGroup
}
