import Foundation

final class GetInstanceGroupUseCase: GetInstanceGroupUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(id: UUID) async throws -> LLMInstanceGroup {
        guard let group = try await repository.getGroup(byId: id) else {
            throw ModelManagementError.groupNotFound
        }
        
        return group
    }
} 