import Foundation

final class CreateInstanceGroupUseCase: CreateInstanceGroupUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(group: LLMInstanceGroup) async throws -> LLMInstanceGroup {
        // Validate group name is not empty
        guard !group.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw ModelManagementError.validationError
        }
        
        // Check if all referenced instances exist
        if !group.instanceIds.isEmpty {
            for instanceId in group.instanceIds {
                if try await repository.getInstance(byId: instanceId) == nil {
                    throw ModelManagementError.instanceNotFound
                }
            }
        }
        
        // Check for duplicate group name across existing groups
        let existingGroups = try await repository.getAllGroups()
        if existingGroups.contains(where: { $0.name.lowercased() == group.name.lowercased() }) {
            throw ModelManagementError.duplicateEntityName
        }
        
        // Create the group
        try await repository.createGroup(group)
        
        return group
    }
} 