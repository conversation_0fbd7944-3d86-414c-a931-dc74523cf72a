import Foundation

final class UpdateInstanceGroupUseCase: UpdateInstanceGroupUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(group: LLMInstanceGroup) async throws -> LLMInstanceGroup {
        // Check if group exists
        guard let existingGroup = try await repository.getGroup(byId: group.id) else {
            throw ModelManagementError.groupNotFound
        }
        
        // Validate group name is not empty
        guard !group.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw ModelManagementError.validationError
        }
        
        // If name changed, check for duplicates
        if existingGroup.name.lowercased() != group.name.lowercased() {
            let existingGroups = try await repository.getAllGroups()
            if existingGroups.contains(where: { $0.id != group.id && $0.name.lowercased() == group.name.lowercased() }) {
                throw ModelManagementError.duplicateEntityName
            }
        }
        
        // Check if all referenced instances exist
        if !group.instanceIds.isEmpty {
            for instanceId in group.instanceIds {
                if try await repository.getInstance(byId: instanceId) == nil {
                    throw ModelManagementError.instanceNotFound
                }
            }
        }
        
        // Update with current timestamp
        var updatedGroup = group
        updatedGroup.lastModifiedAt = Date()
        
        try await repository.updateGroup(updatedGroup)
        
        return updatedGroup
    }
} 