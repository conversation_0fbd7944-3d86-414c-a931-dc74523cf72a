import Foundation

final class GetInstancesForModelUseCase: GetInstancesForModelUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(modelId: UUID) async throws -> [LLMInstance] {
        // Get all instances and filter by model ID
        return try await repository.getAllInstances(for: modelId)
    }
} 