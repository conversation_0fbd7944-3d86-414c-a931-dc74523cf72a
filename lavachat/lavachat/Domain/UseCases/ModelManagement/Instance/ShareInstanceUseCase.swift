import Foundation

/// Use case for sharing LLM instances using various formats
final class ShareInstanceUseCase: ShareInstanceUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let shareService: ShareServiceProtocol
    
    // MARK: - Initialization
    
    init(shareService: ShareServiceProtocol) {
        self.shareService = shareService
    }
    
    // MARK: - ShareInstanceUseCaseProtocol Implementation
    
    func execute(
        _ instance: LLMInstance,
        configuration: ShareConfiguration
    ) async -> ShareResult {
        return await shareService.shareItem(instance, configuration: configuration)
    }
    
    func shareAsFile(
        _ instance: LLMInstance,
        fileName: String?
    ) async throws -> URL {
        return try await shareService.shareAsFile(instance, fileName: fileName)
    }
    
    func shareViaICloud(
        _ instance: LLMInstance,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        return try await shareService.shareViaICloud(instance, permissions: permissions)
    }
    
    func generateQRCode(
        for instance: LLMInstance,
        size: CGSize
    ) async throws -> Data {
        return try await shareService.generateQRCode(for: instance, size: size)
    }
}
