import Foundation

final class GetFavoritedInstancesUseCase: GetFavoritedInstancesUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute() async throws -> [LLMInstance] {
        // Get all instances and filter to only favorites
        let allInstances = try await repository.getAllInstances()
        return allInstances.filter { $0.isFavorited }
    }
} 