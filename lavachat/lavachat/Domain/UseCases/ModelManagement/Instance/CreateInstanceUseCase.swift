import Foundation

final class CreateInstanceUseCase: CreateInstanceUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(
        modelId: UUID,
        name: String,
        systemPrompt: String?,
        defaultParameters: [String: String]?,
        customLogoData: Data?
    ) async throws -> LLMInstance {
        // Verify the model exists
        guard try await repository.getModel(byId: modelId) != nil else {
            throw ModelManagementError.modelNotFound
        }
        
        // Create a new instance
        let instance = LLMInstance(
            modelId: modelId,
            name: name,
            customLogoData: customLogoData,
            systemPrompt: systemPrompt,
            defaultParameters: defaultParameters,
            isUserModified: true
        )
        
        // Save the instance
        try await repository.createInstance(instance)
        
        return instance
    }
} 