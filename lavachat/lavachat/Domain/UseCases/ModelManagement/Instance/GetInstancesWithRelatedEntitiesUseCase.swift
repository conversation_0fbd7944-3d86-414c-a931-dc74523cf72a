import Foundation

/// Use case for fetching multiple LLM instances with their related entities in a single optimized operation
final class GetInstancesWithRelatedEntitiesUseCase: GetInstancesWithRelatedEntitiesUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    /// Initialize the use case with required dependencies
    /// - Parameter repository: Repository for LLM instance operations
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    /// Fetches multiple LLM instances along with their associated models and providers
    /// - Parameter instanceIds: Array of instance UUIDs to fetch
    /// - Returns: Array of LLMInstanceContext containing instance, model, and provider data
    /// - Throws: ModelManagementError if the operation fails
    func execute(instanceIds: [UUID]) async throws -> [LLMInstanceContext] {
        guard !instanceIds.isEmpty else {
            return []
        }
        
        // Remove duplicates while preserving order
        let uniqueInstanceIds = Array(NSOrderedSet(array: instanceIds)) as! [UUID]
        
        do {
            let instanceContexts = try await repository.getInstancesWithRelatedEntities(instanceIds: uniqueInstanceIds)
            
            // Log successful operation
            print("GetInstancesWithRelatedEntitiesUseCase: Successfully fetched \(instanceContexts.count) instance contexts for \(uniqueInstanceIds.count) requested IDs")
            print("GetInstancesWithRelatedEntitiesUseCase: instanceIds: \(instanceIds)")
            print("GetInstancesWithRelatedEntitiesUseCase: instanceModelIds: \(instanceContexts.map { $0.instance.modelId })")
            print("GetInstancesWithRelatedEntitiesUseCase: instanceModelNames: \(instanceContexts.map { $0.model.name })")
            
            return instanceContexts
        } catch {
            print("GetInstancesWithRelatedEntitiesUseCase: Failed to fetch instance contexts - \(error)")
            throw error
        }
    }
} 