import Foundation

final class DuplicateInstanceUseCase: DuplicateInstanceUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(id: UUID) async throws -> LLMInstance {
        // Get the original instance
        guard let originalInstance = try await repository.getInstance(byId: id) else {
            throw ModelManagementError.instanceNotFound
        }
        
        // Create a copy with a new ID and name
        let copyName = "\(originalInstance.name) Copy"
        
        let duplicateInstance = LLMInstance(
            modelId: originalInstance.modelId,
            name: copyName,
            customLogoData: originalInstance.customLogoData,
            systemPrompt: originalInstance.systemPrompt,
            defaultParameters: originalInstance.defaultParameters,
            isFavorited: false, // New instance starts as not favorited
            isUserModified: true, // Mark as user-modified since it's a manual duplication
            metadata: originalInstance.metadata
        )
        
        // Save the duplicate instance
        try await repository.createInstance(duplicateInstance)
        
        return duplicateInstance
    }
} 