import Foundation

final class UpdateInstanceUseCase: UpdateInstanceUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(instance: LLMInstance) async throws {
        // Check if instance exists
        guard try await repository.getInstance(byId: instance.id) != nil else {
            throw ModelManagementError.instanceNotFound
        }
        
        // Create a new instance with isUserModified set to true
        var updatedInstance = instance
        updatedInstance.isUserModified = true
        
        // Update the instance
        try await repository.updateInstance(updatedInstance)
    }
} 