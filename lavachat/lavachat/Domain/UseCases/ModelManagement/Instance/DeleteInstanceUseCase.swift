import Foundation

final class DeleteInstanceUseCase: DeleteInstanceUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(id: UUID) async throws {
        // Check if instance exists
        guard try await repository.getInstance(byId: id) != nil else {
            throw ModelManagementError.instanceNotFound
        }
        
        // Delete the instance
        try await repository.deleteInstance(byId: id)
    }
} 