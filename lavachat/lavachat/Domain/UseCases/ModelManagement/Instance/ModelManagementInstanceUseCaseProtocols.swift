import Foundation

/// Creates a new LLM instance
protocol CreateInstanceUseCaseProtocol {
    /// Creates a new LLM instance
    /// - Parameters:
    ///   - modelId: The UUID of the model this instance is based on
    ///   - name: The name of the new instance
    ///   - systemPrompt: Optional system prompt for the instance
    ///   - defaultParameters: Optional default parameters for the instance
    ///   - customLogoData: Custom logo data
    /// - Returns: The newly created LLMInstance
    /// - Throws: ModelManagementError if there are issues creating the instance
    func execute(
        modelId: UUID,
        name: String,
        systemPrompt: String?,
        defaultParameters: [String: String]?,
        customLogoData: Data?
    ) async throws -> LLMInstance
}

/// Deletes an LLM instance by its ID
protocol DeleteInstanceUseCaseProtocol {
    /// Deletes an LLM instance by its ID
    /// - Parameter id: The UUID of the instance to delete
    /// - Throws: ModelManagementError if there are issues deleting the instance
    func execute(id: UUID) async throws
}

/// Creates a duplicate of an existing LLM instance with a new ID
protocol DuplicateInstanceUseCaseProtocol {
    /// Creates a duplicate of an existing LLM instance with a new ID
    /// - Parameter id: The UUID of the instance to duplicate
    /// - Returns: The newly created duplicate LLMInstance
    /// - Throws: ModelManagementError if there are issues with the duplication
    func execute(id: UUID) async throws -> LLMInstance
}

/// Retrieves all LLM instances in the system
protocol GetAllInstancesUseCaseProtocol {
    /// Retrieves all LLM instances in the system
    /// - Returns: An array of all LLMInstances
    /// - Throws: ModelManagementError if there are issues retrieving the instances
    func execute() async throws -> [LLMInstance]
}

/// Retrieves all LLM instances marked as favorites
protocol GetFavoritedInstancesUseCaseProtocol {
    /// Retrieves all LLM instances marked as favorites
    /// - Returns: An array of favorited LLMInstances
    /// - Throws: ModelManagementError if there are issues retrieving the instances
    func execute() async throws -> [LLMInstance]
}

/// Retrieves a specific LLM instance by its ID
protocol GetInstanceUseCaseProtocol {
    /// Retrieves a specific LLM instance by its ID
    /// - Parameter id: The UUID of the instance to retrieve
    /// - Returns: The LLMInstance if found, nil otherwise
    /// - Throws: ModelManagementError if there are issues retrieving the instance
    func execute(id: UUID) async throws -> LLMInstance?
}

/// Retrieves all LLM instances associated with a specific model
protocol GetInstancesForModelUseCaseProtocol {
    /// Retrieves all LLM instances associated with a specific model
    /// - Parameter modelId: The UUID of the model to get instances for
    /// - Returns: An array of LLMInstances associated with the model
    /// - Throws: ModelManagementError if there are issues retrieving the instances
    func execute(modelId: UUID) async throws -> [LLMInstance]
}

/// Toggles the favorite status of an LLM instance
protocol ToggleInstanceFavoriteUseCaseProtocol {
    /// Toggles the favorite status of an LLM instance
    /// - Parameter id: The UUID of the instance to toggle favorite status
    /// - Returns: The updated instance with new favorite status
    /// - Throws: ModelManagementError if there are issues updating the instance
    func execute(id: UUID) async throws -> LLMInstance
}

/// Updates an existing LLM instance
protocol UpdateInstanceUseCaseProtocol {
    /// Updates an existing LLM instance
    /// - Parameter instance: The instance with updated values
    /// - Throws: ModelManagementError if there are issues updating the instance
    func execute(instance: LLMInstance) async throws
}

/// Protocol for getting multiple instances with their related entities (model and provider) in a single optimized operation
protocol GetInstancesWithRelatedEntitiesUseCaseProtocol {
    /// Fetches multiple LLM instances along with their associated models and providers
    /// - Parameter instanceIds: Array of instance UUIDs to fetch
    /// - Returns: Array of LLMInstanceContext containing instance, model, and provider data
    /// - Throws: ModelManagementError if the operation fails
    func execute(instanceIds: [UUID]) async throws -> [LLMInstanceContext]
}

// MARK: - Share and Import UseCase Protocols

/// Shares an LLM instance using various formats
protocol ShareInstanceUseCaseProtocol {
    /// Shares an LLM instance with the specified configuration
    /// - Parameters:
    ///   - instance: The LLM instance to share
    ///   - configuration: Configuration for the sharing operation
    /// - Returns: Result of the sharing operation
    func execute(
        _ instance: LLMInstance,
        configuration: ShareConfiguration
    ) async -> ShareResult

    /// Shares an LLM instance as a file
    /// - Parameters:
    ///   - instance: The LLM instance to share
    ///   - fileName: Optional custom file name
    /// - Returns: URL of the created file
    /// - Throws: ShareError if the operation fails
    func shareAsFile(
        _ instance: LLMInstance,
        fileName: String?
    ) async throws -> URL

    /// Shares an LLM instance via iCloud
    /// - Parameters:
    ///   - instance: The LLM instance to share
    ///   - permissions: iCloud sharing permissions
    /// - Returns: iCloud share URL
    /// - Throws: ShareError if the operation fails
    func shareViaICloud(
        _ instance: LLMInstance,
        permissions: ICloudSharePermissions
    ) async throws -> URL

    /// Generates QR code for sharing an LLM instance
    /// - Parameters:
    ///   - instance: The LLM instance to share
    ///   - size: Size of the QR code image
    /// - Returns: QR code image data
    /// - Throws: ShareError if the operation fails
    func generateQRCode(
        for instance: LLMInstance,
        size: CGSize
    ) async throws -> Data
}

/// Imports LLM instances from various sources
protocol ImportInstanceUseCaseProtocol {
    /// Imports an LLM instance from a file
    /// - Parameters:
    ///   - fileURL: URL of the file to import
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports an LLM instance from iCloud share URL
    /// - Parameters:
    ///   - shareURL: iCloud share URL
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult

    /// Imports an LLM instance from QR code data
    /// - Parameters:
    ///   - qrCodeData: Data extracted from QR code
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult
}
