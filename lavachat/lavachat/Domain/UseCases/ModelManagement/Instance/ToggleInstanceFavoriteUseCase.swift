import Foundation

final class ToggleInstanceFavoriteUseCase: ToggleInstanceFavoriteUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(id: UUID) async throws -> LLMInstance {
        // Get the current instance
        guard var instance = try await repository.getInstance(byId: id) else {
            throw ModelManagementError.instanceNotFound
        }
        
        // Toggle the favorite status
        instance.isFavorited.toggle()
        
        // Update the instance
        try await repository.updateInstance(instance)
        
        // Return the updated instance
        return instance
    }
} 