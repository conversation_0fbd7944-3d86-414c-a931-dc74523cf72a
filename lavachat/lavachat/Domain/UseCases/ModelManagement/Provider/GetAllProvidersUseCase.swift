import Foundation

final class GetAllProvidersUseCase: GetAllProvidersUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(onlyUserCreated: Bool? = nil) async throws -> [LLMProvider] {
        if let isUserCreated = onlyUserCreated {
            return try await repository.getProviders(isUserCreated: isUserCreated)
        } else {
            return try await repository.getAllProviders()
        }
    }
} 