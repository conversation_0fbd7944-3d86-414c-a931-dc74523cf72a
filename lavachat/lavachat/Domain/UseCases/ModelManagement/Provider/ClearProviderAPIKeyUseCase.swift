import Foundation

final class ClearProviderAPIKeyUseCase: ClearProviderAPIKeyUseCaseProtocol {
    private let repository: LLMInstanceRepository
    private let keychainRepository: KeychainRepository
    
    init(repository: LLMInstanceRepository, keychainRepository: KeychainRepository) {
        self.repository = repository
        self.keychainRepository = keychainRepository
    }
    
    func execute(providerId: UUID) async throws {
        // Validate that the provider exists
        guard let provider = try await repository.getProvider(byId: providerId) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Only proceed if the provider type requires an API key
        guard provider.providerType == .userApiKey else {
            throw ModelManagementError.providerDoesNotRequireApiKey
        }
        
        // Check if there is an API key to delete
        let keyExists = try await keychainRepository.checkApiKeyExists(for: providerId)
        
        if keyExists {
            // Delete the API key from Keychain
            try await keychainRepository.deleteApiKey(for: providerId)
        } else {
            // No key exists, just continue
        }
        
        // Update the provider's apiKeyStored flag if it was previously set
        if provider.apiKeyStored {
            var updatedProvider = provider
            updatedProvider.apiKeyStored = false
            try await repository.updateProvider(updatedProvider)
        }
        
        // // Get instances that use models from this provider
        // let models = try await repository.getAllModels(for: providerId)
        // let allInstances = try await repository.getAllInstances()
        
        // // Identify instances that use models from this provider
        // let modelIds = Set(models.map { $0.id })
        // let affectedInstances = allInstances.filter { modelIds.contains($0.modelId) }
        
        // Optionally take action on affected instances (e.g., mark as inactive)
        // This part depends on business requirements
    }
} 