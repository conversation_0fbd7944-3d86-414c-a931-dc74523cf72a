import Foundation

/// Creates a new LLM provider
protocol CreateProviderUseCaseProtocol {
    func execute(provider: LLMProvider) async throws -> LL<PERSON>rovider
}

/// Retrieves a specific LLM provider by ID
protocol GetProviderUseCaseProtocol {
    func execute(providerId: UUID) async throws -> LL<PERSON>rovider
}

/// Clears the API key for a provider
protocol ClearProviderAPIKeyUseCaseProtocol {
    func execute(providerId: UUID) async throws
}

/// Deletes a user-defined LLM provider
protocol DeleteProviderUseCaseProtocol {
    func execute(providerId: UUID) async throws
}

/// Retrieves all LLM providers, optionally filtered by user-created status
protocol GetAllProvidersUseCaseProtocol {
    func execute(onlyUserCreated: Bool?) async throws -> [LLMProvider]
}

/// Retrieves the API key for a provider
protocol GetProviderAPIKeyUseCaseProtocol {
    func execute(providerId: UUID) async throws -> String
}

/// Saves an API key for a provider
protocol SaveProviderAPIKeyUseCaseProtocol {
    func execute(providerId: UUID, apiKey: String) async throws
}

/// Updates an existing LLM provider
protocol UpdateProviderUseCaseProtocol {
    func execute(provider: LLMProvider) async throws
}
