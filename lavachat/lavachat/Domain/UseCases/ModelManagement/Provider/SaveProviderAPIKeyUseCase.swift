import Foundation

final class SaveProviderAPIKeyUseCase: SaveProviderAPIKeyUseCaseProtocol {
    private let repository: LLMInstanceRepository
    private let keychainRepository: KeychainRepository
    
    init(repository: LLMInstanceRepository, keychainRepository: KeychainRepository) {
        self.repository = repository
        self.keychainRepository = keychainRepository
    }
    
    func execute(providerId: UUID, apiKey: String) async throws {
        // Validate that the provider exists
        guard let provider = try await repository.getProvider(byId: providerId) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Validate that the provider type requires API key
        guard provider.providerType == .userApiKey else {
            throw ModelManagementError.providerDoesNotRequireApiKey
        }
        
        // Validate that the API key is not empty
        guard !apiKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw ModelManagementError.emptyApiKey
        }
        
        // Check if API key already exists
        let keyExists = try await keychainRepository.checkApiKeyExists(for: providerId)
        
        if keyExists {
            // Update existing key
            try await keychainRepository.updateApiKey(apiKey, for: providerId)
        } else {
            // Save new key
            try await keychainRepository.saveApiKey(apiKey, for: providerId)
        }
        
        // Update the provider's apiKeyStored flag if needed
        if !provider.apiKeyStored {
            var updatedProvider = provider
            updatedProvider.apiKeyStored = true
            try await repository.updateProvider(updatedProvider)
        }
    }
} 