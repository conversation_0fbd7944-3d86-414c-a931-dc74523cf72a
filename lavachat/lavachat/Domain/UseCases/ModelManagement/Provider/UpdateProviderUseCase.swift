import Foundation

final class UpdateProviderUseCase: UpdateProviderUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(provider: LL<PERSON>rovider) async throws {
        // Retrieve the existing provider to check if it exists and determine if it's built-in
        guard let existingProvider = try await repository.getProvider(byId: provider.id) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Validate provider data
        guard !provider.name.isEmpty else {
            throw ModelManagementError.validationError
        }
        
        // Check for name duplicates (excluding the current provider)
        let allProviders = try await repository.getAllProviders()
        let hasDuplicate = allProviders.contains { 
            $0.id != provider.id && $0.name.lowercased() == provider.name.lowercased() 
        }
        
        if hasDuplicate {
            throw ModelManagementError.duplicateEntityName
        }
        
        // Different update logic based on whether it's a built-in or custom provider
        if !existingProvider.isUserCreated {
            // For built-in providers, we only allow limited updates
            var updatedProvider = existingProvider
            
            // Only update fields that are allowed to be modified for built-in providers
            updatedProvider.metadata = provider.metadata
            updatedProvider.isUserModified = true
            
            // For built-in providers, we don't update name, logoImageName, apiBaseUrl, etc.
            // apiKeyStored is managed separately via dedicated use cases
            
            try await repository.updateProvider(updatedProvider)
        } else {
            // For custom providers, we allow full updates
            try await repository.updateProvider(provider)
        }
    }
} 