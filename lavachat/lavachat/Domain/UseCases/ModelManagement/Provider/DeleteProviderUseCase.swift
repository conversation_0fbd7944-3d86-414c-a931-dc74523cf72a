import Foundation

final class DeleteProviderUseCase: DeleteProviderUseCaseProtocol {
    private let repository: LLMInstanceRepository
    private let keychainRepository: KeychainRepository
    
    init(repository: LLMInstanceRepository, keychainRepository: KeychainRepository) {
        self.repository = repository
        self.keychainRepository = keychainRepository
    }
    
    func execute(providerId: UUID) async throws {
        // Check if the provider exists
        guard let provider = try await repository.getProvider(byId: providerId) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Only allow deletion of user-created providers
        if !provider.isUserCreated {
            throw ModelManagementError.cannotDeleteBuiltInProvider
        }
        
        // Get all models for this provider
        let models = try await repository.getAllModels(for: providerId)
        
        // Get all instances to check if any use models from this provider
        let allInstances = try await repository.getAllInstances()
        
        // Check if any models from this provider are used in instances
        let modelIds = Set(models.map { $0.id })
        let instancesUsingProviderModels = allInstances.filter { modelIds.contains($0.modelId) }
        
        if !instancesUsingProviderModels.isEmpty {
            throw ModelManagementError.providerHasActiveInstances
        }
        
        // Delete API key from keychain if stored
        if provider.apiKeyStored {
            try await keychainRepository.deleteApiKey(for: providerId)
        }
        
        // Delete all models associated with this provider
        for model in models {
            try await repository.deleteModel(byId: model.id)
        }
        
        // Finally delete the provider
        try await repository.deleteProvider(byId: providerId)
    }
} 