import Foundation

final class CreateProviderUseCase: CreateProviderUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(provider: LLMProvider) async throws -> LLMProvider {
        // Validate provider data
        guard !provider.name.isEmpty else {
            throw ModelManagementError.validationError
        }
        
        // Check for duplicate provider names
        let existingProviders = try await repository.getAllProviders()
        if existingProviders.contains(where: { $0.name.lowercased() == provider.name.lowercased() }) {
            throw ModelManagementError.duplicateEntityName
        }
        
        // Ensure the provider is marked as user-created
        var newProvider = provider
        if !newProvider.isUserCreated {
            newProvider = LLMProvider(
                id: provider.id,
                name: provider.name,
                logoImageName: provider.logoImageName,
                customLogoData: provider.customLogoData,
                websiteUrl: provider.websiteUrl,
                apiDocumentationUrl: provider.apiDocumentationUrl,
                apiBaseUrl: provider.apiBaseUrl,
                providerType: provider.providerType,
                apiKeyStored: provider.apiKeyStored,
                apiStyle: provider.apiStyle,
                apiEndpointPath: provider.apiEndpointPath,
                isUserCreated: true,
                isUserModified: provider.isUserModified,
                metadata: provider.metadata
            )
        }
        
        try await repository.createProvider(newProvider)
        return newProvider
    }
} 