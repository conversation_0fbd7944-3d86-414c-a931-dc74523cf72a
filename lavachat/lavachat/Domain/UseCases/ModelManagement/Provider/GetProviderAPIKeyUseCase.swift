import Foundation

final class GetProviderAPIKeyUseCase: GetProviderAPIKeyUseCaseProtocol {
    private let repository: LLMInstanceRepository
    private let keychainRepository: KeychainRepository
    
    init(repository: LLMInstanceRepository, keychainRepository: KeychainRepository) {
        self.repository = repository
        self.keychainRepository = keychainRepository
    }
    
    func execute(providerId: UUID) async throws -> String {
        // Validate that the provider exists
        guard let provider = try await repository.getProvider(byId: providerId) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Validate that the provider type requires API key
        guard provider.providerType == .userApiKey else {
            throw ModelManagementError.providerDoesNotRequireApiKey
        }
        
        // Check if the provider has an API key stored
        guard provider.apiKeyStored else {
            throw ModelManagementError.apiKeyNotStored
        }
        
        // Retrieve the API key from Keychain
        guard let apiKey = try await keychainRepository.getApiKey(for: providerId) else {
            // If the key is not found but apiKeyStored is true, we have an inconsistency
            // Update the provider to reflect the actual state
            var updatedProvider = provider
            updatedProvider.apiKeyStored = false
            try await repository.updateProvider(updatedProvider)
            
            throw ModelManagementError.apiKeyNotStored
        }
        
        return apiKey
    }
} 