import Foundation

final class GetProviderUseCase: GetProviderUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(providerId: UUID) async throws -> LLMProvider {
        guard let provider = try await repository.getProvider(byId: providerId) else {
            throw ModelManagementError.providerNotFound
        }
        return provider
    }
} 