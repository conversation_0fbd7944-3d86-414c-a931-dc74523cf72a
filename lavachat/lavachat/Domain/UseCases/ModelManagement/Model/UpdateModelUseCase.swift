import Foundation

/// A use case that updates an existing LLM model
final class UpdateModelUseCase: UpdateModelUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(model: LLMModel) async throws -> LLMModel {
        // First fetch the original model to check if it exists and get its details
        guard let existingModel = try await repository.getModel(byId: model.id) else {
            throw ModelManagementError.modelNotFound
        }
        
        var updatedModel = model
        
        // Different handling for built-in vs user-created models
        if !existingModel.isUserCreated {
            // For built-in models:
            // 1. Cannot change: providerId, modelIdentifier, core attributes
            // 2. Can only modify: customLogoData, isUserModified flag, and metadata
            
            // Reject if trying to change provider or model identifier for built-in model
            if updatedModel.providerId != existingModel.providerId ||
               updatedModel.modelIdentifier != existingModel.modelIdentifier {
                throw ModelManagementError.cannotModifyBuiltInModelCore
            }
            
            // Create a modified version that preserves core properties but allows specific modifications
            updatedModel = LLMModel(
                id: existingModel.id,
                providerId: existingModel.providerId,
                modelIdentifier: existingModel.modelIdentifier,
                name: existingModel.name,
                modelDescription: existingModel.modelDescription,
                logoImageName: existingModel.logoImageName,
                customLogoData: updatedModel.customLogoData, // Allow custom logo updates
                contextWindowSize: existingModel.contextWindowSize,
                inputModalities: existingModel.inputModalities,
                outputModalities: existingModel.outputModalities,
                thinkingCapabilities: existingModel.thinkingCapabilities,
                maxOutputTokens: existingModel.maxOutputTokens,
                pricingInfo: existingModel.pricingInfo,
                group: existingModel.group,
                availabilityStatus: existingModel.availabilityStatus,
                isDefaultRecommendation: existingModel.isDefaultRecommendation,
                isUserCreated: false,
                isUserModified: true, // Mark as user-modified
                metadata: updatedModel.metadata // Allow metadata updates
            )
        }
        
        // Update the model
        try await repository.updateModel(updatedModel)
        return updatedModel
    }
} 