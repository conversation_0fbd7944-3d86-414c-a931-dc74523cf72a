import Foundation

/// A use case that creates a new user-defined LLM model
final class CreateModelUseCase: CreateModelUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(model: LLMModel) async throws -> LLMModel {
        // Check if the provider exists
        guard let _ = try await repository.getProvider(byId: model.providerId) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Check if a model with the same identifier already exists for this provider
        if let _ = try await repository.findModel(identifier: model.modelIdentifier, providerId: model.providerId) {
            throw ModelManagementError.duplicateModelIdentifier
        }
        
        // Create a new model with isUserCreated set to true
        var newModel = model
        newModel.isUserCreated = true
        
        try await repository.createModel(newModel)
        return newModel
    }
} 