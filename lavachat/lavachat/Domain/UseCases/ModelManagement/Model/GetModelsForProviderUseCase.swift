import Foundation

/// A use case that fetches all LLM models for a specific provider
final class GetModelsForProviderUseCase: GetModelsForProviderUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(providerId: UUID) async throws -> [LLMModel] {
        // First check if the provider exists
        guard let _ = try await repository.getProvider(byId: providerId) else {
            throw ModelManagementError.providerNotFound
        }
        
        // Then fetch all models for this provider
        return try await repository.getAllModels(for: providerId)
    }
} 