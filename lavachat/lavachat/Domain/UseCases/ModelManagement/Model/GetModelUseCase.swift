import Foundation

/// A use case that fetches a single LLM model by its ID
final class GetModelUseCase: GetModelUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(modelId: UUID) async throws -> LLMModel {
        guard let model = try await repository.getModel(byId: modelId) else {
            throw ModelManagementError.modelNotFound
        }
        return model
    }
} 
