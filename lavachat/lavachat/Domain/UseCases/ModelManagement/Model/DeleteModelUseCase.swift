import Foundation

/// A use case that deletes a user-defined LLM model
final class DeleteModelUseCase: DeleteModelUseCaseProtocol {
    private let repository: LLMInstanceRepository
    
    init(repository: LLMInstanceRepository) {
        self.repository = repository
    }
    
    func execute(modelId: UUID) async throws {
        // First check if the model exists
        guard let model = try await repository.getModel(byId: modelId) else {
            throw ModelManagementError.modelNotFound
        }
        
        // Check if it's a built-in model (cannot delete built-in models)
        if !model.isUserCreated {
            throw ModelManagementError.cannotDeleteBuiltInModel
        }
        
        // Check if there are instances using this model
        let instancesUsingModel = try await repository.getAllInstances(for: modelId)
        
        if !instancesUsingModel.isEmpty {
            throw ModelManagementError.cannotDeleteModelUsedByInstances
        }
        
        // Delete the model
        try await repository.deleteModel(byId: modelId)
    }
} 