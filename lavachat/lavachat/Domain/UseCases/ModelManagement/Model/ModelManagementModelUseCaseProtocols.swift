import Foundation

protocol CreateModelUseCaseProtocol {
    /// Creates a new user-defined LLM model
    /// - Parameter model: The LLMModel to create (with providerId set)
    /// - Returns: The created LLMModel with generated ID
    /// - Throws: ModelManagementError.providerNotFound if the provider doesn't exist
    ///           ModelManagementError.duplicateModelIdentifier if a model with the same identifier already exists for this provider
    func execute(model: LLMModel) async throws -> LLMModel
}

protocol DeleteModelUseCaseProtocol {
    /// Deletes a user-defined LLM model
    /// - Parameter modelId: The UUID of the model to delete
    /// - Throws: ModelManagementError.modelNotFound if the model doesn't exist
    ///           ModelManagementError.cannotDeleteBuiltInModel if attempting to delete a built-in model
    ///           ModelManagementError.cannotDeleteModelUsedByInstances if the model is used by instances
    func execute(modelId: UUID) async throws
}

protocol GetAllModelsUseCaseProtocol {
    /// Fetches all LLM models
    /// - Returns: An array of all LLMModel objects
    /// - Throws: ModelManagementError if there are issues retrieving the models
    func execute() async throws -> [LLMModel]
}

protocol GetModelsForProviderUseCaseProtocol {
    /// Fetches all LLM models associated with a specific provider
    /// - Parameter providerId: The UUID of the provider whose models to fetch
    /// - Returns: An array of LLMModel objects associated with the provider
    /// - Throws: ModelManagementError.providerNotFound if the provider doesn't exist
    func execute(providerId: UUID) async throws -> [LLMModel]
}

protocol UpdateModelUseCaseProtocol {
    /// Updates an existing LLM model
    /// - Parameter model: The updated LLMModel object
    /// - Returns: The updated model
    /// - Throws: ModelManagementError.modelNotFound if the model doesn't exist
    ///           ModelManagementError.cannotModifyBuiltInModelCore if attempting to modify core properties of a built-in model
    func execute(model: LLMModel) async throws -> LLMModel
}

protocol GetModelUseCaseProtocol {
    /// Fetches a specific LLM model by its ID
    /// - Parameter id: The UUID of the model to fetch
    /// - Returns: The requested LLMModel if found
    /// - Throws: ModelManagementError.modelNotFound if no model exists with the given ID
    func execute(modelId: UUID) async throws -> LLMModel
} 