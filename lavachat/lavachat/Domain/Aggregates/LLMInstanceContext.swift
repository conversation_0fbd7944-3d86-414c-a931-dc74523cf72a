import Foundation

struct LLMInstanceContext {
    let instance: LLMInstance
    let model: LLMModel
    let provider: LLMProvider

    /// Generate default session instance settings based on model capabilities
    func defaultSessionInstanceSetting() -> SessionInstanceSetting {
        // Default thinking to true if the model supports it, otherwise nil
        let defaultThinkingEnabled: Bool?
        if let thinkingCapabilities = model.thinkingCapabilities, thinkingCapabilities.controlType != ThinkingCapabilities.ControlType.none {
            defaultThinkingEnabled = true
        } else {
            defaultThinkingEnabled = nil
        }
        
        // Default network to false
        let defaultNetworkEnabled = false
        
        return SessionInstanceSetting(
            thinkingEnabled: defaultThinkingEnabled,
            networkEnabled: defaultNetworkEnabled
        )
    }
}

// MARK: - Instance Context Cache

/// Thread-safe cache for LLMInstanceContext with configurable TTL
actor InstanceContextCache {
    
    private struct CacheItem {
        let context: LLMInstanceContext
        let timestamp: Date
    }
    
    private var cache: [UUID: CacheItem] = [:]
    private let ttl: TimeInterval
    private let maxCacheSize: Int
    
    /// Initialize cache with configurable TTL and cache size
    /// - Parameters:
    ///   - ttl: Time to live in seconds (default: 1 second)
    ///   - maxCacheSize: Maximum number of items to cache (default: 100)
    init(ttl: TimeInterval = 1.0, maxCacheSize: Int = 100) {
        self.ttl = ttl
        self.maxCacheSize = maxCacheSize
    }
    
    /// Get cached context if not expired
    func get(instanceId: UUID) -> LLMInstanceContext? {
        cleanExpiredItems()
        
        guard let item = cache[instanceId] else {
            return nil
        }
        
        let now = Date()
        if now.timeIntervalSince(item.timestamp) > ttl {
            cache.removeValue(forKey: instanceId)
            return nil
        }
        
        return item.context
    }
    
    /// Set single context in cache
    func set(instanceId: UUID, context: LLMInstanceContext) {
        cleanExpiredItems()
        enforceMaxCacheSize()
        
        cache[instanceId] = CacheItem(
            context: context,
            timestamp: Date()
        )
    }
    
    /// Set multiple contexts in batch
    func setBatch(_ contexts: [UUID: LLMInstanceContext]) {
        cleanExpiredItems()
        
        let timestamp = Date()
        for (instanceId, context) in contexts {
            cache[instanceId] = CacheItem(
                context: context,
                timestamp: timestamp
            )
        }
        
        enforceMaxCacheSize()
    }
    
    /// Clear all expired items
    func clearExpired() {
        cleanExpiredItems()
    }
    
    /// Clear all cached items
    func clearAll() {
        cache.removeAll()
    }
    
    /// Get cache statistics
    func getStats() -> (count: Int, ttl: TimeInterval, maxSize: Int) {
        cleanExpiredItems()
        return (count: cache.count, ttl: ttl, maxSize: maxCacheSize)
    }
    
    // MARK: - Private Methods
    
    private func cleanExpiredItems() {
        let now = Date()
        let expiredKeys = cache.compactMap { (key, item) in
            now.timeIntervalSince(item.timestamp) > ttl ? key : nil
        }
        
        for key in expiredKeys {
            cache.removeValue(forKey: key)
        }
    }
    
    private func enforceMaxCacheSize() {
        guard cache.count > maxCacheSize else { return }
        
        // Simple LRU: remove oldest items
        let sortedByTimestamp = cache.sorted { $0.value.timestamp < $1.value.timestamp }
        let itemsToRemove = sortedByTimestamp.prefix(cache.count - maxCacheSize)
        
        for (key, _) in itemsToRemove {
            cache.removeValue(forKey: key)
        }
    }
}
