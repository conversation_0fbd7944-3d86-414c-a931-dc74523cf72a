import Foundation
import Combine

/// Defines the interface for managing LLM providers, models, instances, and groups.
protocol LLMInstanceRepository {

    // MARK: - Data Change Observation
    
    /// Publishes a void event when any underlying data managed by this repository changes.
    var dataDidChangePublisher: AnyPublisher<Void, Never> { get }

    // MARK: - Batch Operations Control
    
    /// Signals the repository to enter a batch update mode, potentially suppressing individual change notifications.
    func beginBatchUpdate() async
    
    /// Signals the repository to exit batch update mode and publish a single consolidated change notification if appropriate.
    func endBatchUpdateAndPublish() async throws

    // MARK: - LLMProvider Operations

    /// Creates a new (likely custom) LLMProvider.
    func createProvider(_ provider: LLMProvider) async throws

    /// Fetches a specific LLMProvider by ID.
    func getProvider(byId id: UUID) async throws -> LLMProvider?

    /// Fetches all LLMProviders (system-defined and custom).
    func getAllProviders() async throws -> [LLMProvider]
    
    /// Fetches providers based on whether they are custom or not.
    func getProviders(isUserCreated: Bool) async throws -> [LLMProvider]

    /// Updates an LLMProvider.
    func updateProvider(_ provider: LLMProvider) async throws

    /// Deletes a custom LLMProvider (system providers likely non-deletable).
    func deleteProvider(byId id: UUID) async throws

    // MARK: - LLMModel Operations

    /// Creates a new (likely custom) LLMModel associated with a provider.
    func createModel(_ model: LLMModel) async throws

    /// Fetches a specific LLMModel by ID.
    func getModel(byId id: UUID) async throws -> LLMModel?

    /// Fetches all LLMModels.
    func getAllModels() async throws -> [LLMModel]

    /// Fetches all LLMModels for a given provider ID.
    func getAllModels(for providerId: UUID) async throws -> [LLMModel]
    
    /// Find a model by its identifier string within a provider.
    func findModel(identifier: String, providerId: UUID) async throws -> LLMModel?

    /// Updates an LLMModel.
    func updateModel(_ model: LLMModel) async throws

    /// Deletes a custom LLMModel.
    func deleteModel(byId id: UUID) async throws

    // MARK: - LLMInstance Operations

    /// Creates a new LLMInstance.
    func createInstance(_ instance: LLMInstance) async throws

    /// Fetches a specific LLMInstance by ID.
    func getInstance(byId id: UUID) async throws -> LLMInstance?

    /// Fetches all LLMInstances.
    func getAllInstances() async throws -> [LLMInstance]

    /// Fetches all LLMInstances for a given model ID.
    func getAllInstances(for modelId: UUID) async throws -> [LLMInstance]
    
    /// Fetches multiple LLMInstances with their related entities (model and provider) in a single optimized query.
    func getInstancesWithRelatedEntities(instanceIds: [UUID]) async throws -> [LLMInstanceContext]
    
    /// Updates an LLMInstance.
    func updateInstance(_ instance: LLMInstance) async throws

    /// Deletes an LLMInstance.
    func deleteInstance(byId id: UUID) async throws
    
    /// Updates the token usage count for an instance.
    func updateInstanceTokenUsage(instanceId: UUID, promptTokens: Int, completionTokens: Int) async throws

    // MARK: - LLMInstanceGroup Operations

    /// Creates a new LLMInstanceGroup.
    func createGroup(_ group: LLMInstanceGroup) async throws

    /// Fetches a specific LLMInstanceGroup by ID.
    func getGroup(byId id: UUID) async throws -> LLMInstanceGroup?

    /// Fetches all LLMInstanceGroups.
    func getAllGroups() async throws -> [LLMInstanceGroup]

    /// Updates an LLMInstanceGroup.
    func updateGroup(_ group: LLMInstanceGroup) async throws

    /// Deletes an LLMInstanceGroup.
    func deleteGroup(byId id: UUID) async throws
} 