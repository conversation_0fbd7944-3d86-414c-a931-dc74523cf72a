import Foundation

/// Defines the interface for managing file sessions and their checkpoints.
protocol FileSessionRepository {

    // MARK: - FileSession Operations

    /// Creates a new FileSession.
    func createFileSession(_ session: FileSession) async throws

    /// Fetches a specific FileSession by its ID.
    func getFileSession(byId id: UUID) async throws -> FileSession?

    /// Fetches all FileSessions for a given user.
    func getAllFileSessions(for userId: UUID) async throws -> [FileSession]

    /// Updates an existing FileSession.
    func updateFileSession(_ session: FileSession) async throws

    /// Deletes a FileSession and its associated checkpoints and messages.
    func deleteFileSession(byId id: UUID) async throws

    // MARK: - FileCheckpoint Operations

    /// Creates a new FileCheckpoint.
    func createCheckpoint(_ checkpoint: FileCheckpoint) async throws

    /// Fetches a specific FileCheckpoint by its ID.
    func getCheckpoint(byId id: UUID) async throws -> FileCheckpoint?

    /// Fetches all checkpoints for a given FileSession, potentially ordered by timestamp.
    func getCheckpoints(for fileSessionId: UUID, sortedBy dateSort: SortOrder?) async throws -> [FileCheckpoint]

      /// Fetches the file content for a specific checkpoint ID.
      /// This might involve resolving bookmark data and reading the file.
    func getFileContent(for checkpointId: UUID) async throws -> String?

      /// Fetches the pending edits for a specific checkpoint ID.
    func getPendingEdits(for checkpointId: UUID) async throws -> [PendingEditInfo]?

    /// Updates the file content and pending edits for a checkpoint (or implies creating a new one).
    /// Note: The exact update mechanism (new checkpoint vs mutable checkpoint) depends on implementation.
    func updateCheckpointFileState(checkpointId: UUID, newContent: String, newPendingEdits: [PendingEditInfo]?) async throws


    // Add more specific fetch methods if needed, e.g., getLatestCheckpoint

    // Consider if file content access should be separate or part of getCheckpoint
}

// Using Foundation's SortOrder or a custom enum if needed
// enum SortOrder { case ascending, descending } 