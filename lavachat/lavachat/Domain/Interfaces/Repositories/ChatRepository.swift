import Foundation
import Combine

/// Defines the interface for managing chat sessions, messages, and chat session settings.
protocol ChatRepository {

    // MARK: - Data Change Observation
    /// Publishes a void event when any underlying data managed by this repository changes.
    var dataDidChangePublisher: AnyPublisher<Void, Never> { get }
    
    /// Observes ChatView-relevant changes for a specific session.
    func observeChatViewRelevantChanges(for sessionId: UUID) -> AnyPublisher<ChatViewRelevantChange, Never>

    /// Observes ChatsView-relevant changes (session creation/deletion only).
    func observeChatsViewRelevantChanges() -> AnyPublisher<ChatsViewRelevantChange, Never>

    // MARK: - Batch Operations Control
    /// Signals the repository to enter a batch update mode, potentially suppressing individual change notifications.
    func beginBatchUpdate() async
    
    /// Signals the repository to exit batch update mode and publish a single consolidated change notification if appropriate.
    func endBatchUpdateAndPublish() async throws

    // MARK: - ChatSession Operations

    /// Creates a new ChatSession.
    func createChatSession(_ session: ChatSession) async throws

    /// Fetches a specific ChatSession by its ID.
    func getChatSession(byId id: UUID) async throws -> ChatSession?

    /// Fetches all ChatSessions for a given user, optionally sorted.
    func getAllChatSessions(for userId: UUID, sortBy sortDescriptors: [NSSortDescriptor]?) async throws -> [ChatSession]

    /// Updates an existing ChatSession.
    func updateChatSession(_ session: ChatSession) async throws

    /// Deletes a ChatSession and its associated messages.
    func deleteChatSession(byId id: UUID) async throws

    // MARK: - Message Operations

    /// Creates a new Message within a session.
    func createMessage(_ message: Message) async throws

    /// Fetches a specific Message by its ID.
    func getMessage(byId id: UUID) async throws -> Message?

    /// Fetches all Messages for a given session ID, optionally constructing a tree.
    func getMessages(for sessionId: UUID) async throws -> [Message] // Implementation might return a structured tree or flat list

    /// Fetches all Messages for list of message IDs.
    func getMessages(for messageIds: [UUID]) async throws -> [Message]

    /// Updates an existing Message.
    func updateMessage(_ message: Message) async throws

    /// Deletes a specific Message.
    func deleteMessage(byId id: UUID) async throws

    /// Fetches messages based on a parent ID to help build the tree structure.
    func getMessages(parentId: UUID?) async throws -> [Message]

     /// Fetches the root messages for a given session.
    func getRootMessages(for sessionId: UUID) async throws -> [Message]

    /// Fetches the children of a specific message.
    func getChildMessages(for parentMessageId: UUID) async throws -> [Message]

    // MARK: - ChatSessionSetting Operations

    /// Creates a new ChatSessionSetting.
    func createSetting(_ setting: ChatSessionSetting) async throws

    /// Fetches a specific ChatSessionSetting by its ID.
    func getSetting(byId id: UUID) async throws -> ChatSessionSetting?

    /// Fetches all ChatSessionSettings available.
    func getAllSettings() async throws -> [ChatSessionSetting]
    
    /// Fetches the system default ChatSessionSetting.
    func getSystemDefaultSetting() async throws -> ChatSessionSetting?

    /// Updates an existing ChatSessionSetting. Should likely prevent changing isSystemDefault flag easily.
    func updateSetting(_ setting: ChatSessionSetting) async throws

    /// Deletes a user-created ChatSessionSetting (system default should be non-deletable).
    func deleteSetting(byId id: UUID) async throws

    // MARK: - MessageAction Operations
    /// Creates a new MessageAction.
    func createMessageAction(_ action: MessageAction) async throws
    
    /// Fetches a specific MessageAction by its ID.
    func getMessageAction(byId id: UUID) async throws -> MessageAction?

    /// Fetches all MessageActions for a list of IDs.
    func getMessageActions(for actionIds: [UUID]) async throws -> [MessageAction]
    
    /// Fetches all MessageActions available.
    func getAllMessageActions() async throws -> [MessageAction]
    
    /// Updates an existing MessageAction.
    func updateMessageAction(_ action: MessageAction) async throws
    
    /// Deletes a MessageAction.
    func deleteMessageAction(byId id: UUID) async throws

}

// Example SortDescriptor if not using Foundation's built-in one yet
// struct SortDescriptor<Value> {
//     var keyPath: PartialKeyPath<Value>
//     var ascending: Bool = true
// } 
