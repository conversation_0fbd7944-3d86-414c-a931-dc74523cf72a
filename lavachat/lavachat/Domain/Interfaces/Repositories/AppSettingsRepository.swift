import Foundation

/// Repository interface for managing app-level settings and configuration
/// Handles system-level configuration like current user ID and version tracking
protocol AppSettingsRepository {
    
    // MARK: - Current User Management
    
    /// Gets the current user ID
    /// - Returns: The current user ID if set, nil otherwise
    func getCurrentUserId() -> UUID?
    
    /// Sets the current user ID
    /// - Parameter userId: The user ID to set as current, or nil to clear
    func setCurrentUserId(_ userId: UUID?)
    
    // MARK: - Version Management
    
    /// Gets the stored version of built-in chat data
    /// - Returns: The version number, or 0 if not set
    func getBuiltinChatVersion() -> Int
    
    /// Sets the version of built-in chat data
    /// - Parameter version: The version number to store
    func setBuiltinChatVersion(_ version: Int)
    
    /// Gets the stored version of built-in LLM data
    /// - Returns: The version number, or 0 if not set
    func getBuiltinLLMVersion() -> Int
    
    /// Sets the version of built-in LLM data
    /// - Parameter version: The version number to store
    func setBuiltinLLMVersion(_ version: Int)

    /// Gets the stored version of user initializer data
    /// - Returns: The version number, or 0 if not set
    func getBuiltinUserVersion() -> Int

    /// Sets the version of user initializer data
    /// - Parameter version: The version number to store
    func setBuiltinUserVersion(_ version: Int)
}
