import Foundation

/// Defines the interface for managing user-specific settings and preferences.
protocol UserSettingsRepository {

    // MARK: - User CRUD Operations

    /// Creates a new user.
    func createUser(_ user: User) async throws -> User

    /// Fetches the User object for a given ID.
    func getUser(byId id: UUID) async throws -> User?

    /// Fetches all users in the system.
    func getAllUsers() async throws -> [User]

    /// Updates an existing user.
    func updateUser(_ user: User) async throws -> User

    /// Deletes a user by ID.
    func deleteUser(byId id: UUID) async throws

    /// Creates or updates the User object (legacy method for backward compatibility).
    func saveUser(_ user: User) async throws

    // MARK: - Current User Management

    /// Gets the current active user.
    func getCurrentUser() async throws -> User?

    /// Sets the current active user.
    func setCurrentUser(userId: UUID) async throws

    // MARK: - Subscription Management

    /// Fetches the current subscription status for the user.
    func getSubscriptionStatus(for userId: UUID) async throws -> SubscriptionStatus?

    /// Saves or updates the user's subscription status.
    func saveSubscriptionStatus(_ status: SubscriptionStatus) async throws

    /// Fetches all available Subscription Plans.
    func getAllSubscriptionPlans() async throws -> [SubscriptionPlan]

    /// Fetches a specific Subscription Plan by its product ID.
    func getSubscriptionPlan(by productId: String) async throws -> SubscriptionPlan?

}