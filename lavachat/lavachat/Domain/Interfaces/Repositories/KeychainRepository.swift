import Foundation

/// Defines the interface for securely interacting with the Keychain.
protocol KeychainRepository {

    /// Saves an API key securely in the Keychain, associated with a specific LLMProvider ID.
    /// - Parameters:
    ///   - apiKey: The API key string to save.
    ///   - providerId: The UUID of the LLMProvider this key belongs to.
    func saveApiKey(_ apiKey: String, for providerId: UUID) async throws

    /// Retrieves the API key associated with a specific LLMProvider ID from the Keychain.
    /// - Parameter providerId: The UUID of the LLMProvider.
    /// - Returns: The retrieved API key string, or nil if not found or an error occurred.
    func getApiKey(for providerId: UUID) async throws -> String?

    /// Updates an existing API key in the Keychain for a specific LLMProvider ID.
    /// - Parameters:
    ///   - apiKey: The new API key string.
    ///   - providerId: The UUID of the LLMProvider.
    func updateApiKey(_ apiKey: String, for providerId: UUID) async throws

    /// Deletes the API key associated with a specific LLMProvider ID from the Keychain.
    /// - Parameter providerId: The UUID of the LLMProvider.
    func deleteApiKey(for providerId: UUID) async throws

    /// Checks if an API key exists in the Keychain for a specific LLMProvider ID.
    /// - Parameter providerId: The UUID of the LLMProvider.
    /// - Returns: True if a key exists, false otherwise.
    func checkApiKeyExists(for providerId: UUID) async throws -> Bool
}