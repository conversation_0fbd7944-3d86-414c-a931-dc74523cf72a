import Foundation

/// Protocol defining the LLM API service interface for streaming communication
protocol LLMAPIServiceProtocol {
    
    // MARK: - Single Instance Streaming
    
    /// Send a message to a single LLM instance and receive streaming responses
    /// - Parameter request: The streaming request containing message history and parameters
    /// - Returns: An async throwing stream of responses from the specified instance
    func sendMessage(request: LLMStreamingRequest) -> AsyncThrowingStream<LLMStreamingResponse, Error>
    
    // MARK: - Multi-Instance Concurrent Streaming
    
    /// Send the same message content to multiple LLM instances concurrently
    /// - Parameter requests: Array of requests for different instances
    /// - Returns: An async throwing stream containing responses from all instances, distinguished by instanceId
    func sendMessageToMultipleInstances(requests: [LLMStreamingRequest]) -> AsyncThrowingStream<LLMStreamingResponse, Error>
    
    /// Send the same message content to multiple instances (convenience method)
    /// - Parameters:
    ///   - instanceIds: Array of instance IDs to send the message to
    ///   - messageHistory: Common message history for all instances
    ///   - overrideModelParameters: Optional model parameters to override instance defaults for all instances
    ///   - overrideSystemPrompt: Optional system prompt to override instance defaults for all instances
    ///   - thinkingControls: Optional dictionary mapping instance IDs to thinking control switches
    ///   - searchingControls: Optional dictionary mapping instance IDs to searching control switches
    /// - Returns: An async throwing stream containing responses from all instances
    func sendMessageToInstances(
        instanceIds: [UUID],
        messageHistory: [Message],
        overrideModelParameters: [String: AnyCodable]?,
        overrideSystemPrompt: String?,
        thinkingControls: [UUID: Bool]?,
        searchingControls: [UUID: Bool]?
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error>
    
    // MARK: - Request Management
    
    /// Cancel a specific request by its ID
    /// - Parameter requestId: The unique identifier of the request to cancel
    func cancelRequest(requestId: UUID) async
    
    /// Cancel all requests for a specific instance
    /// - Parameter instanceId: The instance ID whose requests should be cancelled
    func cancelInstanceRequests(instanceId: UUID) async
    
    /// Cancel all active requests
    func cancelAllRequests() async
    
    // MARK: - Status Monitoring
    
    /// Get the list of currently active request IDs
    /// - Returns: Array of request IDs that are currently being processed
    func getActiveRequests() -> [UUID]
    
    /// Get the list of active requests for a specific instance
    /// - Parameter instanceId: The instance ID to query
    /// - Returns: Array of request IDs currently active for the specified instance
    func getActiveRequests(for instanceId: UUID) -> [UUID]
    
    /// Check if a specific request is currently active
    /// - Parameter requestId: The request ID to check
    /// - Returns: True if the request is currently being processed
    func isRequestActive(requestId: UUID) -> Bool
    
    // MARK: - Service Health
    
    /// Check if the service is ready to handle requests
    /// - Returns: True if the service is operational
    var isServiceAvailable: Bool { get }
    
    /// Get the current service status information
    /// - Returns: A dictionary containing service status details
    func getServiceStatus() -> [String: Any]
}

// MARK: - Default Implementation Extensions

extension LLMAPIServiceProtocol {
    
    /// Default implementation for sending to multiple instances using convenience method
    func sendMessageToInstances(
        instanceIds: [UUID],
        messageHistory: [Message],
        overrideModelParameters: [String: AnyCodable]? = nil,
        overrideSystemPrompt: String? = nil,
        thinkingControls: [UUID: Bool]? = nil,
        searchingControls: [UUID: Bool]? = nil
    ) -> AsyncThrowingStream<LLMStreamingResponse, Error> {
        
        let requests = instanceIds.map { instanceId in
            LLMStreamingRequest(
                instanceId: instanceId,
                messageHistory: messageHistory,
                overrideModelParameters: overrideModelParameters ?? [:],
                overrideSystemPrompt: overrideSystemPrompt,
                thinkingControls: thinkingControls ?? [:],
                searchingControls: searchingControls ?? [:]
            )
        }
        
        return sendMessageToMultipleInstances(requests: requests)
    }
} 
