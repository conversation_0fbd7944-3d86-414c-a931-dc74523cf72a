import Foundation
import UIKit

/// Protocol for sharing service that handles different sharing formats
protocol ShareServiceProtocol {
    
    // MARK: - Core Sharing Methods
    
    /// Share an item using the specified configuration
    /// - Parameters:
    ///   - item: The item to share (must conform to ShareableItem)
    ///   - configuration: Configuration for the sharing operation
    /// - Returns: Result of the sharing operation
    func shareItem<T: ShareableItem>(
        _ item: T,
        configuration: ShareConfiguration
    ) async -> ShareResult
    
    /// Share multiple items as a bundle
    /// - Parameters:
    ///   - items: Array of items to share
    ///   - configuration: Configuration for the sharing operation
    /// - Returns: Result of the sharing operation
    func shareItems<T: ShareableItem>(
        _ items: [T],
        configuration: ShareConfiguration
    ) async -> ShareResult
    
    // MARK: - Format-Specific Sharing
    
    /// Share as a file (.lavachat format)
    /// - Parameters:
    ///   - item: The item to share
    ///   - fileName: Optional custom file name
    /// - Returns: URL of the created file
    func shareAsFile<T: ShareableItem>(
        _ item: T,
        fileName: String?
    ) async throws -> URL
    
    /// Share via iCloud using CloudKit
    /// - Parameters:
    ///   - item: The item to share
    ///   - permissions: iCloud sharing permissions
    /// - Returns: iCloud share URL
    func shareViaICloud<T: ShareableItem>(
        _ item: T,
        permissions: ICloudSharePermissions
    ) async throws -> URL
    
    /// Generate QR code for sharing
    /// - Parameters:
    ///   - item: The item to share
    ///   - size: Size of the QR code image
    /// - Returns: QR code image data
    func generateQRCode<T: ShareableItem>(
        for item: T,
        size: CGSize
    ) async throws -> Data
    
    // MARK: - Utility Methods
    
    /// Check if a specific sharing format is available
    /// - Parameter format: The sharing format to check
    /// - Returns: True if the format is available
    func isFormatAvailable(_ format: ShareFormat) async -> Bool
    
    /// Get supported sharing formats for the current device/context
    /// - Returns: Array of available sharing formats
    func getSupportedFormats() async -> [ShareFormat]
    
    /// Validate that an item can be shared
    /// - Parameter item: The item to validate
    /// - Throws: ShareError if the item cannot be shared
    func validateItem<T: ShareableItem>(_ item: T) throws
    
    /// Get the default file name for an item
    /// - Parameter item: The item to get the file name for
    /// - Returns: Suggested file name
    func getDefaultFileName<T: ShareableItem>(for item: T) -> String
    
    // MARK: - Progress and Cancellation
    
    /// Cancel an ongoing sharing operation
    /// - Parameter operationId: ID of the operation to cancel
    func cancelSharingOperation(_ operationId: UUID) async
    
    /// Get progress of an ongoing sharing operation
    /// - Parameter operationId: ID of the operation
    /// - Returns: Progress value between 0.0 and 1.0, or nil if operation not found
    func getSharingProgress(_ operationId: UUID) -> Double?
}

/// Protocol for import service that handles importing shared content
protocol ImportServiceProtocol {
    
    // MARK: - Core Import Methods
    
    /// Import content from a file
    /// - Parameters:
    ///   - fileURL: URL of the file to import
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult
    
    /// Import content from iCloud share URL
    /// - Parameters:
    ///   - shareURL: iCloud share URL
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult
    
    /// Import content from QR code data
    /// - Parameters:
    ///   - qrCodeData: Data extracted from QR code
    ///   - configuration: Configuration for the import operation
    /// - Returns: Result of the import operation
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult
    
    // MARK: - Validation and Preview
    
    /// Validate that a file can be imported
    /// - Parameter fileURL: URL of the file to validate
    /// - Returns: True if the file can be imported
    func canImportFile(_ fileURL: URL) async -> Bool
    
    /// Get preview information about importable content
    /// - Parameter fileURL: URL of the file to preview
    /// - Returns: Preview information or nil if cannot be previewed
    func getImportPreview(_ fileURL: URL) async -> ImportPreview?
    
    /// Validate QR code data for import
    /// - Parameter qrCodeData: QR code data string
    /// - Returns: True if the QR code contains valid import data
    func canImportFromQRCode(_ qrCodeData: String) async -> Bool
    
    // MARK: - Progress and Cancellation
    
    /// Cancel an ongoing import operation
    /// - Parameter operationId: ID of the operation to cancel
    func cancelImportOperation(_ operationId: UUID) async
    
    /// Get progress of an ongoing import operation
    /// - Parameter operationId: ID of the operation
    /// - Returns: Progress value between 0.0 and 1.0, or nil if operation not found
    func getImportProgress(_ operationId: UUID) -> Double?
}

/// Preview information for importable content
struct ImportPreview {
    let contentType: ShareContentType
    let itemCount: Int
    let itemNames: [String]
    let formatVersion: String
    let exportedAt: Date
    let metadata: ShareableMetadata
    
    init(
        contentType: ShareContentType,
        itemCount: Int,
        itemNames: [String],
        formatVersion: String,
        exportedAt: Date,
        metadata: ShareableMetadata,
    ) {
        self.contentType = contentType
        self.itemCount = itemCount
        self.itemNames = itemNames
        self.formatVersion = formatVersion
        self.exportedAt = exportedAt
        self.metadata = metadata
    }
}
