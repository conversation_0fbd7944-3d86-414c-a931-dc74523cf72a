<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23788" systemVersion="24D81" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="YES" userDefinedModelVersionIdentifier="">
    <entity name="CDChatSession" representedClassName=".CDChatSession" syncable="YES">
        <attribute name="activeContextServerIds" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="activeLLMInstanceIds" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="usedLLMInstanceIds" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="createdAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="instanceSettings" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="lastModifiedAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="activeMessageId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="settingsId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="userId" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <relationship name="messages" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="CDMessage" inverseName="session" inverseEntity="CDMessage"/>
        <relationship name="settings" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDChatSessionSetting" inverseName="chatSessions" inverseEntity="CDChatSessionSetting"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDUser" inverseName="chatSessions" inverseEntity="CDUser"/>        
    </entity>
    <entity name="CDChatSessionSetting" representedClassName=".CDChatSessionSetting" syncable="YES">
        <attribute name="createdAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="defaultContextServerIds" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="isSystemDefault" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="shouldExpandThinking" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="lastModifiedAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="llmParameterOverrides" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="messageActionSettings" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <attribute name="uiThemeSettings" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="savedPromptSegments" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="auxiliaryLLMInstanceId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="shouldAutoGenerateTitle" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="contextMessageCount" attributeType="Integer 64" defaultValueString="9223372036854775807" usesScalarValueType="YES"/>
        <relationship name="chatSessions" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="CDChatSession" inverseName="settings" inverseEntity="CDChatSession"/>
        <relationship name="defaultForUser" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDUser" inverseName="defaultChatSettings" inverseEntity="CDUser"/>
    </entity>
    <entity name="CDFileCheckpoint" representedClassName=".CDFileCheckpoint" syncable="YES">
        <attribute name="basedOnCheckpointId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="fileContent" attributeType="String" defaultValueString=""/>
        <attribute name="fileSessionId" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="pendingEditStates" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="timestamp" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="triggeringMessageId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <relationship name="fileSession" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDFileSession" inverseName="checkpoints" inverseEntity="CDFileSession"/>
    </entity>
    <entity name="CDFileSession" representedClassName=".CDFileSession" syncable="YES">
        <attribute name="activeLLMInstanceId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="bookmarkData" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="createdAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="currentFileCheckpointId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="lastModifiedAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="originalSourceInfo" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="rootMessageId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="settingsId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="userId" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <relationship name="checkpoints" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="CDFileCheckpoint" inverseName="fileSession" inverseEntity="CDFileCheckpoint"/>
    </entity>
    <entity name="CDLLMInstance" representedClassName=".CDLLMInstance" syncable="YES">
        <attribute name="createdAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="customLogoData" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="defaultParameters" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="isFavorited" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isUserModified" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="lastUsedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="modelId" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <attribute name="systemPrompt" optional="YES" attributeType="String"/>
        <attribute name="totalCompletionTokensUsed" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="totalPromptTokensUsed" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="groups" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="CDLLMInstanceGroup" inverseName="instances" inverseEntity="CDLLMInstanceGroup"/>
        <relationship name="messages" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="CDMessage" inverseName="llmInstance" inverseEntity="CDMessage"/>
        <relationship name="model" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDLLMModel" inverseName="instances" inverseEntity="CDLLMModel"/>
        <relationship name="utilityForUser" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDUser" inverseName="systemUtilityLLMInstance" inverseEntity="CDUser"/>
    </entity>
    <entity name="CDLLMInstanceGroup" representedClassName=".CDLLMInstanceGroup" syncable="YES">
        <attribute name="createdAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="customLogoData" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="description_" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="instanceIds" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="isFavorited" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="lastModifiedAt" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <relationship name="instances" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="CDLLMInstance" inverseName="groups" inverseEntity="CDLLMInstance"/>
    </entity>
    <entity name="CDLLMModel" representedClassName=".CDLLMModel" syncable="YES">
        <attribute name="availabilityStatus" optional="YES" attributeType="String"/>
        <attribute name="contextWindowSize" optional="YES" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="customLogoData" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="inputModalities" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="isDefaultRecommendation" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isUserCreated" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isUserModified" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="logoImageName" optional="YES" attributeType="String"/>
        <attribute name="maxOutputTokens" optional="YES" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="modelDescription" optional="YES" attributeType="String"/>
        <attribute name="modelIdentifier" attributeType="String" defaultValueString=""/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <attribute name="outputModalities" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="pricingInfo" optional="YES" attributeType="String"/>
        <attribute name="group" optional="YES" attributeType="String"/>
        <attribute name="providerId" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="thinkingCapabilities" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="searchingCapabilities" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="apiConfigsOverride" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <relationship name="instances" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="CDLLMInstance" inverseName="model" inverseEntity="CDLLMInstance"/>
        <relationship name="provider" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDLLMProvider" inverseName="models" inverseEntity="CDLLMProvider"/>
    </entity>
    <entity name="CDLLMProvider" representedClassName=".CDLLMProvider" syncable="YES">
        <attribute name="apiKeyStored" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="apiBaseUrl" optional="YES" attributeType="String"/>
        <attribute name="apiDocumentationUrl" optional="YES" attributeType="String"/>
        <attribute name="apiEndpointPath" optional="YES" attributeType="String"/>
        <attribute name="apiStyle" attributeType="String" defaultValueString="openai_compatible"/>
        <attribute name="customLogoData" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="isUserCreated" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isUserModified" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="logoImageName" optional="YES" attributeType="String"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <attribute name="providerType" attributeType="String" defaultValueString="user_api_key"/>
        <attribute name="websiteUrl" optional="YES" attributeType="String"/>
        <relationship name="models" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="CDLLMModel" inverseName="provider" inverseEntity="CDLLMModel"/>
    </entity>
    <entity name="CDMessage" representedClassName=".CDMessage" syncable="YES">
        <attribute name="completionTokens" optional="YES" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="content" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="isFavorited" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isReplied" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="depth" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="llmInstanceId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="parentId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="parsedFileOperations" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="promptTokens" optional="YES" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="rawResponseText" optional="YES" attributeType="String"/>
        <attribute name="role" attributeType="String" defaultValueString=""/>
        <attribute name="sessionId" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="status" attributeType="String" defaultValueString="received"/>
        <attribute name="timestamp" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="userFeedback" attributeType="String" defaultValueString="none"/>
        <attribute name="userId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <relationship name="children" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="CDMessage" inverseName="parent" inverseEntity="CDMessage"/>
        <relationship name="llmInstance" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDLLMInstance" inverseName="messages" inverseEntity="CDLLMInstance"/>
        <relationship name="parent" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDMessage" inverseName="children" inverseEntity="CDMessage"/>
        <relationship name="session" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDChatSession" inverseName="messages" inverseEntity="CDChatSession"/>
    </entity>
    <entity name="CDUser" representedClassName=".CDUser" syncable="YES">
        <attribute name="appSettings" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="avatarIdentifier" optional="YES" attributeType="String"/>
        <attribute name="defaultChatSettingsId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="lastSeenAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastSyncTimestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="memoryFileBookmarkData" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="nickname" optional="YES" attributeType="String"/>
        <attribute name="registrationDate" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="NO"/>
        <attribute name="syncStatus" optional="YES" attributeType="String"/>
        <attribute name="systemUtilityLLMInstanceId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <relationship name="chatSessions" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="CDChatSession" inverseName="user" inverseEntity="CDChatSession"/>
        <relationship name="defaultChatSettings" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDChatSessionSetting" inverseName="defaultForUser" inverseEntity="CDChatSessionSetting"/>
        <relationship name="systemUtilityLLMInstance" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="CDLLMInstance" inverseName="utilityForUser" inverseEntity="CDLLMInstance"/>
    </entity>
    <entity name="CDMessageAction" representedClassName=".CDMessageAction" syncable="YES">
        <attribute name="id" optional="YES" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="actionType" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="prompts" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
        <attribute name="targetLLMInstanceId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="metadata" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
    </entity>
</model>