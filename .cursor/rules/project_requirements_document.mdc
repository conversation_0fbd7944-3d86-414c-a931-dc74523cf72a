---
description: 
globs: 
alwaysApply: true
---
**熔言 / LavaChat - 项目需求文档 (PRD)**

**版本:** 1.1 (修改版)
**日期:** 2024年5月22日
**作者:** alwayskeepcoding

**目录**

1.  应用概述
2.  用户流程
3.  技术栈 & API
4.  核心功能
5.  范围内的项目与范围外的项目 (V1)

---

**1. 应用概述**

**1.1. 应用名称**
*   中文区域: 熔言 - 你的AI中枢
*   非中文区域: LavaChat - Your AI Hub

**1.2. 应用定位**
LavaChat 是一款面向 iOS (首发) 并规划跨平台的 AI 应用，旨在成为用户的智能中枢。它集成并管理多个大型语言模型 (LLM)，提供强大的对话、创新的文件交互能力，并整合了便捷的文件访问与管理入口。用户既可以通过付费订阅使用应用内置 API Key 访问精选模型，也可以安全地接入和管理自己拥有的 API Key 以使用自定义或第三方模型，还可以将本地文件作为知识库或在对话中引用。

**1.3. 目标用户**
*   需要与多个不同 LLM 进行交互的用户（如研究人员、开发者、内容创作者）。
*   希望在一个统一界面管理和配置不同 AI 模型实例的用户。
*   寻求更高级、结构化 AI 对话体验的用户（如需要分支、编辑、回溯）。
*   需要在移动设备上进行 AI 辅助文件编辑、阅读和理解的用户（如开发者、作家、学生）。
*   需要方便地在 AI 对话中引用本地文件或管理与应用相关文件的用户。
*   希望构建个人本地知识库供 AI 查询的用户。
*   愿意为优质 AI 服务和高级功能付费的用户。

**1.4. 核心价值**
*   **统一入口:** 集成并管理众多 LLM，简化用户与 AI 的交互。
*   **灵活接入:** 支持内置付费模型和用户自定义模型，满足不同需求和预算。
*   **高级对话:** 提供非线性的树状对话结构，支持编辑、刷新、融合、回溯等高级交互。
*   **文件智能:** 创新的文件交互模式，结合对话与编辑，提升移动端处理文档的效率。
*   **便捷文件访问:** 提供应用专属文件区域的快捷入口，支持对话中直接引用本地文件。
*   **跨平台愿景:** iOS 首发，但架构设计考虑未来向 macOS 等其他平台扩展。

---

**2. 用户流程**

*   **首次启动与引导:** 应用介绍，登录/注册（CloudKit），引导配置第一个模型或选择订阅。
*   **创建与管理对话会话 (`ChatSession`):**
    *   从对话列表页创建新会话。
    *   选择或配置会话设置 (`ChatSessionSetting`)。
    *   进入对话界面进行交互。
    *   返回对话列表，查看、搜索、删除历史会话。
*   **核心对话交互:**
    *   发送用户消息。
    *   接收单个或多个 AI 的回复。
    *   在输入框中输入 `@`：
        *   **选择提及类型:** 弹出选项让用户选择是"@ 提及文件/文件夹"还是继续输入以"@ 提及实例/组"。
        *   **提及实例/组:** 输入实例名称或组名称并发送，与指定的单个实例或组内所有实例进行交互。也可通过模型选择栏选取。
        *   **提及文件/文件夹:**
            *   触发系统文件选择器 (`UIDocumentPickerViewController` 或 SwiftUI 等效控件)，允许选择一个或多个文件/文件夹。
            *   读取选中文件的内容。
            *   进行简单的 Token 估算。如果估算量较大，向用户发出警告提示，由用户确认是否继续发送。
            *   将文件内容嵌入消息中（例如使用 `ContentBlock.attachedFileContent`）并发送。
    *   编辑用户历史消息 (创建新分支)。
    *   刷新 AI 回复 (创建新版本)。
    *   选中多个 AI 回复并进行融合。
    *   选中并基于历史消息进行新的提问或操作。
    *   对 AI 回复进行点赞/点踩/收藏 (`userFeedback`, `isFavorited`)。
    *   在消息上使用自定义 AI 操作按钮。
    *   发送/查看图片等多模态内容。
    *   导出/分享会话 (`.lavachat` 格式)。
    *   导入会话 (`.lavachat` 格式)。
*   **创建与管理文件会话 (`FileSession`):**
    *   导入文件 (从 PDF, Word, TXT, MD 等) 或创建新 Markdown 文件。
    *   **文件存储与访问:**
        *   导入或创建后，应用会生成一个 **核心的 Markdown 版本** 的文件。
        *   这个 核心 Markdown 文件 存储在 **应用专属的文件夹内**（位于系统"文件"应用的"我的 iPhone"或 iCloud Drive 下，例如 `LavaChatFiles/Sessions/`）。
        *   应用通过 **Security-Scoped Bookmark 数据 (`FileSession.bookmarkData`)** 维护对这个 核心 Markdown 文件 的持久访问权限，以应对文件在 `Sessions/` 文件夹内的移动或重命名。后续所有与该 `FileSession` 的交互（编辑、读取、@提及引用、导出 MD）都基于这个文件。
        *   原始导入文件的信息（如名称、类型）记录在 `FileSession.originalSourceInfo` 中，但不保证持续访问原始文件。
    *   进入文件交互界面（操作对象是上述核心 Markdown 文件）。
    *   在 `chat` 模式下提问、请求读取文件、获取建议。
    *   在 `edit` 模式下接收 AI 的 `DiffBlock` 修改建议。
    *   接受/拒绝/手动修改 AI 的编辑建议。
    *   在文件区域手动编辑内容。
    *   发送消息触发 Checkpoint。
    *   通过对话记录回溯到历史文件 Checkpoint（恢复核心 Markdown 文件的内容和状态）。
    *   导出最终文件 (可直接导出核心 Markdown 文件，或通过 AI/库 尝试转换回 `originalSourceInfo` 中记录的原始格式)。
*   **文件管理与访问 ("门户 (Hub)" Tab):**
    *   点击 "文件管理" 按钮： 应用将**跳转到系统"文件"应用**，并直接打开或定位到 **LavaChat 的专属文件夹**（例如 `LavaChatFiles/`）。
    *   用户文件管理: 用户可以在此文件夹内自由创建子文件夹（如 `KnowledgeBase/` 用于存放知识库文档），整理 `FileSession` 文件（在 `Sessions/` 子文件夹内），或添加其他供 `@提及` 使用的文件。
*   **模型管理 ("模型" Tab):**
    *   浏览预设的 LLM 提供商 (`LLMProvider`) 和模型 (`LLMModel`)。
    *   添加自定义提供商和模型。
    *   创建、编辑、删除 LLM 实例 (`LLMInstance`)。
    *   为 `user_provided` 类型的实例安全输入和存储 API Key (通过 Keychain)。
    *   配置实例的默认参数 (`defaultParameters`)。
    *   收藏/标记常用实例。
    *   创建、编辑、删除 LLM 实例组 (`LLMInstanceGroup`)。将实例添加到组或从组中移除。确保实例和组的名称在用户范围内唯一。
    *   导出/分享实例配置 (`.lavachatinstance` 格式)。
    *   导入实例配置 (`.lavachatinstance` 格式)。
*   **订阅与用户管理 ("我/设置" Tab):**
    *   查看个人信息 (`User`)。
    *   浏览订阅计划 (`SubscriptionPlan`)。
    *   通过 Apple IAP 完成订阅购买/续费。
    *   查看当前订阅状态 (`SubscriptionStatus`) 和 Token 使用情况 (针对 `system_provided` 实例)。
    *   管理应用全局设置（如默认对话设置、系统杂活 LLM 选择）。
*   **应用状态保持:** 用户退出 App 时若在具体对话/文件界面，下次冷启动时应直接恢复到该界面。

---

**3. 技术栈 & API**

*   **平台:** iOS (首发)
*   **UI 框架:** SwiftUI
*   **架构:** Clean Architecture
*   **本地数据存储:** Core Data (初期实现), 需考虑未来迁移或兼容 SQLite 的可能性。
*   **云同步 & 用户数据:** Apple CloudKit
    *   **Private Database:** 用于同步用户会话、消息、设置、实例、`FileSession` 元数据 (`id`, `originalSourceInfo`, `title`, `settingsId` 等) 等个人数据。**注意：`FileSession.bookmarkData` 指向的是设备上（或通过 iCloud Drive 同步的）那个核心 Markdown 文件。CloudKit 会同步 `bookmarkData` 这个数据本身。但在另一台设备上，该 `bookmarkData` 能否成功解析取决于那个核心 Markdown 文件是否也存在于该设备的 `LavaChatFiles/Sessions/` 路径下（通常需要 iCloud Drive 同步该文件夹）。应用需要在新设备上处理书签可能失效并尝试重新定位文件或提示用户的情况。**
    *   **Shared Database:** 用于用户通过 iCloud 链接或二维码分享 `ChatSession`。
    *   **Public Database:** 考虑用于未来"发现"功能。
*   **后端服务 (内置 API Key & 订阅):** Cloudflare Workers
    *   功能: 验证 Apple IAP 收据、管理用户订阅状态、代理访问 LLM API (使用安全存储的开发者 Key)、（可选）生成短时效认证 Token。
    *   优势: Serverless、全球边缘节点、低成本启动、无需 ICP 备案（针对 Workers 服务本身）。
*   **支付:** Apple In-App Purchase (StoreKit 2)
*   **安全存储:** iOS Keychain (用于存储用户提供的 API Keys)
*   **文件系统访问:**
    *   `FileManager` API 用于基本文件操作和管理 `LavaChatFiles/` 目录。
    *   **Security-Scoped Bookmarks API:** 用于创建和解析 `FileSession.bookmarkData`，以持久、安全地访问对应的 核心 Markdown 文件。
    *   **`UIDocumentPickerViewController` (或 SwiftUI 封装):** 用于初始文件导入，以及实现 `@提及` 文件/文件夹时的选择功能。
*   **外部 API:**
    *   LLM Provider APIs (OpenAI, Anthropic, Google, etc.) - 通过 App 直接调用 (User Key) 或通过 CF Workers 代理 (Built-in Key)。
    *   Apple Server API (用于 IAP 收据验证)。
    *   CloudKit API (Private 和 Shared Database 操作)。
    *   (可选) Cloudflare AI API (用于内容审核等辅助任务)。
*   **开发辅助:** AI 编程工具 (如 Cursor)
*   **基础设施准备:** 阿里云 ECS (用于未来可能的 ICP 备案需求，非 V1 功能直接依赖)。

---

**4. 核心功能 (基于领域实体设计)**

**4.1. 对话模块 (`ChatSession`, `Message`, `ChatSessionSetting`)**
*   **会话管理:**
    *   创建、读取、更新、删除 `ChatSession`。
    *   会话列表展示与排序 (按 `lastModifiedAt`)。
    *   会话标题 (自动生成或用户自定义)。
*   **消息树结构:**
    *   `Message` 实体包含 `parentId` 实现树状关系。
    *   区分 `user`, `assistant`, `system`, `merged_assistant` 等角色。
    *   支持根消息 (`parentId` 为 null)。
*   **高级消息交互:**
    *   **编辑用户消息:** 创建新 `Message` 节点，`metadata` 记录 `is_edit_of`，形成分支。
    *   **多 AI 回复:** 单个用户消息下可并列多个 `assistant` 消息节点 (不同 `llmInstanceId`)。
    *   **AI 回复刷新:** 创建新 `Message` 节点，`metadata` 记录 `is_regeneration_of`，形成版本。
    *   **AI 回复融合:** 创建 `merged_assistant` 角色 `Message`，`metadata` 记录 `merged_from` 列表。
    *   **上下文引用:** `metadata` 记录 `referenced_message`，支持基于历史消息的操作。
    *   **提及交互 (`@mention`):**
        *   支持用户输入 `@` 后选择"提及文件/文件夹"或"提及实例/组"。
        *   **提及实例/组:** 通过 `@实例名称` 或 `@组名称` 指定消息的特定接收者。提及组时，并行地向组内所有实例发送消息，并在 UI 上聚合展示各实例的回复。解析 `@` 符号后的名称，确保唯一性。
        *   **提及文件/文件夹:** 触发文件选择器，允许多选。
            *   如果用户选择了某个 **`FileSession` 对应的核心 Markdown 文件**（例如直接在 `LavaChatFiles/Sessions/` 中选取），则通过其 `bookmarkData` 安全读取最新内容。
            *   如果用户选择其他文件/文件夹（如 `KnowledgeBase/` 中的文件），则直接读取其内容。
            *   将选中文件的内容作为 `ContentBlock.attachedFileContent` 嵌入 `Message.content` 列表。**发送前进行简单的 Token 估算并对用户进行警告确认。**
    *   **多模态与附件:** `ContentBlock` 支持 `text`, `image` 和 **`attachedFileContent` (用于 @提及 的文件)** 类型。
    *   **Token 记录:** `Message` 记录 `promptTokens`, `completionTokens`。
    *   **用户反馈:** `Message` 记录 `userFeedback` (like/dislike), `isReplied`, `isFavorited`。
*   **会话设置:**
    *   `ChatSessionSetting` 实体管理配置。
    *   支持系统默认设置 (`isSystemDefault`) 和用户自定义设置。
    *   LLM 参数覆盖 (`llmParameterOverrides`): 会话级别覆盖 `LLMInstance` 的默认参数。
    *   UI 主题定制 (`uiThemeSettings`): 主题、颜色、背景、字体。
    *   消息操作定制 (`messageActionSettings`): 启用/禁用默认按钮，添加自定义 AI 操作 (`customAIActionsForUser` or `customAIActionsForAssistant` - 标签、图标、Prompt 模板、目标 LLM)。
*   **导入/导出与分享:**
    *   **文件导出:** 支持将会话导出为 `.lavachat` 文件 (JSON 格式，可能打包图片资源)。
    *   **文件导入:** 支持从 `.lavachat` 文件导入会话，处理 UUID 冲突、用户 ID 替换、LLM 实例映射。
    *   **iCloud 分享:**
        *   支持将会话通过 CloudKit Shared Database 创建分享链接 (CKShare)。
        *   支持将 iCloud 分享链接导出为 QR 码图片。
    *   **iCloud 导入:**
        *   支持通过接收到的 iCloud 分享链接导入会话。
        *   支持通过扫描相机或从相册选择包含 iCloud 分享链接的 QR 码图片导入会话。

**4.2. 文件交互模块 (`FileSession`, `FileCheckpoint`, `PendingEditInfo`)**
*   **文件会话:**
    *   `FileSession` 实体管理单个文件的交互过程。
    *   **文件持久化:**
        *   核心交互对象是 **一个 Markdown 文件**，存储在应用专属文件夹内 (`LavaChatFiles/Sessions/`)。
        *   通过 `FileSession.bookmarkData` (Security-Scoped Bookmark 数据) 确保持久访问 **这个 Markdown 文件**。
        *   `originalSourceInfo` 记录原始导入信息。
    *   支持 `chat` 和 `edit` 两种模式切换（针对核心 Markdown 文件）。
*   **Markdown 核心:**
    *   默认编辑 核心 Markdown 文件。
    *   利用 AI (或库) 辅助**导入**非 Markdown 文件转换为 Markdown (生成核心文件)。
    *   利用 AI (或库) 辅助**导出**核心 Markdown 文件到其他格式 (基于 `originalSourceInfo` 类型)。
*   **结构化编辑与展示:**
    *   AI 在 `edit` 模式下生成 `DiffBlock` (包含原文、替换内容、范围、解释) 用于修改 核心 Markdown 文件。
*   **Checkpointing 与回溯:**
    *   `FileCheckpoint` 实体记录 核心 Markdown 文件 的快照 (完整内容 + 待处理编辑状态)。
    *   **主要触发点:** 用户发送消息时。
    *   支持通过关联的消息回溯到历史 `FileCheckpoint`，恢复 核心 Markdown 文件 的内容和待处理状态。
*   **用户手动编辑处理:**
    *   识别用户在 核心 Markdown 文件 视图中的手动编辑区域，生成 `manual_edit` 类型的 `PendingEditInfo`。
    *   处理用户在 AI 建议区域 (`DiffBlock`) 内的手动修改，状态变为 `modified_pending`，记录 `manualEditDiff`。
*   **UI 概念:**
    *   Z 轴分层: 核心 Markdown 文件 (Z=0), 对话悬浮 (Z=1)。
    *   区分聊天输入与文件编辑输入模式。
    *   毛玻璃对话框，高度限制，支持展开/折叠 (`thinking` 块)。
    *   支持对话/文件全屏切换。
*   **AI 文件操作:**
    *   AI 请求/操作的对象是 核心 Markdown 文件。`DiffBlock.filePath` 应指向这个文件。
    *   `Message.content` 扩展 `ContentBlock` (如 `thinking`, `diffBlock`, `fileReadRequest`, `fileWriteResult`, `fileSearchRequest`, `fileSearchResult`)。
    *   `Message.parsedFileOperations` 记录解析出的待执行操作 (`FileOperation` enum)。

**4.3. 模型管理模块 (`LLMProvider`, `LLMModel`, `LLMInstance`, `LLMInstanceGroup`)**
*   **模型库:**
    *   预置主流 `LLMProvider` 和 `LLMModel` 信息。Provider 需要区分类型（如 `subscription_based` - 应用订阅提供, `user_api_key` - 用户提供 Key）。
    *   允许用户添加自定义 `LLMProvider` (类型默认为 `user_api_key`) 和 `LLMModel`。
    *   安全存储用户 API Key (Keychain)。Key 与 `LLMProvider` 关联，并通过 `LLMProvider.apiKeyStored` 标记状态。
    *   **Logo 管理系统:** 
        *   **LLM 提供商 Logo:** 每个 `LLMProvider` 都应包含对应的官方 Logo 图像 (`logoImageName`)。
            *   预置提供商使用内置的官方 Logo 图像资源。
            *   用户自定义提供商可以上传自定义 Logo 图像，存储在 `customLogoData` 中，或使用默认图标。
        *   **LLM 模型 Logo:** 每个 `LLMModel` 也应能显示相应的 Logo。
            *   预置模型使用内置的模型专属 Logo 或其提供商的 Logo 作为基础。
            *   用户自定义模型可以上传自定义 Logo 图像，存储在 `customLogoData` 中，或继承其提供商的 Logo。
        *   **LLM 实例 Logo:** 用户可以为每个 `LLMInstance` 自定义 Logo 显示。
            *   默认情况下，实例继承其关联模型的 Logo。
            *   用户可以通过 `customLogoData` 为实例设置完全自定义的 Logo。
        *   **LLM 实例组 Logo:** 用户可以为每个 `LLMInstanceGroup` 自定义 Logo 显示。
            *   默认情况下，组显示其包含的实例 Logo 的组合。
            *   用户可以通过 `customLogoData` 为组设置完全自定义的 Logo。
        *   **Logo 技术规范:**
            *   提供商和模型 Logo 以 PNG 或 PDF 格式存储在应用资源中，提供标准尺寸和小尺寸两种版本，并支持 Dark Mode（如果官方提供）。
            *   图像资源应统一使用命名约定：
                *   提供商: `provider_logo_[provider_name]`  
                *   模型: `model_logo_[provider_name]_[model_identifier]`
            *   用户上传的自定义 Logo 应经过处理，确保满足尺寸限制和显示要求。
            *   所有 Logo 在显示时都应考虑背景适配，可能需要添加边框或阴影以增强视觉边界。
*   **模型实例配置:**
    *   `LLMInstance` 代表用户对 `LLMModel` 的具体配置。实例的类型和 API Key 状态由其关联的 `LLMProvider` 和 `LLMModel` 决定。
    *   配置实例的 `defaultParameters` (System Prompt, Temperature 等)。实例名称需与组名称一起保持用户范围内的唯一性。
    *   追踪 Token 使用量 (`totalPromptTokensUsed`, `totalCompletionTokensUsed`)。
    *   收藏 (`isFavorited`) 和标记为系统杂活。
*   **实例分组 (`LLMInstanceGroup`):**
    *   允许用户创建、编辑、删除实例组。
    *   用户可以将 `LLMInstance` 添加到组或从组中移除。
    *   组名称需与实例名称一起保持用户范围内的唯一性，以支持 `@mention`。
*   **导入/导出:**
    *   支持将 `LLMInstance` 配置导出为 `.lavachatinstance` 文件 (JSON 格式，不含 API Key)。
    *   支持从 `.lavachatinstance` 文件导入实例。导入时需用户确认基础 `LLMModel` 和 `LLMProvider` 存在。实例的类型和 Key 状态由选择关联的 `LLMModel` 和 `LLMProvider` 决定。如果目标 Provider 是 `user_api_key` 且 Key 未存储，需提示用户为该 Provider 设置 API Key。实例名称冲突时需处理。
    *   *(V1 可能不包含组的导入导出，待定)*

**4.4. 用户与订阅模块 (`User`, `SubscriptionPlan`, `SubscriptionStatus`)**
*   **用户管理:**
    *   `User` 实体存储用户基本信息、偏好设置（默认对话设置、系统杂活 LLM）。
    *   CloudKit `recordID` 可作为跨设备稳定标识符。
*   **订阅系统:**
    *   `SubscriptionPlan`: 定义 App Store 的订阅产品信息 (通过 StoreKit 或服务器同步)。
    *   `SubscriptionStatus`: 记录用户当前的订阅状态、有效期、周期 Token 使用量 (基于 `Message` Token 记录和 Plan 的 CostFactor 计算更新)。
    *   **验证流程:** App 内购买 -> 发送收据给 CF Workers -> Workers 验证收据 -> Workers 更新用户状态 (可能通过 CloudKit 或内部存储) -> App 从可信源获取最新状态。
*   **内置 API 访问:**
    *   只有拥有有效 `SubscriptionStatus` (`active` 或 `in_grace_period` 等) 的用户才能通过 CF Workers 代理使用 `system_provided` 的 `LLMInstance`。
    *   (可选) CF Workers 可基于验证后的订阅状态发放短时效 Token 给 App，用于后续的 API 请求认证。

**4.5. 文件管理与知识库**
*   **门户 Tab:**
    *   **文件快捷入口:** 在"门户"页内提供一个按钮，点击后跳转至系统"文件"应用中的 LavaChat 专属文件夹 (`LavaChatFiles/`)。
*   **应用文件结构:**
    *   在"文件"应用内维护一个顶层文件夹，如 `LavaChatFiles/`。
    *   内部包含子文件夹，如 `Sessions/` (存放 `FileSession` 对应的 核心 Markdown 文件) 和 `KnowledgeBase/` (用户可在此创建子文件夹存放知识库文档)。
*   **知识库访问 (通过 @提及):** 用户可以通过 `@提及` 文件/文件夹功能，选择 `KnowledgeBase/` 或其子文件夹下的文件，供 AI 读取（一次性内容读取）。也可以选择 `Sessions/` 文件夹下的某个 核心 Markdown 文件，通过其 `bookmarkData` 读取最新内容。**V1 仅实现简单的内容读取和塞入上下文，不涉及 RAG。**
*   **核心文件交互功能:** 实现 `FileSession`, `FileCheckpoint` 定义的核心逻辑（围绕 核心 Markdown 文件 进行编辑、Chat/Edit 模式、DiffBlock 处理、Checkpointing、回溯、手动编辑集成、`.md` 导入/导出、文件存储在应用专属 `Sessions/` 目录并使用 Security-Scoped Bookmark 访问）。
*   **基础知识库支持:** 允许用户在 LavaChat 专属文件夹内创建子文件夹（如 `KnowledgeBase/`），并通过 `@提及` 功能选取其中的文件供 AI 读取（一次性内容读取）。也允许 `@提及` `FileSession` 对应的 核心 Markdown 文件。

**4.6. 跨模块与架构**
*   **Clean Architecture:** 清晰划分 Domain, Data, Presentation 层。
*   **平台无关 Domain:** Entities 和 Use Cases 设计不依赖特定平台 API。
*   **平台相关 Data/Presentation:** Core Data, CloudKit, SwiftUI 的实现细节封装在对应层。
*   **CloudKit 同步:** `ChatSession`, `Message`, `ChatSessionSetting`, `FileSession`元数据, `FileCheckpoint`, `LLMInstance`, `User` 等核心用户数据应设计为支持 CloudKit 同步。
*   **异步处理:** Token 记录更新、CloudKit 同步等应使用后台任务或队列处理。

---

**5. 范围内的项目与范围外的项目 (V1)**

**5.1. 范围内 (In Scope for V1)**

*   **平台:** iOS 应用 (SwiftUI)。
*   **核心对话功能:** 实现 `ChatSession`, `Message`, `ChatSessionSetting` 定义的所有核心交互（树状结构、编辑、刷新、融合、多AI、@mention (包括实例/组 和 文件/文件夹，后者含文件选择器、内容嵌入和 Token 警告)、设置、反馈、Token 记录、`.lavachat` 导入导出、通过 iCloud 链接/QR 码分享和导入会话）。
*   **核心文件交互功能:** 实现 `FileSession`, `FileCheckpoint` 定义的核心逻辑（围绕 核心 Markdown 文件 进行编辑、Chat/Edit 模式、DiffBlock 处理、Checkpointing、回溯、手动编辑集成、`.md` 导入/导出、文件存储在应用专属 `Sessions/` 目录并使用 Security-Scoped Bookmark 访问）。
*   **模型管理:** 实现 `LLMProvider`, `LLMModel`, `LLMInstance`, `LLMInstanceGroup` 的管理（预置+自定义、实例创建/编辑/删除/分组、API Key 安全存储、`.lavachatinstance` 导入导出）。
*   **用户与订阅:** 实现 `User`, `SubscriptionPlan`, `SubscriptionStatus` 管理，集成 Apple IAP，通过 Cloudflare Workers 实现订阅验证和内置 API 代理。
*   **文件管理入口:** 实现 "门户 (Hub)" 标签页，包含跳转到系统"文件"应用中 LavaChat 专属文件夹的快捷按钮。
*   **基础知识库支持:** 允许用户在 LavaChat 专属文件夹内创建子文件夹（如 `KnowledgeBase/`），并通过 `@提及` 功能选取其中的文件供 AI 读取（一次性内容读取）。也允许 `@提及` `FileSession` 对应的 核心 Markdown 文件。
*   **数据存储:** 使用 Core Data 进行本地持久化。
*   **云同步与分享:** 使用 CloudKit Private Database 同步核心用户数据，使用 CloudKit Shared Database 实现会话分享功能。
*   **基础 UI:** 实现底部导航栏 (对话、模型、门户、我/设置) 及各核心功能的界面。
*   **状态恢复:** 实现冷启动时恢复到上次所在的对话/文件界面。

**5.2. 范围外 (Out of Scope for V1 / Future Considerations)**

*   **其他平台:** macOS, Android, Web 版本。
*   **"门户 (Hub)" Tab 的高级功能:** 除文件快捷入口外的所有社交、自动化、高级信息管理功能。
*   **高级 CloudKit Public DB / Cloudflare Storage 应用:** 用于"发现"等高级功能的数据存储和访问。
*   **SQLite 迁移:** 完全切换到 SQLite 或提供双存储选项。
*   **高级文件功能:** 多文件同时编辑、非 Markdown 文件的原生编辑支持、更复杂的格式转换、实时协作编辑、复杂的文件元数据管理和搜索。
*   **高级 RAG:** 针对 `@提及` 的文件/文件夹实现基于检索增强生成（RAG）的智能问答，而非简单的内容塞入。
*   **高级实例分享:** 超出文件导入/导出的更复杂的实例共享机制。
*   **高级 AI 辅助:** 用于社交内容审核、高级自动摘要等的复杂 AI 工作流 (超出基本的 System Utility LLM 应用)。
*   **完整后端系统:** 构建超出 Cloudflare Workers 代理功能的独立后端服务 (如果需要)。
*   **阿里云 ECS 的实际应用:** 超出仅用于 ICP 备案准备的目的。
*   **离线功能:** 在无网络连接下的完整功能支持（V1 可能仅支持查看缓存数据和使用 `user_provided` Key 进行本地操作）。
*   **详细的错误处理和用户引导:** V1 会有基础实现，但全面的覆盖和优化可能在后续版本。
*   **性能优化:** 针对大规模数据（大量会话、长消息历史、大量实例/组、大量文件引用）的深度性能调优。
