---
description: 
globs: 
alwaysApply: false
---
**熔言 / LavaChat - iOS MVP 实现计划文档 (中文)**

**版本:** 1.0
**日期:** 2024年5月22日
**目标:** 本文档为 LavaChat iOS 应用的最小可行产品 (MVP) 版本提供详细的、分步骤的实现计划。MVP 的核心目标是快速上线基础对话功能，仅支持用户自有的 API Key，并为后续 V1 完整功能的迭代（包括 CloudKit 同步、订阅、文件交互等）奠定坚实的架构基础。本文档旨在指导 AI 编程工具和开发人员精确执行，减少不确定性。

**MVP 核心范围:**

*   **功能:** 基础对话 (`ChatSession`, `Message`)，模型实例管理 (`LLMInstance` - 仅限 `user_provided` 类型)，API Key 安全存储 (Keychain)，基础应用设置。
*   **数据:** 仅本地存储 (Core Data)，无云同步。
*   **后端:** 无 Cloudflare Workers 交互，无订阅验证，无内置 API Key 代理。
*   **UI:** SwiftUI 实现，包含底部导航栏（对话、模型、*占位符*、设置）和核心界面。

---

**实现计划步骤 (分阶段)**

**阶段 0: 项目初始化与架构搭建 (预备工作)**

1.  **创建 Xcode 项目:**
    *   使用 Xcode 创建新的 iOS App 项目。
    *   选择 SwiftUI 作为用户界面。
    *   **启用 Core Data** 选项。
    *   设置最低部署目标为 iOS 16.0。
    *   配置 Bundle Identifier、显示名称 ("熔言" / "LavaChat")。
2.  **设置版本控制:** 初始化 Git 仓库并进行首次提交。
3.  **建立 Clean Architecture 目录结构:**
    *   创建 `LavaChatApp` (主应用入口)
    *   创建 `Domain` (包含 `Entities`, `UseCases`, `Interfaces/Repositories`)
    *   创建 `Data` (包含 `Repositories/Implementations`, `DataSources/Local`, `DataSources/Network`, `Utils/Keychain`)
    *   创建 `Presentation` (包含 `Views`, `ViewModels`, `Coordinators` 或 `Navigation`)
4.  **配置 App 主入口:**
    *   在 `LavaChatApp.swift` 中设置基础的 `WindowGroup`。
    *   创建 `MainTabView.swift` 作为根视图，包含一个 `TabView`。
5.  **实现底部导航栏:**
    *   在 `MainTabView` 中设置 4 个 Tab：对话、模型、门户 (Hub - *MVP 中仅占位符*)、我/设置。
    *   使用 SF Symbols 作为 Tab 图标。
    *   为每个 Tab 关联一个初始的占位符 SwiftUI View。
6.  **(可选) 集成 SwiftLint:** 添加 SwiftLint 依赖并配置规则，确保代码风格一致性。

**阶段 1: 核心领域实体与本地数据层 (Domain & Data Layers - 本地优先)**

7.  **实现 Domain Entities:** 在 `Domain/Entities` 创建所有核心 Swift 实体，遵循 `Identifiable`, `Codable`。重点实现 `Message` 树结构及 `ContentBlock`。
8.  **定义 Repository 接口:** 在 `Domain/Interfaces/Repositories` 定义各数据操作协议，如 `ChatRepository`, `LLMInstanceRepository`, `UserSettingsRepository`, `KeychainRepository`。
9.  **设计 Core Data 模型 (`.xcdatamodeld`):** 为 MVP 核心实体创建 Core Data Entities，精确映射属性，特别注意 `Data` 类型用于复杂结构（需 `Codable`）及 `CDMessage` 父子关系。
10. **实现 Core Data 栈与本地数据源:** 完善 `Persistence.swift` 以正确初始化 `NSPersistentCloudKitContainer`。手动生成 `NSManagedObject` 子类，重写 `awakeFromInsert` 初始化非可选 `UUID`/`Date`。提供上下文访问。
11. **实现 Core Data Repositories:** 在 `Data/Repositories/Implementations` 实现 Repository 接口，处理 Domain Entities 与 Managed Objects 转换及 `Data` 序列化。实现 `KeychainHelper` 用于安全存储 API Key。
12. **依赖注入 (DependencyInjection):** 建立 DI 系统。创建 `RepositoriesFactory`, `UseCasesFactory`, `ViewModelsFactory`。实现 `DIContainer` 作为协调器，并在 `lavachatApp` 中初始化注入。

**阶段 2: 模型管理模块 (MVP: User-Provided Instances)**

13. **实现内置 LLM Provider 和 Model 数据初始化:**
    *   创建 `BuiltinLLMInitializer.swift`，定义预置的 `LLMProvider` 和 `LLMModel` 领域对象列表及数据版本号。
    *   实现 `initializeIfNeeded` 方法，检查 `UserDefaults` 中的版本。
    *   若需更新，则从 Core Data 获取现有内置数据，比较并调用 Repository 进行增、改操作，确保幂等性。
    *   处理弃用模型，并更新 `UserDefaults` 中的版本号。在应用启动时调用此初始化器。
14. **实现模型列表 UI (模型 Tab):**
    *   创建 `ModelsView.swift`，包含搜索栏和列表。列表顶部为"收藏夹"和"模型组"占位入口。
    *   使用 `List` 和 `Section` 按 `LLMProvider` 分组展示 `LLMInstance`。
    *   Provider 分组头显示 Logo、名称，并导航到 `ProviderDetailView`。
    *   Instance 行显示 Logo、名称、模型名（若非包含），并导航到 `InstanceDetailView`。导航栏添加 "+" 按钮。
15. **实现 Provider 详情 UI (`ProviderDetailView`):**
    *   创建 `ProviderDetailView.swift`，接收 `providerId` 或创建标记。顶部显示 Logo 和名称。
    *   使用 `Form` 展示网址、API 配置等信息。API Key 行根据 `providerType` 和 `apiKeyStored` 状态条件显示，支持安全输入/查看。
    *   "Available Models" Section (用 `List`) 展示关联的 `LLMModel`，每行导航至 `ModelDetailView`，并提供 "+" 按钮创建新模型。
    *   实现 "Edit"/"Done" 逻辑，区分内置（仅 Key 可编辑）和自定义 Provider 的编辑权限。
16. **实现模型详情 UI (`ModelDetailView`):**
    *   创建 `ModelDetailView.swift`，接收 `modelId` 或 `providerId`。顶部展示 Logo、名称、描述。
    *   `Form` Sections 展示 Provider (可导航)、Identifier、Pricing、Modalities、Context Window 等核心信息。
    *   "Available Instances" Section (用 `List`) 展示关联的 `LLMInstance`，每行导航至 `InstanceDetailView`，并提供 "+" 按钮创建新实例。
    *   实现 "Edit"/"Done" 逻辑，仅允许编辑用户创建的模型 (`isUserCreated == true`) 的部分字段。
17. **实现模型实例详情/编辑 UI (`InstanceDetailView`):**
    *   创建 `InstanceDetailView.swift`，接收 `instanceId` 或 `modelId`。顶部 Header Section 展示 Logo 和名称。
    *   横向动作按钮 Section 包含 "Duplicate", "Favorite", "Share"。
    *   核心信息 Section (`Form`) 显示 Provider 和 Model (均可导航)，及 Token 使用情况 (仅查看)。
    *   "Parameters" Section (`List`) 管理 `defaultParameters`，支持增删改查，编辑参数标签和值（多行文本，含推荐标签）。
18. **实现模型管理 Use Cases 和错误类型:**
    *   在 `Domain/UseCases/ModelManagement/` 下，为 Provider, Model, Instance 创建 Use Case 文件。
    *   实现如 `GetProviderUseCase`, `CreateInstanceUseCase`, `SaveProviderAPIKeyUseCase` 等业务逻辑。
    *   在 `Domain/Errors/` 下定义 `ModelManagementError.swift` 和 `KeychainError.swift`，包含相关错误枚举。
    *   Use Cases 中抛出这些定义的错误。
19. **实现 Provider 详情 ViewModel (`ProviderDetailViewModel`):**
    *   创建 `ProviderDetailViewModel.swift` (在 `Presentation/ViewModels/ModelManagement/`)。
    *   负责 `ProviderDetailView` 数据与逻辑：加载 Provider/关联模型，处理编辑，管理 API Key (安全输入/获取/存/清)。
    *   调用 Use Cases (CRUD Provider, API Key)，控制编辑/加载状态和错误。
20. **实现模型详情 ViewModel (`ModelDetailViewModel`):**
    *   创建 `ModelDetailViewModel.swift` (在 `Presentation/ViewModels/ModelManagement/`)。
    *   负责 `ModelDetailView` 数据与逻辑：加载 Model/关联Provider/Instances，处理用户创建/编辑/删除 Model。
    *   调用 Use Cases (CRUD Model)，控制编辑/加载状态和错误。
21. **实现实例详情 ViewModel (`InstanceDetailViewModel`):**
    *   创建 `InstanceDetailViewModel.swift` (在 `Presentation/ViewModels/ModelManagement/`)。
    *   负责 `InstanceDetailView` 数据与逻辑：加载 Instance/关联Model/Provider，处理创建/编辑/复制/收藏/删除 Instance 及参数。
    *   调用 Use Cases (CRUD Instance)，控制编辑/加载状态和错误。
22. **实现模型列表 ViewModel (`ModelsViewModel`):**
    *   创建 `ModelsViewModel.swift` (在 `Presentation/ViewModels/ModelManagement/`)。
    *   负责 `ModelsView` 数据与逻辑：加载 Provider/Instances，分组展示，处理搜索/筛选，管理收藏夹/组。
    *   调用 Use Cases (获取 Providers/Instances/收藏夹)，协调导航，控制加载状态和错误。
23. **连接 Presentation -> Domain -> Data:**
    *   在 ViewModels (`ModelsViewModel`, `InstanceDetailViewModel`, `ProviderDetailViewModel`, `ModelDetailViewModel`) 中调用 Use Cases。
    *   Use Cases 调用 Repositories。
    *   确保数据流正确，UI 能反映 Core Data 和 Keychain 中的变化。

**阶段 3: 核心对话模块 (MVP: 本地基础交互)**

24. **实现内置对话数据初始化:**
    *   在 `Data/Utils/Initialization/` 创建 `BuiltinChatInitializer.swift`。
    *   定义内置数据版本号、用户默认键、系统默认 `ChatSessionSetting` 领域对象（含固定UUID、名称、时间戳）。
    *   定义示例 `ChatSession` 和 `Message` 领域对象列表（含固定UUID、用户ID、设置ID、通过`BuiltinLLMInitializer`获取的`llmInstanceId`）。
    *   实现 `initializeIfNeeded(chatRepository: ChatRepository, chatRepository: ChatRepository, userDefaults: UserDefaults = .standard)` 方法，包含版本检查、批量更新、数据比较与创建/更新（确保幂等性）、版本号更新及错误处理。
    *   在应用启动时（如 `lavachatApp.swift` 的 `init`）调用此初始化器。
25. **实现对话列表 UI (`ChatsView.swift`):**
    *   在 `Presentation/Views/Chats/` 创建 `ChatsView.swift`，使用 `NavigationStack` 和 `List`。
    *   集成搜索功能，导航栏标题为 "Chats"，右侧添加 "+" 按钮用于创建新对话。
    *   创建 `ChatSessionRowView.swift` (或私有 struct) 显示会话标题、最后消息摘要和时间，并包裹在 `NavigationLink` 中。
    *   支持下拉刷新和左滑删除，通过 `@StateObject` 持有 `ChatsViewModel`，处理加载、错误和空状态显示。
    *   在预览中创建 `DIContainer`，使用 `MockChatPreviewData` 填充 ViewModel。
26. **实现对话界面 UI (`ChatView.swift`, `MessageRowView.swift`):**
    *   在 `Presentation/Views/Chats/` 创建 `ChatView.swift` 和 `MessageRowView.swift`。
    *   `ChatView` 接收 `chatSessionId`，导航栏显示会话标题和 "New Chat" 按钮，主体使用 `ScrollViewReader` 和 `LazyVStack` 展示消息。
    *   底部输入区域包含 "+" 按钮 (占位符)、`TextEditor` 和发送按钮。
    *   `MessageRowView` 根据 `message.role` 采用不同布局显示消息内容。MVP 范围外：头像、消息状态、操作按钮。
    *   `ChatView` 通过 `@StateObject` 持有 `ChatViewModel`，在预览中使用 `MockChatPreviewData` 或 Mock UseCases。
27. **定义并实现对话模块 Use Cases:**
    *   在 `Domain/UseCases/Chats/` 创建 Use Case 文件和 `ChatUseCaseProtocols.swift`。
    *   定义协议如 `CreateChatSessionUseCaseProtocol`, `GetAllChatSessionsUseCaseProtocol`, `SendMessageUseCaseProtocol`, `ObserveChatSessionsUseCaseProtocol` 等。
    *   实现 Use Cases，如 `CreateChatSessionUseCase` (依赖 `ChatRepository`, `LLMInstanceRepository`)，`SendMessageUseCase` (依赖 `ChatRepository`, `LLMInstanceRepository`, `KeychainRepository`, `LLMAPIClientFactory`) 等。
    *   在 `Domain/Errors/` 创建 `ChatError.swift` 并定义相关错误枚举。创建 `Mocks/Data/Repositories/MockChatRepository.swift`。
28. **实现对话列表 ViewModel (`ChatsViewModel.swift`):**
    *   在 `Presentation/ViewModels/Chats/` 创建 `ChatsViewModel.swift`。
    *   定义 `@Published` 属性：`sessions`, `isLoading`, `errorMessage`, `searchText`。
    *   注入 `GetAllChatSessionsUseCaseProtocol`, `CreateChatSessionUseCaseProtocol`, `DeleteChatSessionUseCaseProtocol`, `ObserveChatSessionsUseCaseProtocol`, `LLMInstanceRepository`。
    *   实现 `loadSessions()`, `refreshSessions()`, `createNewSession()`, `deleteSession(at:)` 和 `subscribeToSessionChanges()` 方法。
    *   定义错误处理方法。
29. **实现对话界面 ViewModel (`ChatViewModel.swift`):**
    *   在 `Presentation/ViewModels/Chats/` 创建 `ChatViewModel.swift`，构造函数接收 `chatSessionId`。
    *   定义 `@Published` 属性：`chatSession`, `messages`, `newMessageText`, `isLoadingSession`, `isLoadingMessages`, `isSendingMessage`, `errorMessage`。
    *   注入 `GetChatSessionUseCaseProtocol`, `GetMessagesForSessionUseCaseProtocol`, `SendMessageUseCaseProtocol`, `ObserveMessagesUseCaseProtocol`。
    *   实现 `loadInitialData()`, `sendMessage()` 和 `subscribeToMessageChanges()` 方法。
    *   定义 `startNewSessionBasedOnCurrent()` 方法和错误处理方法。
30. **完善 LLM API Client (Data Layer):**
    *   回顾并确认 `Data/DataSources/Network/` 下的客户端（如 `OpenAIClient.swift`）能正确处理 API Key、消息历史、参数，使用 `URLSession` 和 `async/await`，解析响应及 Token，并抛出具体 API 错误。
    *   创建 `LLMAPIClientFactory.swift` (可选但推荐) 根据 `LLMProvider.apiStyle` 返回相应客户端实例。
    *   定义 `LLMAPIClientProtocol` 通用接口。
31. **连接对话模块的 Presentation -> Domain -> Data 层:**
    *   更新 `DIContainer` 中的 `RepositoriesFactory`, `UseCasesFactory`, `ViewModelsFactory` 以包含对话模块相关方法。
    *   在 `Data/Repositories/Implementations/` 创建 `CoreDataChatRepository.swift` 实现 `ChatRepository` 协议，处理 `CDChatSession`/`CDMessage` 与领域实体转换 (使用 Mappers)。
    *   确保 `Message.content` ([ContentBlock]) 正确序列化/反序列化到 `CDMessage.content` (Data)。
    *   使用 `NSFetchedResultsControllerDelegate` 或 Combine-CoreData 包装器实现 `observeSessions()` 和 `observeMessages(forSessionId:)`。
    *   在 `MainTabView` 和 `ChatsView` 中正确注入和使用 ViewModel，确保导航和 `@EnvironmentObject var container: DIContainer` 注入。
32. **实现基础消息功能 (MVP 数据层面):**
    *   在 `SendMessageUseCase` 中，从 `LLMAPIClient` 获取回复和 token 计数后，填充 `assistant` 消息的 `promptTokens` 和 `completionTokens` 属性。
    *   在 `ChatRepository` (CoreData 实现) 的 `createMessage` 方法中确保 `CDMessage` 的 token 属性被正确赋值。
    *   明确数据流：用户发送消息 -> ViewModel -> UseCase -> APIClient -> UseCase (创建含token的助手消息) -> Repository (保存含token的消息) -> ViewModel (UI更新)。
    *   MVP 范围外：UI上直接显示单条消息的token消耗。

**阶段 4: 设置与其他基础功能**

33. **实现设置界面 UI (我/设置 Tab):**
    *   创建 `SettingsView.swift`。
    *   添加一个简单的列表或表单。
    *   **MVP 核心功能:**
        *   提供一个选项让用户选择"默认对话设置"(`defaultChatSettingsId`)，从 Core Data 加载所有 `ChatSessionSetting` 供选择（需要先实现创建/管理 `ChatSessionSetting` 的基本功能）。
        *   提供一个选项让用户选择"系统杂活 LLM 实例"(`systemUtilityLLMInstanceId`)，从 Core Data 加载所有 `LLMInstance` 供选择（MVP 中可能暂时不实现此功能或允许选择任何 `user_provided` 实例）。
    *   **MVP 占位符:** 显示"订阅管理"、"账户管理"、"数据管理"等区域，但功能禁用或显示"即将推出"。
34. **实现 `ChatSessionSetting` 管理 (基础):**
    *   提供一个简单界面（可能从设置页导航进入）用于创建/查看/编辑 `ChatSessionSetting`。MVP 中只需支持设置名称 (`name`) 和一个基础的 `system_prompt` 覆盖。
35. **实现设置 ViewModel 和 Use Cases:**
    *   创建 `SettingsViewModel.swift`。
    *   创建 `FetchUserSettingsUseCase`, `UpdateUserSettingsUseCase`, `FetchChatSessionSettingsUseCase`, `SaveChatSessionSettingUseCase` 等。
    *   连接 ViewModel -> Use Cases -> Repositories (Core Data)。
36. **实现应用启动逻辑:**
    *   在 `LavaChatApp` 或根 ViewModel 中，实现首次启动检查。
    *   如果是首次启动，可以预置一个默认的 `ChatSessionSetting` (标记 `isSystemDefault=true`) 和必要的 `User` 记录。
    *   可以引导用户前往"模型"标签页添加第一个 API Key。
37. **实现状态恢复:**
    *   使用 `@SceneStorage` 或其他机制在 App 退出前保存当前活动 `ChatSession.id`。
    *   在 App 启动时检查此值，如果存在，则直接导航到对应的 `ChatView`。
38. **本地化准备:**
    *   为所有面向用户的 UI 文本创建 `.xcstrings` 文件，并添加英文和简体中文的本地化条目。
    *   在代码中使用本地化 Key (`Text("localization.key")`)。

**阶段 5: MVP 打包与测试**

39. **UI 整合与导航:** 确保所有 MVP 范围内的界面都已创建并可以通过底部 Tab 和视图内导航正确访问。
40. **前端样式应用:** 根据 `frontend_guidelines_document.md`，应用基础的颜色、字体（支持 Dynamic Type）、间距规则。
41. **错误处理 (基础):** 在 ViewModel 和 Use Case 层添加基本的错误处理逻辑（如 API 调用失败、数据加载失败），并在 UI 上向用户显示友好的提示信息。
42. **可访问性 (基础):** 为主要 UI 元素添加 `.accessibilityLabel`。
43. **单元测试:** 为 Domain Layer (Entities, Use Cases) 和 Data Layer (Repository 逻辑, Mappers) 编写单元测试。
44. **集成测试:** 测试 Presentation -> Domain -> Data 的完整流程（如发送消息、保存实例）。可以 Mock 数据层进行测试。
45. **手动测试:**
    *   测试核心流程：添加模型实例和 API Key -> 创建会话 -> 发送消息 -> 接收回复 -> 查看历史 -> 删除会话 -> 删除实例。
    *   测试设置功能。
    *   测试状态恢复。
    *   测试不同设备尺寸和字体大小下的 UI 布局。
    *   测试中英文切换。
46. **构建与打包:** 配置 App 图标、启动屏幕，生成 Ad Hoc 或 TestFlight 构建版本。

---

**V1 完整功能后续阶段 (MVP 之后)**

**阶段 6: CloudKit 集成与基础同步**

47. 替换 Core Data 容器为 `NSPersistentCloudKitContainer`。
48. 配置 CloudKit Schema (Record Types 对应 Core Data Entities)。
49. 实现 CloudKit 用户认证状态检查和处理 (iCloud 登录/登出)。
50. 测试 Private Database 的自动同步功能（`ChatSession`, `Message`, `LLMInstance` 等）。
51. 添加 UI 反馈以显示同步状态。
52. 处理 `bookmarkData` 在跨设备同步时的解析和文件存在性问题。

**阶段 7: Cloudflare Workers 与订阅功能**

53. 开发 Cloudflare Workers 端点 (`/verifyReceipt`, `/llmProxy`)。
54. 实现 Apple IAP 收据验证逻辑。
55. 在 Worker Secrets 中安全存储开发者 API Key。
56. 实现 LLM 请求代理逻辑。Worker 需要根据请求中的 Provider ID 或 Model Group 等信息判断是否需要代理以及路由到哪个后端。
57. 在 iOS 端集成 StoreKit 2，实现订阅购买流程。
58. 实现 iOS 端与 Worker 的 API 通信（发送收据、发送 LLM 代理请求）。
59. 实现 `SubscriptionPlan` 和 `SubscriptionStatus` 的管理和展示。
60. 启用 `system_provided` 类型的 `LLMInstance`。
61. 实现基于订阅状态的 Token 使用量跟踪和限制。

**阶段 8: FileSession 文件交互功能**

62. 实现文件导入流程（包括非 MD 格式的转换）。
63. 实现 `FileSession` UI (Z-轴分层、悬浮对话、文件编辑区)。
64. 实现 Security-Scoped Bookmark 的创建、存储 (`FileSession.bookmarkData`) 和解析，确保对托管 Markdown 文件的持久访问。
65. 实现 `FileCheckpoint` 的创建（用户发送消息时）和存储。
66. 实现 `DiffBlock` 的解析、UI 展示和 Accept/Reject 交互逻辑。
67. 实现 Checkpoint 回溯功能（恢复文件内容和待处理状态）。
68. 实现手动编辑与 AI 编辑的整合处理。
69. 实现 "门户" Tab 跳转到文件 App 的功能。
70. 实现 `@提及文件/文件夹` 的文件选择器和内容读取/嵌入逻辑（含 Token 警告）。

**阶段 9: 分享、高级功能与打磨**

71. 实现基于 CloudKit Shared Database 的会话分享 (`CKShare`)。
72. 实现 QR 码生成与扫描（用于分享链接）。
73. 实现更高级的消息交互（编辑、刷新、融合的完整 UI 和逻辑）。
74. 完善错误处理、性能优化、UI 细节打磨、动画效果。
75. 实现 "发现" Tab 相关功能（可能涉及 CloudKit Public DB 或 Cloudflare 存储）。
76. 实现 RAG 或其他高级 AI 功能。
77. 开始规划和开发其他平台版本 (macOS 等)。

---

**总结:**

本计划详细列出了 LavaChat iOS MVP 的实现步骤，并规划了后续迭代方向。AI 编程工具和开发者应严格按照此计划执行，优先完成 MVP 范围内的功能，并确保代码质量和架构的健壮性，为未来的快速扩展打下良好基础。每个阶段完成后，都应进行充分的测试。