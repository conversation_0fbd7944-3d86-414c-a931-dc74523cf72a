---
description: 
globs: 
alwaysApply: true
---
# 熔言 / LavaChat - 项目结构指南 (V1.0)

本文档旨在规范 LavaChat iOS 项目的代码文件组织结构，以遵循 Clean Architecture 原则，确保代码的可维护性、可测试性和模块化。

## 根目录结构

```
lavachat-ios/
├── .cursor/rules/            # cursor rules 目录
├── doc/                      # 项目文档目录
│   └── implementation_plan/  # 实现计划相关文档
├── lavachat/                 # Xcode 项目主目录
├── README.md                 # 项目介绍与设置指南
└── .gitignore                # Git 忽略文件
```

## `lavachat/` (Xcode 项目目录)

包含 Xcode 项目文件 (`.xcodeproj`) 和主要的源代码及测试目标。

```
lavachat/
├── lavachat.xcodeproj/       # Xcode 项目文件
├── lavachat/                 # 主要源代码目录
├── lavachatTests/           # 单元测试目录
└── lavachatUITests/         # UI 测试目录
```

## `lavachat/lavachat/` (源代码根目录)

这是存放应用核心代码的地方。

```
lavachat/lavachat/
├── Application/              # 应用入口、生命周期管理、导航协调
│   ├── AppDelegate.swift     # (如果需要)
│   ├── SceneDelegate.swift   # (如果需要)
│   ├── lavachatApp.swift     # App 主结构体
│   ├── MainTabView.swift     # 根 Tab 视图
│   └── DependencyInjection/  # 依赖注入相关
│       ├── DIContainer.swift # 依赖注入容器
│       └── Factories/        # 工厂类
├── Domain/                   # 领域层 (平台无关)
│   ├── Entities/             # 核心业务实体
│   ├── Aggregates/           # 聚合根实体
│   ├── ValueObjects/         # 值对象
│   ├── UseCases/             # 应用特定业务逻辑 (SendMessageUseCase 等)
│   ├── Errors/               # 错误定义
│   └── Interfaces/           # 接口定义
│       └── Repositories/     # 仓库接口定义
├── Data/                     # 数据层 (平台相关)
│   ├── Repositories/         # 仓库接口的具体实现
│   │   └── Implementations/
│   ├── DataSources/          # 数据源
│   │   └── Local/            # 本地数据处理
│   │       ├── CoreData/     # Core Data 相关
│   │       └── Keychain/     # Keychain 相关
│   │   ├── Network/          # 网络请求 (LLM API Client, CF Worker Client)
│   │   └── Cloud/            # 云服务交互 (CloudKit API 封装)
│   ├── Models/               # 数据传输对象 (DTOs), Core Data 托管对象
│   └── Utils/                # 数据处理工具 (Mappers, Parsers, KeychainHelper)
├── Presentation/             # 表现层 (UI, 平台相关 - SwiftUI)
│   ├── Views/                # SwiftUI 视图 (按功能/屏幕组织)
│   │   ├── Chats/            # 聊天相关视图
│   │   ├── ModelManagement/  # 模型管理相关视图
│   │   ├── FileSession/
│   │   ├── Settings/
│   │   └── Shared/           # 可复用的 UI 组件
│   ├── ViewModels/           # 视图模型
│   ├── Modifiers/            # 自定义 ViewModifiers
│   ├── Extensions/           # SwiftUI 或其他 UI 相关扩展
│   └── Resources/            # (通常 Assets 在根，但字体等可放此)
├── Mocks/                    # 测试用的模拟对象
│   └── Data/                 # 数据层的模拟实现
├── Resources/                # 主要资源
│   ├── Assets.xcassets       # 图片、颜色资源
│   │   ├── LLMProviderLogos/
│   │   │   ├── provider_logo_openai.imageset/
│   │   │   ├── provider_logo_anthropic.imageset/
│   │   │   ├── provider_logo_openai_dark.imageset/
│   │   │   └── ...
│   │   ├── LLMModelLogos/
│   │   │   ├── model_logo_openai_gpt4.imageset/
│   │   │   ├── model_logo_anthropic_claude3opus.imageset/
│   │   │   └── ...
│   │   └── AppIcons/
│   │       └── ...
│   ├── Localizable.xcstrings # 本地化字符串
│   ├── Fonts/                # (如果使用自定义字体)
│   └── llmproviderModelListResult/ # LLM 提供商模型列表相关资源
├── Info.plist                # 应用配置信息
├── Persistence.swift         # Core Data 栈初始化与管理
├── lavachat.entitlements     # 应用权限配置
└── lavachat.xcdatamodeld/    # Core Data 模型文件
```

## `lavachat/lavachatTests/`

存放单元测试代码，主要测试 Domain 层和 Data 层逻辑。

## `lavachat/lavachatUITests/`

存放 UI 测试代码。

---

**维护约定:**

*   所有新功能和模块应遵循此结构创建文件和目录。
*   跨层引用必须遵循 Clean Architecture 的依赖规则（外层依赖内层）。
*   保持目录和文件命名清晰、一致。