---
description: 
globs: 
alwaysApply: false
---
**熔言 / LavaChat - iOS 前端设计系统指南 (V1.0)**

**1. 设计哲学与原则**

*   **清晰高效 (Clarity & Efficiency):** 界面应优先保证信息传达的清晰度和用户操作的效率。避免不必要的装饰，尤其是在信息密集的对话和文件交互区域。借鉴微信的简洁风格。
*   **平台一致性 (Platform Consistency):** 遵循 Apple 的人机界面指南 (HIG)，利用标准的 iOS 控件和交互模式，确保用户体验的熟悉度和自然感。设置等界面参考原生"设置"应用的布局和风格。
*   **品牌融入 (Subtle Branding):** 主品牌色 `#FD5E53` (日落橙) 应作为点缀和强调色谨慎使用，主要用于关键操作、活动状态或品牌标识，避免大面积铺陈导致视觉疲劳。借鉴 Reddit 对品牌色的克制运用。
*   **灵活适应 (Flexibility & Adaptability):** 设计需能适应不同类型的内容（文本、代码块、图片、数学公式、文件差异等）和不同语言（英文、中文）的显示需求。布局应具有弹性，支持动态类型 (Dynamic Type)。
*   **可访问性优先 (Accessibility First):** 确保所有用户都能顺畅使用，遵循 WCAG 标准，支持 VoiceOver，保证足够的色彩对比度和触摸目标尺寸。

**2. 字体 (Typography)**

*   **字体家族:**
    *   **英文及其他拉丁语系:** `SF Pro` (系统默认字体)
    *   **中文:** `PingFang SC` (苹方-简，系统默认中文字体)
    *   **代码块:** `SF Mono` (系统默认等宽字体)
    *   **理由:** 使用系统默认字体能确保与 iOS 平台视觉风格统一，获得最佳的渲染效果和多语言支持，并自动适配系统级的字体调整。
*   **动态类型 (Dynamic Type):** **必须** 全面支持。使用 SwiftUI 的标准文本样式 (`.largeTitle`, `.title1`, `.body`, `.caption` 等)，而不是固定字号，以响应用户的系统字体大小设置。
*   **字体层级 (示例):**
    *   `Screen Titles`: `.largeTitle` (导航栏大标题)
    *   `Section Headers / Important Labels`: `.headline` 或 `.title3`
    *   `Primary Content / Body Text / Message Content`: `.body`
    *   `Secondary Labels / Captions / Timestamps`: `.subheadline` 或 `.caption`
    *   `Button Labels`: `.body` 或 `.headline` (根据按钮重要性)
    *   `List Row Text`: 主要 `.body`, 次要 `.subheadline`
*   **行高 (Leading):** 通常使用 SwiftUI 文本样式的默认行高。对于大段文本（如消息内容、文件内容），可根据需要微调（例如 `Text(...).lineSpacing(4)`) 以提升阅读舒适性。
*   **字重 (Weight):** 使用 `.regular` 作为标准字重。需要强调时使用 `.medium` 或 `.semibold`，避免过度使用 `.bold`。

**3. 调色板 (Color Palette)**

*   **核心原则:** 以中性色为主构建界面骨架，品牌色用于点睛，语义色用于状态反馈。所有颜色**必须**提供 Light Mode 和 Dark Mode 版本。颜色命名尽量遵循 SwiftUI / iOS 语义化命名习惯。

*   **品牌色 (Brand Color):**
    *   `brandPrimary`: `#FD5E53` (日落橙) - 用于非常关键的操作按钮（谨慎使用）、活动指示器（如下划线）、少量需要品牌强调的图标或元素。

*   **中性色 (Neutrals):** (提供 Light / Dark 两套值)
    *   `backgroundPrimary`: 主要背景色 (Light: White, Dark: Black 或接近黑色的深灰)
    *   `backgroundSecondary`: 次要背景色 (如卡片、分组列表背景) (Light: 接近白色的浅灰, Dark: 比 `backgroundPrimary` 稍亮的深灰)
    *   `backgroundTertiary`: 更深/更浅层次的背景 (如输入框背景)
    *   `labelPrimary`: 主要文本颜色 (Light: Black, Dark: White)
    *   `labelSecondary`: 次要文本颜色 (如副标题、描述性文字) (Light: Gray, Dark: Lighter Gray)
    *   `labelTertiary`: 更次要的文本颜色 (如占位符)
    *   `labelQuaternary`: 最次要的文本颜色
    *   `separator`: 分隔线颜色 (Light: Light Gray with Alpha, Dark: Dark Gray with Alpha)
    *   `opaqueSeparator`: 不透明分隔线颜色
    *   `fillPrimary / fillSecondary / fillTertiary / fillQuaternary`: 用于填充形状、控件背景等的灰色系。

*   **语义色 (Semantic Colors):** (提供 Light / Dark 两套值)
    *   `systemRed`: 用于错误提示、删除操作。
    *   `systemGreen`: 用于成功状态、完成操作。
    *   `systemBlue`: 用于信息提示、链接、常规操作按钮（替代品牌色）。
    *   `systemOrange`: 用于警告状态。
    *   `systemYellow`: 用于不太严重的警告或需要注意的标记。
    *   `systemPurple / systemTeal / etc.`: 其他根据需要定义的语义色。

*   **强调色 (Accent Color):**
    *   `accentColor`: 默认使用 `systemBlue`。这是 SwiftUI 控件（如 `Button`, `Toggle`）默认使用的颜色，避免滥用 `brandPrimary`。可在特定视图层级按需覆盖。

*   **特定元素颜色:**
    *   `chatBubbleUserBackground`: 用户聊天气泡背景色 (如果使用气泡)。
    *   `chatBubbleAssistantBackground`: AI 聊天气泡背景色 (如果使用气泡)。
    *   `diffAdditionBackground`: 文件 Diff 增加部分的背景高亮色。
    *   `diffDeletionBackground`: 文件 Diff 删除部分的背景高亮色。
    *   `codeBlockBackground`: 代码块背景色。

*   **不透明度 (Opacity):** 善用不透明度（Alpha 通道）来创建层次感和禁用状态，而不是创建全新的灰色。例如，禁用按钮的背景/文字颜色可以是其正常状态颜色的 30-50% 不透明度。

**4. 间距与布局规则 (Spacing & Layout)**

*   **基准单位:** `8 pt`。所有间距、边距、填充应尽可能使用 `4pt` 的倍数（4, 8, 12, 16, 20, 24, 32...）。
*   **屏幕边距 (Screen Padding):**
    *   主要内容区域距离屏幕左右边缘的标准间距为 `16pt` 或 `20pt`。
    *   顶部和底部间距需考虑安全区域 (Safe Area Insets)。
*   **元素间距 (Spacing):**
    *   `VStack` 和 `HStack` 的默认 `spacing` 建议为 `8pt` 或 `12pt`。
    *   文本块之间的垂直间距：`8pt` 或 `12pt`。
    *   标签与其对应控件的间距：`8pt`。
    *   列表项之间的间距：由 `List` 样式决定，通常是 `8pt` 或 `10pt`。
*   **填充 (Padding):**
    *   容器内部内容填充：`16pt` 或 `20pt`。
    *   按钮内部文字与边框的填充：水平 `16pt`，垂直 `12pt` (示例)。
    *   卡片内部填充：`16pt`。
*   **布局容器:** 优先使用 SwiftUI 的布局容器 (`VStack`, `HStack`, `ZStack`, `LazyVStack`, `Grid`, `Form`, `List`)。
*   **对齐:** 文本和控件优先使用 `leading` 和 `trailing` 对齐，以适应不同语言方向。
*   **列表行:**
    *   标准行高：`44pt` (或更高以适应内容和 Dynamic Type)。
    *   遵循 Apple Settings 的风格：图标、标题、可选的副标题/值、指示器（如箭头）。
*   **响应式设计:** 布局应能优雅地适应不同的屏幕尺寸和 Dynamic Type 设置。使用 `GeometryReader` 时需谨慎，避免过度复杂的布局计算。

**5. 图标集 (Iconography)**

*   **通用图标来源:** 应用界面的通用图标和交互元素**必须**使用 Apple 的 `SF Symbols`。
    *   **理由:** 与系统字体完美融合，支持字重、缩放比例、渲染模式（单色、层级、调色板、多色），自动适配 Dynamic Type，并提供丰富的图标库。
*   **AI 相关 Logo 规范:**
    *   **LLM 提供商 Logo:**
        *   **例外规定:** LLM 提供商的 Logo（如 OpenAI、Anthropic、Google 等）使用各提供商的官方图片素材。
        *   **存储方式:** 这些 Logo 图片应以 PNG 或 PDF 格式存储在应用资源中，并提供相应的 Dark Mode 版本（如果官方有提供）。
        *   **命名约定:** 统一使用 `provider_logo_[provider_name]` 格式命名，并根据需要添加后缀如 `_dark`。
        *   **尺寸规范:** 所有 Logo 图片应准备至少两种尺寸：
            *   `标准尺寸 (Standard)`: 在 Provider 详情页、Instance 选择界面等处使用，建议宽度 32pt-48pt（根据设计需求）。
            *   `小尺寸 (Small)`: 在列表项、消息气泡上的小型标识等处使用，建议宽度 20pt-24pt。
    *   **LLM 模型 Logo:**
        *   **官方模型:** 对于知名模型，应使用官方提供的标识图片。当官方没有提供专门的模型标识时，可使用提供商的 Logo 并添加模型特定的视觉标记（如数字、颜色变体等）。
        *   **命名约定:** 统一使用 `model_logo_[provider_name]_[model_identifier]` 格式命名，并根据需要添加后缀如 `_dark`。
        *   **尺寸规范:** 与提供商 Logo 保持一致的尺寸规范（标准尺寸和小尺寸）。
    *   **LLM 实例 Logo:**
        *   **默认行为:** 默认情况下，LLM 实例应继承其关联模型的 Logo。
        *   **自定义 Logo:** 用户应能上传自定义图片作为实例的 Logo。上传的图片应经过处理以符合应用的尺寸规范：
            *   最大尺寸限制: 1MB
            *   推荐格式: PNG、JPEG 或 SVG
            *   自动裁剪/缩放: 确保上传的图片在 UI 中显示时正确适配标准和小尺寸显示要求
    *   **LLM 实例组 Logo:**
        *   **默认行为:** 默认情况下，实例组应显示由组内实例 Logo 组合而成的拼贴图（例如前 2-4 个实例的 Logo 按网格排列）。
        *   **自定义 Logo:** 用户应能上传自定义图片作为组的 Logo，规格要求与实例 Logo 相同。
    *   **视觉一致性:** 虽然使用不同来源的 Logo，但应确保在视觉表现上保持一定的一致性：
        *   背景应为透明或应用背景色匹配的颜色。
        *   可考虑统一添加细微边框或轻微阴影以增强视觉边界，尤其是当 Logo 与背景色接近时。
        *   自定义 Logo 应保持一致的圆角风格，推荐使用圆形或圆角矩形（corner radius 8pt-12pt）。
*   **使用规范:**
    *   **一致性:** 在整个应用中保持图标风格（如字重 `.regular` 或 `.medium`）和比例 (`.imageScale(.medium)`) 的一致性。
    *   **颜色:** 图标颜色应遵循调色板规范，使用 `labelSecondary`, `accentColor`, 或特定语义色。避免使用与背景对比度不足的颜色。
    *   **尺寸:** 优先使用文本尺寸 (`.imageScale`)。如需固定尺寸，使用标准触摸目标尺寸（最小 `44x44pt` 区域，图标本身可略小，如 `24x24pt` 或 `28x28pt`）。
    *   **语义:** 选择最能准确表达操作或含义的 SF Symbol。
    *   **渲染模式:** 合理使用 `.hierarchical` 或 `.palette` 模式为图标增加表现力，但需保持克制。

**6. 首选 UI 库或框架**

*   **核心框架:** **SwiftUI**。
*   **第三方 UI 库:** **V1 阶段应避免引入大型第三方 UI 库。** 优先利用 SwiftUI 的原生控件和布局能力。如果确实需要特定功能（如图表、高级编辑器），应谨慎评估并选择轻量级、维护良好的库。

**7. 语言与本地化**

*   **布局:** 使用 `leading`/`trailing` 约束，`HStack`/`VStack`，避免写死宽度，确保布局能适应中英文本长度差异。
*   **文本换行:** 确保文本控件能正确处理多行显示。
*   **本地化机制:** 使用 Xcode 的 String Catalogs (`.xcstrings`) 进行字符串本地化。所有面向用户的文本**必须**通过本地化键来引用 (`Text("your.localization.key")`)。

**8. 组件示例与风格指导**

*   **按钮 (Buttons):**
    *   **Primary:** （谨慎使用）背景可考虑 `brandPrimary`，文字 `labelPrimary` (需确保对比度)，或使用 `systemBlue` 作为背景。圆角 `8pt-12pt`。
    *   **Secondary:** 背景 `fillSecondary` 或透明，边框 `separator` 或 `accentColor`，文字 `accentColor`。
    *   **Tertiary/Text:** 无背景和边框，文字 `accentColor`。
    *   **Disabled:** 背景/文字使用 50% 透明度。
*   **列表 (Lists) & 表单 (Forms):**
    *   遵循 `InsetGroupedListStyle` 或 `PlainListStyle`（根据上下文）。
    *   行样式参考 Apple Settings：左侧可选图标 (`labelSecondary` 或语义色)，中间标题 (`labelPrimary`) 和可选副标题 (`labelSecondary`)，右侧可选值 (`labelSecondary`) 或箭头 (`separator`)。
    *   **模型管理列表:**
       *   Provider Section Header: 应清晰展示 Provider Logo 和名称。整个 Header 区域或右侧的特定图标应指示其可点击，导航至 `ProviderDetailView`。
       *   Instance Row: 在实例名称 (`labelPrimary`, `.body`) 下方，添加一行小字 (`labelSecondary`, `.caption`)，说明其基于的模型，例如 "*模型: GPT-4o*"。
*   **输入框 (Text Fields / TextEditor):**
    *   使用标准的圆角矩形背景 (`backgroundTertiary` 或 `fillSecondary`)。
    *   清晰的占位符文本 (`labelTertiary`)。
    *   提供清晰的焦点状态指示（如边框变色为 `accentColor`）。
*   **对话界面:**
    *   **消息展示:** 考虑简洁的无气泡或极简气泡设计。用户消息靠右，AI 消息靠左。使用 `labelPrimary` 显示文本，`labelSecondary` 显示时间戳或元信息。
    *   **操作按钮:** 使用小尺寸图标按钮（SF Symbols），颜色 `labelSecondary` 或 `accentColor`，确保触摸区域足够 (`44x44pt`)。
    *   **多 AI 回复:** 水平滑动切换时提供清晰的指示器 (e.g., "1/3") 和视觉反馈。
*   **文件交互界面:**
    *   **悬浮对话:** 使用 `Material.ultraThinMaterial` 或类似毛玻璃效果，确保下方文件内容可辨识。
    *   **Diff Block:** 清晰标记增加（绿色背景/标记）和删除（红色背景/标记），并提供明确的 Accept/Reject 按钮。

**9. 可访问性 (Accessibility)**

*   **强制要求:**
    *   全面支持 Dynamic Type。
    *   为所有图像、图标和交互元素提供准确的 VoiceOver 标签 (`.accessibilityLabel()`) 和提示 (`.accessibilityHint()`)。
    *   确保色彩对比度符合 WCAG AA 标准。
    *   确保所有触摸目标尺寸不小于 `44x44pt`。
    *   合理使用 `.accessibilityTrait` 标记元素类型（按钮、标题、链接等）。
