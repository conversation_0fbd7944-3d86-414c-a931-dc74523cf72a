---
description: 
globs: 
alwaysApply: false
---
**熔言 / LavaChat - iOS 应用流程文档 (中文)**

**初次启动与引导**

当用户第一次打开熔言应用时，应用会首先展示一个简洁的欢迎界面，介绍其核心价值：成为用户的AI中枢，集成多种模型，并提供创新的文件交互以及便捷的文件管理入口。随后，应用会请求必要的权限，例如网络访问权限。

接下来，用户会看到一个登录/注册界面。用户可以选择使用 Apple ID 通过 CloudKit 登录。界面上会明确提示，如果不登录，用户将无法使用需要云同步的功能，如跨设备同步会话、付费订阅内置模型以及未来的分享功能，并且所有数据仅保留在本地设备上。选择不登录的用户将被引导直接使用，但只能添加并使用自己提供 API Key 的模型实例。

对于选择登录或已登录的用户，在首次进入主界面时，应用会在"对话"标签页预置几个示例会话。这些示例会包括一个常规的聊天会话（ChatSession），展示基本的对话、多AI回复和刷新等功能；还会包含一个文件交互会话（FileSession），演示如何在应用内与文档进行对话和编辑。这些示例旨在帮助用户快速理解和上手应用的核心功能。

**主界面：底部导航栏**

应用的主界面底部是一个标准的 iOS 标签栏（Tab Bar），包含四个主要区域，方便用户在核心功能间切换：

1.  **对话:** 这是用户最常用的区域，展示所有历史对话会话和文件交互会话的列表。用户启动应用后默认会进入此页面。
2.  **模型:** 这里是用户管理所有 AI 模型实例和提供商的地方，类似一个 AI 能力的通讯录。用户可以在此添加、配置、分组和分享他们的 AI 模型设置。
3.  **门户 (Hub):** 这是应用的文件管理和未来扩展功能的入口。在 V1 版本中，它的核心功能是提供一个快捷方式，方便用户访问和管理与 LavaChat 相关的文件。
4.  **我/设置:** 用户管理个人账户、订阅、应用全局设置的地方。

**"对话"标签页：会话列表**

进入应用后默认显示的是"对话"标签页。这个页面像微信的聊天列表一样，展示了用户所有进行中的和历史的"对话会话"（ChatSession）以及"文件交互会话"（FileSession）。每个会话条目会显示会话的标题（可能是用户自定义的，或根据内容自动生成的），最后一条消息的简短预览，以及最后活动的时间戳。会话按最后活动时间倒序排列，最新的在最上面。

在此页面的顶部导航栏右侧，有一个加号（+）按钮。点击这个按钮，会从屏幕下方弹出一个类似 WhatsApp 或 Telegram 的菜单页面。这个弹出页面的最上方是一个搜索框，方便用户快速查找特定的模型或功能。搜索框下方是一些快捷操作按钮，例如"新建对话"、"导入对话"、"编辑文档"（新建或打开文件会话）、"扫描或选取二维码"（统一入口：通过相机扫描或从相册选择包含 iCloud 链接的二维码图片来读取并导入别人分享的对话、模型实例或模型组）、"查看模型组"、"查看收藏夹"等。再往下，则会列出用户在"模型"标签页中配置的所有模型提供商和具体的模型实例，用户可以直接从这里选择一个模型快速发起新的对话。

用户可以向左滑动某个会话条目，出现"删除"按钮来删除该会话。点击列表中的任意一个会话条目，应用将导航到对应的具体"对话界面"或"文件交互界面"。

**对话界面 (ChatSession)**

当用户进入一个具体的对话界面时，布局设计旨在高效地展示信息并方便交互。

最上方的导航栏，左侧是一个标准的"返回"箭头图标，点击后回到"对话"列表页。中间显示当前会话的标题，标题旁边有一个向右的小箭头图标，明确指示这里是可以点击的。点击标题区域会跳转到"会话设置"界面，用户可以在那里修改标题、调整该会话的模型参数、自定义界面主题、管理消息操作按钮，以及导出当前会话为 `.lavachat` 文件或iCloud链接或包含iCloud链接的二维码。导航栏的最右侧是一个包含加号的聊天气泡图标，点击它会创建一个新的对话会话，这个新会话会沿用当前会话的所有设置。

界面的主体是消息展示区域。这里会按照时间顺序，自上而下地展示用户和 AI 之间的消息。用户的消息通常靠右显示，AI 的消息靠左显示。消息的展示可能采用简洁的无气泡设计，或者是非常轻量的气泡效果，以保证界面的干净和信息密度。整个消息区域支持用户上下滑动来浏览完整的对话历史。

当一个用户消息触发了多个 AI 模型实例同时回复时，这些并列的 AI 回复需要特殊处理。它们会出现在对应的用户消息下方。一种可能的 UI 设计是：这些并列的回复在垂直方向上堆叠，但提供清晰的标识（例如，"助手回复 1/3"，"助手回复 2/3"），并且用户可以通过在该区域进行水平左右滑动来切换查看不同的 AI 回复。这种设计平衡了垂直滚动查看历史和水平滑动比较并行回复的需求。

对于每个 AI 回复（无论是单个回复还是并列回复中的一个），其下方会显示一排小的操作图标按钮，从左到右可能包括：复制、点赞 (Like)、点踩 (Dislike)、刷新（重新生成）、转到文件会话（将此回复内容作为基础启动一个新的文件交互会话）、融合 (Merge) 等。此外，如果用户在会话设置中添加了自定义 AI 操作按钮（比如"总结"、"翻译"），这些按钮也会出现在这里。用户可以在会话设置中选择显示或隐藏这些默认和自定义的按钮。

交互方面，借鉴 Tinder 的左右滑动机制被应用在这些并列的 AI 回复上（当用户通过水平滑动查看某个具体的 AI 回复时）：
*   **向右滑动 (Like):** 表示用户喜欢这个回复。被右滑的回复对应的"点赞"图标按钮会被点亮。如果存在多个并列回复，用户可以对多个进行右滑点赞。当至少有两个回复被点赞后，"融合 (Merge)"图标按钮会高亮，表示可以点击执行融合操作。即使滑动到下一个回复，之前被右滑点赞的回复仍然保持被选中状态。
*   **向左滑动 (Dislike):** 表示用户不喜欢这个回复。这个回复在当前的浏览循环中会被暂时隐藏（但数据仍然保留，可以通过其他方式找回，例如版本历史或设置）。应用会自动显示下一个可用的 AI 回复。
*   **动态排序:** 多个并列 AI 回复的初始展示顺序，以及左右滑动切换的顺序，会根据一系列因素动态调整，例如：该模型在当前会话中近期被点赞的次数、该回复是否被用户收藏过、AI 输出前几个字符的速度快慢等。目标是优先展示用户可能更偏好的回复。
*   **直接回复:** 如果用户在查看某个特定的 AI 回复时，直接在下方的输入框输入并发送了新消息，那么这个行为被视为对该 AI 回复的"点赞"和"选中"。后续构建发送给 AI 的上下文（Chat History）时，在这个层级的 AI 回复中，将只包含被用户选中的这一个。

如果用户长按某条 AI 消息，也会弹出一个包含上述操作按钮及其文字标签的菜单，方便用户理解和操作。

最底部的对话输入栏设计简洁，类似微信风格。它通常只有一行高度。左边是一个加号（+）按钮，点击后会收起键盘（如果已弹出），并从下方展示一个功能面板，提供选项如：选择当前使用的模型或模型组、开启或关闭网络搜索功能、发送相册中的照片、拍照发送、选择文件发送、撤销上一步操作（例如撤销一次刷新或编辑）、以及用户可能添加的自定义快速指令按钮等。中间是文本输入框，用户点击这里会弹出键盘进行文字输入。输入框支持 `@提及` 功能，当用户输入"@"符号时，应用会立刻弹出一个小的选择提示，让用户选择是想"提及文件/文件夹"还是想"提及实例/组"（如果用户继续输入文字，则默认为提及实例/组）。如果用户选择"提及文件/文件夹"，应用会立即调用系统的文件选择器 (`UIDocumentPickerViewController`)，允许用户从本地设备或 iCloud Drive 中选择一个或多个文件或文件夹。

*   如果选择的是位于 `LavaChatFiles/Sessions/` 目录下的某个 **FileSession 核心 Markdown 文件**，应用会尝试使用其对应的 `bookmarkData` 来安全访问并读取最新内容。
*   如果选择的是其他位置的文件（例如 `KnowledgeBase/` 目录下的文件），应用会直接尝试读取其内容。
*   选择完成后，应用会尝试读取所选项目的内容，并进行一个简单的 Token 数量估算。如果估算结果显示内容可能非常大（超过某个阈值），应用会弹出一个警告提示用户："文件内容较多，可能消耗大量 Token 或超出模型上下文限制，是否确认发送？"，用户确认后，文件的内容（或其代表性摘要，根据实现）会被嵌入到即将发送的消息中。

输入栏的右侧通常是一个麦克风图标，代表语音输入按钮，用户长按此按钮可以进行语音识别，并将转换后的文字输入到文本框中。

**文件交互界面 (FileSession)**

文件交互界面的设计理念是在不离开文档的情况下进行流畅的对话和编辑。这里的"文档"指的是 **FileSession 管理的核心 Markdown 文件**。

顶部导航栏与对话界面类似：左侧返回按钮，中间是文件标题（通常源自原始文件名，点击进入文件会话设置），右侧是一个三个点的图标。点击这三个点会弹出一个菜单，提供诸如"新建会话" （打开一个现在文件的新的FileSession，会沿用当前会话的所有设置）、"撤销"（回溯到上一个 Checkpoint，恢复核心 Markdown 文件的状态）、"分享"（导出 Markdown 文件或尝试转回原格式）、"在文件中搜索"（V1 可能暂不提供此复杂功能）等操作。**特别说明文件的存储位置：** 与文件会话关联的 核心 Markdown 文件 实际存储在用户设备上（或 iCloud Drive 中）的一个由 LavaChat 应用管理的专属文件夹内（例如位于系统"文件"应用路径下的 `LavaChatFiles/Sessions/` 子目录中）。应用通过在 `FileSession` 记录中保存一个安全范围书签数据 (`bookmarkData`) 来确保持久地访问这个 核心 Markdown 文件，即使文件在该专属文件夹内被用户移动或重命名，应用也能通过解析书签重新找到它。

界面的核心是 Z 轴分层设计。底层 (Z=0) 是 核心 Markdown 文件 内容的展示和编辑区域。上层 (Z=1) 是一个悬浮的、半透明的对话记录视图，覆盖在文件内容之上，通常采用毛玻璃效果，以便用户能隐约看到下方的文件。这个对话视图的高度默认限制在屏幕的一半左右，用户可以上下滚动查看对话历史。

用户可以通过特定的手势（例如，在对话区域长按并上滑）将对话视图切换到全屏模式，专注于聊天；也可以通过在文件区域进行类似操作（例如长按并下滑）切换到文件全屏模式，专注于编辑。

对话记录的显示方式与普通对话界面类似，但增加了对文件特定内容块的处理：
*   `thinking` 块默认可能只显示一个加载动画或"思考中..."的提示，点击后才展开显示详细的思考过程。
*   `diffBlock`（AI 提出的修改建议）会在对话记录中以特殊形式展示，可能并排显示原文和建议修改后的内容，或者用颜色高亮标记出差异。每个 `DiffBlock` 旁边必须有关联的"接受 (Accept)"和"拒绝 (Reject)"按钮。用户点击这些按钮会直接更新内部的编辑状态 (`PendingEditInfo`)，并且文件视图层 (Z=0) 可能会实时预览接受或拒绝后的效果。

最下方的输入栏也与对话界面相似，但左侧的加号按钮图标会根据当前模式变化：
*   在 `chat` 模式下，它可能是一个带加号的聊天气泡图标。
*   在 `edit` 模式下，它可能是一个带加号的笔形图标。
点击这个按钮弹出的功能面板，除了对话界面已有的选项外，最重要的是会包含"切换到 Chat 模式"或"切换到 Edit 模式"的选项。其他选项（如模型选择、发送图片等）也会根据当前模式进行适当调整。

用户交互模式区分：
*   点击下方输入栏：进入 `chat` 交互模式，用户输入的是对 AI 的指令或关于文件内容的问题。
*   点击悬浮对话层上方的文件区域：进入手动文件编辑模式，弹出键盘，用户的修改直接作用于 Z=0 层的 核心 Markdown 文件 视图。这些手动编辑的内容会在用户下一次发送聊天消息时被捕获并记录到 Checkpoint 中。

**Checkpoint 与回溯逻辑:**

Checkpoint 是文件交互的核心机制。一个新的 `FileCheckpoint` 会在用户每次点击发送按钮发送消息时被创建。这个 Checkpoint 会完整记录那一刻 核心 Markdown 文件 的内容 (`fileContent`) 以及所有尚未被最终接受或拒绝的编辑区域的状态列表 (`pendingEditStates`)。这个状态列表精确描述了每个待处理区域的来源（AI 建议或手动编辑）、当前状态（待定、接受、拒绝、或被用户手动修改过）、以及它在 核心 Markdown 文件 中的精确位置。当用户在对话历史中找到一条与某个 Checkpoint 关联的消息（可能通过消息旁边的一个特殊按钮），点击"回到此版本"后，应用会加载对应的 `FileCheckpoint` 数据，恢复 核心 Markdown 文件 内容视图和所有待处理编辑区域的 UI 状态，让用户可以从那个时间点继续工作。

**"模型"标签页：管理 AI 实例**

"模型"标签页是用户管理所有 AI 能力的地方，设计上类似 iOS 的通讯录。

最上方是一个搜索栏，方便用户快速查找特定的模型提供商、模型或自己创建的实例。

搜索栏下方可能会有一些快捷入口，类似微信通讯录顶部的"群聊"、"标签"等，这里可以放置"收藏夹"（展示用户收藏的 `LLMInstance`）、"模型组"（展示用户创建的 `LLMInstanceGroup`）。

下方的主体区域是模型列表。**它主要以 `LLMProvider`（模型提供商）进行分组，直接展示用户创建的 `LLMInstance` 列表。** 应用内置的、通过订阅使用的模型会作为一个特殊的提供商（例如，"熔言精选模型"）排在最前面。接下来是用户添加的其他提供商（如 OpenAI, Anthropic 等）。

*   **分组标题 (Provider Header):** 每个 Provider 的分组标题会显示提供商的名称和 Logo。**这个标题区域是可交互的**（例如，整行可点击或右侧有详情图标），点击后会**导航到一个新的"提供商详情"页面 (`ProviderDetailView`)**。
*   **实例行 (Instance Row):** 每个 `LLMInstance` 作为列表中的一行展示。除了显示用户设置的实例名称外，**还会增加一行小字或标签清晰地指示该实例基于哪个 `LLMModel`**（例如，"My GPT-4o / *模型: GPT-4o*"）。对于 `user_api_key` 类型的提供商，其实例行旁边或提供商标题处会有一个清晰的 UI 标识（例如，锁图标或颜色标记）来区分其关联的 API Key 是否已添加。

列表右侧会有一个类似 iOS 通讯录的快速导航栏。但这里不是显示 A-Z 字母，而是显示各个模型提供商的 Logo 图标。只有用户配置过实例的提供商 Logo 才会出现在这个快速导航栏中，方便用户快速跳转到特定提供商的模型区域。

- **提供商详情页面 (`ProviderDetailView`)**

当用户点击主列表中的 Provider Header 时，会进入此页面，用于深入了解和管理该提供商相关的内容：

1.  **提供商信息:** 页面顶部展示该 `LLMProvider` 的详细信息，如名称、Logo、官方网站和 API 文档链接等。
2.  **API Key 管理 (若适用):** 如果该提供商是 `user_api_key` 类型，这里会提供界面让用户安全地输入、更新或清除关联的 API Key，并清晰地显示当前 Key 是否已存储 (`apiKeyStored` 状态)。
3.  **可用模型列表 (Available Models):** 这是展示 `LLMModel` 层的核心位置。会列出该 `LLMProvider` 旗下所有可用的 `LLMModel`（包括内置的和用户可能自定义添加的）。
    *   每一行代表一个模型，显示其名称、简要描述、上下文窗口大小、是否支持多模态等关键信息。
    *   **关键交互:** 点击列表中的某个 `LLMModel` 行，会**直接导航到模型详情编辑页面 (`ModelDetailView`)**，并且**自动预选了刚刚点击的那个 `LLMModel`**。这样，用户就可以方便地为这个特定的模型创建一个新的、自定义配置的 `LLMModel`。

通过这种设计，用户在模型 Tab 的主列表可以高效地看到和访问自己常用的实例，同时也有清晰的路径去发现提供商提供的不同模型、管理 API Key，并基于特定模型创建新实例。

用户仍然可以在模型 Tab 通过顶部的 "+" 按钮或特定菜单来直接创建自定义 Provider、自定义 Model 或 LLMInstance（可能需要先选择 Provider 和 Model）。编辑现有实例的操作则可以直接在主列表的实例行上触发（例如通过滑动或点击）。

- **模型导入/导出逻辑 (保持不变):** 用户可以通过特定操作（例如，在实例/组的详情页点击分享按钮）来分享选定的 `LLMInstance` 或 `LLMInstanceGroup`。分享操作会提供生成 iCloud 链接或显示 QR 码的选项。分享实例时，明确提示不会包含 API Key。分享组时，会分享组的结构和实例引用。

- **应用导入流程 (统一入口 - 适应性调整):** 当用户通过"扫描或选取二维码"功能成功解析出一个 LavaChat 分享链接...（后续导入逻辑基本不变，但在导入 LLMInstance 时，除了检查 Provider 的 Key 状态，还应确保基础 LLMModel 存在）...

**"门户 (Hub)" 标签页**

"门户"标签页在 V1 版本中扮演着一个简洁但重要的角色：作为用户访问和管理 LavaChat 相关文件的便捷入口。

进入这个标签页，用户会看到一个相对简单的界面。界面的主要元素可能是一个醒目的横向类似微信朋友圈打开条的按钮，例如标记为"打开 LavaChat 文件位置"或类似的文字。用户点击这个按钮后，LavaChat 应用会调用系统功能，直接跳转到 iOS 的"文件"应用程序，并且"文件"应用会自动打开并定位到 LavaChat 在用户设备（或 iCloud Drive）上创建和管理的专属文件夹（例如 `LavaChatFiles/`）。

这个专属文件夹是 LavaChat 存储和管理用户文件的地方。它内部会包含一些预定义的子文件夹，比如 `Sessions/` 文件夹，专门用于存放所有 `FileSession`（文件交互会话）所对应的 **核心 Markdown 文档**。用户也可以在这个专属文件夹内自由地创建自己的子文件夹，例如创建一个名为 `KnowledgeBase/` 的文件夹，用来存放他们希望 AI 能够参考的各种文档。用户可以在"文件"应用中像管理普通文件一样整理这个文件夹里的内容。当用户在 LavaChat 的对话界面使用 `@提及文件/文件夹` 功能时，文件选择器会默认从这个专属文件夹开始，方便用户快速选取存储在这里的 核心 Markdown 文件 或 `KnowledgeBase/` 中的其他文件作为上下文提供给 AI。

除了这个核心的文件快捷入口功能外，"门户"标签页在 V1 中可能暂时没有其他复杂功能。未来的版本可能会在这里扩展，加入诸如社区分享、发现新模型或 Prompts、自动化任务管理等更多与信息管理和应用生态相关的功能。

**"我/设置"标签页：用户与应用配置**

这个标签页是用户的个人中心和应用设置区域。

顶部可能会显示用户的头像和昵称（如果用户已登录并设置）。

主要功能包括：
*   **订阅管理:** 显示当前的订阅状态 (`SubscriptionStatus`)，例如是免费用户、Pro 订阅者、试用期等，以及订阅的到期时间。提供入口让用户浏览不同的订阅计划 (`SubscriptionPlan`) 并通过 Apple IAP 进行购买或管理续订。如果订阅计划包含 Token 额度，这里会显示本周期的 Token 使用情况。
*   **账户管理:** （如果已登录）提供退出登录选项。
*   **应用设置:** 这里包含一些影响整个应用行为的全局配置。
    *   用户可以选择一个默认的对话设置 (`ChatSessionSetting`)，以后每次新建对话时，将自动应用这个选定的设置。
    *   用户可以指定一个"系统杂活 LLM 实例"(`systemUtilityLLMInstanceId`)。应用会在执行一些内部的自动化 AI 任务时（比如自动为新会话生成标题、总结长文本等）调用这个选定的模型实例。
    *   可能会有外观相关的全局设置，例如全局字体大小调整、或者选择应用整体的日间/夜间模式（如果这个不由单个会话设置控制的话）。
    *   通知相关的设置选项。
*   **数据管理:** 提供"导出所有数据"、"清理缓存"等选项。
*   **关于与帮助:** 显示应用版本信息、隐私政策、用户协议、联系开发者等。

**应用状态恢复**

为了提供无缝的体验，如果用户在某个具体的"对话界面"或"文件交互界面"时退出（无论是主动退出还是系统杀后台），下次冷启动应用时，应用应该能够直接恢复到用户上次离开的那个界面，而不是回到"对话"列表页。这需要应用在退出前保存当前界面的状态（例如，当前打开的 `ChatSession` 或 `FileSession` 的 ID），并在启动时检查并恢复。