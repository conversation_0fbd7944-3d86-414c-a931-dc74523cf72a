---
description: 
globs: 
alwaysApply: false
---
**熔言 / LavaChat - iOS 应用技术栈文档 (V1.0)**

**版本:** 1.0
**日期:** 2024年5月21日
**目的:** 本文档旨在明确 LavaChat iOS 应用 V1 版本所使用的技术选型、依赖库和 API，为开发团队（包括 AI 编程工具）提供清晰的指导，确保技术选择的一致性和前瞻性。

**1. 核心平台与环境**

*   **平台:** iOS
*   **最低系统版本:** iOS 16.0
*   **编程语言:** Swift (充分利用 iOS 16+ 带来的最新语言特性和 API)
*   **UI 框架:** SwiftUI (充分利用其声明式特性，结合 MVVM 或类似架构模式)
*   **开发工具:** Xcode (推荐使用最新稳定版)
*   **版本控制:** Git

**2. 应用架构**

*   **架构模式:** Clean Architecture
    *   **Domain Layer (领域层):** 包含 Entities (如 `Message`, `FileSession`, `LLMInstance` 等) 和 Use Cases。此层代码应保持平台无关性，便于未来跨平台复用。
    *   **Data Layer (数据层):** 负责数据的持久化、网络请求和与外部服务（CloudKit, Cloudflare Workers, LLM API）以及本地文件系统的交互。包含 Repositories 的实现和数据源（本地、远程）。
    *   **Presentation Layer (表现层):** 包含 SwiftUI Views 和 ViewModels，负责 UI 展示和用户交互逻辑。

**3. 数据持久化**

*   **本地数据存储:** Core Data (V1 首选)
    *   利用其强类型、对象图管理和与 SwiftUI 的集成优势。
    *   需精心设计数据模型，特别是实体间的关系（如 `Message` 的父子关系）。
    *   考虑使用 `NSPersistentCloudKitContainer` 实现 Core Data 与 CloudKit Private Database 的自动同步。
    *   **序列化复杂类型:** 对于存储复杂数据结构（如自定义枚举 `ContentBlock`、字典 `[String: Any]`、数组 `[UUID]` 等）的属性（例如 `Message.content`, `*.metadata`, `ChatSession.activeLLMInstanceIds`, `FileCheckpoint.pendingEditStates` 等），**不使用** 不安全的 `Transformable` 类型。推荐的做法是：
        *   在 Core Data 模型编辑器中将这些属性的类型选择为 **`Binary Data`** (对应 XML 中的 `attributeType="Binary"`)。
        *   确保对应的 Swift 领域实体类型（如 `ContentBlock`, `PendingEditInfo` 等）遵循 `Codable` 协议。
        *   在 Data Layer (Repository 实现) 中使用 `JSONEncoder` 和 `JSONDecoder` (或其他 `Codable` 兼容的编码器) 在 `Data` 和实际 Swift 类型之间进行转换。
    *   **模型定义注意事项:**
        *   **默认值:** 对于定义为非可选 (non-optional) 的基础类型属性（如 `Boolean`, `Integer`, `String`），必须在 Core Data 模型编辑器中提供一个默认值（例如 `false`, `0`, `""`）。
        *   **UUID 和 Date:** 对于非可选的 `UUID` 和 `Date` 类型属性（如 `id`, `createdAt`, `timestamp` 等），虽然模型编辑器可能不直接支持设置动态默认值（如 `UUID()`, `Date()`），但它们仍应保持非可选状态。必须在代码层面（例如在 `NSManagedObject` 子类的 `awakeFromInsert()` 方法或创建对象时）确保为这些属性赋予初始值。
        *   **可选关系:** 根据 Xcode 的错误提示和实际需要，以下关系应在 Core Data 模型中设置为可选 (Optional)：`CDChatSession.user`, `CDLLMInstance.model`, `CDLLMInstance.user`, `CDLLMInstanceGroup.user`, `CDLLMModel.provider`, `CDMessage.session`。这有助于处理对象生命周期中的临时状态或同步过程中的不确定性。
    *   **手动生成 NSManagedObject 子类:**
        *   在 Core Data 模型检查器中，将每个实体的 "Codegen" 选项设置为 "Manual/None"。
        *   在 Editor 标签页中，选择 "Create NSManagedObject Subclass" 生成文件。
        *   生成的文件（如 `CDMessage+CoreDataClass.swift` 和 `CDMessage+CoreDataProperties.swift`）需要手动修改：
            *   添加 `awakeFromInsert()` 方法为非可选的 `UUID` 和 `Date` 属性设置初始值。
            *   添加自定义逻辑或扩展。
        *   将生成的文件统一存放在 `Data/DataSources/Local/CoreData/Entities/` 目录下。
    *   **注意:** `FileSession` 实体包含 `bookmarkData` (Data 类型) 字段，用于存储 Security-Scoped Bookmark 数据。这个书签指向的是 **LavaChat 内部管理和编辑的 Markdown 文件**（通常位于如 `LavaChatFiles/Sessions/` 的应用专属目录中），而非原始导入文件。`bookmarkData` 本身会被 Core Data 持久化并通过 CloudKit 同步。但在不同设备上解析书签时，需要操作系统能成功在该设备的相应位置找到这个 **托管的 Markdown 文件**（例如，如果 `LavaChatFiles` 文件夹被配置在 iCloud Drive 并已同步）。书签的生成和解析依赖本地（或已同步的云端）文件系统状态。
    *   *未来考虑:* 如遇性能瓶颈或有更复杂查询需求，可评估迁移至 GRDB (基于 SQLite 的 Swift 库)。

**4. 云服务与后端**

*   **用户数据同步与分享:** Apple CloudKit
    *   **Private Database:** 用于存储和同步用户的个人数据，如 `ChatSession`, `Message`, `FileSession` (元数据和 `bookmarkData`), `FileCheckpoint`, `LLMInstance`, `User` 偏好设置等。利用 `NSPersistentCloudKitContainer` 或手动 CloudKit API 操作。
    *   **Shared Database:** 用于实现 `ChatSession`, `LLMInstance`, `LLMInstanceGroup` 的 iCloud 链接和 QR 码分享功能。需要手动管理 `CKShare` 对象的创建和接受，并为不同分享类型定义不同的 `CKRecordType` 或元数据。
    *   **Public Database:** V1 范围外，未来用于“发现”功能时考虑。
    *   *文档链接:* [CloudKit Documentation](mdc:https:/developer.apple.com/documentation/cloudkit), [Sharing CloudKit Data with Other iCloud Users](mdc:https:/developer.apple.com/documentation/cloudkit/sharing_cloudkit_data_with_other_icloud_users)
*   **内置 API Key 代理 & 订阅验证:** Cloudflare Workers
    *   负责处理 `system_provided` 类型 `LLMInstance` 的 API 请求代理、Apple IAP 收据验证和用户订阅状态管理。
    *   App 与 Workers 之间需定义清晰的 API 接口 (RESTful JSON 或 gRPC)。
    *   Workers 端需安全存储开发者拥有的 LLM API Keys。
    *   *参考资料:*
        *   [Cloudflare Workers Documentation](mdc:https:/developers.cloudflare.com/workers)
        *   [Apple Server-to-Server Notifications & Receipt Validation](mdc:https:/developer.apple.com/documentation/appstoreservernotifications)
*   **（备用）ICP 备案准备:** 阿里云 ECS (低配，仅用于未来可能的备案需求，V1 不直接依赖)

**5. 网络与 API 集成**

*   **网络请求库:** URLSession (Swift 标准库)
    *   结合 Swift Concurrency (`async`/`await`) 进行异步网络操作。
*   **LLM 提供商 API:**
    *   **支持的提供商 (V1):** OpenAI, Anthropic, Google Gemini, DeepSeek, Grok (xAI), Qwen (通义千问), OpenRouter。
    *   **调用方式:**
        *   `user_api_key` LLMProviderType: App 直接调用相应服务商 API (使用存储在 Keychain 中的用户 Key)。
        *   `subscriptionBased` LLMProviderType: App 调用 Cloudflare Workers 代理 API。
    *   **API 文档链接:** (开发时需查找并添加各提供商的最新 API 文档链接)
        *   [OpenAI API](mdc:https:/platform.openai.com/docs/api-reference)
        *   [Anthropic API](mdc:https:/docs.anthropic.com/claude/reference/getting-started-with-the-api)
        *   [Google AI Gemini API](mdc:https:/ai.google.dev/docs/gemini_api_overview)
        *   (其他提供商 API 文档链接需补充)
*   **支付 API:** Apple In-App Purchase (使用 StoreKit 2)
    *   处理订阅购买、恢复和状态管理。
    *   *文档链接:* [StoreKit Documentation](mdc:https:/developer.apple.com/documentation/storekit)
*   **云同步与分享 API:** Apple CloudKit API (直接调用 `CKDatabase` 的 `privateCloudDatabase` 和 `sharedCloudDatabase` 进行操作，包括处理 `CKShare`)。
*   **后端代理 API:** 自定义 Cloudflare Workers API 端点 (内部定义)。

**6. 核心库与依赖 (通过 Swift Package Manager 管理)**

*   **Markdown 处理:**
    *   **优先方案:** Foundation 内置的 `AttributedString(markdown:options:baseURL:)` (iOS 15+，满足最低 iOS 16 要求)。
        *   优势：系统原生，无需额外依赖，直接生成富文本用于 SwiftUI 显示 (`Text`)。
        *   适用场景：将 Markdown 文本（包括 AI 返回的包含 Markdown 格式的内容）渲染到 UI 上。
        *   *文档链接:* [AttributedString Markdown Initializer](mdc:https:/developer.apple.com/documentation/foundation/attributedstring/init(markdown:options:baseurl:)-52n3u)
    *   **备选/底层库:** `swift-markdown` (官方底层库)。
        *   适用场景：如果未来需要对 Markdown 进行精细的结构化解析（访问 AST）、严格的 GFM 兼容性或自定义渲染逻辑时考虑。
        *   *链接:* [https://github.com/swiftlang/swift-markdown](mdc:https:/github.com/swiftlang/swift-markdown)
*   **数学公式渲染:**
    *   **推荐库:** `iosMath`
        *   用于渲染 LaTeX 格式的数学公式（如 `\[ ... \]` 或 `$$ ... $$`）。
        *   需要通过 `UIViewRepresentable` 桥接在 SwiftUI 中使用。
        *   *链接:* [https://github.com/kostub/iosMath](mdc:https:/github.com/kostub/iosMath)
*   **文件系统交互与访问控制:**
    *   **文件管理器 (`FileManager` - Foundation):** 用于创建和管理应用专属文件夹（如 `LavaChatFiles/` 及其子目录 `Sessions/`, `KnowledgeBase/`），以及执行基本文件操作。
    *   **Security-Scoped Bookmarks API (Foundation):**
        *   用于为 `FileSession` 对应的 **托管 Markdown 文件** 创建安全书签 (`URL.bookmarkData(options:includingResourceValuesForKeys:relativeTo:)`)，并将返回的 `Data` 存储在 `FileSession.bookmarkData` 字段中。
        *   用于在需要访问 `FileSession` 文件时（如读取内容进行编辑、Checkpoint 保存、@提及、导出），通过存储的 `bookmarkData` 解析出安全的 URL (`URL(resolvingBookmarkData:options:relativeTo:bookmarkDataIsStale:)`)，并调用 `startAccessingSecurityScopedResource()` 和 `stopAccessingSecurityScopedResource()`。
        *   *文档链接:* [Accessing Security-Scoped Resources](mdc:https:/developer.apple.com/documentation/security/app_sandbox/accessing_security-scoped_resources)
    *   **文件选择器 (File Picker):**
        *   **SwiftUI:** 使用 `.fileImporter` 视图修饰符，配置 `allowedContentTypes` 和 `allowsMultipleSelection`。
        *   **UIKit (备选/桥接):** `UIDocumentPickerViewController`，配置相应的 `documentTypes`。
        *   *适用场景:* 用于**初始导入**文件创建 `FileSession` 时，或者在对话中实现 `@提及文件/文件夹` 功能时弹出让用户选择（可以选择 `KnowledgeBase` 或其他位置的文件进行一次性读取，或者选取某个已存在的 `FileSession` 对应的 Markdown 文件进行引用）。
        *   *文档链接:* [fileImporter](mdc:https:/developer.apple.com/documentation/swiftui/view/fileimporter(ispresented:allowedcontenttypes:allowsmultipleselection:oncompletion:)), [UIDocumentPickerViewController](mdc:https:/developer.apple.com/documentation/uikit/uidocumentpickerviewcontroller)
    *   **文本内容读取:** 使用 `String(contentsOf:encoding:)` 或 `Data(contentsOf:)` 读取文件内容。对于 `FileSession` 的核心交互，读取的是通过 `bookmarkData` 访问的 **托管 Markdown 文件**。对于 `@提及` 的其他本地文件，是直接读取其内容。
*   **差异比较 (Diff) 与 Token 估算:**
    *   **基础文本差异计算:** Swift 标准库 `String.difference(from:)`。用于计算两个版本 **托管 Markdown 文件** 内容之间的差异，或识别用户手动编辑区域。
        *   用于计算两个字符串版本之间的差异（增、删），适用于识别用户手动编辑区域或版本比较。能处理 
        Unicode。
        *   **文档链接:** [String difference(from:)](https://developer.apple.com/documentation/
        swift/string/difference(from:))
    *   **AI 结构化编辑应用 (`DiffBlock`)**: **自定义实现**。
        *   根据 `entities_file.md` 中定义的 `DiffBlock` 结构，编写逻辑来解析其 `originalContent`, `replacementContent`, `rangeInOriginal` 等字段，并直接修改文件内容字符串 (`FileCheckpoint.fileContent`)。这部分 V1 阶段不依赖第三方库。
    *   **文件系统操作:** Foundation 框架中的 `FileManager` API。
    *   **Token 估算 (用于 `@提及` 文件):** 自定义实现。采用简单策略（如基于字符数、常见词比例等）进行粗略估算，无需引入复杂库。
*   **QR 码处理:**
    *   **扫描:** `AVFoundation` (系统框架)。
        *   用于从摄像头捕获输入并检测、解析 QR 码内容（提取 **通用的 LavaChat 分享链接**，链接内容需包含类型信息以区分 `ChatSession`, `LLMInstance`, `LLMInstanceGroup`）。
        *   *文档链接:* [AVCaptureMetadataOutput](mdc:https:/developer.apple.com/documentation/avfoundation/avcapturemetadataoutput)
    *   **检测 (图片):** `Vision` (系统框架)。
        *   用于检测和解析静态图像 (`CIImage`, `CGImage`, `CVPixelBuffer`) 中的 QR 码内容（提取 **通用的 LavaChat 分享链接**）。
        *   *文档链接:* [Detecting Barcodes and QR Codes](mdc:https:/developer.apple.com/documentation/vision/detecting_barcodes_and_qr_codes)
    *   **生成:** `CoreImage` (系统框架)。
        *   使用 `CIQRCodeGenerator` 滤镜将 iCloud 分享链接字符串 (针对 `ChatSession`, `LLMInstance`, 或 `LLMInstanceGroup`) 生成 QR 码图像 (`CIImage` 或 `UIImage`)。
        *   *文档链接:* [CIQRCodeGenerator](mdc:https:/developer.apple.com/documentation/coreimage/ciqrcodegenerator)

**7. 安全**

*   **用户 API Key 存储:** iOS Keychain (钥匙串)。
    *   **优先使用 iOS 16+ 提供的原生 API (`SecItem`)** 进行增删改查，无需引入第三方库。封装安全的 Keychain 操作工具类。
    *   *文档链接:* [Keychain Services](mdc:https:/developer.apple.com/documentation/security/keychain_services)
*   **文件访问控制:** 使用 Security-Scoped Bookmarks 确保持久、安全地访问 `FileSession` 文件。
*   **网络传输:** 强制使用 HTTPS。
*   **内置 API Key 安全:** 存储在 Cloudflare Workers 的环境变量或 Secret 中，绝不硬编码在客户端。

**8. 日志与监控**

*   **日志框架:** OSLog (Apple 官方统一日志系统)。
    *   使用不同的日志级别 (debug, info, notice, error, fault) 记录应用运行状态和问题。
    *   便于使用 Console.app 进行调试。
    *   *文档链接:* [OSLog](mdc:https:/developer.apple.com/documentation/os/oslog)

**9. 本地化**

*   **实现方式:** String Catalogs (`.xcstrings`, Xcode 15+)。
*   **默认语言:** 英语 (en)。
*   **支持语言 (V1):** 简体中文 (zh-Hans)。
*   **语言切换:** 不在应用内提供直接切换按钮，而是在“我/设置”界面提供一个按钮，**跳转到 iOS 系统的“设置 > 熔言 > 语言”界面**，让用户使用标准的系统方式切换 App 的首选语言。

**10. 系统集成**

*   **照片库访问:** `PhotosUI` (SwiftUI 优先) 或 `PhotoKit`。
    *   用于从用户相册选择包含 QR 码的图片。
    *   *文档链接:* [PhotosUI](mdc:https:/developer.apple.com/documentation/photokit/photospicker), [PhotoKit](mdc:https:/developer.apple.com/documentation/photokit)
*   **跳转到文件 App (`Files.app`):**
    *   获取应用专属文件夹的 `fileURL` (例如通过 `FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.appendingPathComponent("LavaChatFiles")`)。
    *   使用 `UIApplication.shared.open(url)` 打开此 URL。iOS 系统会自动处理，并在文件 App 中显示该文件夹内容。
*   **快捷指令 & Siri:** App Intents (iOS 16+)。
    *   提供核心操作的 Intent 定义，例如：
        *   `StartNewChatIntent` (新建对话)
        *   `StartNewFileSessionIntent` (新建文件交互)
        *   `AskLavaChatIntent` (向 LavaChat 提问，可能需要选择会话或模型)
    *   *文档链接:* [App Intents](mdc:https:/developer.apple.com/documentation/appintents)
*   **权限:** 需要在 `Info.plist` 中添加必要的权限描述：
    *   `NSCameraUsageDescription`: 请求相机权限，用于 QR 码扫描。
    *   `NSPhotoLibraryUsageDescription`: 请求照片库访问权限，用于选择 QR 码图片。
    *   **注意:** 访问应用自身的 Documents 或 Library 目录、或通过 Security-Scoped Bookmarks 访问文件通常不需要额外的特殊文件访问权限声明。

**11. 其他**

*   **AI 编程工具:** 支持使用 Cursor 等工具，本文档为其提供技术选型依据。
*   **代码规范:** 遵循 Swift API Design Guidelines，保持代码整洁和可维护性。
*   **依赖管理:** 使用 Swift Package Manager (SPM)。

**12. 未来技术选型考虑 (Future Considerations)**

*   **模型上下文协议 (Model Context Protocol - MCP):**
    *   **目标:** 为了更丰富、更标准化的 LLM 上下文集成和工具调用能力，计划在未来版本 (非 V1) 中引入 MCP 支持。
    *   **角色:** LavaChat 应用将作为 **MCP Host**。
    *   **潜在依赖:** `modelcontextprotocol/swift-sdk` (Swift)。
    *   **架构影响:** 需要在应用内集成 **MCP Client** 逻辑，并考虑如何管理与外部 **MCP Server**（可能由 LavaChat 提供或第三方提供）的连接和通信。MCP Server 可能负责访问本地文件系统、特定 API、数据库等。
    *   **价值:** 极大地扩展 LavaChain 与本地数据、其他应用或服务的交互能力，提供更强大的智能体验。
    *   *参考链接:* [MCP Introduction](mdc:https:/modelcontextprotocol.io/introduction)
*   **高级 RAG:** 实现对本地知识库文件的检索增强生成。

**13. 总结**

本技术栈选择旨在平衡开发效率、应用性能、安全性、用户体验以及未来跨平台扩展的需求。优先采用 Apple 官方推荐的技术和框架，谨慎引入第三方依赖。随着项目的进展，本文档可能会进行必要的更新。