**熔言 / LavaChat - 核心领域实体设计文档 (补充部分)**

**3. 领域层实体设计 (Domain Layer Entities - 续)**

**3.1. `User` (用户)**

`User` 代表应用的使用者。

*   **`id` (唯一标识符):** UUID。在集成 CloudKit 时，可以考虑使用 CloudKit 的 `recordID.recordName` 作为稳定标识符，或者单独维护一个应用内的 UUID 并与 CloudKit Record ID 关联。
*   **`nickname` (昵称):** String, 可选。用户自定义的显示名称。
*   **`avatarIdentifier` (头像标识符):** String, 可选。用于引用用户头像的标识（例如，本地资源名、CloudKit Asset ID 或 URL）。
*   **`registrationDate` (注册时间):** Timestamp。用户首次使用或账户创建的时间。
*   **`lastSeenAt` (最后活跃时间):** Timestamp。用户最后一次使用应用的时间。
*   **`appSettingsId` (应用设置ID):** UUID, 可选。指向一个关联的 `ApplicationSetting` 实体（如果决定将全局设置抽象出来），或者直接在此处存储部分偏好设置。
*   **`defaultChatSettingsId` (默认对话设置ID):** UUID。指向用户偏好的默认 `ChatSessionSetting` 的 ID，新对话将使用此设置，除非特别指定。用户可以将其设置为系统默认设置或自己创建的任何设置。
*   **`systemUtilityLLMInstanceId` (系统杂活LLM实例ID):** UUID, 可选。用户选定的、用于执行应用内自动化任务（如总结、标题生成、审核）的 `LLMInstance` 的 ID。
*   **`syncStatus` (同步状态):** Enum 或 String, 可选。例如 `synced`, `syncing`, `error`, `disabled`。用于表示用户数据（特别是 CloudKit）的同步情况。
*   **`lastSyncTimestamp` (最后同步时间):** Timestamp, 可选。
*   **`memoryFileBookmarkData` (配置的记忆文件):** Data?, 用户配置的记忆存储文件的Security-Scoped Bookmark格式文件， V1 阶段此字段仅为占位，功能待未来实现。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。存储其他用户相关信息，如是否同意特定协议版本、A/B 测试分组等。对应的 Swift 类型需遵循 `Codable`。

**3.2. `LLMProvider` (LLM 提供商)**

`LLMProvider` 代表提供大型语言模型的公司或组织。

*   **`id` (唯一标识符):** UUID。
*   **`name` (提供商名称):** String。例如 "OpenAI", "Anthropic", "Google", "用户自定义"。
*   **`logoImageName` (Logo图像名称):** String, 可选。用于在 UI 中展示提供商 Logo 的资源名称。对于内置提供商，应遵循 `provider_logo_[provider_name]` 格式，可能包含 `_dark` 后缀用于暗色模式。
*   **`customLogoData` (自定义Logo数据):** Data?, 可选。用户上传的自定义Logo图像数据（仅适用于 `isUserCreated == true` 的情况）。
*   **`websiteUrl` (官方网站):** String, 可选。
*   **`apiDocumentationUrl` (API文档链接):** String, 可选。
*   **`providerType` (提供商类型):** `LLMProviderType`。指示使用此提供商模型的认证方式。Enum，例如`subscription_based`, `user_api_key`
*   **`apiKeyStored` (API Key 是否已存储):** Boolean。仅当 `providerType == .userApiKey` 时有意义，指示用户是否已为此提供商在 Keychain 中安全存储了 API Key。**注意：API Key 本身绝不能存储在此实体中，必须存储在 Keychain 中，用 `LLMProvider.id` 作为 Key 的一部分来查找。**
*   **`isUserCreated` (是否为用户自定义):** Boolean。标记此提供商是否由用户添加，用于区分内置提供商。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。例如，存储特定的认证类型要求、地区限制信息等。对应的 Swift 类型需遵循 `Codable`。
*   **说明:** 应用可以预置一些主流提供商的信息。用户也可以添加自定义提供商（此时可能只需要 `name` 和 `id`）。

**3.3. `LLMModel` (LLM 模型)**

`LLMModel` 代表由某个 `LLMProvider` 提供的具体模型。

*   **`id` (唯一标识符):** UUID。
*   **`providerId` (提供商ID):** UUID。指向所属的 `LLMProvider`。
*   **`modelIdentifier` (模型标识符):** String。在 API 调用中实际使用的模型名称，例如 "gpt-4-turbo", "claude-3-opus-20240229"。**此字段需要唯一（在同一个 Provider 内）。**
*   **`name` (模型名称):** String。用户友好的显示名称，例如 "GPT-4 Turbo", "Claude 3 Opus"。
*   **`description` (描述):** String, 可选。模型的简要介绍。
*   **`logoImageName` (Logo图像名称):** String, 可选。用于在 UI 中展示模型 Logo 的资源名称。对于内置模型，建议使用格式 `model_logo_[provider_name]_[model_identifier]`，可包含 `_dark` 后缀。
*   **`customLogoData` (自定义Logo数据):** Data?, 可选。用户上传的自定义Logo图像数据（适用于用户自定义或想替换默认Logo的情况）。
*   **`contextWindowSize` (上下文窗口大小):** Integer, 可选。模型支持的最大 Token 数。
*   **`supportsMultiModal` (支持多模态):** Boolean。指示模型是否支持处理图像等非文本输入/输出。
*   **`pricingInfo` (价格信息):** String 或 结构化数据 (例如 JSON), 可选。描述模型的计费方式（如每百万 Token 的输入/输出价格）。
*   **`availabilityStatus` (可用状态):** Enum 或 String。例如 `available`, `beta`, `preview`, `deprecated`, `restricted`。
*   **`group` (模型分组/类别):** String, 可选。用于 UI 组织和可能的后端路由（例如区分订阅 Provider 内部的实际模型来源）。例如 "openai", "anthropic", "image_generation", "long_context"。
*   **`isDefaultRecommendation` (是否为默认推荐):** Boolean。标记此模型是否是应用开发者推荐的、适用于一般任务的默认选项。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。例如，支持的特定 API 参数、版本信息、功能标签（如 "code", "creative", "long_context"）。对应的 Swift 类型需遵循 `Codable`。
*   *说明:* 应用会预置许多已知模型的信息。对于用户自定义的 Provider，用户也需要能添加自定义的模型条目。

**3.4. `LLMInstance` (LLM 实例)**

`LLMInstance` 代表对某个 `LLMModel` 的一个具体配置和使用实例。用户可以在"模型" Tab 中管理这些实例。

*   **`id` (唯一标识符):** UUID。
*   **`modelId` (模型ID):** UUID。指向此实例基于的 `LLMModel`。
*   **`name` (实例名称):** String。用户自定义的名称，例如 "我的创意写作助手 (GPT-4)", "代码审查员 (Claude Opus)", "自定义API - 公司内部模型"。
*   **`customLogoData` (自定义Logo数据):** Data?, 可选。用户为此实例上传的自定义Logo图像数据。
*   **`defaultParameters` (默认参数):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典 (Dictionary<String, Any> 或 JSON)。存储此实例的**基础**配置，如：
        *   `system_prompt`: String
        *   `temperature`: Double
        *   `maxTokens`: Integer
        *   `topP`: Double
        *   `presencePenalty`: Double
        *   `frequencyPenalty`: Double
        *   其他模型特定的参数...
        *   *说明:* 这些参数是该实例的默认值，可以在具体的 `ChatSessionSetting` 中被覆盖 (`llmParameterOverrides`)。
        *   对应的 Swift 类型需遵循 `Codable`。
*   **`totalPromptTokensUsed` (累计 Prompt Token 使用量):** Integer, 默认 0。记录此实例自创建以来累计消耗的 Prompt Token 总数。
*   **`totalCompletionTokensUsed` (累计 Completion Token 使用量):** Integer, 默认 0。记录此实例自创建以来累计生成的 Completion Token 总数。
    *   *说明:* 这两个字段由 Token 记录流程（见下文）异步更新。主要用于用户参考，或未来可能的按实例用量统计。对于 `system_provided` 类型，实际计费和限制通常基于 `SubscriptionStatus`。
*   **`createdAt` (创建时间):** Timestamp。
*   **`lastUsedAt` (最后使用时间):** Timestamp, 可选。记录此实例最后一次被用于生成消息的时间。
*   **`isFavorited` (是否收藏):** Boolean。用户是否将此实例标记为常用。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。存储其他配置信息，如特定的请求头、模型能力标签、速率限制信息等（对应的 Swift 类型需遵循 `Codable`）：
        *   `tags`: [String] (用户添加的标签)
        *   `sharingInfo`: { `isShared`: Bool, `shareLink`: String?, `lastSharedAt`: Timestamp? } (用于支持实例分享功能)
        *   `version`: Integer (用于导入/导出版本控制)

**3.5. `LLMInstanceGroup` (LLM 实例组)**

`LLMInstanceGroup` 代表用户创建的一个 `LLMInstance` 的集合，方便在对话中同时与组内所有实例进行交互。

*   **`id` (唯一标识符):** UUID。
*   **`userId` (用户ID):** UUID。指向拥有此组的 `User`。
*   **`name` (组名称):** String。用户自定义的名称，用于在对话中通过 `@name` 提及。**此名称在用户的所有组和实例中需要保持唯一性**，以便提及。
*   **`description` (描述):** String, 可选。对该组用途的简要说明。
*   **`instanceIds` (实例ID列表):** Array of UUID。包含在此组中的 `LLMInstance.id` 列表。
*   **`customLogoData` (自定义Logo数据):** Data?, 可选。用户为此组上传的自定义Logo图像数据。
*   **`createdAt` (创建时间):** Timestamp。
*   **`lastModifiedAt` (最后修改时间):** Timestamp。组信息（名称、描述、包含的实例）最后一次被修改的时间。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。例如：
        *   `tags`: [String] (用户添加的标签)
        *   对应的 Swift 类型需遵循 `Codable`。

*说明:* 用户可以在"模型"Tab 中创建和管理这些实例组。当用户在对话输入框中输入 `@组名称` 并发送消息时，该消息会被同时发送给组内配置的所有 `LLMInstance`。应用会在用户消息下展示来自组内每个实例的单独回复。类似地，用户也可以通过 `@实例名称` 来直接与单个 `LLMInstance` 交互。

**3.6. `SubscriptionPlan` (订阅计划)**

`SubscriptionPlan` 定义了应用提供的付费订阅选项。

*   **`id` (唯一标识符):** String。**建议直接使用 Apple App Store Connect 中定义的 Product ID** 作为主键，便于关联 IAP。
*   **`name` (计划名称):** String。用户在 App Store 和应用内看到的名称，例如 "熔言 Pro", "LavaChat Max"。
*   **`description` (计划描述):** String。详细说明计划包含的内容。
*   **`priceLocaleIdentifier` (价格地区标识):** String。例如 "USD", "CNY"。
*   **`price` (价格):** Decimal 或 String。显示的本地化价格。
*   **`billingCycle` (计费周期):** Enum。`monthly`, `yearly`, `lifetime` (如果提供)。
*   **`featureList` (功能列表):** Array of String。描述此计划解锁的功能点，用于 UI 展示。
*   **`tokenAllowancePerCycle` (每周期 Token 限额):** Integer, 可选。如果订阅计划包含一定的免费 Token 额度。例如，每月 1,000,000 Tokens。设为 0 或 null 表示无限制或不基于 Token 计数限制。
*   **`promptTokenCostFactor` (Prompt Token 成本因子):** Double, 可选, 默认 1.0。用于计算额度消耗时，Prompt Token 的权重。
*   **`completionTokenCostFactor` (Completion Token 成本因子):** Double, 可选, 默认 1.0。用于计算额度消耗时，Completion Token 的权重。（例如，某些模型输出 Token 更贵）。
*   **`concurrentRequestLimit` (并发请求限制):** Integer, 可选。允许同时进行的 `system_provided` LLM 请求数量。
*   **`prioritySupport` (优先支持):** Boolean。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。例如：
        *   `uiHighlightColor`: String (在 UI 中突出显示的颜色)
        *   `isMostPopular`: Boolean (标记为最受欢迎)
        *   `appleFamilySharingEligible`: Boolean (是否支持家人共享)
        *   对应的 Swift 类型需遵循 `Codable`。

*说明:* 这些信息主要由开发者在 App Store Connect 配置，并通过 StoreKit 同步到 App 内，或通过自己的服务器下发以保持最新。

**3.7. `SubscriptionStatus` (订阅状态)**

`SubscriptionStatus` 记录了**单个用户**当前的订阅情况和相关使用数据。

*   **`id` (唯一标识符):** UUID。
*   **`userId` (用户ID):** UUID。指向关联的 `User`。
*   **`activePlanProductId` (当前生效计划的产品ID):** String, 可选。当前用户激活的 `SubscriptionPlan` 的 Apple Product ID。`null` 表示未订阅或已过期。
*   **`status` (订阅状态):** Enum。
    *   `active`: 活跃。
    *   `expired`: 已过期。
    *   `in_billing_retry_period`: 支付失败，处于重试期 (grace period)。
    *   `in_grace_period`: Apple 提供的宽限期内。
    *   `revoked`: 被撤销。
    *   `cancelled`: 用户已取消自动续费，但当前周期仍有效。
    *   `free_trial`: 处于免费试用期。
    *   `inactive`: 从未订阅过。
*   **`expiryDate` (到期时间):** Timestamp, 可选。当前订阅周期的结束时间。
*   **`purchaseDate` (购买/续费时间):** Timestamp, 可选。最近一次购买或续费的时间。
*   **`startDate` (原始购买时间):** Timestamp, 可选。首次购买此订阅的时间 (用于判断是否为老用户等)。
*   **`autoRenewEnabled` (自动续费是否开启):** Boolean。用户是否在 App Store 设置中开启了自动续费。
*   **`lastVerificationDate` (最后验证时间):** Timestamp。上次通过 Apple 服务器验证收据的时间。
*   **`tokensUsedThisCycle` (本周期已用 Token 数):** Integer, 默认 0。记录当前计费周期内，通过 `system_provided` LLM 实例消耗的 Token 总量（可能需要根据计划的 CostFactor 加权计算）。
*   **`lastCycleResetDate` (本周期开始时间):** Timestamp。当前计费周期开始的时间，用于确定 `tokensUsedThisCycle` 的计算范围。
*   **`originalTransactionId` (原始交易ID):** String, 可选。首次购买此订阅的交易 ID，用于关联用户的购买历史。
*   **`latestTransactionId` (最新交易ID):** String, 可选。最近一次成功续费的交易 ID。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典。例如：
        *   `trialEligibility`: Boolean (是否仍有资格享受试用)
        *   `cancellationReason`: String (用户取消原因，如果提供)
        *   `promotionalOfferId`: String (如果当前是促销价)
        *   对应的 Swift 类型需遵循 `Codable`。

---

**4. 功能实现详解 (补充)**

**4.1. Token 使用记录实现**

1.  **`Message` 记录:** 当一个 `role` 为 `assistant` 或 `merged_assistant` 的 `Message` 成功生成 (`status = received`) 时，其 `promptTokens` 和 `completionTokens` 字段应被填充（这些信息通常由 LLM API 的响应提供）。
2.  **异步更新流程:**
    *   触发时机：`Message` 状态变为 `received` 后。
    *   操作：启动一个后台任务或将任务放入队列。
    *   任务内容：
        *   读取该 `Message` 的 `promptTokens`, `completionTokens`, `llmInstanceId`, `userId`。
        *   根据 `llmInstanceId` 查找对应的 `LLMInstance` 实体。
        *   **原子性更新 `LLMInstance`:** 增加其 `totalPromptTokensUsed` 和 `totalCompletionTokensUsed` 字段。
        *   **检查实例类型:** 如果 `LLMInstance.instanceType` 是 `system_provided`:
            *   根据 `userId` 查找对应的 `SubscriptionStatus` 实体。
            *   读取当前激活计划 `SubscriptionPlan` (通过 `activePlanProductId`)，获取其 `promptTokenCostFactor` 和 `completionTokenCostFactor`。
            *   计算本次消息消耗的**计费 Token 数**: `billedTokens = (message.promptTokens * plan.promptFactor) + (message.completionTokens * plan.completionFactor)`。
            *   **原子性更新 `SubscriptionStatus`:** 增加其 `tokensUsedThisCycle` 字段 (增加 `billedTokens`)。
    *   **注意:** "原子性更新" 很重要，尤其是在并发场景下，需要确保读取-修改-写入操作的完整性，避免数据竞争。Core Data 或 SQLite 都提供了相应的机制。此更新应该是增量更新，而不是覆盖。

**4.2. `LLMInstance` 分享、下载、导入**

*   **下载/导出:**
    *   **触发:** 用户在"模型"界面选择一个 `LLMInstance` 并点击"导出"或"分享"。
    *   **数据准备:**
        *   获取 `LLMInstance` 实体数据。
        *   获取关联的 `LLMModel` 的 `modelIdentifier` 和 `providerId`。
        *   获取关联的 `LLMProvider` 的 `name` (用于人类可读性)。
        *   **关键：绝不能包含 `apiKeyStored` 的状态或实际的 API Key。**
    *   **序列化 (JSON):** 创建一个包含以下信息的 JSON 对象：
        ```json
        {
          "formatVersion": "1.0", // 版本号，用于未来兼容
          "exportedAt": "2024-05-21T10:00:00Z",
          "instance": {
            "name": "我的创意写作助手 (GPT-4)", // LLMInstance.name
            "modelIdentifier": "gpt-4-turbo",   // LLMModel.modelIdentifier
            "providerName": "OpenAI",          // LLMProvider.name (用于显示)
            // 或者使用 providerId + modelId，让导入方自行查找
            // "providerId": "uuid-for-openai",
            // "modelId": "uuid-for-gpt4-turbo-model",
            "customLogoData": "base64-encoded-image-data", // 如果提供了自定义Logo
            "defaultParameters": {             // LLMInstance.defaultParameters
              "system_prompt": "你是一个乐于助人的创意写作助手。",
              "temperature": 0.8
              // ... 其他参数
            },
            "metadata": {                      // 部分安全的 metadata
              "tags": ["writing", "creative"]
            }
          }
        }
        ```
    *   **文件生成与分享:** 将 JSON 保存为 `.lavachatinstance` 文件（自定义扩展名），通过 `UIActivityViewController` 分享。

*   **导入:**
    *   **触发:** 通过"Open In..."或应用内文件选择器打开 `.lavachatinstance` 文件。
    *   **文件处理与反序列化:** 读取 JSON 数据。
    *   **验证:** 检查 `formatVersion`。
    *   **数据处理与冲突解决:**
        *   **查找模型:** 根据导入数据中的 `providerName` + `modelIdentifier` (或 `providerId` + `modelId`) 在本地 `LLMProvider` 和 `LLMModel` 数据库中查找对应的 `LLMModel`。
            *   **找不到:** 提示用户"需要的基础模型 [模型名] 不存在，请先添加该模型或选择一个本地模型替代"，或者如果 Provider 存在但模型不存在，可以尝试引导用户添加该模型。无法解析则导入失败。
        *   **创建 `LLMInstance`:**
            *   生成**新的** `LLMInstance.id` (UUID)。
            *   关联当前 `userId` 和找到的 `LLMModel.id`。
            *   使用导入的 `name`。可以检查名称是否已存在，如果存在，提示用户重命名或自动添加后缀 (如 "(导入)")。
            *   **`instanceType` 必须设置为 `user_provided`**，因为导出的实例不含内置 Key 信息，即使它原本是 `system_provided`。用户需要自行提供 API Key 才能使用。`apiKeyStored` 设为 `false`。
            *   应用导入的 `defaultParameters` 和 `metadata`。
            *   如果导入数据中包含 `customLogoData`，则适当应用这些值。
            *   `totalTokensUsed` 字段设为 0。
    *   **持久化:** 保存新的 `LLMInstance` 到 Core Data / SQLite。
    *   **UI 更新:** 刷新"模型"列表，提示用户导入成功，并可能建议用户"为此实例设置 API Key"。

**4.3. 对话中的提及 (`@mention`) 功能**

1.  **输入解析:**
    *   在用户输入消息时，实时或在发送前解析文本，识别 `@<名称>` 模式。
    *   查找用户的 `LLMInstance` (通过 `name`) 和 `LLMInstanceGroup` (通过 `name`)，检查名称是否存在且唯一。
    *   如果匹配成功，UI 可以高亮显示提及，并关联到对应的实例或组 ID。
2.  **消息发送:**
    *   当用户发送包含 `@提及` 的消息时：
        *   如果提及的是单个 `LLMInstance` (`@实例名称`)：
            *   将此消息及其上下文发送给该 `LLMInstance`。
            *   在 `Message` 的 `metadata` 中可记录 `targetInstanceId`。
        *   如果提及的是 `LLMInstanceGroup` (`@组名称`)：
            *   获取该组包含的所有 `LLMInstance.id`。
            *   **并行地**将该消息及其上下文分别发送给组内的每一个 `LLMInstance`。
            *   在 `Message` 的 `metadata` 中可记录 `targetGroupId`。
3.  **结果展示:**
    *   对于 `@组名称` 的调用，用户的单个消息下会并列出现多个 `assistant` 角色的 `Message`，分别对应组内每个实例的回复。每个回复需要清晰地标识出它来自哪个 `LLMInstance`。
4.  **Token 计算:**
    *   当消息发送给一个组时，产生的 `promptTokens` 按单个实例计算（因为是同一份 Prompt），但 `completionTokens` 需要将组内所有实例返回的 `completionTokens` **累加**，用于更新 `LLMInstance` 和 `SubscriptionStatus` 中的 Token 使用量。

**4.3. 系统杂活 LLM**

1.  **用户选择:** 在应用的全局设置中（可能存储在 `User.preferences` 或专门的 `ApplicationSetting` 实体，或者简单地用 `UserDefaults`），提供一个选项让用户从所有 `LLMInstance` 中选择一个。将选中的 `LLMInstance.id` 存储到 `User.systemUtilityLLMInstanceId` 或对应的设置字段中。
2.  **应用调用:** 当需要执行内部 AI 任务时（如为新会话生成标题、总结长文本、内容审核）：
    *   读取用户选择的 `systemUtilityLLMInstanceId`。
    *   如果 ID 存在，则获取对应的 `LLMInstance` 配置（包括其 `defaultParameters`）。
    *   应用开发者**预设**针对特定任务的**系统提示词 (Role/Prompt)**，例如：
        *   **标题生成:** "请根据以下对话摘要生成一个简洁的标题（不超过 10 个字）：\n{对话摘要}"
        *   **内容审核:** "请检查以下文本是否包含个人身份信息（姓名、电话、地址、身份证号、邮箱等）、不当言论或敏感内容。如果包含，请仅输出 'Contains sensitive content.'，否则输出 'Content is safe.'。\n文本：\n{待审核内容}"
    *   使用选定的 `LLMInstance` 和**任务特定的提示词**（覆盖实例的默认 `system_prompt`）调用 LLM API。
    *   处理返回结果。

---

这样，我们就补充设计了用户、提供商、模型、实例、订阅计划和状态等实体，并详细说明了 Token 记录、实例分享/导入以及系统杂活 LLM 的实现思路。这些设计应该能为后续的开发工作提供清晰的指导。