**阶段 2: 模型管理模块 (MVP: User-Provided Instances)**

13. **实现内置 LLM Provider 和 Model 数据初始化:**
    *   **创建初始化器:**
        *   在 `Data/Utils/` 目录下创建新的子目录 `Initialization`。
        *   在 `Data/Utils/Initialization/` 中创建 `BuiltinLLMInitializer.swift` 文件。
    *   **定义内置数据:**
        *   在 `BuiltinLLMInitializer.swift` 中：
            *   定义一个常量，表示当前内置 Provider/Model 数据的版本号，例如 `static let currentBuiltinLLMVersion = 1`。
            *   定义一个私有静态属性，存储所有 V1 版本需要内置的 `LLMProvider` 和 `LLMModel` **领域对象 (Domain Entities)** 列表。确保这些对象的 `isUserCreated` 字段设为 `false`，并为它们分配固定的 UUID。
    *   **实现初始化方法:**
        *   在 `BuiltinLLMInitializer` 中创建一个静态方法，例如 `initializeIfNeeded(repository: LLMInstanceRepository, userDefaults: UserDefaults = .standard)`。
        *   该方法内部逻辑：
            1.  从 `UserDefaults` 读取之前存储的内置数据版本号，例如 `let storedVersion = userDefaults.integer(forKey: "BuiltinLLMVersion")`。
            2.  **检查是否需要更新:** 如果 `storedVersion < currentBuiltinLLMVersion`，则执行初始化/更新逻辑。
            3.  **获取现有内置数据:** 调用 `repository.getProviders(isUserCreated: false)` 和相应的 `repository.getAllModels(for:)` 方法获取当前 Core Data 中所有 `isUserCreated == false` 的 Provider 和 Model。将它们存储在字典中以便快速查找（例如，`[UUID: LLMProvider]` 和 `[UUID: LLMModel]`）。
            4.  **比较并执行操作 (Idempotent Update):**
                *   遍历代码中定义的 **内置 Provider 列表**：
                    *   如果 Provider ID 在 Core Data 现有字典中不存在，调用 `repository.createProvider(_:)` 新增。
                    *   如果存在，比较代码定义和 Core Data 实体的属性。如有差异，调用 `repository.updateProvider(_:)` 更新。（**注意:** 不要更新 ID 或 `isUserCreated` 字段）。
                *   遍历代码中定义的 **内置 Model 列表**：
                    *   如果 Model ID 在 Core Data 现有字典中不存在，调用 `repository.createModel(_:)` 新增。
                    *   如果存在，比较属性。如有差异（如 `name`, `description`, `contextWindowSize`, `availabilityStatus` 等），调用 `repository.updateModel(_:)` 更新。
                *   **处理弃用 (可选但推荐):** 遍历 Core Data 中的内置 Model，如果某个 Model 的 ID **不在** 代码定义的当前版本列表中了，调用 `repository.updateModel(_:)` 将其 `availabilityStatus` 设置为 `.unavailable` 或 `.deprecated`。对于 Provider，类似处理（如果 Provider 下所有 Model 都被标记为不可用，可以考虑更新 Provider 自身的某种状态）。
            5.  **更新版本号:** 所有操作成功完成后，将 `currentBuiltinLLMVersion` 写入 `UserDefaults`：`userDefaults.set(currentBuiltinLLMVersion, forKey: "BuiltinLLMVersion")`。
            6.  **错误处理:** 添加 `do-catch` 块处理 Repository 操作可能抛出的错误，并进行适当的日志记录。
    *   **触发初始化:**
        *   在应用启动逻辑中（例如 `lavachatApp.swift` 的 `init` 或主视图的 `.onAppear` 中，确保只执行一次），获取 `LLMInstanceRepository` 的实例（通过依赖注入或直接从 `PersistenceController` 创建），并调用 `BuiltinLLMInitializer.initializeIfNeeded(repository: yourRepositoryInstance)`。
14. **实现模型列表 UI (模型 Tab):**
    *   在`lavachat/lavachat/Presentation/Views/ModelManagement/ModelsView.swift`创建 `ModelsView.swift` (模型 Tab 的根视图)。
    *  **UI 结构**：
        - **顶部区域**：
            - 搜索栏。
        - **列表**：
            - 快捷入口区域（类似微信通讯录顶部的“群聊”、“标签”）。**MVP 阶段在此处为"收藏夹"和"模型组"保留占位入口。**
                - 这个快捷入口需要和 `LLMInstance` 的项目长得一样，我们可以利用相同的列表实现这一部分，不过这一部分没有 `LLMProvider` 分组
            - 使用 `List` 和 `Section` 按 `LLMProvider` 分组展示 `LLMInstance`。
            - 每个 `LLMProvider` 分组标题显示：
                - **左侧**：Provider 的 Logo（通过 `logoImageName` 加载）。
                - **中间**：Provider 名称。
                - **右侧**：灰色箭头图标（SF Symbols: `chevron.right`），表示可点击进入 `ProviderDetailView`。
            - 每个 `LLMInstance` 行显示：
                - **左侧**：Instance 的 Logo（优先使用自定义 Logo，若无则使用关联 `LLMModel` 的 Logo）。
                - **中间**：
                - 主文本：Instance 名称（`name`）。
                - 副文本（灰色小字）：如果 Instance 非默认（`isUserModified == true`），显示关联 `LLMModel` 的名称（例如“模型: GPT-4o”）。
            - **右侧**：留空或显示其他状态标识（如收藏图标）。
    *  **交互设计**：
        - **顶部导航栏右侧**：添加一个 "+" 按钮。点击后弹出菜单或导航到新实例创建流程。本步骤先不实现点击后的逻辑。
        - **导航栏标题保持不变**：导航栏居中的标题需要一致保持在导航栏居中位置，也就是 "+" 按钮左边，当屏幕scroll到最上方的时候也不会变成大写出现在导航栏下方左边位置。
        - **Provider 分组标题**：整体可点击，点击后导航至 `ProviderDetailView`。
        - **Instance 行**：点击后导航至 `InstanceDetailView`。
    *  **样式参考**：
        - 整体布局类似微信通讯录，分组标题和行高保持一致。
        - 使用 `labelPrimary` 颜色显示主文本，`labelSecondary` 颜色显示副文本和箭头。
        - 分组标题背景色为 `backgroundSecondary`，行背景色为 `backgroundPrimary`。
    *  **数据绑定**：
        - 通过 `ForEach` 动态加载 `LLMProvider` 和其下的 `LLMInstance` 列表（数据由后续 ViewModel 提供）。
    *  **占位符**：
        - 若无实例，显示提示文本（如“暂无模型实例”）。
15. **实现 Provider 详情 UI (`ProviderDetailView`):**
    *   **目标:** 创建 `ProviderDetailView.swift`，作为 `LLMProvider` 的详情展示、编辑和创建界面。
    *   **输入:** 接收 `LLMProvider.id` (用于查看/编辑现有 Provider) 或一个标记 (用于创建新 Provider) 作为输入。
    *   **数据源 (MVP/UI 阶段):** 使用 `BuiltinLLMInitializer.swift` 或类似的 mock 数据结构来填充界面，模拟从 Repository 获取 `LLMProvider` 及其关联的 `LLMModel` 列表。
    *   **布局与风格:**
        *   **整体:** 采用类似 iOS "设置" 应用的二级/三级界面风格或"通讯录"联系人详情页风格，使用 `Form` 和 `List` 承载内容（下方区域规定了具体section类别）。
        *   **顶部区域:** 显著展示 `LLMProvider` 的 Logo (使用 `LogoImage` 视图) 和名称 (`name`)。
        *   **信息区域 (`Form` Section):**
            *   "Website" 行：显示 `websiteUrl`，点击时应准备打开 URL (实际打开逻辑后续实现)。
            *   "API Documentation" 行：显示 `apiDocumentationUrl`，点击时应准备打开 URL。
        *   **API 配置区域 (`Form` Section):**
            *   "API Base URL" 行：显示 `apiBaseUrl`。
            *   "API Endpoint Path" 行：显示 `apiEndpointPath`。
            *   "API Style" 行：显示 `apiStyle` (枚举值，可能需要转换为用户可读字符串)。
            *   **API Key 行 (条件显示):** 仅当 `providerType == .userApiKey` 时显示。
                *   **有 Key (`apiKeyStored == true`):** 显示一行，左侧为 "API Key"，右侧为一串隐藏的圆点 (`●●●●●●●●`)。点击该行会触发 Face ID/Touch ID 验证。验证通过后，圆点变为实际的 Key 字符串，并允许编辑。
                *   **无 Key (`apiKeyStored == false`):** 显示一行，左侧为 "API Key"，右侧为一个 `SecureField` (密码输入框)，提示用户输入 Key。**关键:** 点击此行（即使在非编辑模式下）应允许用户输入 Key，并将视图自动置于该行的编辑状态。
        *   **可用模型区域 (`List` Section):**
            *   **Section Header:** 标题为 "Available Models"。右侧放置一个 `Image(systemName: "plus")` 按钮，点击后应导航到 `ModelDetailView` 以创建新的自定义模型（与此 Provider 关联）。
            *   **模型列表:** 展示与此 Provider 关联的所有 `LLMModel`。
                *   每行显示 `LLMModel` 的名称 (`name`)，可选地显示 Logo 或简短描述。
                *   点击任意模型行，导航至 `ModelDetailView`，并传递 `provider.id`，目的是基于此provider创建新的 `LLMModel`。
    *   **编辑模式:**
        *   在导航栏右侧添加 "Edit" 按钮 (`.navigationBarItems(trailing: EditButton())`)。
        *   点击 "Edit" 进入编辑模式：
            *   **非自定义 Provider (`isUserCreated == false`):** 只有 API Key 行可以交互（输入或查看/编辑）。其他所有信息行（名称、URL、API 配置等）保持只读。
            *   **自定义 Provider (`isUserCreated == true`):** 名称、网址、API Base URL、Endpoint Path、API Style 等字段变为可编辑的 `TextField` 或 `Picker`。API Key 行逻辑不变。
        *   编辑模式下，导航栏按钮变为 "Done"。点击 "Done" 退出编辑模式（保存逻辑由 ViewModel 处理）。
    *   **创建模式:**
        *   当视图以创建模式启动时（例如，从 `ModelsView` 的 "+" 菜单导航而来），所有字段（名称、网址、API 配置等）都应为空或提供默认值，且处于可编辑状态。
        *   导航栏不显示 "Edit" 按钮，而是显示 "Cancel" 和 "Save" 按钮。
        *   内部标记 `isUserCreated = true`。
    *   **UI 细节:**
        *   所有行内容（特别是 URL 和路径）应使用 `.lineLimit(1)` 避免折行。
    *   **占位符替换:** 在 `ModelsView.swift` 中，将 `providerSectionHeader` 的 `NavigationLink` 指向 `ProviderDetailView(providerId: provider.id)`，替换现有的 `Text("Provider Detail View...")` 占位符。
16. **实现模型详情 UI (`ModelDetailView`):**
    *   **目标:** 创建 `ModelDetailView.swift`，用于展示 `LLMModel` 的详细信息，并为用户自定义模型提供编辑功能。
    *   **输入:** 接收 `LLMModel.id` 作为输入参数。
    *   **数据源 (MVP/UI 阶段):** 暂时使用来自 `BuiltinLLMInitializer.swift` 的预置数据或类似的 Mock 数据结构来填充界面，模拟从 Repository 获取 `LLMModel` 及其关联的 `LLMProvider` 信息。
    *   **UI 布局与风格 (参考 `ProviderDetailView.swift` 和 iOS 设置/通讯录详情):**
        *   **整体结构:** 使用 `Form` 承载主要的结构化信息 Section，最后可能使用 `List` 或 `Section` 放置操作按钮。
        *   **顶部区域 (`Section`):**
            *   显著展示模型 Logo (`LogoImage`) 和模型名称 (`name`)。Logo 尺寸比列表中的略大。
            *   在名称下方用稍小字体显示模型描述 (`modelDescription`)，允许最多显示 2-3 行，超出部分截断。
        *   **核心信息 Section (`Form`):**
            *   **Provider 行:** 左侧显示 "Provider"，右侧显示关联的 `LLMProvider` 的 Logo 和名称。**此行需要可点击**，点击后导航至对应的 `ProviderDetailView(providerId: model.providerId)`。
            *   **Model Identifier 行:** 左侧显示 "Identifier"，右侧显示 `modelIdentifier`。
            *   **Pricing Info 行:** 左侧显示 "Pricing"，右侧显示 `pricingInfo`。
        *   **能力 Section (`Form`):**
            *   **Modalities 行:** 左侧显示 "Modalities"，右侧并列显示：
                *   "Input:" 后跟代表 `inputModalities` 的 SF Symbol 图标（如 `text.bubble`, `photo`, `waveform`, `video`, `doc.text`）。
                *   "Output:" 后跟代表 `outputModalities` 的 SF Symbol 图标。
            *   **Context Window 行:** 左侧显示 "Context Window"，右侧显示格式化后的 `contextWindowSize` (例如 "128,000 tokens")。
            *   **Max Output Tokens 行:** 左侧显示 "Max Output"，右侧显示格式化后的 `maxOutputTokens`。
            *   **Thinking Capabilities 行:** 左侧显示 "Thinking"，右侧根据 `thinkingCapabilities.controlType` 显示简短描述，例如 "Supported", "Budget Based", "Prompt Based", "Not Supported"。
        *   **可用实例区域 (`List` Section):**
            *   **Section Header:** 标题为 "Available Instances"。右侧放置一个 `Image(systemName: "plus")` 按钮，点击后应导航到 `InstanceDetailView` 以创建新的自定义模型（与此 Model 关联）。
            *   **实例列表:** 展示与此 Model 关联的所有 `LLMInstance`。
                *   每行显示 `LLMInstance` 的名称 (`name`)，显示 Logo。
                *   点击任意模型行，导航至 `InstanceDetailView`，并传递 `model.id`，目的是基于此模型创建新的 `LLMInstance`。
    *   **编辑模式 (与 `ProviderDetailView` 类似):**
        *   在导航栏右侧添加 "Edit"/"Done" 按钮 (`EditButton()`)。
        *   **仅当 `model.isUserCreated == true` 时**，以下字段在编辑模式下变为可交互控件：
            *   顶部区域的 `name` (`TextField`) 和 `modelDescription` (`TextEditor`)。
            *   核心信息 Section 的 `modelIdentifier` (`TextField`) 和 `pricingInfo` (`TextField`)。
            *   能力 Section 的 `contextWindowSize` (`TextField` - 数字输入) 和 `maxOutputTokens` (`TextField` - 数字输入)。(Modalities 和 Thinking Capabilities 暂时保持只读，或根据后续需求设计更复杂的编辑控件)。
        *   **非自定义模型 (`isUserCreated == false`)** 在编辑模式下所有字段保持只读。
        *   导航栏提供 "Cancel" 按钮用于取消编辑或创建。
    *   **样式细节:**
        *   确保所有信息行内容在 `HStack` 中使用 `Spacer()` 对齐，文本使用 `.lineLimit(1)` 避免折行。
        *   使用 `.foregroundColor(.secondary)` 区分标签和数值。
    *   **占位符替换:**
        *   在 `ProviderDetailView.swift` 的 "Available Models" Section 中，修改 `ForEach` 循环内的 `NavigationLink`，使其 `destination` 指向 `ModelDetailView(modelId: model.id)`。
17. **实现模型实例详情/编辑 UI (`InstanceDetailView`):**
    *   **目标:** 创建 `InstanceDetailView.swift`，用于创建和编辑 `LLMInstance`。
    *   **输入:**
        *   接收 `instanceId: UUID?` (用于查看/编辑现有实例) 或 `modelId: UUID?` (用于基于特定模型创建新实例)。
        *   **创建模式:** 当 `instanceId` 为 `nil` 且 `modelId` 非 `nil` 时。
        *   **编辑/查看模式:** 当 `instanceId` 非 `nil` 时。
    *   **数据源 (MVP/UI 阶段):** 使用 `BuiltinLLMInitializer.swift` 或类似的 Mock 数据结构来填充界面，模拟从 Repository 获取 `LLMInstance` 及其关联的 `LLMModel` 和 `LLMProvider` 信息。
    *   **布局与风格 (参考 `ProviderDetailView`/`ModelDetailView`、WhatsApp 联系人详情、iOS 设置):**
        *   **整体结构:** 使用 `Form` 承载主要的结构化信息 Section，顶部可以放置突出的 Logo 和名称，中间穿插一个横向按钮 Section，底部可能使用 `List` 展示动态参数。
        *   **导航栏:**
            *   根据模式（查看/编辑/创建）显示不同的标题和按钮（Edit/Done/Save/Cancel）。
            *   保持标题居中显示。
    *   **界面 Sections:**
        *   **顶部 Header Section (`Form` 的 `Section`):**
            *   显著展示模型 Logo (优先使用自定义 `customLogoData`，否则回退到关联 `LLMModel` 的 Logo) 和实例名称 (`name`)。Logo 尺寸应大于列表中的图标 (e.g., 48x48)。
        *   **横向动作按钮 Section (`Section` 内嵌 `HStack` 或自定义布局):**
            *   **显示模式:** 默认显示 "Duplicate" (复制实例), "Favorite" (收藏/取消收藏，用星形图标表示状态), "Share" (分享实例配置) 按钮。按钮应包含图标和简洁的文字标签。
            *   **UI风格:** 参考WhatsApp联系人界面横向动作按钮, 每个按钮是一个单独的圆角矩形，按钮宽度是根据页面宽度等分得来（互相保留一定的padding）
            *   **编辑模式:** 在上述按钮基础上， 在右侧添加一个红色的 "Delete Instance" (删除实例) 按钮。
        *   **核心信息 Section (`Form`):**
            *   **Provider 行:** 左侧显示 "Provider"，右侧显示关联 `LLMProvider` 的 Logo 和名称。**此行需要可点击**，点击后导航至对应的 `ProviderDetailView(providerId: instance.providerId)`。
            *   **Model 行:** 左侧显示 "Model"，右侧显示关联 `LLMModel` 的 Logo 和名称。**此行需要可点击**，点击后导航至对应的 `ModelDetailView(modelId: instance.modelId)`。
            *   **Prompt Tokens Usage 行 (仅查看模式):** ：显示 `totalPromptTokensUsed` 的格式化数值。
            *   **Completion Tokens Usage 行 (仅查看模式):** ：显示 `totalCompletionTokensUsed` 的格式化数值。
        *   **默认参数 Section (`List` - 因为内容动态):**
            *   **Section Header:** 标题为 "Parameters"。Header 右侧有 "+" 按钮，用于添加新的参数。
            *   **UI参考:** iOS设置 -> 通用 -> 键盘 -> 自定义短语 设置逻辑。
            *   **参数列表:**
                *   使用 `List` 展示 `instance.defaultParameters` 字典中的键值对。
                *   **显示模式:** 
                    *   每行左侧显示参数标签，右侧显示参数值。对于 `system_prompt` 或较长的文本值，可以考虑显示预览并允许点击展开查看完整内容。
                    *   点击可以进入一个新的导航视图，允许用户编辑参数标签和参数值
                        *   优先使用Form展示，该视图右上角有保存按钮。
                        *   第一个section是参数标签和参数值编辑
                            *   两者都是文本编辑模式，就算输入的是数字或者bool，也按照文本处理。
                            *   参数值的编辑框支持多行编辑。
                        *   第二个部分是推荐的参数标签（可能不能用Form？）
                            *   类似很多app的interest picker UI，从左到右，从上到下，把参数标签放在流式布局的圆角按钮里。让用户可以点击
                            *   点击后，会把参数标签换成这个新的参数。
                            *   推荐的参数标签固定是`system_prompt`, `temperature`, `top_p`等一些常见的参数。
                *   **编辑模式:**
                    *   编辑模式下，在每行左侧添加一个红色圆内部-的按钮，点击后右侧划出List的自带的删除确认按钮，正常显示模式如果向左滑也可直接唤出带的删除确认按钮进行删除。
                *   **添加新参数:**
                    *   点击 "+" 按钮时，弹出显示模式同款导航视图用于添加新参数。
            *   **空状态:** 如果没有默认参数，显示提示信息。
    *   **UI 细节:**
        *   确保 `Form` Section 中的所有行内容（标签和值）在 `HStack` 中使用 `Spacer()` 对齐，文本值使用 `.lineLimit(1)` 避免折行，除非是明确需要多行显示的（如 System Prompt 预览）。
        *   使用 `.foregroundColor(.secondary)` 区分标签和数值/控件。
    *   **编辑/创建模式逻辑:**
        *   实现与 `ProviderDetailView` 和 `ModelDetailView` 类似的 `EditButton()` / "Done" / "Save" / "Cancel" 逻辑。
        *   所有实例都允许用户编辑。
    *   **占位符替换:**
        *   **`ModelDetailView.swift`:** 在 "Available Instances" Section 的 Header 中，将 `NavigationLink` 指向 `InstanceDetailView(modelId: model.id)`，替换现有的占位符。
        *   **`ModelsView.swift`:** 在实例行 (`LLMInstance` row) 上添加 `NavigationLink`，使其 `destination` 指向 `InstanceDetailView(instanceId: instance.id)`，替换现有的占位符。
18. **实现模型管理 Use Cases:**
    *   **目标:** 在 `Domain/UseCases/ModelManagement/` 目录下创建 Use Case 文件，封装模型管理相关的业务逻辑。
    *   **结构:** 采用子文件夹结构组织 Use Cases，按实体类型划分：
        *   `Domain/UseCases/ModelManagement/Provider/`
        *   `Domain/UseCases/ModelManagement/Model/`
        *   `Domain/UseCases/ModelManagement/Instance/`
        *   `Domain/UseCases/ModelManagement/Group/` (MVP 范围外，创建占位)
    *   **职责:** 每个 Use Case 类负责一个具体的、单一的应用场景（例如，获取提供商列表、保存一个实例、管理 API Key 等）。Use Cases 接收领域对象或简单参数，调用一个或多个 Repository 接口来执行操作，并返回结果（领域对象或状态）。
    *   **基于 UI 和计划识别的核心 Use Cases (MVP):**
        *   **Provider:**
            *   `GetProviderUseCase`: 根据 ID 获取单个 `LLMProvider` 的详细信息。
            *   `GetAllProvidersUseCase`: 获取所有 `LLMProvider` 的列表（可能需要区分用户创建和内置的）。
            *   `CreateProviderUseCase`: 创建一个新的用户自定义 `LLMProvider`。
            *   `UpdateProviderUseCase`: 更新一个已存在的 `LLMProvider` 信息。需要处理内置 Provider 和用户自定义 Provider 的不同更新逻辑（例如，内置 Provider 的某些字段不允许用户修改，除了 API Key）。
            *   `DeleteProviderUseCase`: 删除一个用户自定义的 `LLMProvider` (不允许删除内置 Provider)。需要处理关联 `LLMModel` 和 `LLMInstance` 的级联删除或状态更新逻辑。
            *   `SaveProviderAPIKeyUseCase`: 安全地将用户提供的 API Key 存储到 Keychain 中，并更新对应 `LLMProvider` 的 `apiKeyStored` 状态。
            *   `ClearProviderAPIKeyUseCase`: 从 Keychain 中移除指定 `LLMProvider` 的 API Key，并更新 `apiKeyStored` 状态。
            *   `GetProviderAPIKeyUseCase`: 安全地从 Keychain 中读取指定 `LLMProvider` 的 API Key（注意：谨慎暴露，UI 通常只显示状态或在验证后显示）。
        *   **Model:**
            *   `GetModelUseCase`: 根据 ID 获取单个 `LLMModel` 的详细信息。
            *   `GetModelsForProviderUseCase`: 获取指定 `LLMProvider` 下的所有 `LLMModel`。
            *   `CreateModelUseCase`: 创建一个用户自定义的 `LLMModel`，关联到指定的 `LLMProvider`。
            *   `UpdateModelUseCase`: 更新一个已存在的 `LLMModel` 信息（区分内置和用户自定义模型的更新逻辑）。
            *   `DeleteModelUseCase`: 删除一个用户自定义的 `LLMModel`，处理关联 `LLMInstance` 的逻辑。
        *   **Instance:**
            *   `GetInstanceUseCase`: 根据 ID 获取单个 `LLMInstance` 的详细信息。
            *   `GetInstancesForModelUseCase`: 获取指定 `LLMModel` 下的所有 `LLMInstance`。
            *   `GetAllInstancesUseCase`: 获取所有的 `LLMInstance`，供列表使用（可能需要组合 Provider 信息）。
            *   `CreateInstanceUseCase`: 创建一个新的 `LLMInstance`，关联到指定的 `LLMModel`。
            *   `UpdateInstanceUseCase`: 更新一个已存在的 `LLMInstance` 信息（名称、参数、收藏、Logo 等）。
            *   `DeleteInstanceUseCase`: 删除一个 `LLMInstance`。
            *   `DuplicateInstanceUseCase`: 复制一个现有的 `LLMInstance`，创建新的副本。
            *   `ToggleInstanceFavoriteUseCase`: 切换 `LLMInstance` 的 `isFavorited` 状态。
            *   `GetFavoritedInstancesUseCase`: 获取所有标记为收藏的 `LLMInstance`。
        *   **Group:**
            *   `CreateInstanceGroupUseCase`: 创建一个新的 `LLMInstanceGroup`。
            *   `GetInstanceGroupUseCase`: 获取单个 `LLMInstanceGroup` 的信息（包括其包含的 Instance 列表）。
            *   `GetAllInstanceGroupsUseCase`: 获取所有 `LLMInstanceGroup` 的列表。
            *   `UpdateInstanceGroupUseCase`: 更新一个 `LLMInstanceGroup`（名称、实例列表等）。
            *   `DeleteInstanceGroupUseCase`: 删除一个 `LLMInstanceGroup`。
            *   `AddInstanceToGroupUseCase`: 将一个实例添加到组中。
            *   `RemoveInstanceFromGroupUseCase`: 将一个实例从组中移除。
    *   **实现:** 创建对应的 Swift 文件和类/结构体定义, 实现具体功能。
    在 `implementation_plan` 的第 18 步中，关于错误管理的部分可以这样精炼地补充：
    *   **错误管理:**
        * 在 `Domain/Errors/` 下创建 `ModelManagementError.swift`，定义 `enum ModelManagementError: Error, Equatable`，包含Use Cases用到的ModelManagementError错误类型。
        * 在 `Domain/Errors/` 下创建 `KeychainError.swift`，定义 `enum KeychainError: Error, Equatable`，包含Use Cases用到的KeychainError错误类型。
        * 在 Use Cases 中抛出这些错误，确保错误类型清晰且可测试。
        * **本地化:** 错误描述（如 `String(localized: "xxx")`）不在 Domain 层实现，由 Presentation 层处理（之后步骤实现）。
19. **实现 Provider 详情 ViewModel (`ProviderDetailViewModel`):**
    *   **目标:** 在 `Presentation/ViewModels/ModelManagement/ProviderDetailViewModel.swift` 创建 `ProviderDetailViewModel` 类，遵循 `ObservableObject` 协议，负责为 `ProviderDetailView` 提供数据和业务逻辑，并与领域层的 Use Cases 交互。
    *   **职责:**
        *   管理视图状态，包括加载状态 (`isLoading`)、错误信息 (`errorMessage`)、编辑模式 (`@Published var isEditing`)、API Key 输入 (`@Published var apiKeyInput`) 和显示状态 (`@Published var showFullApiKey`)。
        *   加载当前展示或编辑的 Provider 详情。
        *   加载 Provider 关联的模型列表。
        *   处理视图的编辑/查看/创建模式切换。
        *   管理 API Key 的输入、安全获取（需认证）、显示、保存和清除逻辑。
        *   响应用户操作（保存、取消、编辑、删除 Provider），调用相应的 Use Cases。
        *   管理加载状态和错误信息。
    *   **核心属性 (`@Published`):**
        *   `provider: LLMProvider?`: 当前正在查看、编辑或创建的 Provider 数据。对于编辑模式，考虑维护一个临时的可变副本以隔离更改。
        *   `associatedModels: [LLMModel]`: 与当前 Provider 关联的模型列表。
        *   `isLoading: Bool`: 指示是否正在加载数据。
        *   `errorMessage: String?`: 用于向视图显示错误信息。
        *   `isEditing: Bool`: 控制视图是否处于编辑模式 (应与 SwiftUI 的 `EditMode` 环境值或视图状态同步)。
        *   `apiKeyInput: String`: 绑定到 `SecureField` 用于 API Key 输入。
        *   `canShowFullApiKey: Bool`: 控制是否显示完整的 API Key（在生物识别/密码验证成功后）。
        *   `retrievedApiKey: String?`: 存储验证后获取的 API Key 字符串，用于显示。
    *   **输入/状态:**
        *   `providerId: UUID?`: 在初始化时传入，决定是加载现有 Provider 还是创建新 Provider。
        *   `isCreatingNewProvider: Bool`: 根据 `providerId == nil` 计算得出。
    *   **初始化逻辑:**
        *   **依赖注入:**
            *   **原则:** ViewModel 通过构造函数注入其依赖的 Use Cases。
            *   **实现:** 
                *   ViewModel 的 `init` 方法接收 `GetProviderUseCase`, `GetModelsForProviderUseCase`, `UpdateProviderUseCase`, `CreateProviderUseCase`, `SaveProviderAPIKeyUseCase`, `GetProviderAPIKeyUseCase`, `ClearProviderAPIKeyUseCase`, `DeleteProviderUseCase` 等相关 Use Case 的实例。
                *   **DI 流程:** ViewModel 的实例**不应**由 `ProviderDetailView` 直接创建。而是由 `DIContainer` 通过其持有的 `ViewModelsFactory` 负责创建。`ViewModelsFactory` 会负责创建所有必需的 Use Case 实例，并将它们注入到 `ProviderDetailViewModel` 的构造函数中。`ProviderDetailView` 将从其 SwiftUI 环境 (`@EnvironmentObject`) 中获取 `DIContainer` 实例，然后调用 `container.makeProviderDetailViewModel(providerId: self.providerId)` 来获取已配置好的 ViewModel 实例。
        *   **区分模式和数据加载:**
            *   ViewModel 接收 `providerId: UUID?`。
            *   **查看/编辑模式 (`providerId` 非 `nil`):** 在 `init` 中调用 `loadProviderDetails()` 方法。
            *   **创建模式 (`providerId` 为 `nil`):** 在 `init` 中调用 `loadProviderDetails()` 方法并设置 `isEditing = true` 和 `isCreatingNewProvider = true`。
    *   **核心功能实现 (调用 Use Cases):**
        *   loadProviderDetails(): (async) 当视图出现时调用此异步函数。如果传入了 `providerId`，它会调用 `GetProviderUseCase` 来获取指定的 `LLMProvider` 实体数据。如果 `providerId` 为 nil（表示创建新 Provider），则初始化一个空的、`isUserCreated = true` 的 `LLMProvider` 实例作为基础。然后，对于已存在的 Provider，它会调用 `GetModelsForProviderUseCase` 来获取与该 Provider 关联的所有 `LLMModel` 列表。同时，如果 Provider 类型是 `userApiKey` 并且 `apiKeyStored` 为 true，它会调用 `GetProviderAPIKeyUseCase` 尝试安全地获取 API Key（用于后续可能的显示或编辑，获取过程本身可能涉及 Touch ID/Face ID，ViewModel 需要处理这个流程或状态）。最后，将获取到的 Provider、模型列表和 API Key 状态（是否已获取或是否存在）更新到 ViewModel 的 `@Published` 属性，驱动 UI 更新。
        *   loadAssociatedModels(): (async) 此异步函数负责获取并更新与当前 Provider 关联的可用 `LLMModel` 列表。它会调用 `GetModelsForProviderUseCase`，传入当前的 `providerId` (如果存在)。获取到的模型列表会更新到 ViewModel 的 `@Published` 属性，供视图中的 "Available Models" Section 展示。在编辑或创建 Provider 成功后，可能也需要调用此函数刷新模型列表。
        *   saveChanges(): (async) 当用户在编辑模式下点击 "Done" 或在创建模式下点击 "Save" 时调用此异步函数。函数首先会进行输入验证（例如，Provider 名称不能为空）。如果验证通过：
            *   **创建模式 (`providerId` 为 nil):** 调用 `CreateProviderUseCase`，传入当前 ViewModel 中编辑好的 `LLMProvider` 数据（确保 `isUserCreated` 和 `isUserModified` 设置正确）。如果用户在创建时输入了 API Key (`apiKeyInput` 不为空)，则在成功创建 Provider 后，**立即**调用 `SaveProviderAPIKeyUseCase` 来安全地存储该 Key。
            *   **编辑模式 (`providerId` 不为 nil):** 调用 `UpdateProviderUseCase`，传入包含修改后数据的 `LLMProvider` 实体（注意更新 `isUserModified = true`）。如果用户在编辑过程中修改或输入了 API Key (`apiKeyInput` 不为空或 `showFullApiKey` 状态被更改过)，则根据情况调用 `SaveProviderAPIKeyUseCase` 或 `ClearProviderAPIKeyUseCase`。
            *   保存成功后，将编辑模式状态 (`isEditing`) 设为 `false`，并可能触发 `loadProviderDetails` 或 `loadAssociatedModels` 来刷新显示的数据。需要处理 Use Case 可能抛出的错误，并更新 UI 以向用户显示错误信息。
        *   deleteProvider(): (async) 当用户在编辑模式下点击 "Delete Instance" 按钮并确认后调用此异步函数。它会调用 `DeleteProviderUseCase`，传入当前的 `providerId`。删除成功后，需要协调导航逻辑，例如将用户导航回 `ModelsView`。同样需要处理可能的错误并通知用户。
        *   requestApiKeyAccess(): (async) 当用户在查看模式下点击 API Key 行（当 `apiKeyStored == true` 且 `showFullApiKey == false` 时）尝试查看完整 Key 时调用此函数。它内部会调用 `GetProviderAPIKeyUseCase`，这个 Use Case 会负责触发系统的 Touch ID/Face ID 验证。ViewModel 需要处理 Use Case 返回的结果：如果验证成功并获取到 Key，则更新 `apiKeyInput` 并将 `showFullApiKey` 设为 `true`；如果验证失败或用户取消，则不改变显示状态或给出提示。
        *   saveApiKey(): (async) 当用户在编辑模式下修改了 `apiKeyInput`，并在点击 "Done" 时触发 `saveChanges()` 后间接调用，或者可以在 `apiKeyInput` 失去焦点时触发。此函数负责调用 `SaveProviderAPIKeyUseCase`，传入 `providerId` 和 `apiKeyInput` 中的值，将新的 API Key 安全地存储到 Keychain 中。成功后需要更新 `providerData.apiKeyStored` 状态为 `true`。
        *   clearApiKey(): (async) 当用户在编辑模式下主动清除 API Key（例如，将 `apiKeyInput` 置空）并在点击 "Done" 时触发 `saveChanges()` 后间接调用。此函数负责调用 `ClearProviderAPIKeyUseCase`，传入 `providerId`，从 Keychain 中移除对应的 API Key。成功后需要更新 `providerData.apiKeyStored` 状态为 `false`。
        *   handleUrlOpening(url: URL): 处理点击 "Website" 或 "API Documentation" 链接的逻辑。此函数可能不需要异步，它会调用系统服务（如 `UIApplication.shared.open`）来打开传入的 URL。
        *   updateLocalProviderData(field: Field, value: Value): 这个同步函数在用户编辑 `TextField`, `Picker`, `Toggle` 等控件时被调用，用于实时更新 ViewModel 中临时的 `providerData` 状态。例如，当用户修改 Provider 名称时，View 会调用此函数更新 ViewModel 中的 `providerData.name`。这确保了 `saveChanges()` 时使用的是最新的编辑内容。
        *   prepareForCreateNewModel(): 当用户点击 "Available Models" Section Header 右侧的 "+" 按钮时，View 的 `NavigationLink` 触发导航前可以调用此函数。此函数主要用于准备传递给 `ModelDetailView` 的上下文信息，确保是以“基于当前 Provider 创建新 Model”的模式启动。它可能不需要直接调用 Use Case，而是设置一些传递给下一个视图的状态。
    *   **SwiftUI Preview Mocking:**
        *   **核心策略:** Mocking 在 Repository 层通过 `RepositoriesFactory` 和 `#if DEBUG` 实现。ViewModel 和 Use Case 无需关心 Mock 细节。
        *   **PreviewProvider 实现:**
            1.  在 `ProviderDetailView_Previews` 结构体内部，创建一个标准的 `DIContainer` 实例。
                *   由于 SwiftUI Previews 在 `DEBUG` 配置下运行，`RepositoriesFactory` (由 `DIContainer` 持有) 会自动创建并返回 `MockLLMInstanceRepository` 和 `MockKeychainRepository` 的实例。
            2.  将这个 `DIContainer` 实例注入到预览的 `ProviderDetailView` 的环境中: `.environmentObject(DIContainer(context: PersistenceController.preview.container.viewContext))` （注意: Preview 可能需要一个特定的 Core Data context，例如 `PersistenceController.preview`）。
            3.  `ProviderDetailView` 的 `init` 或 `.onAppear` 逻辑会使用注入的 `DIContainer` 来创建 `ProviderDetailViewModel` 实例。
            4.  因为 `DIContainer` 提供了基于 Mock Repositories 的 Use Cases，所以 `ProviderDetailViewModel` 会自动使用来自 `MockLLMInstanceRepository` 和 `MockKeychainRepository` 的预设假数据进行工作。
    *   **与 View 集成:**
        *   修改 `ProviderDetailView`：移除所有 `@State` 驱动的数据。
        *   在 `ProviderDetailView` 中添加 `@StateObject var viewModel: ProviderDetailViewModel` 属性。
        *   **注入方式:** 在 `ProviderDetailView` 的 `init` 方法或 `body` 的 `.onAppear` 中（或直接在 `init` 中），从 `@EnvironmentObject var container: DIContainer` 获取 `DIContainer` 实例，并使用 `container.makeProviderDetailViewModel(providerId: self.providerId)` 来初始化 `viewModel`。确保 `viewModel` 的生命周期与视图正确绑定。
        *   视图的数据绑定改为读取 `viewModel` 的 `@Published` 属性。
        *   视图的交互改为调用 `viewModel` 的方法。
        *   根据 `viewModel.errorMessage` 显示错误提示。
        *   编辑状态和导航栏按钮由 `viewModel` 控制，将 `ProviderDetailView` 中原有的 `isEditing` `@State` 移除，完全由 `viewModel.isEditing` 控制。
20. **实现模型详情 ViewModel (`ModelDetailViewModel.swift`)**
    *   **目标:** 创建 `ModelDetailViewModel.swift`，负责处理 `ModelDetailView` 的业务逻辑，包括加载模型数据、处理编辑、创建新模型以及与用户交互。
    *   **输入参数:**
        *   `modelId: UUID?`: 用于查看/编辑现有 `LLMModel`。如果为 `nil`，则表示创建新模型。
        *   `providerIdForNewModel: UUID?`: 仅在创建新模型 (`modelId` 为 `nil`) 时使用，指定新模型所属的 `LLMProvider` 的 ID。
    *   **核心职责:**
        *   根据 `modelId` 加载并管理 `LLMModel` 的详细信息。
        *   如果正在创建新模型，根据 `providerIdForNewModel` 初始化待创建的 `LLMModel` 框架。
        *   加载并展示与当前 `LLMModel` 关联的 `LLMProvider` 的信息（如名称、Logo），以便用户了解模型来源并提供导航。
        *   加载并展示基于当前 `LLMModel` 创建的 `LLMInstance` 列表。
        *   处理用户创建新 `LLMModel`（如果 `modelId` 为 `nil` 且 `providerIdForNewModel` 已提供）或编辑现有 `LLMModel`（仅限 `isUserCreated == true` 的模型）的逻辑。
        *   提供保存更改、取消编辑的功能。
        *   (可选，但推荐) 提供删除用户创建的 `LLMModel` 的功能 (仅对 `isUserCreated == true` 的模型)。
        *   管理视图的加载状态 (`isLoading`) 和错误信息 (`errorMessage`)。
    *   **主要属性 (`@Published`):**
        *   `model: LLMModel?`: 当前查看或编辑的 `LLMModel` 实例 (从数据层加载)。
        *   `editableModel: LLMModel`: 用于表单绑定的可编辑 `LLMModel`副本。在创建新模型时，这是一个空白模板；在编辑时，这是 `model` 的一个副本。
        *   `associatedProvider: LLMProvider?`: 当前 `LLMModel` 所属的 `LLMProvider`。
        *   `associatedInstances: [LLMInstance] = []`: 基于当前 `LLMModel` 创建的 `LLMInstance` 列表。
        *   `isLoading: Bool = false`: 指示是否正在加载数据。
        *   `errorMessage: String? = nil`: 用于向用户显示错误信息。
        *   `isEditing: Bool = false`: 指示当前是否处于编辑模式。
        *   `isCreatingNewModel: Bool`: 计算属性，通过 `modelId == nil` 判断是否为创建模式。
    *   **依赖的 Use Cases (通过构造函数注入):**
        *   `GetModelUseCaseProtocol`: 用于根据 `modelId` 获取 `LLMModel`。
        *   `GetProviderUseCaseProtocol`: 用于根据 `LLMModel.providerId` 或 `providerIdForNewModel` 获取 `LLMProvider` 的详细信息。
        *   `GetInstancesForModelUseCaseProtocol`: 用于获取特定 `LLMModel` 下的所有 `LLMInstance`。
        *   `CreateModelUseCaseProtocol`: 用于创建新的用户自定义 `LLMModel`。
        *   `UpdateModelUseCaseProtocol`: 用于更新现有的用户自定义 `LLMModel`。
        *   `DeleteModelUseCaseProtocol`: 用于删除用户自定义的 `LLMModel` (应检查模型是否仍被实例使用)。
    *   **主要方法:**
        *   `init(modelId: UUID?, providerIdForNewModel: UUID?, getModelUseCase: GetModelUseCaseProtocol, getProviderUseCase: GetProviderUseCaseProtocol, getInstancesForModelUseCase: GetInstancesForModelUseCaseProtocol, createModelUseCase: CreateModelUseCaseProtocol, updateModelUseCase: UpdateModelUseCaseProtocol, deleteModelUseCase: DeleteModelUseCaseProtocol)`:
            *   保存 `modelId` 和 `providerIdForNewModel`。
            *   注入所有必要的 Use Case。
            *   如果 `modelId` 为 `nil` (创建模式):
                *   设置 `isCreatingNewModel = true` 和 `isEditing = true`。
                *   基于 `providerIdForNewModel` 初始化一个 `editableModel` 的基本结构 (例如，设置 `providerId`，`isUserCreated = true`，其他字段为空或默认值)。
                *   异步调用 `loadAssociatedProviderDetails(providerId: providerIdForNewModel!)`。
            *   如果 `modelId` 存在 (查看/编辑模式):
                *   设置 `isCreatingNewModel = false` 和 `isEditing = false`。
                *   异步调用 `loadModelDetails()`。
        *   `loadModelDetails()`: (在查看/编辑模式下调用)
            *   设置 `isLoading = true`。
            *   使用 `GetModelUseCase` 获取 `LLMModel`。
            *   成功获取后，设置 `self.model` 和 `self.editableModel`。
            *   调用 `loadAssociatedProviderDetails(providerId: model.providerId)`。
            *   调用 `loadAssociatedInstances(modelId: model.id)`。
            *   捕获任何错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
        *   `loadAssociatedProviderDetails(providerId: UUID)`:
            *   使用 `GetProviderUseCase` 获取 `LLMProvider`，并更新 `associatedProvider`。
        *   `loadAssociatedInstances(modelId: UUID)`:
            *   使用 `GetInstancesForModelUseCase` 获取 `LLMInstance` 列表，并更新 `associatedInstances`。
        *   `saveChanges()`:
            *   执行 `validateInput()` 进行输入验证。
            *   设置 `isLoading = true`。
            *   如果 `isCreatingNewModel`:
                *   调用 `CreateModelUseCase` 保存 `editableModel`。
                *   成功后，更新 `self.model` 为返回的新模型，重置 `isCreatingNewModel = false`。
            *   否则 (编辑模式):
                *   调用 `UpdateModelUseCase` 保存 `editableModel`。
                *   成功后，更新 `self.model`。
            *   如果操作成功，设置 `isEditing = false`。
            *   捕获任何错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
        *   `deleteModel()`: (如果提供此功能)
            *   确保 `model` 存在且 `model.isUserCreated == true`。
            *   调用 `DeleteModelUseCase`。
            *   处理成功（例如，视图应被 dismiss）或失败（设置 `errorMessage`）。
        *   `prepareForEditing()`:
            *   如果 `isCreatingNewModel` 为 `false` 且 `model` 存在，则确保 `editableModel` 是 `model` 的最新副本。
            *   设置 `isEditing = true`。
        *   `cancelEditing()`:
            *   如果 `isCreatingNewModel` 为 `false` 且 `model` 存在，则将 `editableModel` 重置为 `model` 的状态。
            *   设置 `isEditing = false`。
            *   清除 `errorMessage`。
            *   如果正在创建新模型时取消，ViewModel 的行为可能需要视图协调（例如，`dismiss` 视图）。
        *   `validateInput() -> Bool`:
            *   验证 `editableModel` 的必填字段 (如 `name`, `modelIdentifier`) 是否为空。
            *   验证数值字段 (如 `contextWindowSize`, `maxOutputTokens`) 是否有效。
            *   如果验证失败，设置 `errorMessage` 并返回 `false`。
            *   返回 `true` 表示通过验证。
        *   `private func handleError(_ error: Error)`: 统一处理错误，更新 `errorMessage`。
    *   **与 `ProviderDetailViewModel` 的主要区别与注意事项**:
        *   **不处理 API Key**: `ModelDetailViewModel` 不直接管理 API Key，这是 `LLMProvider` 的责任。
        *   **可编辑字段**: `editableModel` 包含 `LLMModel` 的属性，如 `name`, `modelDescription`, `modelIdentifier` (对于用户创建的模型), `logoImageName` (或 `customLogoData`), `contextWindowSize`, `maxOutputTokens`, `pricingInfo` 等。预置模型 (`isUserCreated == false`) 的核心属性（如 `modelIdentifier`, `contextWindowSize` 等）通常是只读的，编辑模式下这些字段应禁用或不显示编辑控件。
        *   **导航**: `ModelDetailView` 会包含到其关联 `ProviderDetailView` 的导航链接，以及创建基于此模型的新 `InstanceDetailView` 的入口。这些导航逻辑由 View 层处理，ViewModel 提供必要的数据。
    *   **预览和 Mock 数据**:
        *   为了方便 SwiftUI Preview，ViewModel 的 `init` 可以有条件地使用 Mock Repository (通过 `DIContainer` 注入)。
        *   Preview 时，可以传入 `MockLLMInstanceRepository` 中定义的现有 `LLMModel` 的 ID，或 `nil` ID 配上一个 `providerIdForNewModel` 来测试创建流程。
21. **实现实例详情 ViewModel (`InstanceDetailViewModel.swift`)**
    *   **目标:** 创建 `InstanceDetailViewModel.swift`，负责处理 `InstanceDetailView` 的业务逻辑，包括加载 LLM 实例数据、处理编辑、创建新实例、以及响应用户交互（如收藏、复制、删除）。
    *   **输入参数:**
        *   `instanceId: UUID?`: 用于查看/编辑现有 `LLMInstance`。如果为 `nil`，则表示创建新实例。
        *   `modelIdForNewInstance: UUID?`: 仅在创建新实例 (`instanceId` 为 `nil`) 时使用，指定新实例基于的 `LLMModel` 的 ID。
    *   **核心职责:**
        *   根据 `instanceId` 加载并管理 `LLMInstance` 的详细信息。
        *   如果正在创建新实例，根据 `modelIdForNewInstance` 初始化待创建的 `LLMInstance` 框架。
        *   加载并展示与当前 `LLMInstance` 关联的 `LLMModel` 的信息。
        *   加载并展示与 `LLMModel` 关联的 `LLMProvider` 的信息。
        *   处理用户创建新 `LLMInstance` 或编辑现有 `LLMInstance` 的逻辑。
        *   提供保存更改、取消编辑、复制实例、切换收藏状态和删除实例的功能。
        *   管理视图的加载状态 (`isLoading`) 和错误信息 (`errorMessage`)。
        *   响应用户在编辑模式下通过选择器更改基础模型 (`editableInstance.modelId`) 或基础提供商的事件，并相应地更新 `associatedModel` 和 `associatedProvider`。
    *   **主要属性 (`@Published`):**
        *   `instance: LLMInstance?`: 当前查看或编辑的 `LLMInstance` 实例 (从数据层加载)。
        *   `editableInstance: LLMInstance`: 用于表单绑定的可编辑 `LLMInstance` 副本。在创建新实例时，这是一个基于 `modelIdForNewInstance` 的模板；在编辑时，这是 `instance` 的一个副本。
        *   `associatedModel: LLMModel?`: 当前 `LLMInstance` 所基于的 `LLMModel`。
        *   `associatedProvider: LLMProvider?`: `associatedModel` 所属的 `LLMProvider`。
        *   `isLoading: Bool = false`: 指示是否正在加载数据。
        *   `errorMessage: String? = nil`: 用于向用户显示错误信息。
        *   `isEditing: Bool = false`: 指示当前是否处于编辑模式。
        *   `isCreatingNewInstance: Bool`: 计算属性，通过 `instanceId == nil` 判断是否为创建模式。
    *   **依赖的 Use Cases (通过构造函数注入):**
        *   `GetInstanceUseCaseProtocol`: 用于根据 `instanceId` 获取 `LLMInstance`。
        *   `GetModelUseCaseProtocol`: 用于根据 `LLMInstance.modelId` 或 `modelIdForNewInstance` 获取 `LLMModel`。
        *   `GetProviderUseCaseProtocol`: 用于根据 `LLMModel.providerId` 获取 `LLMProvider`。
        *   `CreateInstanceUseCaseProtocol`: 用于创建新的 `LLMInstance`。
        *   `UpdateInstanceUseCaseProtocol`: 用于更新现有的 `LLMInstance`。
        *   `DeleteInstanceUseCaseProtocol`: 用于删除 `LLMInstance`。
        *   `DuplicateInstanceUseCaseProtocol`: 用于复制 `LLMInstance`。
        *   `ToggleInstanceFavoriteUseCaseProtocol`: 用于切换 `LLMInstance` 的收藏状态。
    *   **主要方法:**
        *   `init(instanceId: UUID?, modelIdForNewInstance: UUID?, getInstanceUseCase: GetInstanceUseCaseProtocol, ..., toggleInstanceFavoriteUseCase: ToggleInstanceFavoriteUseCaseProtocol)`:
            *   保存 `instanceId` 和 `modelIdForNewInstance`。
            *   注入所有必要的 Use Case。
            *   如果 `instanceId == nil` (创建模式):
                *   设置 `isCreatingNewInstance = true` 和 `isEditing = true`。
                *   基于 `modelIdForNewInstance!` 初始化一个 `editableInstance` 的基本结构 (设置 `modelId`，`name` 可以默认为关联模型的名称，`defaultParameters` 可以为空或预设通用值，`isUserModified = true`)。
                *   异步调用 `loadAssociatedModelAndProviderDetails(modelId: modelIdForNewInstance!)`。
            *   如果 `instanceId` 存在 (查看/编辑模式):
                *   设置 `isCreatingNewInstance = false` 和 `isEditing = false`。
                *   异步调用 `loadInstanceDetails()`。
        *   `loadInstanceDetails()`: (在查看/编辑模式下调用)
            *   设置 `isLoading = true`。
            *   使用 `GetInstanceUseCase` 获取 `LLMInstance`。
            *   成功获取后，设置 `self.instance` 和 `self.editableInstance`。
            *   调用 `loadAssociatedModelAndProviderDetails(modelId: instance.modelId)`。
            *   捕获任何错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
        *   `loadAssociatedModelAndProviderDetails(modelId: UUID)`:
            *   设置 `isLoading = true` (或使用局部加载状态)。
            *   使用 `GetModelUseCase` 获取 `LLMModel`，并更新 `associatedModel`。
            *   如果成功获取模型，则使用 `model.providerId` 和 `GetProviderUseCase` 获取 `LLMProvider`，并更新 `associatedProvider`。
            *   捕获错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
                *   **`instanceProviderDidChange(newProviderId: UUID)`**: (新增方法)
            *   此方法在编辑模式下，当用户通过UI（例如 `ProviderSelectionView`）选择了新的提供商时被调用。
            *   设置 `isLoading = true`。
            *   使用 `GetProviderUseCase` 获取 `newProviderId` 对应的 `LLMProvider`，更新 `self.associatedProvider`。
            *   使用 `GetModelsForProviderUseCase` 获取该 `newProviderId` 下的所有 `LLMModel`，更新 `self.availableModelsForProvider`。
            *   如果 `availableModelsForProvider` 不为空:
                *   选择一个默认模型（例如列表中的第一个，或有 `isDefaultRecommendation` 标记的模型）。
                *   更新 `self.editableInstance.modelId` 为所选模型的 `id`。
                *   调用 `loadAssociatedModelAndProviderDetails(modelId: self.editableInstance.modelId)` 来刷新 `self.associatedModel`。
            *   如果 `availableModelsForProvider` 为空:
                *   设置 `self.associatedModel = nil`。
                *   设置 `self.editableInstance.modelId` 为一个表示无效或未选择的状态（可能需要UI配合）。
                *   设置 `errorMessage` 提示用户该提供商下无可用模型。
            *   捕获任何错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
        *   `instanceModelDidChange(newModelId: UUID)`:
            *   当 `editableInstance.modelId` 在编辑时被用户更改后（例如，通过 `ItemSelectionView`），此方法被调用。
            *   更新 `editableInstance.modelId = newModelId`。
            *   异步调用 `loadAssociatedModelAndProviderDetails(modelId: newModelId)` 以刷新 `associatedModel` 和 `associatedProvider`。
        *   `saveChanges()`:
            *   执行 `validateInput()` 进行输入验证。
            *   设置 `isLoading = true`。
            *   在保存前，确保 `editableInstance.isUserModified = true` (除非是首次创建且未做任何修改)。
            *   如果 `isCreatingNewInstance`:
                *   调用 `CreateInstanceUseCase` 保存 `editableInstance` (传递 `editableInstance.modelId`, `editableInstance.name`, `editableInstance.defaultParameters`, `editableInstance.customLogoData`)。
                *   成功后，更新 `self.instance` 为返回的新实例，重置 `isCreatingNewInstance = false`。
            *   否则 (编辑模式):
                *   调用 `UpdateInstanceUseCase` 保存 `editableInstance`。
                *   成功后，更新 `self.instance`。
            *   如果操作成功，设置 `isEditing = false`。
            *   捕获任何错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
        *   `deleteInstance()`:
            *   确保 `instanceId`（或 `editableInstance.id`）存在。
            *   调用 `DeleteInstanceUseCase`。
            *   处理成功（例如，视图应被 dismiss）或失败（设置 `errorMessage`）。
        *   `duplicateInstance()`:
            *   确保 `instanceId`（或 `editableInstance.id`）存在。
            *   调用 `DuplicateInstanceUseCase`。
            *   处理成功（例如，通知列表刷新或导航到新复制的实例详情）或失败（设置 `errorMessage`）。
        *   `toggleFavorite()`:
            *   获取当前实例的 ID（从 `editableInstance.id`）。
            *   调用 `ToggleInstanceFavoriteUseCase`。
            *   成功后，更新 `editableInstance.isFavorited`。如果 `instance` 存在，也更新 `instance.isFavorited`。
        *   `prepareForEditing()`:
            *   如果 `isCreatingNewInstance` 为 `false` 且 `instance` 存在，则确保 `editableInstance` 是 `instance` 的最新副本。
            *   设置 `isEditing = true`。
        *   `cancelEditing()`:
            *   如果 `isCreatingNewInstance` 为 `false` 且 `instance` 存在，则将 `editableInstance` 重置为 `instance` 的状态。
            *   设置 `isEditing = false`。
            *   清除 `errorMessage`。
            *   如果正在创建新实例时取消，ViewModel 的行为可能需要视图协调（例如，`dismiss` 视图）。
        *   `validateInput() -> Bool`:
            *   验证 `editableInstance.name` 是否为空。
            *   `editableInstance.modelId` 必须有效。
            *   如果验证失败，设置 `errorMessage` 并返回 `false`。
            *   返回 `true` 表示通过验证。
        *   `private func handleError(_ error: Error)`: 统一处理错误，更新 `errorMessage`，并打印日志。
    *   **与 `ModelDetailViewModel` 的主要区别与注意事项**:
        *   核心实体是 `LLMInstance` 而不是 `LLMModel`。
        *   依赖的 Use Cases 不同，专注于实例操作。
        *   `editableInstance` 的字段包括 `name`, `defaultParameters`, `customLogoData`，以及关键的 `modelId`。
        *   `InstanceDetailView` 允许在编辑/创建时更改基础的 `LLMProvider` 和 `LLMModel`。ViewModel 需要通过 `instanceProviderDidChange` 和 `instanceModelDidChange` 方法响应这些变化。
        *   `defaultParameters` 是一个 `[String: String]?` 字典，其编辑逻辑在 View 中通过 `ParameterEditView` 处理，ViewModel 仅需确保持久化 `editableInstance.defaultParameters`。
        *   Token 使用情况 (`totalPromptTokensUsed`, `totalCompletionTokensUsed`) 在 `InstanceDetailView` 中为只读，其更新逻辑不在此 ViewModel 的直接编辑范围内。
    *   **预览和 Mock 数据**:
        *   ViewModel 的 `init` 应能通过 `DIContainer` 接收 Mock Repository 实现。
        *   SwiftUI Preview 应能传入 `MockLLMInstanceRepository` 中定义的现有 `LLMInstance` 的 ID，或 `nil` ID 配上一个 `modelIdForNewInstance` 来测试创建流程。
22. **实现模型列表 ViewModel (`ModelsViewModel.swift`)**
    *   **目标:** 创建 `ModelsViewModel.swift`，负责为 `ModelsView` 提供数据和业务逻辑。这包括加载所有相关的模型提供商 (`LLMProvider`)、模型实例 (`LLMInstance`)、实例组 (`LLMInstanceGroup`) 以及用户收藏的实例。ViewModel 还将处理搜索过滤逻辑，并管理加载状态和错误信息。
    *   **核心职责:**
        *   加载并管理所有 `LLMProvider` 的列表。
        *   加载所有 `LLMInstance`，并按其所属的 `LLMProvider` 进行分组。
        *   加载所有用户收藏的 `LLMInstance`，用于“收藏夹”快捷入口。
        *   加载所有 `LLMInstanceGroup`，用于“模型组”快捷入口。
        *   根据用户在搜索框中输入的文本 (`searchText`)，对提供商和实例列表进行过滤。
        *   管理视图的加载状态 (`isLoading`) 和错误信息 (`errorMessage`)。
    *   **主要属性 (`@Published`):**
        *   `allProviders: [LLMProvider] = []`: 从数据层加载的所有模型提供商。
        *   `instancesByProviderId: [UUID: [LLMInstance]] = [:]`: 所有模型实例，以其 `LLMProvider` 的 ID 为键进行分组。
        *   `favoritedInstances: [LLMInstance] = []`: 用户收藏的所有模型实例。
        *   `instanceGroups: [LLMInstanceGroup] = []`: 所有的实例组。
        *   `searchText: String = ""`:绑定到 `ModelsView` 搜索框的文本。
        *   `filteredProviders: [LLMProvider] = []`: 经过搜索过滤后的提供商列表，用于驱动视图显示。
        *   `filteredInstancesByProviderId: [UUID: [LLMInstance]] = [:]`: 经过搜索过滤后的实例列表（已分组），用于驱动视图显示。
        *   `isLoading: Bool = false`: 指示是否正在加载数据。
        *   `errorMessage: String? = nil`: 用于向用户显示错误信息。
    *   **依赖的 Use Cases (通过构造函数注入):**
        *   `GetAllProvidersUseCaseProtocol`: 用于获取所有 `LLMProvider`。
        *   `GetAllInstancesUseCaseProtocol`: 用于获取所有 `LLMInstance`。
        *   `GetFavoritedInstancesUseCaseProtocol`: 用于获取所有收藏的 `LLMInstance`。
        *   `GetAllGroupsUseCaseProtocol`: 用于获取所有 `LLMInstanceGroup`。
    *   **主要方法:**
        *   `init(getAllProvidersUseCase: GetAllProvidersUseCaseProtocol, getAllInstancesUseCase: GetAllInstancesUseCaseProtocol, getFavoritedInstancesUseCase: GetFavoritedInstancesUseCaseProtocol, getAllGroupsUseCase: GetAllGroupsUseCaseProtocol)`:
            *   注入所有必要的 Use Case。
            *   异步调用 `loadAllData()`。
        *   `loadAllData()`:
            *   设置 `isLoading = true`，清除 `errorMessage`。
            *   并发或顺序调用以下 Use Cases:
                *   `getAllProvidersUseCase.execute()` 来填充 `allProviders`。
                *   `getAllInstancesUseCase.execute()` 来获取所有实例，然后进行处理，按 `providerId`（通过实例的 `modelId` 找到关联的 `LLMModel`，再找到 `providerId`）填充 `instancesByProviderId`。
                *   `getFavoritedInstancesUseCase.execute()` 来填充 `favoritedInstances`。
                *   `getAllGroupsUseCase.execute()` 来填充 `instanceGroups`。
            *   所有数据加载完成后，调用 `applyFilters()` 来初始化 `filteredProviders` 和 `filteredInstancesByProviderId`。
            *   捕获任何错误并设置 `errorMessage`。
            *   最终设置 `isLoading = false`。
        *   `applyFilters()`:
            *   如果 `searchText` 为空，则 `filteredProviders` 设置为 `allProviders`，`filteredInstancesByProviderId` 设置为 `instancesByProviderId`。
            *   如果 `searchText` 不为空，则：
                *   遍历 `allProviders`，筛选出名称包含 `searchText` (忽略大小写) 的提供商，或者其下有任何实例名称包含 `searchText` 的提供商。结果存入 `filteredProviders`。
                *   对于每个在 `filteredProviders` 中的提供商，筛选其在 `instancesByProviderId` 中的实例，只保留名称包含 `searchText` 的实例。结果存入 `filteredInstancesByProviderId`。
                *   （注意：收藏夹和模型组的过滤逻辑可能需要单独处理，或者在各自的专属视图中实现。）
        *   `refreshData()`:
            *   提供一个方法来手动触发 `loadAllData()`，例如用于下拉刷新操作。
        *   `private func handleError(_ error: Error)`: 统一处理错误，更新 `errorMessage`，并打印日志。
    *   **与 `InstanceDetailViewModel` 的主要区别与注意事项**:
        *   核心职责是管理和展示列表数据，而非单个实体的详细信息。
        *   主要的数据结构是数组和字典（用于分组）。
        *   包含搜索过滤逻辑 (`searchText`, `applyFilters()`)。
        *   加载的是多个不同类型的实体列表 (`LLMProvider`, `LLMInstance`, `LLMInstanceGroup`)。
        *   不直接处理编辑、创建、删除等操作，这些通常由导航到的详情视图的 ViewModel 负责。
    *   **预览和 Mock 数据**:
        *   ViewModel 的 `init` 应能通过 `DIContainer` 接收 Mock Repository 实现，从而获取 `MockLLMInstanceRepository` 中定义的 mock 数据。
        *   SwiftUI Preview 应能方便地展示 `ModelsView` 在不同数据状态（加载中、有数据、无数据、错误）下的表现。
23. **连接 Presentation -> Domain -> Data:**
    *   在 ViewModels (`ModelsViewModel`, `InstanceDetailViewModel`, `ProviderDetailViewModel`, `ModelDetailViewModel`) 中调用 Use Cases。
    *   Use Cases 调用 Repositories。
    *   确保数据流正确，UI 能反映 Core Data 和 Keychain 中的变化。