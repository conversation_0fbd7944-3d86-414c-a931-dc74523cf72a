**阶段 3: 核心对话模块 (MVP: 本地基础交互)**

24. **实现内置对话数据初始化:**
    *   **创建初始化器:**
        *   在 `Data/Utils/Initialization/` 目录下创建 `BuiltinChatInitializer.swift` 文件。
    *   **定义内置数据:**
        *   在 `BuiltinChatInitializer.swift` 中：
            *   定义一个常量，表示当前内置对话数据的版本号，例如 `static let currentBuiltinChatVersion = 1`。
            *   定义一个用户默认键，用于存储版本号，例如 `private static let BuiltinChatVersionKey = "BuiltinChatVersion"`。
            *   定义私有静态属性，存储示例的 **系统默认 `ChatSessionSetting` 领域对象**。这个设置实体应具有固定的、预定义的 UUID，并且 `isSystemDefault` 属性为 `true`。这个设置将作为所有新创建对话会话的默认设置，也会关联到预置的示例会话上。
                *   为该设置指定一个固定的UUID，如 `UUID(uuidString: "C142A374-5536-7778-C142-A37455367778")!`。
                *   设置 `name` 为 "System Default"。
                *   确保 `lastModifiedAt` 和 `createdAt` 时间戳相同，作为检测用户是否修改过的依据。
            *   定义私有静态属性，存储示例 `ChatSession` 和 `Message` **领域对象 (Domain Entities)** 列表。这些实体应具有固定的、预定义的 UUID。
                *   **用户标识 (UserId):** 所有创建的 `ChatSession` 实体的 `userId` 属性应设置为系统默认用户ID `UUID(uuidString: "C142A374-0001-0001-0001-000000000001")!`。
                *   **设置标识 (settingsId):** 所有创建的 `ChatSession` 实体的 `settingsId` 属性应设置为上面定义的系统默认 `ChatSessionSetting` 的 ID。
                *   **LLM实例ID获取:** 对于 `Message` 中的 `llmInstanceId`，应直接调用 `BuiltinLLMInitializer.getBuiltinDefaultLLMInstance(forModelId: modelId)` 方法来获取。`modelId` 应选择自 `BuiltinLLMInitializer` 中定义的某个预置模型的ID。避免直接查询 `LLMInstanceRepository`。
                *   **示例会话1 (基础对话):**
                    *   创建一个 `ChatSession`，包含两轮简单的用户和助手角色交替的 `Message`，内容应包含 LavaChat 的简单使用介绍，展示基础问答流程。
                    *   标题可设为 "Welcome to LavaChat"。
                    *   `Message` 的 `llmInstanceId` 使用一个通用的内置模型实例ID。
                *   **示例会话2 (复杂交互与分支示例):**
                    *   创建一个 `ChatSession`，用于演示复杂的消息交互、分支和重新生成，标题可设为 "Multi Instance Interaction"。
                    *   包含以下一系列 `Message` 领域对象，通过 `parentId` 和 `metadata` 构建关系：
                        *   **第一轮用户消息 (根消息):** 创建一条 `user` 角色消息。内容如："What is LavaChat?" (`parentId` 为 `nil`)
                        *   **第一轮助手回复 (三条并列):** 创建三条 `assistant` 角色消息，它们的 `parentId` 都指向第一轮用户消息的ID。内容为不同的 introductions to LavaChat.
                            *   为这三条助手消息分配不同的内置 `llmInstanceId` (通过 `BuiltinLLMInitializer.getBuiltinDefaultLLMInstance` 获取，选择不同的模型ID)。
                            *   将其中一条的 `userFeedback` 设为 `.liked`。
                            *   将另一条的 `userFeedback` 设为 `.disliked`。
                            *   这三条消息的 `isReplied` 都设为 `false`。
                        *   **第二轮用户消息 (编辑):** 创建一条 `user` 角色消息。其 `parentId` 还是 `nil`。内容为编辑后的版本，例如："What is LavaChat and what are its features?" 在其 `metadata` 字典中添加 `["isEditOf": 第一轮用户消息ID.uuidString]`，表明它是对该消息的编辑。
                        *   **第二轮助手回复 (三条并列):** 创建三条 `assistant` 角色消息，它们的 `parentId` 都指向第二轮用户消息的ID。内容为 different explanations of LavaChat's features.
                            *   同样分配不同的内置 `llmInstanceId`。
                            *   将其中两条的 `userFeedback` 设为 `.liked`。
                            *   将另一条的 `userFeedback` 设为 `.disliked`。
                            *   将 liked 的一条的 `isReplied` 设为 `true`，表示用户基于此回复继续了对话。
                        *   **第三轮用户消息:** 创建一条 `user` 角色消息。其 `parentId` 指向第二轮助手中 `isReplied` 为 `true` 的那条消息的ID。内容为基于该助手回复的后续问题，例如："Explain the multi-assistant feature in detail."
                        *   **第三轮助手回复 (原始):** 创建三条 `assistant` 角色消息，其 `parentId` 指向第三轮用户消息的ID。内容为 detailed explanations of the multi-assistant feature. 分配不同内置 `llmInstanceId`。
                        *   **第三轮助手回复 (重新生成):** 创建一条 `assistant` 角色消息，其 `parentId` 仍然指向第三轮用户消息的ID。是原始回复其中一条的重新生成的另一个回答（与原始回复不同）。分配相同的 `llmInstanceId`。在**这条重新生成的消息**的 `metadata` 字典中添加 `["isRegenerationOf": 第三轮助手原始消息ID.uuidString]`，表明它是后一次生成的版本。
                    *   `ChatSession.rootMessageIds` 需要是同时包含第一轮用户消息和第二轮编辑的用户消息的数组
                *   **示例会话3 (文件交互占位):**
                    *   创建一个 `ChatSession`，其包含的第一条 `Message` (可以是 `system` 或 `assistant` 角色) 的内容应为提示性文本，说明这是一个关于文件交互的演示会话，但目前仅作为占位符，例如："Welcome to LavaChat! I'm your AI hub for managing and interacting with multiple LLMs. Try asking me about the content of 'Project_Report.md' .
                    *   标题可设为 "FileSession (演示)"。
    *   **实现初始化方法:**
        *   在 `BuiltinChatInitializer` 中创建一个静态方法，例如 `initializeIfNeeded(chatRepository: ChatRepository, chatRepository: ChatRepository, userDefaults: UserDefaults = .standard)`。该方法不再需要 `LLMInstanceRepository` 作为参数。
        *   该方法内部逻辑：
            1.  从 `UserDefaults` 使用 `BuiltinChatVersionKey` 读取之前存储的内置对话数据版本号。
            2.  **检查是否需要初始化或更新:** 如果 `storedVersion < currentBuiltinChatVersion`，则执行后续步骤。
            3.  调用 `chatRepository.beginBatchUpdate()` 开始批量更新。
            4.  **获取该用户已有的、标记为系统预置的对话会话:**
                *   调用 `chatRepository.getAllChatSessions(for: userId, ...)` 获取系统默认用户的所有预置会话，其中 userId 是预定义的 `UUID(uuidString: "U1E2S3R4-I5D6-7890-U1E2-S3R4I5D67890")!`
            5.  **比较并执行操作 (确保幂等性):**
                *   **处理系统默认 ChatSessionSetting:**
                    *   尝试通过其预定义 ID 调用 `chatRepository.getSetting(byId:)` 获取系统默认设置。
                    *   如果获取不到，则使用上面定义的默认 `ChatSessionSetting` 领域对象调用 `chatRepository.createSetting()` 创建它。
                    *   如果获取到了:
                        *   检查 `lastModifiedAt` 和 `createdAt` 时间戳是否相同:
                            *   如果相同 (未被用户修改过):
                                *   检查其他属性是否需要更新 (如名称、默认参数等)
                                *   如果需要更新，则调用 `chatRepository.updateSetting()`
                            *   如果不相同 (已被用户修改过):
                                *   保持现有设置不变，不进行任何更新
                *   遍历代码中定义的示例 `ChatSession` 列表：
                    *   检查此预定义 `ChatSession` 的ID是否已存在于第4步获取的该用户预置会话列表中。
                    *   如果不存在，则调用 `chatRepository.createChatSession(_:)` 将其新增到数据库。
                    *   如果已存在，则可以跳过创建，或选择性更新其非用户可修改的属性（如标题，如果内置定义发生变化）。V1阶段可简化为仅创建不存在的。
                *   遍历代码中定义的示例 `Message` 列表：
                    *   检查此预定义 `Message` 的ID是否已存在于数据库中（可能需要先根据 `sessionId` 获取对应会话的所有消息，然后比较ID）。
                    *   如果不存在，则调用 `chatRepository.createMessage(_:)` 将其新增到数据库。
                    *   如果已存在，则跳过创建或选择性更新。V1阶段可简化为仅创建不存在的。
            6.  **提交批量更新:** 如果一切顺利，调用 `chatRepository.endBatchUpdateAndPublish()`。
            7.  **更新版本号:** 操作成功后，将 `currentBuiltinChatVersion` 写入 `UserDefaults`。
            8.  **错误处理:** 使用 `do-catch` 块包裹数据库操作和版本号更新，并在捕获到错误时调用 `chatRepository.endBatchUpdateAndPublish()` (即使有错也尝试结束批处理，具体错误处理策略待定，可能只是打印日志)。
    *   **触发初始化:**
        *   在应用启动逻辑中（例如 `lavachatApp.swift` 的 `init` 中，确保只执行一次），通过 `DIContainer` 获取 `ChatRepository` 和 `ChatRepository` 的实例，并调用 `BuiltinChatInitializer.initializeIfNeeded(chatRepository: ..., chatRepository: ...)`。

25. **实现对话列表 UI (`ChatsView.swift`):**
    *   **文件创建:** 在 `Presentation/Views/Chats/` 目录下创建 `ChatsView.swift`。
    *   **UI 结构:**
        *   使用 `NavigationStack` 作为根。
        *   主体内容使用 `List` 来展示 `ChatSession` 条目。
        *   在 `List` 上方或导航栏内集成搜索功能 (可使用 `.searchable(text: $viewModel.searchText)`)，搜索范围应包括:
            *   会话标题 (`ChatSession.title`)
            *   会话最后一条消息内容 (从 `ChatSession.lastMessageSnippet` 获取)
            *   关联的 AI 实例名称 (通过 `activeLLMInstanceIds` 查询)
        *   导航栏标题为 "Chats" (或 "对话")，使用 `.navigationBarTitleDisplayMode(.inline)` 或 `.automatic`。
        *   导航栏右侧添加 "+" 按钮 (`ToolbarItem(placement: .navigationBarTrailing)`), 点击后应显示一个从底部弹出的 sheet 视图:
            *   创建 `NewChatSheetViewModel.swift` 和 `NewChatSheetView.swift`
            *   使用 `.sheet(isPresented: $showNewChatSheet)` 修饰符
            *   Sheet 内容应包含:
                *   顶部工具栏:
                    *   标题 "New Chat"
                    *   右侧 "Cancel" 按钮关闭 sheet
                *   主要操作区 (类似 ModelsView 的布局):
                    *   "New Empty Chat" 按钮 (使用 `Button` 但 action 为空)
                    *   "Import Chat" 按钮 (使用 `Button` 但 action 为空)
                    *   "Edit Document" 按钮 (使用 `Button` 但 action 为空)
                    *   "Scan QR Code" 按钮 (使用 `Button` 但 action 为空)
                    *   每个按钮应包含相应的 SF Symbol 图标
                *   Provider 和 Instance 选择区:
                    *   复用 ModelsView 的 provider section 布局
                    *   Provider 行显示为不可点击 (`.disabled(true)`)
                    *   Instance 行点击后:
                        *   关闭 sheet
                        *   使用该 instance 创建新对话
            *   使用 `@State private var showNewChatSheet = false` 控制显示状态
    *   **Session 行 (`ChatSessionRowView.swift` - 可作为私有 struct 或单独文件):**
        *   输入: `ChatSession` 对象。
        *   **布局:** 实现以下布局:
            *   最左侧: 显示一个或多个 LLM Instance 的 Logo (参见下方详细说明)。
            *   Logo 右侧 (垂直堆叠):
                *   上方: `session.title` (如果为空，ViewModel 可能需要提供一个默认标题，如 "New Chat" 或基于首条消息生成)。使用主要文本样式。
                *   下方: `session.lastMessageSnippet` (由 ViewModel 提供，可能是截断的最后一条消息文本)。使用次要文本样式。
            *   最右侧: `session.lastModifiedAt` (格式化为用户友好的相对时间或日期)，右对齐。使用次要或更次要文本样式。
        *   整行包裹在 `NavigationLink(destination: ChatView(viewModel: container.makeChatViewModel(chatSessionId: session.id)))` 中。
        *   **列表样式 (List Style):**: 给 `NewChatSheetView.swift` 中的 `List` 组件添加 `.listStyle(.plain)` 修饰符，使其与 `ModelsView.swift` 的列表样式统一。
        *   **列表行内边距 (List Row Insets):**: 为 `NewChatSheetView.swift` 中 `List` 内的每个 `Section`（包括快捷操作按钮区和提供者/实例区）应用与 `ModelsView.swift` 一致的行内边距：`.listRowInsets(EdgeInsets(top: 4, leading: 20, bottom: 4, trailing: 20))`。
        *   **多 Instance Logo 显示:**
            *   创建新的 SwiftUI 视图文件 `Presentation/Views/Shared/ChatSessionInstancesLogoView.swift`，用于封装和展示多个 LLM Instance Logo 的复杂逻辑。
            *   **`ChatSessionInstancesLogoView.swift` 职责与设计:**
                *   **输入参数:** 该视图将接收 `activeLLMInstanceIds: [UUID]` 和一个 `size: CGFloat` 用于控制整体显示尺寸。
                *   **专属 ViewModel (`ChatSessionInstancesLogoViewModel.swift`):**
                    *   在 `Presentation/ViewModels/Shared/` 创建此 ViewModel。
                    *   该 ViewModel 接收 `activeLLMInstanceIds: [UUID]`。
                    *   依赖注入相关的 Use Cases (例如 `GetInstancesUseCase`, `GetModelUseCase`, `GetProviderUseCase` 或一个组合了这些功能的 Use Case) 来异步获取必要的 `LLMInstance`, `LLMModel`, 和 `LLMProvider` 领域对象。
                    *   **Logo 信息处理:** ViewModel 负责根据获取到的领域对象，遵循定义的 fallback 逻辑 (实例自定义 Logo -> 模型自定义 Logo -> 模型目录 Logo -> 提供商自定义 Logo -> 提供商目录 Logo -> 默认占位符 SF Symbol)，为每个相关的活跃实例准备最终要显示的 Logo 信息。
                    *   它会输出一个有序的 Logo 描述列表 (例如，一个包含 `logoSource: LogoSourceType` (可以是 `.customData(Data)`, `.assetName(String)`, `.sfSymbol(String)`) 和 `id: UUID` 的结构体数组) 给 `ChatSessionInstancesLogoView` 进行渲染。
                *   **视图渲染逻辑:**
                    *   `ChatSessionInstancesLogoView` 遍历从其 ViewModel 接收到的 Logo 描述列表。
                    *   它将重用或参考 `LogoDisplayView.swift` 中的组件/逻辑 (如 `CustomLogoImage`, `CatalogLogoImage`, 或直接使用 `Image`) 来渲染单个 Logo。
                    *   **动态布局 (基于活跃实例数量):**
                        *   **1 个实例:** 清晰地显示该实例的 Logo。
                        *   **2-3 个实例:**
                            *   布局灵感参考 `CNUIIdentityAvatarConfiguration.json` 文件中 `SnowglobeAvatarLayoutConfigurations` 部分（特别是针对2和3个头像的配置）。在 `ChatSessionInstancesLogoView` 中使用 SwiftUI `ZStack` 实现一个简化的、硬编码的重叠布局。ViewModel 将提供按顺序（例如，最重要的在前/在顶层）排列的 Logo 描述。
                            *   Logo 的视觉表现（如圆角、边框）应参考 `frontend_guidelines_document.mdc` 中 "AI 相关 Logo 规范" -> "视觉一致性" 部分。
                        *   **4 个及以上实例:** 显示一个通用的组合图标（例如 SF Symbol `rectangle.stack.fill`，或根据 `frontend_guidelines_document.mdc` 的指导选择更合适的图标）并可选择性地叠加一个数量标记 (例如，一个小圆形背景上显示 "+N")。
            *   `ChatSessionRowView` 将实例化 `ChatSessionInstancesLogoView` (并通过其 ViewModel 或直接传递参数) 来显示 Logo。它会从 `ChatSession` 对象中提取 `activeLLMInstanceIds` 并传递给 `ChatSessionInstancesLogoView`。
    *   **创建 ViewModel 文件:** 在 `Presentation/ViewModels/Chats/` 目录下创建 `ChatsViewModel.swift` 文件。在此步中，ViewModel 将暂时使用 Mock 数据 (`MockChatPreviewData.getAllExampleSessions()`)，不与 Use Cases 交互。它需要包含 `sessions`, `isLoading`, `errorMessage`, `searchText` 等 `@Published` 属性，以及 `loadSessions`, `refreshSessions`, `deleteSession` 等方法签名 (具体实现用 Mock 数据)。
    *   **数据绑定与 ViewModel:**
        *   视图通过 `@StateObject var viewModel: ChatsViewModel` 持有 ViewModel 实例。
        *   `List` 遍历 `viewModel.sessions`。
        *   显示 `viewModel.isLoading` 时的 `ProgressView` 和 `viewModel.errorMessage` 时的错误提示。
    *   **交互设计:**
        *   支持下拉刷新 (`.refreshable { await viewModel.refreshSessions() }`)。
        *   支持左滑删除 (`.onDelete(perform: viewModel.deleteSession)`)。
    *   **空状态:**
        *   当 `viewModel.sessions` 为空且非加载/错误状态时，显示提示信息，例如 "No chats yet. Tap '+' to start one!" 和一个相关的 SF Symbol。
    *   **Mock 数据与预览:**
        *   在 `ChatsView_Previews` 中:
            *   创建 `DIContainer` 实例，配置为使用 `MockLLMInstanceRepository`。
            *   初始化 `ChatsViewModel` 时，直接使用 `MockChatPreviewData.getAllExampleSessions()` 填充其 sessions 属性。
            *   将 `DIContainer` 注入到 `ChatsView` 的环境中。

26. **实现对话界面 UI (`ChatView.swift`, `MessageRowView.swift`):**
    *   **文件创建:**
        *   在 `Presentation/Views/Chats/` 目录下创建 `ChatView.swift`。
        *   在 `Presentation/Views/Chats/` 目录下创建 `MessageRowView.swift`。
    *   **`ChatView.swift` UI 结构:**
        *   接收 `chatSessionId: UUID` (通过 ViewModel 初始化时传入)。
        *   导航栏标题应动态显示 `viewModel.chatSession?.title` (若 `chatSession` 已加载)，并包装在可点击区域中：
            *   标题旁边显示 `chevron.right` SF Symbol 作为视觉提示
            *   点击标题区域导航到会话设置视图 (`ChatSessionSettingsView`)，在此步我们只print点击了这个按钮 (未来实现实际导航)
        *   导航栏右侧可放置一个 "New Chat" 按钮，点击后触发 `viewModel.startNewSessionBasedOnCurrent()` (此方法在ViewModel中创建，用于基于当前会话设置快速开启新会话)，成功后应触发导航到新的 `ChatView`。
        *   主体消息区域:
            *   使用 `ScrollViewReader` 和 `ScrollView` 包含一个 `LazyVStack` 来展示消息列表 (`viewModel.messages`)。
            *   `ScrollView` 应能自动滚动到底部新消息处。
        *   底部输入区域:
            *   使用 `HStack` 包含：
                *   一个 "+" 按钮 (使用 `Image(systemName: "plus.circle.fill")`)，点击时：
                    *   首先调用 `hideKeyboard()` 收起键盘（如果已弹出）
                    *   从下方弹出一个功能面板（自定义 View）类似键盘把输入栏顶起，提供以下选项：
                        *   "Select Model" - 选择当前会话要与之交互的模型或模型组。
                        *   "Toggle Web Search" - 切换网络搜索功能 (如果已实现)。
                        *   "Send Photo" - 打开相册选择照片。
                        *   "Take Photo" - 打开相机拍照。
                        *   "Send File" (@提及文件/文件夹) - **触发系统文件选择器 (`UIDocumentPickerViewController` 或 SwiftUI 等效) 供用户选择文件/文件夹。选择后，尝试读取内容、进行 Token 估算，若内容过多弹出警告提示用户，用户确认后将内容作为 `ContentBlock.attachedFileContent` 嵌入待发送消息中。** (此功能根据 PRD 在 V1 范围内)
                        *   "Undo" - 撤销上一步操作（如撤销编辑、刷新）。
                        *   "Custom Commands" - 触发用户定义的自定义 AI 操作。
                *   一个 `TextEditor(text: $viewModel.newMessageText)`，配置合适的行高限制。
                *   一个发送按钮 (如 `Image(systemName: "arrow.up.circle.fill")`)，当 `viewModel.newMessageText` 非空时启用，点击触发 `viewModel.sendMessage()`。
    *   **`MessageRowView.swift` UI 结构:**
        *   **输入:**
            *   `messagesInLayer: [Message]`：代表当前"一层"的所有相关消息（例如，一个用户发言的所有编辑版本，或者对一个用户发言的所有助理回复及其重生成版本）。
            *   `chatViewModel: ChatViewModel`：用于回调用户操作，如版本选择，以便 ViewModel 更新整个对话树的显示状态。
        *   **内部状态管理:** 使用 `@State` 变量（如 `activeUserEditId`, `activeAssistantResponseGroupId`, `activeRegenerationId`）来追踪并显示 `messagesInLayer` 中的哪一个具体消息。
        *   **Markdown 渲染:** 对当前`activeMessageIdInLayer` 对应消息的 `Message.content` 中的文本 (`.text` 类型) 使用 `AttributedString(markdown:)` 进行渲染。
        *   **根据 `messagesInLayer.first?.role` 采用不同布局：**
            *   **User 消息:**
                *   靠右对齐，特定背景色/样式。
                *   长按当前显示的用户消息气泡呼出操作菜单。
                *   如果 `messagesInLayer` (过滤后的用户编辑版本) 数量大于1，显示导航控件 "<1/N>"。切换时：
                    *   更新内部 `@State` 以显示不同的编辑版本。
                    *   调用 `chatViewModel` 内部 function 通知 ViewModel，**这将导致后续依赖此用户消息的助理回复层和其全部子消息层接收到全新的 `messagesInLayer` 数据。**
            *   **Assistant 或 MergedAssistant 消息:** 
                *   靠左对齐，使用另一种背景色/样式。
                *   **数据准备与分组：** `messagesInLayer`（其中所有消息的 `parentId` 指向上一个活动用户消息）会先被组织。系统会（基于有无 `metadata.is_regeneration_of`）识别出所有独立的"原始助理回复"。随后，每个"原始助理回复"及其所有通过 `metadata.is_regeneration_of` 关联的**重生成版本**，会被归为一个逻辑上的"**回复组**"。每个"回复组"内部的消息通常按时间戳排序。这样，`messagesInLayer` 就被构造成了一个或多个"回复组"的集合。
                *   **多回复组的卡片切换 (若有多个)：**
                    *   如果处理后形成了多个"回复组"（例如，来自不同助理的多个回复，或同一助理在不同时间点提供的独立回复系列），这些"回复组"将以卡片堆叠的形式呈现。用户可以通过**左右水平滑动**（类似Tinder卡片交互）来表达对当前显示"回复组"的偏好。
                    *   **向左滑动视为"不喜欢" (Dislike)**：该"回复组"卡片将被移走，在当前视图中不再优先显示（ViewModel会记录此偏好，可能会将其过滤或置于次要位置）。
                    *   **向右滑动视为"喜欢" (Like)**：该"回复组"卡片被接受，用户会看到下一个可评估的"回复组"。如果所有未评估的"回复组"都向右滑动完毕，会循环展示之前"喜欢"的回复组。
                    *   **全部"不喜欢"后的处理**：如果所有可用的"回复组"均被用户向左滑动（即全部"不喜欢"），视图中会出现一个"**重新生成**"按钮。点击此按钮会请求 ViewModel 基于**原始用户消息**生成全新的助理回复。这些新生成的回复将形成新的"回复组"，但它们的 `metadata` 中**不会**包含 `is_regeneration_of` 标记，因为这是针对用户消息的全新生成，而非对某个已存在助理回复的迭代。
                    *   内部 `@State` 变量会追踪当前正在交互的"回复组"及其用户的偏好（喜欢/不喜欢），并将此信息通过回调传递给 `chatViewModel`，以便 ViewModel 更新 `Message` 的相关元数据或维护偏好状态。
                *   **回复组内的重生成版本切换：**
                    *   在当前选中的"回复组"卡片内部，如果该组包含多个消息（即原始回复+至少一个重生成版本），则会显示一个 "<1/M>" 样式的导航控件，用于切换该回复组内的不同**重生成版本**。
                    *   `@State` 变量也追踪此"回复组"内当前激活的具体重生成版本。
                *   **状态同步与操作：**
                    *   当用户切换"回复组"或组内的"重生成版本"时，`@State` 更新，并通知 `chatViewModel` 当前最终显示的具体消息ID。
                    *   操作按钮（如复制、重新生成）始终作用于当前水平滑动选中、且通过 "<1/M>" 选中的那个具体助理消息。
                *   在当前显示的助理回复卡片下方显示操作按钮（包含复制、重新生成等从 `messageActionSettings` 读取的默认操作），这些操作应作用于当前显示的回复。
            *   其他类型消息在 MVP 中不显示。
        *   内容展示: 主要显示 `message.content` 列表中类型为 `.text` 的第一个内容块。
        *   头像显示: 用户消息不显示头像，assistant 消息使用 `InstanceLogoView` 显示 instance logo。
    *   **数据绑定与 ViewModel:**
        *   `ChatView` 通过 `@StateObject var viewModel: ChatViewModel` 持有 ViewModel。
        *   `LazyVStack` 遍历 `viewModel.messages` 并为每个 `Message` 创建 `MessageRowView`。
        *   显示 `viewModel.isLoadingMessages` 时的加载指示和 `viewModel.errorMessage`。
    *   **Mock 数据与预览:**
        *   在 `ChatView_Previews` 中:
            *   创建 `DIContainer` 实例，配置为使用 `MockLLMInstanceRepository`。
            *   从 `MockChatPreviewData.getAllExampleSessions()` 获取示例 `ChatSession.id`。
            *   初始化 `ChatViewModel` 时，直接使用 `MockChatPreviewData.getMessagesForSession(sessionId: ...)` 填充其 messages 属性，或者通过 Use Case Factory 注入 MockUseCases，这些 UseCase 在内部引用 `MockChatPreviewData`。
            *   将 `DIContainer` 注入到 `ChatView` 的环境中.

27. **完善 LLM API Client (Data Layer - 构建API基础设施):**
    *   **目录结构优化:**
        *   **Domain层服务接口** - 在 `Domain/Interfaces/Services/` 下创建LLM API服务协议定义
        *   **Domain层数据传输对象** - 在 `Domain/DTOs/` 下创建请求响应模型定义
        *   **Data层实现结构** - 在 `Data/DataSources/Network/` 下创建具体实现：
            *   `LLMClients/` - LLM API客户端相关
                *   `Implementations/` - 具体客户端实现
                *   `Protocols/` - 内部客户端协议定义
                *   `Shared/` - 共享工具和数据模型
                *   `Service/` - 统一客户端服务层
            *   `CloudKit/` (未来CloudKit API封装)
            *   `Cloudflare/` (未来Cloudflare Workers API封装)
    *   **Domain层请求响应模型设计 (`Domain/DTOs/LLMStreamingRequest.swift` 和 `Domain/DTOs/LLMStreamingResponse.swift`):**
        *   **简化的流式请求模型:** 定义包含标准消息历史（使用[Message]实体数组作为历史记录）、模型参数配置、实例标识符的请求结构。每个请求包含唯一标识符用于响应路由，但不包含时间戳或请求类型枚举，因为对于LLM API来说所有类型的对话（新消息、重新生成、编辑后续）本质上都是相同的聊天完成请求。
        *   **简化的流式响应模型:** 定义统一的响应结构，包含实例标识符用于多实例场景下的数据路由。响应类型简化为内容增量、完成标记、错误信息（可直接使用`Domain/Errors/APIError.swift`）三种基本类型。思考过程作为常规文本内容的一部分处理，不单独定义响应块类型。
        *   **基础元数据结构:** 响应元数据仅包含核心信息如Token使用量、模型标识符等必要数据，避免过度设计。工具调用、复杂处理时间追踪等高级功能在MVP阶段不实现。
    *   **Domain层服务协议设计 (`Domain/Interfaces/Services/LLMAPIServiceProtocol.swift`):**
        *   **单实例流式接口:** 定义接收单个请求并返回对应响应流的基础方法，每个响应块包含实例标识符用于路由。
        *   **多实例并发接口:** 提供向多个实例发送相同消息内容的批量方法，自动为每个实例创建独立请求，并在统一的响应流中返回所有实例的结果，通过实例标识符区分数据来源。
        *   **基础取消控制:** 提供基于请求标识符的取消机制，支持取消特定请求而不影响其他并发请求。支持基于实例标识符的批量取消操作。支持全部请求批量取消操作。
        *   **简化的状态管理:** 提供基础的活跃请求状态查询，避免复杂的请求生命周期追踪。
    *   **Data层统一API服务实现 (`Data/DataSources/Network/LLMClients/Service/LLMAPIServiceImpl.swift`):**
        *   **请求管理:** 维护活跃请求的基础映射表，跟踪请求标识符与Task实例的对应关系，支持精确取消。实现简化的请求生命周期管理，专注于创建、执行、完成或取消的基本流程。
        *   **TaskGroup并发架构:** 使用Swift TaskGroup为每个实例创建独立的并发任务，确保多实例请求能够并行处理。实现基础的任务调度，无需复杂的资源优化算法。
        *   **响应数据汇聚:** 将来自不同内部客户端的响应数据汇聚到单一的AsyncThrowingStream中，保持实例标识符信息。实现基础的背压处理，避免快速响应压垮消费者。
        *   **简化的客户端路由:** 根据LLMProvider的apiStyle属性自动选择合适的内部客户端实现。这一步仅实现OpenAI兼容API，其他API风格在后续阶段实现。
        *   **基础错误隔离:** 确保单个实例的失败不影响其他并发请求，为每个失败请求生成带有实例标识符的错误响应。暂不实现复杂的重试机制。
    *   **内部客户端协议设计 (`Data/DataSources/Network/LLMClients/Protocols/InternalLLMClientProtocol.swift`):**
        *   **统一的内部接口:** 定义所有具体API客户端必须实现的基础接口，接收标准化请求参数并返回标准化响应流。专注于流式调用模式，非流式调用作为流式的特殊情况处理。
        *   **简化的配置提取:** 定义从LLMInstance、LLMModel、LLMProvider实体中提取基础API配置信息的方法，包括认证信息、端点URL、模型标识符、基本参数等。
        *   **标准化响应格式:** 确保不同API风格的客户端都返回统一格式的简化响应数据，便于上层服务处理。
    *   **OpenAI兼容客户端实现 (`Data/DataSources/Network/LLMClients/Implementations/OpenAICompatibleClient.swift`):**
        *   **OpenAI API格式适配:** 实现内部客户端协议，专门处理OpenAI及其兼容API的调用。将标准化请求参数转换为OpenAI API格式，处理基础的模型参数差异。
        *   **Server-Sent Events流式处理:** 实现基础的SSE流式响应处理，支持OpenAI标准流式数据格式解析。处理基本的数据块边界和连接中断情况。
        *   **简化的认证处理:** 支持API Key认证方式，实现基础的密钥管理，确保敏感信息安全。
        *   **基础实例配置:** 支持实例级别的基本配置，如自定义端点、参数覆盖等核心功能。
        *   **Token计数提取:** 正确提取和返回prompt_tokens、completion_tokens等基础使用量信息。
    *   **共享工具组件 (`Data/DataSources/Network/LLMClients/Shared/`):**
        *   **HTTP请求构建器:** 创建标准化的HTTP请求构建工具，处理URL构建、基础头部设置、认证信息添加、参数编码等核心逻辑。
        *   **SSE数据解析器:** 实现基础的Server-Sent Events数据解析器，支持标准SSE格式解析和基本错误处理。
        *   **网络状态监控:** 集成基础的网络连接状态监控，支持简单的离线检测。复杂的网络质量评估和自动重连在后续阶段实现。
        *   **基础配置缓存:** 为模型元数据提供简单的内存缓存机制，减少重复的配置查询。
    *   **简化的错误类型系统 (`Domain/Errors/APIError.swift`):**
        *   **基础错误定义:** 定义核心的API错误类型，包括网络错误、认证错误、API响应错误、解析错误等基本类别。
        *   **简化的错误处理:** 为不同错误提供基础的处理建议，如是否可重试、是否需要用户干预等。暂不实现复杂的错误恢复策略。
        *   **用户友好错误描述:** 提供基础的本地化错误描述，将技术错误转换为用户可理解的提示。
    *   **Mock实现和测试支持:**
        *   **Domain层Mock服务:** 在 `Mocks/Domain/Services/` 下创建LLMAPIServiceProtocol的Mock实现，支持可配置的响应数据、基本延迟模拟、错误注入等测试功能。
        *   **简化的流式响应模拟:** Mock实现能够模拟基础的多实例流式响应场景，包括不同响应速度、交错数据、部分失败等常见情况。
        *   **基础验证工具:** 提供请求参数的基本验证，确保UseCase层传递的参数格式正确。
    *   **依赖注入和生命周期管理:**
        *   **Repository工厂集成:** 在RepositoriesFactory中创建和管理LLMAPIServiceProtocol实例，确保与其他Repository组件的一致性。
        *   **环境自适应:** 根据运行环境（开发、测试、生产）自动选择合适的实现（Mock或真实实现）。
        *   **基础资源管理:** 实现核心的资源生命周期管理，确保网络连接、Task实例等资源的正确释放。


28. **定义并实现对话模块 Use Cases:**
    *   **目录结构:** 在 `Domain/UseCases/` 下创建子目录 `Chats/`，并按功能域分组：
        *   `Chats/ChatSession/` - 对话会话相关用例
        *   `Chats/Message/` - 消息相关用例
        *   `Chats/Setting/` - 对话设置相关用例
    *   **ChatSession协议定义 (`ChatSession/ChatSessionUseCaseProtocols.swift`):**
        *   `protocol CreateChatSessionUseCaseProtocol { func execute(title: String?, llmInstanceId: UUID?) async throws -> ChatSession }`
        *   `protocol GetChatSessionUseCaseProtocol { func execute(sessionId: UUID) async throws -> ChatSession? }`
        *   `protocol GetAllChatSessionsUseCaseProtocol { func execute() async throws -> [ChatSession] }`
        *   `protocol UpdateChatSessionUseCaseProtocol { func execute(_ session: ChatSession) async throws -> ChatSession }`
        *   `protocol DeleteChatSessionUseCaseProtocol { func execute(sessionId: UUID) async throws }`
        *   `protocol DuplicateChatSessionUseCaseProtocol { func execute(sourceSessionId: UUID) async throws -> ChatSession }`
    *   **Message协议定义 (`Message/MessageUseCaseProtocols.swift`):**
        *   `protocol SendMessageUseCaseProtocol { func execute(sessionId: UUID, content: [ContentBlock], instanceIds: [UUID], operationType: MessageOperationType? = nil, relatedMessageId: UUID? = nil) -> AsyncThrowingStream<LLMStreamingResponse, Error> }`
        *   `protocol GetMessagesForSessionUseCaseProtocol { func execute(sessionId: UUID) async throws -> [Message] }`
        *   `protocol CreateMessageUseCaseProtocol { func execute(_ message: Message) async throws -> Message }`
        *   `protocol UpdateMessageUseCaseProtocol { func execute(_ message: Message) async throws -> Message }`
        *   `protocol DeleteMessageUseCaseProtocol { func execute(messageId: UUID) async throws }`
        *   `protocol RegenerateSingleMessageUseCaseProtocol { func execute(messageId: UUID) -> AsyncThrowingStream<LLMStreamingResponse, Error> }`
        *   `protocol EditMessageUseCaseProtocol { func execute(originalMessageId: UUID, newContent: [ContentBlock]) -> AsyncThrowingStream<LLMStreamingResponse, Error> }`
        *   `protocol MessageTreeManagerUseCaseProtocol { func getCachedTree(sessionId: UUID) -> MessageTreeCache?; func rebuildTree(sessionId: UUID, activeMessageId: UUID?) async throws -> MessageTreeCache; func updateActiveMessage(sessionId: UUID, activeMessageId: UUID?) async throws -> MessageTreeCache }`
    *   **Setting协议定义 (`Setting/ChatSessionSettingUseCaseProtocols.swift`):**
        *   `protocol GetChatSessionSettingUseCaseProtocol { func execute(settingId: UUID) async throws -> ChatSessionSetting? }`
        *   `protocol GetDefaultChatSessionSettingUseCaseProtocol { func execute() async throws -> ChatSessionSetting }`
        *   `protocol CreateChatSessionSettingUseCaseProtocol { func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting }`
        *   `protocol UpdateChatSessionSettingUseCaseProtocol { func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting }`
    *   **ChatSession Use Case 实现 (在 `Domain/UseCases/Chats/ChatSession/` 下创建对应文件):**
        *   **`CreateChatSessionUseCase`**:\
            依赖: `ChatRepository`, `LLMInstanceRepository` (用于获取默认或指定的 LLM 实例信息)。\
            逻辑: 创建 `ChatSession` 领域对象，调用 `chatRepository.createSession()`. 自动生成标题（如 "New Chat" 或基于时间）如果未提供。
        *   **`GetChatSessionUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 调用 `chatRepository.getChatSession(byId:)`.
        *   **`GetAllChatSessionsUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 调用 `chatRepository.getAllSessions()`，按 `lastModifiedAt` 降序排序。
        *   **`UpdateChatSessionUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 接收完整的 `ChatSession` 实体，调用 `chatRepository.updateChatSession(session)`。无需Updates结构体。
        *   **`DeleteChatSessionUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 调用 `chatRepository.deleteSession(byId:)` (Repository 应处理级联删除消息)。
        *   **`DuplicateChatSessionUseCase`**:\
            依赖: `ChatRepository`, `ChatRepository`.\
            逻辑: 获取源会话设置，创建新会话，复制相关配置但生成新的ID和时间戳。
    *   **Message Use Case 实现 (在 `Domain/UseCases/Chats/Message/` 下创建对应文件):**
        *   **`SendMessageUseCase` (统一流式消息处理中心):**\
            **依赖:** `ChatRepository`, `LLMAPIServiceProtocol`, `MessageTreeManagerUseCase`.\
            **参数说明:** 支持通过 `operationType` 和 `relatedMessageId` 处理不同场景（新消息、编辑、重新生成）。\
            **核心逻辑 (返回 AsyncThrowingStream<LLMStreamingResponse, Error>):**\
                1. **操作类型处理:** 根据 `operationType` 执行不同的前置逻辑:\
                    - `newMessage`: 创建新用户消息\
                    - `editMessage`: 创建编辑消息并设置 `metadata.isEditOf`\
                    - `regenerateSingleMessage`: 跳过用户消息创建，直接基于 `relatedMessageId` 重新生成\
                2. **实例选择:** 根据操作类型选择目标实例:\
                    - 新消息/编辑: 使用传入的 `instanceIds`\
                    - 重新生成: 从 `relatedMessageId` 对应的原消息获取 `llmInstanceId`\
                3. **历史消息构建:** 使用 `MessageTreeManagerUseCase` 获取相应的消息历史上下文。\
                4. **会话设置处理:** 获取当前 `ChatSession` 的相关配置。\
                5. **多实例并发:** 调用 `LLMAPIServiceProtocol.sendMessageToInstances` 返回合并的流式响应。\
                6. **流式数据处理与缓冲保存:** 实时UI更新和批量数据库保存。\
                7. **元数据设置:** 根据操作类型为生成的助手消息设置正确的 `metadata`。\
                8. **缓存更新:** 消息保存后调用 `MessageTreeManagerUseCase` 更新消息树缓存。\
                9. **错误处理:** 统一的错误包装和处理。
        *   **`GetMessagesForSessionUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 调用 `chatRepository.getMessages(forSessionId:)`，按 `createdAt` 升序排序。
        *   **`CreateMessageUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 接收完整的 `Message` 实体，调用 `chatRepository.createMessage(message)`。
        *   **`UpdateMessageUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 接收完整的 `Message` 实体，调用 `chatRepository.updateMessage(message)`。无需Updates结构体。
        *   **`DeleteMessageUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 删除指定消息，考虑对消息树结构的影响，可能需要更新相关消息的 `parentId`。
        *   **`MessageTreeManagerUseCase` (中心化消息树管理):**\
            **依赖:** `ChatRepository`.\
            **职责:** 作为消息树的中心管理器，提供缓存和树结构计算功能。\
            **核心方法:**\
                - `getCachedTree`: 返回当前缓存的树结构，如果不存在返回nil。\
                - `rebuildTree`: 重新构建完整的消息树缓存，包括活跃路径计算。\
                - `updateActiveMessage`: 更新活跃消息并重新计算相关的兄弟节点缓存。\
            **缓存策略:** 维护每个会话的 `MessageTreeCache` 实例，包含消息字典、活跃路径、各深度的兄弟节点等。\
            **更新触发:** 在消息创建、更新、删除时自动更新缓存。支持增量更新以提升性能。
        *   **`RegenerateSingleMessageUseCase` (简化为调用SendMessage):**\
            **依赖:** `ChatRepository`, `SendMessageUseCase`.\
            **逻辑:** 获取原消息信息（包括sessionId和llmInstanceId），然后调用 `SendMessageUseCase.execute` 并传入 `operationType: .regenerateSingleMessage` 和 `relatedMessageId: messageId`。所有具体的重新生成逻辑由 `SendMessageUseCase` 统一处理。\
            **返回:** 直接返回 `SendMessageUseCase` 的 `AsyncThrowingStream<LLMStreamingResponse, Error>`。
        *   **`EditMessageUseCase` (简化为调用SendMessage):**\
            **依赖:** `ChatRepository`, `SendMessageUseCase`.\
            **逻辑:** 获取原消息的会话信息，然后调用 `SendMessageUseCase.execute` 并传入新内容、`operationType: .editMessage` 和 `relatedMessageId: originalMessageId`。编辑消息创建和AI回复生成都由 `SendMessageUseCase` 统一处理。\
            **返回:** 直接返回 `SendMessageUseCase` 的 `AsyncThrowingStream<LLMStreamingResponse, Error>`。
    *   **Setting Use Case 实现 (在 `Domain/UseCases/Chats/Setting/` 下创建对应文件):**
        *   **`GetChatSessionSettingUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 调用 `chatRepository.getSetting(byId:)`。
        *   **`GetDefaultChatSessionSettingUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 调用 `chatRepository.getSystemDefaultSetting()`，获取 `isSystemDefault=true` 的设置。
        *   **`CreateChatSessionSettingUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 接收完整的 `ChatSessionSetting` 实体，调用 `chatRepository.createSetting(setting)`。
        *   **`UpdateChatSessionSettingUseCase`**:\
            依赖: `ChatRepository`.\
            逻辑: 接收完整的 `ChatSessionSetting` 实体，调用 `chatRepository.updateSetting(setting)`。对于系统默认设置，需要检查是否允许修改。
    *   **错误定义 (`Domain/Errors/ChatError.swift`):**
        *   **错误层次设计:** `ChatError` 包装并扩展 `APIError`，提供聊天模块特有的错误处理
        *   `enum ChatError: Error, LocalizedError, Equatable {`\
            `// 聊天特有错误`\
            `case sessionNotFound`\
            `case messageSendFailed(underlying: Error? = nil)`\
            `case settingNotFound`\
            `case invalidMessageTree`\
            `case duplicateSessionCreationFailed`\
            `case messageEditFailed`\
            `case regenerationFailed`\
            `case streamingInterrupted(instanceId: UUID)`\
            `case batchSaveFailed`\
            `// API错误包装 - 复用APIError但增加聊天上下文`\
            `case apiError(APIError, instanceId: UUID? = nil)`\
            `case multiInstancePartialFailure([UUID: APIError])` // 多实例时部分失败\
            `// 辅助方法`\
            `static func from(_ apiError: APIError, instanceId: UUID? = nil) -> ChatError`\
            `var errorDescription: String? { ... }` (提供用户友好的本地化描述)\
        `}`
    *   **消息操作类型定义 (`Domain/ValueObjects/MessageOperationType.swift`):**
        *   **操作类型枚举:** 定义不同的消息操作类型以支持统一的SendMessageUseCase处理
        *   **类型定义:** `enum MessageOperationType { case newMessage, editMessage, regenerateSingleMessage }`
        *   **用途:** 帮助SendMessageUseCase根据不同操作类型执行相应的业务逻辑（如设置metadata、选择实例等）
    *   **消息树缓存设计 (`Domain/ValueObjects/MessageTreeCache.swift`):**
        *   **缓存结构:** 使用class实现，支持增量更新和高效查询
        *   **核心属性:** `sessionId`, `chatSession`, `allMessages` (消息字典), `messagesByParentId` (父子关系映射), `activePathSiblings` (按深度分组的兄弟消息), `activeMessagePath` (活跃路径)
        *   **主要方法:** `updateMessage`, `addMessage`, `removeMessage`, `setActiveMessage`, `getActivePathMessage`, `getSiblingsAt`, `invalidateCache`
        *   **性能优化:** 支持增量更新，避免全量重建。维护多个索引结构加速查询。
    *   **流式响应缓冲策略 (`Domain/Utils/StreamingBuffer.swift`):**
        *   **缓冲区设计:** 为每个实例维护独立的累积缓冲区
        *   **核心属性:** `contentBuffer`, `lastSaveTime`, `tokenCount`, `saveIntervalSeconds`, `tokenBatchSize`
        *   **主要方法:** `append` (追加内容并检查是否需要保存), `shouldSave` (检查保存条件), `getBufferedContent` (获取并清空缓冲区)
    *   **UseCase集成与优化设计:**
        *   **双重中心化架构:** 
            *   `MessageTreeManagerUseCase` 作为消息树管理的中心，负责缓存和树结构计算
            *   `SendMessageUseCase` 作为消息处理的中心，统一处理新消息、编辑、重新生成等操作
        *   **流式统一:** 所有消息操作UseCase都返回相同的 `AsyncThrowingStream<LLMStreamingResponse, Error>`，简化ViewModel处理逻辑。
        *   **代码复用最大化:** `RegenerateSingleMessageUseCase` 和 `EditMessageUseCase` 仅作为 `SendMessageUseCase` 的轻量级包装器，避免重复实现。
        *   **参数化操作:** 通过 `MessageOperationType` 枚举让 `SendMessageUseCase` 支持不同的处理逻辑，保持接口简洁统一。
        *   **性能优化:** 通过缓存避免重复的数据库查询和树结构计算。支持增量更新减少不必要的计算开销。
        *   **扩展性:** 设计支持未来添加更多消息操作类型而不需要大幅重构。
    *   **MockRepository实现:**
        *   创建 `Mocks/Data/Repositories/MockChatRepository.swift`。
        *   实现所有Repository协议方法。
        *   包含静态或可变的 `ChatSession`, `Message`, `ChatSessionSetting` 数组作为模拟数据源。
        *   使用 `MockChatPreviewData` 作为数据源。
        *   提供所有CRUD操作和观察者方法的模拟实现。

29. **实现对话列表 ViewModel 真实数据集成 (`ChatsViewModel.swift`):**
    *   **移除 Mock 数据依赖:** 更新现有的 `ChatsViewModel.swift`，移除使用 `MockChatPreviewData` 的临时实现。
    *   **`@Published` 属性 (保持现有):**
        *   `sessions: [ChatSession] = []`
        *   `isLoading: Bool = false`
        *   `errorMessage: String? = nil`
        *   `searchText: String = ""`
        *   `filteredSessions: [ChatSession] = []`
        *   `isNavigatingToChatSession: Bool = false`
        *   `selectedSession: ChatSession? = nil`
    *   **依赖注入更新 (构造函数):**
        *   `getAllSessionsUseCase: GetAllChatSessionsUseCaseProtocol`
        *   `deleteSessionUseCase: DeleteChatSessionUseCaseProtocol`
        *   `observeChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol` (替换原计划的 `observeSessionsUseCase`)
        *   **移除 `createSessionUseCase` 和 `llmInstanceRepository`：** 新会话创建通过 `NewChatSheetView` 的回调机制实现，符合职责分离原则
    *   **初始化方法重构:**
        *   移除 mock 数据初始化调用。
        *   保存注入的 Use Cases。
        *   调用 `subscribeToSessionChanges()`。
        *   异步调用 `loadSessions()`。
        *   保持现有的搜索过滤 Combine 逻辑。
    *   **核心方法实现:**
        *   `func loadSessions() async`:\
            设置 `isLoading = true`, `errorMessage = nil`。\
            使用 `do-catch` 调用 `getAllSessionsUseCase.execute()`。\
            成功后更新 `self.sessions` 并调用 `applyFilters()`。\
            错误处理使用 `ChatError` 类型，调用 `handleError(_ error: ChatError)`。\
            设置 `isLoading = false`。
        *   `func refreshSessions() async`: 调用 `loadSessions()`。
        *   `func deleteSession(at offsets: IndexSet)`:\
            获取 `sessionId` 从 `filteredSessions[offsets]`。\
            调用 `deleteSessionUseCase.execute(sessionId: sessionId)`。\
            处理 `ChatError` 类型的错误。
        *   `private func subscribeToSessionChanges()`:\
            调用 `observeChatsChangesUseCase.execute()`。\
            `.sink` 接收变化事件，在主线程调用 `loadSessions()` 刷新数据。\
            存储 `AnyCancellable`。
        *   `private func applyFilters()` 更新:\
            移除对 `MockChatPreviewData.getMessagesForSession()` 的调用。\
            基于 `sessions` 中的真实数据进行过滤 (暂时仅基于 `title` 和 `lastMessageSnippet`)。\
            **注意:** 完整的消息内容搜索需要额外的 Use Case 或 Repository 方法，V1 阶段可简化处理。
    *   **错误处理更新:** 
        *   `private func handleError(_ error: Error)` 重构为 `private func handleError(_ error: ChatError)`。
        *   使用 `ChatError.errorDescription` 提供用户友好的错误信息。
        *   对于非 `ChatError` 类型，包装为 `ChatError.messageSendFailed(underlying: error)`。
    *   **依赖注入工厂更新:**
        *   **`UseCasesFactory.swift`:** 添加 `makeObserveChatsChangesUseCase()` 方法返回 `ObserveChatsChangesUseCase` 实例。
        *   **`ViewModelsFactory.swift`:** 更新 `makeChatsViewModel()` 方法，移除 `ChatsViewModel()` 简单调用，改为传入真实的 use cases:\
            ```swift
            return ChatsViewModel(
                getAllSessionsUseCase: useCasesFactory.makeGetAllChatSessionsUseCase(),
                deleteSessionUseCase: useCasesFactory.makeDeleteChatSessionUseCase(),
                observeChatsChangesUseCase: useCasesFactory.makeObserveChatsChangesUseCase()
            )
            ```
    *   **新会话创建流程优化:**
        *   **职责分离:** `ChatsViewModel` 专注于会话列表管理，不负责新会话创建。
        *   **实现 NewChatSheet 回调:** 在 `ChatsView` 中更新 `NewChatSheetView` 的回调实现，调用 `CreateChatSessionUseCase` 创建会话。
        *   **自动导航:** 新会话创建成功后，自动导航到对应的 `ChatView`，并通过观察者机制更新会话列表。
    *   **ChatsView 适配确认:** 确保 `ChatsView.swift` 能够正确处理基于真实数据的 `ChatsViewModel`，特别是错误处理和加载状态显示。

30. **实现对话界面 ViewModel 真实数据集成 (`ChatViewModel.swift`):**
    *   **移除 Mock 数据依赖:** 更新现有的 `ChatViewModel.swift`，移除使用 `MockChatPreviewData` 的临时实现和相关mock方法。
    *   **依赖注入更新 (构造函数):**
        *   `getChatSessionUseCase: GetChatSessionUseCaseProtocol`
        *   `sendMessageUseCase: SendMessageUseCaseProtocol`
        *   `updateMessageUseCase: UpdateMessageUseCaseProtocol`
        *   `messageTreeManagerUseCase: MessageTreeManagerUseCaseProtocol`
        *   `observeChatsChangesUseCase: ObserveChatsChangesUseCaseProtocol`
        *   `getChatSessionSettingUseCase: GetChatSessionSettingUseCaseProtocol`
        *   `getInstancesWithRelatedEntitiesUseCase: GetInstancesWithRelatedEntitiesUseCaseProtocol`
    *   **初始化方法重构:**
        *   移除所有对 `MockChatPreviewData` 的调用。
        *   保存注入的 Use Cases。
        *   调用 `subscribeToRelevantChanges()`。
        *   异步调用 `loadInitialData()`。
    *   **核心方法重构:**
        *   `func loadInitialData() async`:\
            使用 `getChatSessionUseCase.execute(sessionId:)` 获取会话数据。\
            使用 `getChatSessionSettingUseCase.execute(settingId:)` 获取设置。\
            调用 `messageTreeManagerUseCase.initializeTreeFromRepository(sessionId:)` 初始化消息树。\
            调用 `messageTreeManagerUseCase.getMessageLayersForDisplay(sessionId:)` 获取 `MessageLayer` 数组。\
            使用新的 `computeMessageRowViewModelsFromLayers(_ layers: [MessageLayer])` 方法构建ViewModel。\
            批量加载所需的 `instanceContext` 并更新缓存。\
            统一使用 `ChatError` 错误处理。
        *   `func sendMessage(content: [ContentBlock]) async`:\
            移除模拟逻辑，使用真实的流式API。\
            编辑模式时：调用 `sendMessageUseCase.execute(operationType: .editMessage)`。\
            普通发送时：调用 `sendMessageUseCase.execute(operationType: .newMessage)`。\
            处理返回的 `AsyncThrowingStream<LLMStreamingResponse, Error>`：\
                `.contentDelta`: 调用 `appendToMessageDisplayContentText` 更新UI。\
                `.completionMarker`: 更新消息状态和token信息。\
                `.error`: 转换为 `ChatError` 并显示。\
            响应完成后调用 `rebuildViewModelsFromUpdatedLayers()` 更新视图。
        *   `func computeMessageRowViewModelsFromLayers(_ layers: [MessageLayer])`:\
            遍历 `MessageLayer` 数组，为每层创建 `MessageRowViewModel`。\
            从 `layer.messages` 获取 `Message` 数组。\
            检查有没有在缓存中的 `instanceContextCache`，没有的批量加载 `instanceContext`。\
            创建 `MessageViewModel` 数组，根据 `layer.activeMessageId` 设置 `MessageRowViewModel` 的 `activeUserMessageId` 和 `activeAssistantMessageId`。\
            确保层级关系和活跃状态正确映射到ViewModel结构。
    *   **消息树更新逻辑重构:**
        *   `func rebuildViewModelsFromUpdatedLayers()`:\
            调用 `messageTreeManagerUseCase.getMessageLayersForDisplay(sessionId:)` 获取最新层级。\
            智能更新：比较新旧ViewModel数组，只更新需要变化的部分: \
            识别 `activeMessageId` 发生变化的层级索引。\
            仅重建该层级之后的所有MessageRowViewModel。\
            保留之前层级的ViewModel以优化性能。\
            使用 `computeMessageRowViewModelsFromLayers` 重建ViewModel数组。\
        *   **MessageInteractionHandler实现更新:**\
            `userMessageDidChange`：调用 `messageTreeManagerUseCase.switchActiveMessage(sessionId: UUID, fromMessageId: UUID, newActiveMessageId: UUID)` 获取新层级数组，然后调用 `rebuildViewModelsFromUpdatedLayers()`。\
            `assistantMessageDidChange`：同上逻辑。\
            `likeMessage`/`dislikeMessage`：调用 `updateMessageUseCase.execute()` 更新数据库，同时更新本地ViewModel状态。\
            移除所有mock相关的本地数组操作。
    *   **流式响应处理:**
        *   `func handleStreamingResponse(_ stream: AsyncThrowingStream<LLMStreamingResponse, Error>)`:\
            直接处理UseCase返回的流式响应，实时更新UI显示文本。\
            LLMAPIServiceProtocol服务层已经实现缓冲机制，ViewModel无需额外缓冲处理。\
            统一错误处理使用 `ChatError` 。
    *   **观察者模式实现:**
        *   `func subscribeToRelevantChanges()`:\
            订阅 `observeChatsChangesUseCase.execute(forSpecificSessionId:)`。\
            接收返回的 `AnyPublisher<ChatViewRelevantChange, Never>` 数据。\
            根据不同的 `ChatViewRelevantChange` (lavachat/Domain/ValueObjects/ChatViewRelevantChange.swift) 类型采取相应动作。\
            确保 `AnyCancellable` 正确管理，避免内存泄漏。
    *   **实例缓存优化:**
        *   保留 `instanceContextCache` 在ViewModel级别。\
        *   优化 `loadInstanceContexts` 方法，使用UseCase批量加载。\
        *   智能更新缓存，避免每次都重新加载。
    *   **错误处理统一:**
        *   所有错误处理使用 `ChatError` 。\
        *   提供用户友好的本地化错误消息。\
        *   为可重试错误提供重试选项UI。\
        *   确保错误状态不会阻塞UI操作。
    *   **依赖注入工厂更新:**
        *   **`ViewModelsFactory.swift`:** 更新 `makeChatViewModel()` 方法，注入所有需要的真实UseCases。
        *   **`UseCasesFactory.swift`:** 确保所有相关UseCase工厂方法已实现。
        *   **`DIContainer.swift`:** 更新相关方法签名，验证依赖链条完整性。
    *   **代码清理:**
        *   移除所有对 `MockChatPreviewData` 的引用。\
        *   删除不再使用的模拟方法和属性。\
        *   更新注释和文档，反映新的真实数据架构。\
        *   优化性能，避免不必要的重复计算。

31. **连接对话模块的 Presentation -> Domain -> Data 层:**
    *   **DIContainer 更新:**
        *   `RepositoriesFactory`: 添加 `makeChatRepository()`。
        *   `UseCasesFactory`: 添加创建所有对话相关 Use Cases 的方法 (e.g., `makeGetAllChatSessionsUseCase()`, `makeSendMessageUseCase()`)。
        *   `ViewModelsFactory`: 添加 `makeChatsViewModel()` 和 `makeChatViewModel(chatSessionId: UUID)`。
    *   **Repository 实现 (`CoreDataChatRepository.swift`):**
        *   在 `Data/Repositories/Implementations/` 创建 `CoreDataChatRepository.swift`。
        *   实现 `ChatRepository` 协议。
        *   **`CDChatSession` & `CDMessage`:** 确保 Core Data 实体已定义，并包含所有必要属性 (`title`, `lastModifiedAt`, `role`, `content` (Data类型用于`[ContentBlock]`), `createdAt`, `promptTokens`, `completionTokens`, `parentId`, `llmInstanceId` 等)。
        *   **Mappers:** 创建 `ChatSessionMapper` 和 `MessageMapper` (在 `Data/Utils/Mappers/`) 用于 `CDChatSession <-> ChatSession` 和 `CDMessage <-> Message` 转换。`Message.content` ([ContentBlock]) 需要序列化/反序列化到 `CDMessage.content` (Data)。
        *   **`observeSessions()` 和 `observeMessages(forSessionId:)`:** 使用 `NSFetchedResultsControllerDelegate` 或 Combine-CoreData 包装器 (如 `CoreDataPublisher`) 来发布更改。
    *   **View 初始化:**
        *   `MainTabView`: "Chats" Tab 指向 `ChatsView(viewModel: container.makeChatsViewModel())`。
        *   `ChatsView`: `NavigationLink` 指向 `ChatView(viewModel: container.makeChatViewModel(chatSessionId: session.id))`。
        *   确保 `@EnvironmentObject var container: DIContainer` 在顶层视图 (`lavachatApp`) 注入。

32. **实现基础消息功能 (MVP 数据层面):**
    *   **`SendMessageUseCase`:**
        *   在从 `LLMAPIClient` 获得回复和 token 计数后，创建 `assistant` 角色的 `Message` 对象时，填充 `promptTokens` 和 `completionTokens` 属性。
        *   调用 `chatRepository.createMessage()` (或类似方法) 保存此助手消息。
    *   **`ChatRepository` (CoreData 实现):**
        *   `createMessage(message: Message)` 方法 (或其内部逻辑) 确保 `CDMessage` 的 `promptTokens` 和 `completionTokens` 属性被正确赋值。
    *   **数据流:**
        *   用户发送消息 -> `ChatViewModel.sendMessage()`
        *   -> `SendMessageUseCase.execute()`
        *   -> `LLMAPIClient` (获取回复 + token)
        *   -> `SendMessageUseCase` (创建助手 `Message` 实体含 tokens)
        *   -> `ChatRepository.saveMessage()` (将 `Message` 实体包括 tokens 存入 CoreData `CDMessage`)
        *   -> `ObserveMessagesUseCase` (通知 `ChatViewModel` 更新UI)
    *   **UI 展示 (MVP 范围外):** MVP 阶段不在 UI 上直接显示单条消息的 token 消耗，但数据会被记录。后续可在消息详情或调试模式中展示。