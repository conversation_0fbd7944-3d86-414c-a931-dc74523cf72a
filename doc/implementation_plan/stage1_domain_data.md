**阶段 1: 核心领域实体与本地数据层 (Domain & Data Layers - 本地优先)**

7.  **实现 Domain Entities:**
    *   根据 `entities_*.md` 文件，在 `Domain/Entities` 目录下创建所有核心实体的 Swift 结构体或类 (`User`, `ChatSession`, `Message`, `ChatSessionSetting`, `FileSession`, `FileCheckpoint`, `LLMProvider`, `LLMModel`, `LLMInstance`, `LLMInstanceGroup`, `SubscriptionPlan`, `SubscriptionStatus` 等)。
    *   确保实体定义与文档一致，添加 `Identifiable` (通常使用 `UUID`) 和 `Codable` (为未来序列化做准备)。
    *   **重点:** 详细实现 `Message` 的树状结构相关字段 (`parentId`) 和 `metadata` (用于存储 `is_edit_of`, `is_regeneration_of`, `merged_from` 等关系，类型为 `Data` 或 `[String: String]`)，以及 `ContentBlock` 枚举 (MVP 暂时只处理 `.text`)。
8.  **定义 Repository 接口:**
    *   在 `Domain/Interfaces/Repositories` 中定义数据操作协议，例如：
        *   `ChatRepository`: 定义 CRUD 操作 `ChatSession`, `Message` 的方法。
        *   `LLMInstanceRepository`: 定义 CRUD 操作 `LLMInstance`, `LLMProvider`, `LLMModel`, `LLMInstanceGroup` 的方法。
        *   `UserSettingsRepository`: 定义获取/更新用户偏好设置的方法。
        *   `KeychainRepository`: 定义安全存取 API Key 的方法。
9.  **设计 Core Data 模型 (`.xcdatamodeld`):**
    *   打开项目的 `.xcdatamodeld` 文件。
    *   为需要本地持久化的 MVP 核心实体创建对应的 Core Data Entities (`CDUser`, `CDChatSession`, `CDMessage`, `CDChatSessionSetting`, `CDLLMProvider`, `CDLLMModel`, `CDLLMInstance`, `CDLLMInstanceGroup`)。
    *   精确映射实体属性到 Core Data Attributes (String, Date, Int64, Boolean, UUID)。
    *   对于 `metadata` 或其他需要存储复杂结构（如数组、字典、自定义对象）的属性，使用 `Data` 类型，并确保对应的 Swift 类型遵循 `Codable` 协议。将在 Repository 层使用 `JSONEncoder`/`JSONDecoder` 进行序列化/反序列化，避免使用不安全的 `Transformable`。
        *   在 Core Data 模型编辑器中将这些属性的类型选择为 **`Binary Data`** (对应 XML 中的 `attributeType="Binary"`)。
        *   relationship 里面 XML 中的所有关系如果不是 `to-many`，即是 `to-one`，则需要添加 `maxCount="1"`
        *   对于 XML 中所有非 optional 的 attribute，就算我们之后会在下一步的 awakeFromInsert 中进行初始化，也需要在 XML 中给一个初始值以避免 error，该初始值会被 awakeFromInsert 覆盖。
            *   Date格式默认值设定：`defaultDateTimeInterval="0"`
            *   UUID格式默认值设定：`defaultValueString="00000000-0000-0000-0000-000000000000"`
    *   **关键:** 正确设置 Relationships，特别是 `CDMessage` 的父子关系 (`parent` <-> `children`)。
    *   设置必要的索引和约束。
10. **实现 Core Data 栈与本地数据源:**
    *   **核心栈初始化:**
        *   检查并完善 Xcode 自动生成的 `Persistence.swift` 文件。确认其位于项目根目录。
        *   确认该文件正确初始化 `NSPersistentCloudKitContainer`。虽然 MVP 阶段不启用 CloudKit 同步，但使用此容器类型可以为后续 V1 功能做好准备。确保容器名称与 `.xcdatamodeld` 文件名匹配。
        *   确保 `container.viewContext.automaticallyMergesChangesFromParent = true` 已设置，以便主上下文能自动合并来自后台上下文的更改。
    *   **手动生成 NSManagedObject 子类:**
        *   在 Core Data 模型检查器中，将每个实体的 "Codegen" 选项设置为 "Manual/None"。
        *   在 Editor 标签页中，选择 "Create NSManagedObject Subclass" 生成文件。
        *   生成的文件（如 `CDMessage+CoreDataClass.swift` 和 `CDMessage+CoreDataProperties.swift`）需要手动修改：
            *   **初始化逻辑 (`awakeFromInsert`):**
                *   根据 <mcfile name="tech_stack_document.mdc" path="/Users/<USER>/Documents/script/lavachat/lavachat-ios/.cursor/rules/tech_stack_document.mdc"></mcfile> 中"模型定义注意事项"的要求，识别出那些包含非可选 `UUID` 或 `Date` 属性（如 `id`, `createdAt`, `timestamp`）的 Core Data 实体。
                *   在对应的 `NSManagedObject` 子类 (`... +CoreDataClass.swift`) 文件中，重写 `awakeFromInsert()` 方法。在此方法内部，为这些非可选属性赋予初始值，确保对象在首次插入时具有有效的默认值。
                    *   对于 `id`，setPrimitiveValue(UUID(), forKey: "id") // 使用字符串字面量 "id" 避免歧义
                    *   对于其他的Date和UUID用, setPrimitiveValue(Date(), forKey: #keyPath(CDChatSessionSetting.createdAt))
            *   添加自定义逻辑或扩展。
        *   将生成的文件统一存放在 `Data/DataSources/Local/CoreData/Entities/` 目录下。
    *   **上下文访问:**
        *   通过 `PersistenceController.shared` (或类似的单例/依赖注入机制) 提供对主上下文 (`container.viewContext`) 的便捷访问点。这是 UI 和 ViewModel 主要交互的上下文。（在lavachat/lavachat/Persistence.swift添加了）
        *   考虑在 `Data/DataSources/Local/CoreData/` 目录下创建 Context 管理相关的辅助扩展或工具类，例如提供一个获取新的私有队列后台上下文 (`container.newBackgroundContext()`) 的便捷方法，用于执行耗时的后台数据操作。（在lavachat/lavachat/Persistence.swift添加了）
        *   考虑在 `Data/DataSources/Local/CoreData/` 目录下创建一个或多个 `CoreDataSource` 类来封装这些底层细节。Repository 实现层随后可以调用这些 `CoreDataSource`。对于 MVP 阶段，直接在 Repository 实现中使用 `NSManagedObjectContext` 可能更直接。
11. **实现 Core Data Repositories:**
    *   在 `Data/Repositories/Implementations` 中创建具体类实现 `ChatRepository`, `LLMInstanceRepository`, `UserSettingsRepository` 接口。
        * `ChatRepository`：管理 `ChatSession` 和 `Message`。
        * `LLMInstanceRepository`：管理 `LLMInstance`（包括 `LLMProvider` 和 `LLMModel` 的关联逻辑）。
        * `UserSettingsRepository`：管理用户偏好设置。
    *   这些实现类将依赖注入的 `NSManagedObjectContext` (通常是 `viewContext`) 或上面提到的 `CoreDataSource`。
    *   实现接口定义的 CRUD 方法：
        *   执行 Core Data 操作 (Fetch, Insert, Update, Delete)。
        *   负责 Domain Entities (如 `Message`) 与 Core Data Managed Objects (如 `CDMessage`) 之间的转换（Mapper 逻辑）。这部分转换代码可以放在 Repository 实现内部，或者提取到 `Data/Utils/Mappers` 目录下的独立 Mapper 类中。
        *   处理 `metadata` 等 `Data` 类型属性的序列化 (使用 `JSONEncoder`) 和反序列化 (使用 `JSONDecoder`)。
    *   **重点:** 实现 `Message` 的树相关查询逻辑（例如，获取某个 Session 的所有 `CDMessage`，然后在 Repository 或 Use Case 层根据 `parent` 关系构建 Domain `Message` 树）。
    *   **实现 Keychain 工具类:**
        *   在 `Data/Utils/Keychain` 中创建 `KeychainHelper.swift`。
        *   实现 `KeychainRepository` 接口。
        *   使用 iOS 原生 `Security` 框架 (`SecItemAdd`, `SecItemCopyMatching`, `SecItemUpdate`, `SecItemDelete`) 安全地存储、读取、更新和删除 API Key。Key 的标识符应与 `LLMProvider.id` (UUID string) 相关联。
12. **依赖注入DependencyInjection:**
    *   **目标:** 建立一个结构化的依赖注入系统，使得 ViewModel 的创建和其依赖（Use Cases）的管理与 View 层解耦。View 层只需向一个中心化的机制请求所需的 ViewModel 实例，而无需关心其内部依赖是如何被满足的。
    *   **实现 Factory:**
        *   **目的:** Factory 负责创建特定类型的对象实例。将创建逻辑集中在 Factory 中，使得对象的创建过程更清晰、更易于管理和替换（例如在测试中替换为 Mock 实现）。
        *   **组织:** 在 `Application/DependencyInjection/Factories/` 目录下创建：
            *   `RepositoriesFactory.swift`: 负责创建 Repository 的具体实现。例如，它可能有一个方法 `makeLLMInstanceRepository(context: NSManagedObjectContext) -> LLMInstanceRepository` 返回一个 `CoreDataLLMInstanceRepository` 实例。
            *   `UseCasesFactory.swift`: 负责创建 Use Case 的实例。它会**依赖** `RepositoriesFactory` (或直接依赖 Repository 实例) 来获取 Use Case 所需的 Repository。例如，`makeGetProviderUseCase() -> GetProviderUseCase` 会调用 `repositoriesFactory.makeLLMInstanceRepository()` 并将其注入到 `GetProviderUseCase` 的构造函数中。
            *   `ViewModelsFactory.swift`: 负责创建 ViewModel 的实例。它会**依赖** `UseCasesFactory` 来获取 ViewModel 所需的 Use Cases。例如，`makeProviderDetailViewModel(providerId: UUID?) -> ProviderDetailViewModel` 会调用 `useCasesFactory` 来获取所有 `ProviderDetailViewModel` 需要的 Use Cases，并将它们和 `providerId` 一起注入到 `ProviderDetailViewModel` 的构造函数中。
        *   **与 Repository 接口的关系:**
            *   `Domain/Interfaces/Repositories/` 定义的是**协议（接口）**，规定了 Repository 应该具有哪些方法（例如 `func getProvider(id: UUID) async throws -> LLMProvider`）。这是业务逻辑层（Use Cases）所依赖的**抽象**。
            *   `RepositoriesFactory` 负责创建**遵循这些协议的具体类**（例如 `CoreDataLLMInstanceRepository`）。Factory 返回的是**协议类型** (`LLMInstanceRepository`)，而不是具体的类，这样调用方（如 `UseCasesFactory`）就只依赖于抽象，而不知道具体的实现是 Core Data 还是未来的 SQLite。
    *   **实现 DIContainer:**
        *   **目的:** 作为整个依赖注入系统的中央协调器和入口点。它持有所有 Factory 的实例，并负责根据请求（通常来自 View 层）组装和提供顶层依赖（主要是 ViewModels）。
        *   **实现:** 创建 `Application/DependencyInjection/DIContainer.swift`。
            *   `DIContainer` 类会持有 `RepositoriesFactory`, `UseCasesFactory`, `ViewModelsFactory` 的实例。
            *   在 `DIContainer` 的 `init` 方法中，需要创建这些 Factory 实例。创建 `RepositoriesFactory` 时，需要传入 `NSManagedObjectContext`。
            *   `DIContainer` 会暴露供 View 层调用的方法，这些方法实际上是调用 `ViewModelsFactory` 的方法来创建 ViewModel (例如，`func makeProviderDetailViewModel(providerId: UUID?) -> ProviderDetailViewModel`)。
    *   **在 `Application/lavachatApp.swift` 中初始化和注入**
        1.  在 `lavachatApp` 结构体内部，使用 `@StateObject` 创建 `DIContainer` 的一个实例。
        2.  将这个 `container` 实例通过 `.environmentObject()` 修改器注入到根视图 (`MainTabView`) 的环境中。
    *   **生命周期管理:** `@StateObject` 会确保 ViewModel 的生命周期与 View 绑定，而 `DIContainer` 作为 `@StateObject` 在 `App` 级别创建，其生命周期将贯穿整个应用程序。
    *   **依赖关系流与 DIContainer 的使用范围:**
        *   **依赖构建流:** 当 View 需要 ViewModel 时，它会从环境 (`@EnvironmentObject`) 中获取 `DIContainer`，然后调用 `container.make...ViewModel()`。这个调用会触发 `ViewModelsFactory` -> `UseCasesFactory` -> `RepositoriesFactory` 的链式调用，最终创建出所有需要的对象并将依赖注入。
        *   **`DIContainer` 的使用者:**
            *   **主要使用者是 View 层:** View 通过 `DIContainer` 来获取它需要的 ViewModel 实例。这是解耦的关键。
            *   **Use Cases 和 Repositories 是否需要 `DIContainer`?** **不需要。** Use Cases 通过构造函数接收 Repository *协议*作为依赖。Repositories 通过构造函数接收 `NSManagedObjectContext` 或其他基础设施作为依赖。向它们注入整个 `DIContainer` 会破坏依赖倒置原则，导致不必要的耦合。它们只需要自己明确声明的依赖即可，这些依赖由 Factories 负责提供。
            *   **ViewModel 内部是否需要 `DIContainer`?** **通常不需要。** ViewModel 通过构造函数接收其所需的所有 Use Cases。如果一个 ViewModel 需要创建 *另一个* ViewModel（这种情况应尽量避免，可能暗示需要引入 Coordinator 模式），理论上可以通过环境对象访问 `DIContainer`，但这通常不是推荐的最佳实践。ViewModel 的主要职责是服务于它的 View，而不是创建其他复杂的依赖。