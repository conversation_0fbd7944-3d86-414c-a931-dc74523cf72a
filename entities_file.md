**熔言 / LavaChat - 文件交互模块领域设计文档 (FileSession)**

**1. 核心设计理念**

*   **状态快照 (Checkpointing):** 每次关键交互（尤其是用户发送消息）后，都应记录文件内容和待处理编辑状态的完整快照 (`FileCheckpoint`)，实现可靠的回溯。
*   **显式操作分离:** 清晰区分用户与 AI 的交互模式 (`chat` vs `edit`)，并在 `Message` 层面体现 AI 的意图（建议、思考、修改请求）。
*   **结构化编辑:** 将 AI 提出的文件修改建议 (`DiffBlock`) 作为结构化数据处理，便于 UI 展示、用户交互（接受/拒绝/修改）和状态追踪。
*   **用户控制:** 用户始终拥有最终决定权，可以接受、拒绝或手动修改 AI 的编辑建议，并且这些决策需要被准确记录和传递。

**2. 领域层实体设计 (Domain Layer Entities)**

**2.1. `FileSession` (文件会话)**

`FileSession` 代表用户与单个文件进行交互的整个过程，包括围绕文件的对话和对文件的编辑。

*   **`id` (唯一标识符):** UUID，唯一识别一个文件会话。
*   **`userId` (用户ID):** 指向拥有此会话的 `User` 实体的ID。
*   **`originalSourceInfo` (原始来源文件信息):** (取代 `managedFileId`) 结构体/字典。存储关于导入的原始文件的元数据（如原始文件名 `originalFileName`, 原始类型 `originalFileType` - 'pdf', 'docx', 'txt', 'md' 等）。应用仅在初次导入/转换时访问原始文件，之后不保证或需要持久访问权限。
*   **`title` (标题):** 字符串。通常来源于 `originalSourceInfo.originalFileName`，用户也可修改。
*   **`createdAt` (创建时间):** 会话创建的时间戳。
*   **`lastModifiedAt` (最后修改时间):** 会话中最后一次活动（消息、文件修改确认等）的时间戳。
*   **`currentFileCheckpointId` (当前文件状态ID):** 指向最新的 `FileCheckpoint` 实体的ID，表示当前编辑器或用户看到的文件的基准版本（对应下方 `bookmarkData` 指向的 Markdown 文件）。
*   **`rootMessageId` (根消息ID):** 指向该会话中消息树的根节点 `Message` 的ID。
*   **`activeLLMInstanceId` (活跃模型实例ID):** (可选，Nullable) 如果当前交互处于 `edit` 模式且由特定 AI 主导，记录其 `LLMInstance` ID。在 `chat` 模式下或未指定编辑 AI 时可为 `null`。
*   **`settingsId` (会话设置ID):** 指向关联的 `ChatSessionSetting` 实体的ID。可以复用 `ChatSessionSetting`，其中可以包含针对文件交互的特定提示词或参数覆盖。
*   **`bookmarkData` (托管 Markdown 文件安全书签数据):** `Data?`
    *   **说明:** 用于存储通过 Security-Scoped Bookmark 机制获取的、指向 **此 FileSession 实际操作和管理的 Markdown 文件** 的安全书签数据。这个 Markdown 文件通常是原始文件转换而来（或直接创建的），存储在应用专属文件夹内（例如 `LavaChatFiles/Sessions/`）。
    *   **作用:** 确保持久、安全地访问这个 **核心的 Markdown 文件**，即使它在 `LavaChatFiles/Sessions/` 文件夹内被移动或重命名。
    *   **应用:** 在创建 `FileSession` 并生成其对应的 Markdown 文件（位于应用管理的文件夹内）时生成此书签数据并存储。后续所有文件读写操作（包括 Checkpoint 应用、@提及内容读取、导出 Markdown 等）都应先从此 `bookmarkData` 解析 URL，并调用 `url.startAccessingSecurityScopedResource()` 获取权限，操作完成后调用 `url.stopAccessingSecurityScopedResource()`。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 键值对集合，用于存储额外信息，如转换设置、标签等。对应的 Swift 类型需遵循 `Codable`。

**2.2. `FileCheckpoint` (文件状态 / Checkpoint)**

`FileCheckpoint` 代表文件在某个特定时间点的快照，是实现回溯和追踪编辑状态的核心。

*   **`id` (唯一标识符):** UUID，唯一识别一个文件状态快照。
*   **`fileSessionId` (文件会话ID):** 指向其所属的 `FileSession` 的ID。
*   **`timestamp` (时间戳):** 此状态快照创建的时间。
*   **`basedOnCheckpointId` (基于状态ID):** (可选，Nullable) UUID。指向创建此状态所基于的前一个 `FileCheckpoint` 的ID。形成文件版本的链条。初始状态此字段为 `null`。
*   **`triggeringMessageId` (触发消息ID):** (可选，Nullable) UUID。指向触发创建此 Checkpoint 的那条 `Message` 的ID（通常是用户发送的消息）。
*   **`fileContent` (文件内容):** 字符串 (String)。存储此时 **托管 Markdown 文件** 的完整内容。对于大文件，数据层实现时可能需要考虑外部存储或分块。
*   **`pendingEditStates` (待处理编辑状态列表):**
    *   **类型:** `Data?` (存储序列化后的 `[PendingEditInfo]`)
    *   **说明:** **核心字段**，存储在此 Checkpoint 创建时，所有尚未被用户最终确认（接受/拒绝）的 AI 编辑建议或用户手动产生的待确认修改的状态。`PendingEditInfo` 结构需要遵循 `Codable`。详见 `PendingEditInfo`。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 键值对集合，例如存储此状态创建的原因（用户发送、AI建议应用完毕等，但主要还是用户发送时创建）。对应的 Swift 类型需遵循 `Codable`。

**2.3. `PendingEditInfo` (待处理编辑信息)**

嵌套在 `FileCheckpoint` 中的结构，用于追踪单个待处理编辑区域的状态。

*   **`diffBlockId` (差异块ID):** UUID。关联到某条 AI `Message` 内容中具体的 `DiffBlock.id`，表示这是由 AI 提出的建议。如果是用户手动编辑产生的待确认区域，可以生成一个新的 UUID 并标记来源。
*   **`sourceType` (来源类型):** 枚举 (`ai_generated`, `manual_edit`)。区分这个待处理区域是 AI 建议还是用户直接修改产生的。
*   **`status` (状态):** 枚举。表示用户对这个编辑区域的当前决策状态。
    *   `pending`: 待处理，用户尚未操作。
    *   `accepted`: 用户已接受。
    *   `rejected`: 用户已拒绝。
    *   `modified_pending`: 用户在此区域内进行了手动修改，但尚未最终确认（接受/拒绝这整个修改后的区域）。
*   **`manualEditDiff` (手动编辑差异):** (可选, String/Diff Structure) 如果 `status` 是 `modified_pending`，这里存储用户相对于 *AI建议后* (如果 `sourceType` 是 `ai_generated`) 或 *原始文件* (如果 `sourceType` 是 `manual_edit`) 所做的具体修改内容或差异描述。
*   **`rangeInFileContent` (在文件内容中的范围):** 结构体，描述这个待处理编辑区域在当前 `FileCheckpoint.fileContent` 中的精确位置（例如，行号、列号范围）。这对于 UI 高亮和交互至关重要。

**2.4. `Message` (消息 - 适配 FileSession)**

复用核心对话模块的 `Message` 实体，比较相关的内容：

*   **`id`, `sessionId`, `parentId`, `timestamp`, `role`, `userId`, `llmInstanceId`, `status`, `promptTokens`, `completionTokens`, `userFeedback`, `isReplied`, `isFavorited`:** 基本保持不变。`sessionId` 指向 `FileSession.id`。
*   **`content` (内容):** `[ContentBlock]`。需要扩展 `ContentBlock` 枚举以支持文件交互：
    ```swift
    enum ContentBlock: Codable {
        case text(String) // 普通文本、用户指令、AI解释
        case image(ImageInfo) // 如果支持在讨论中插入图片
        // ... 其他通用类型

        // --- FileSession 特有 (或在 ChatSession 中通过 @文件 功能引入) ---
        case attachedFileContent(fileName: String, content: String) // **(新增)** 用于 @文件 功能引入的内容

        // --- FileSession 特有 ---
        case thinking(String) // AI 思考过程，UI可选择性展示
        case fileReadRequest(path: String, range: Range?) // AI 请求读取文件内容
        case diffBlock(DiffBlock) // AI 提出的文件修改建议 (核心)
        case fileWriteResult(success: Bool, message: String?) // (可选) AI确认文件写入操作的结果
        case fileSearchRequest(query: String, path: String?) // AI 请求在文件中搜索
        case fileSearchResult(results: [SearchResultInfo]) // AI 返回搜索结果
        // ... 未来可能需要的其他文件操作相关块 ...
    }

    struct DiffBlock: Codable, Identifiable { // 使其 Identifiable 便于 SwiftUI 列表处理
        let id: UUID // 唯一标识一个 diff 建议，用于 PendingEditInfo 关联
        let originalContent: String // 被替换的原文
        let replacementContent: String // 建议替换成的内容
        let filePath: String // 目标文件路径 (初期固定)
        let rangeInOriginal: RangeInfo // 在原始文件中的位置信息 (行/列)
        let explanation: String? // (可选) AI 对此修改的解释
    }

    struct RangeInfo: Codable { ... } // 定义范围的数据结构
    struct SearchResultInfo: Codable { ... } // 定义搜索结果的数据结构
    ```
*   **`rawResponseText` (原始LLM响应):** (新增字段, 可选, String?) 存储未经解析的 LLM 完整响应文本。方便调试和追溯。
*   **`parsedFileOperations` (解析出的文件操作):** (新增字段, 可选, `[FileOperation]`) 存储从 `rawResponseText` 或结构化输出中解析出的、需要应用或执行的文件相关操作列表（如读、写/应用Diff、搜索）。这部分由 Use Case 层处理。
    ```swift
    enum FileOperation: Codable {
        case readFile(path: String, range: Range?)
        case applyDiff(diff: DiffBlock)
        case searchFile(query: String, path: String?)
        // ...
    }
    ```
*   **`metadata` (元数据):** 键值对。可以用来存储与该消息相关的特定上下文：
    *   `requested_mode`: `chat` | `edit` (用户消息上，表明意图)
    *   `input_file_checkpoint_id`: UUID (触发此消息时基于的 `FileCheckpoint` ID)
    *   `output_file_checkpoint_id`: UUID (如果此消息导致文件状态变更，记录变更后的 `FileCheckpoint` ID，例如AI应用了修改)
    *   `references_diff_block_id`: UUID (如果用户消息是针对某个具体 diff 的评论)

**2.5. `ManagedFile` (被管理文件 - 简单占位)**

初期可能不需要一个非常复杂的实体，但概念上存在。

*   **`id` (唯一标识符):** UUID。
*   **`userId` (用户ID):** 所属用户。
*   **`originalPath` (原始路径):** (可选) 文件导入时的路径。
*   **`storedPath` (存储路径/引用):** App 内部安全存储区域的路径或标识符。
*   **`originalFormat` (原始格式):** 如 "docx", "pdf", "txt"。
*   **`currentMarkdownPath` (当前Markdown路径):** 如果进行了格式转换，指向 Markdown 版本的存储位置。
*   **`createdAt`, `lastAccessedAt`:** 时间戳。

**3. 功能实现详解**

1.  **模式切换 (`chat` vs `edit`):**
    *   用户在输入框旁或通过特定指令 (`/edit`, `/chat`) 选择模式。
    *   发送 `Message` 时，在 `metadata` 中记录 `requested_mode`。
    *   Use Case 层根据此模式：
        *   `chat` 模式：可以调用多个 AI (如果 `FileSession` 设置支持)，AI 不应生成 `DiffBlock`（但可以生成文本建议、`fileReadRequest` 等）。
        *   `edit` 模式：通常只调用一个指定的 AI (`activeLLMInstanceId`)，AI 的主要目标是理解用户意图并生成 `DiffBlock` 来修改文件。

2.  **Markdown 转换与处理:**
    *   **输入:** 当用户导入非 Markdown 文件时，调用一个 Use Case，该 Use Case 可能使用 AI (或本地库) 将文件内容转换为 Markdown，然后创建 `FileSession` 和初始 `FileCheckpoint` (存储 Markdown 内容)。`ManagedFile` 记录原始格式。
    *   **编辑:** 所有编辑操作（AI 生成的 `DiffBlock` 和用户手动编辑）都直接作用于 Markdown 格式的 `FileCheckpoint.fileContent`。
    *   **输出:** 用户选择导出时，提供选项。如果选择非 Markdown 格式，调用 Use Case 使用 AI (或库) 将当前的 `FileCheckpoint.fileContent` (Markdown) 转换回目标格式。

3.  **LLM 返回内容解析与存储:**
    *   接收到 LLM 响应后，先存储 `rawResponseText` 到 `Message`。
    *   然后尝试解析响应：
        *   识别特殊标记（如 `<thinking>`, `<diff>`, `<read_file>`）。
        *   将识别出的部分构造成对应的 `ContentBlock` 枚举实例（`.thinking`, `.diffBlock`, `.fileReadRequest` 等），放入 `content` 数组。普通文本放入 `.text`。
        *   将解析出的需要执行的文件操作（如应用 diff，读取文件）构造成 `FileOperation` 枚举实例，放入 `parsedFileOperations` 数组。
    *   这样既保存了原始文本，也保存了解析后的结构化内容（用于显示）和待执行操作（用于 Use Case 处理）。

4.  **UI 设计交互逻辑:**
    *   **Z 轴分层:** 文件内容 (Z=0)，对话记录毛玻璃悬浮 (Z=1)。
    *   **输入区域:**
        *   点击下方聊天输入框：触发 `chat`/`ask` 交互，键盘弹出，用户输入的是对 AI 的指令或问题。发送后创建用户 `Message`。
        *   点击文件区域：进入文件手动编辑模式，键盘弹出。用户的修改直接作用于 UI 上的文件视图。这些修改在用户下次发送 *聊天消息* 时才会被捕获进 Checkpoint。
    *   **对话显示:**
        *   毛玻璃效果，透视文件。高度限制（如半屏）。
        *   `thinking` 块默认折叠或显示动画，点击展开。
        *   `diffBlock` 块在文件内容中清晰展示（可能并排或上下显示原文和修改，或用颜色标记），并提供 Accept/Reject 按钮。点击 Accept/Reject 直接更新对应 `PendingEditInfo` 的状态，并可能实时更新文件视图（但 checkpoint 要等用户发送）。
    *   **全屏切换:** 长按或其他手势触发对话/文件全屏。

5.  **Checkpoint 及回溯逻辑:**
    *   **触发 Checkpoint:**
        *   **主要时机：用户点击发送消息时。** 这是最可靠的触发点。
            *   获取当前编辑器中的完整文件内容。
            *   获取上一个 `FileCheckpoint` 的 `pendingEditStates`。
            *   结合用户在 UI 上对这些 pending edits 的最新操作（Accept/Reject/Modify 点击），计算出当前所有 *仍然待处理* 的编辑状态（包括用户在两次发送之间新产生的手动编辑区域，这些需要 diff 算法识别出来并创建为 `manual_edit` 类型的 `PendingEditInfo`）。
            *   创建一个新的 `FileCheckpoint`，包含新的 `fileContent` 和计算出的 `pendingEditStates` 列表。设置 `basedOnCheckpointId` 指向上一个 checkpoint，`triggeringMessageId` 指向刚发送的用户消息。
            *   更新 `FileSession.currentFileCheckpointId` 指向这个新的 `FileCheckpoint`。
        *   **次要时机（可选，待评估）：** AI 返回了 `edit` 模式的 `DiffBlock` 并且这些修改被 Use Case 自动应用到文件视图后？*不建议*。这会产生过多 Checkpoint。最好是用户确认（Accept/Reject）或进一步交互时再记录。用户的发送行为是自然的分割点。
        *   **用户 Accept/Reject 点击时是否 Checkpoint？** *不建议* 创建完整的 `FileCheckpoint`。这个操作只更新当前活动 `FileCheckpoint` 的 `pendingEditStates` 中对应项的状态。最终状态会在下次用户发送时被固化到新的 `FileCheckpoint` 中。
    *   **回溯实现:**
        *   用户在对话历史中找到一个 Checkpoint（可能通过与某条用户消息关联的按钮）。
        *   点击“回到此版本”。
        *   Use Case 层找到对应的 `FileCheckpoint.id`。
        *   加载该 `FileCheckpoint` 的 `fileContent` 到文件编辑器视图。
        *   加载该 `FileCheckpoint` 的 `pendingEditStates`，并据此在 UI 上重新渲染所有待处理的编辑区域及其状态（高亮、Accept/Reject 按钮等）。
        *   将 `FileSession.currentFileCheckpointId` 更新为这个回溯到的 `FileCheckpoint.id`。后续的操作将基于这个状态进行。

6.  **处理手动编辑与 AI 编辑交互:**
    *   **情况一 (手动编辑在 AI 编辑区域之外):**
        *   用户编辑文件。
        *   用户发送新消息，触发 Checkpoint。
        *   Checkpoint 逻辑通过比较当前编辑器内容和上一个 `FileCheckpoint.fileContent` 来识别出新的手动编辑区域。
        *   为这些区域创建新的 `PendingEditInfo`，`sourceType = 'manual_edit'`, `status = 'pending'`，并记录范围。这些会显示为需要用户确认的独立更改。
    *   **情况二 (手动编辑在 AI 编辑区域之内):**
        *   UI 需要知道光标/编辑操作是否发生在某个已渲染的 `DiffBlock` 区域内。
        *   用户的修改直接应用在 UI 文件视图的这个区域。
        *   **关键：** 当用户发送新消息触发 Checkpoint 时，对于这个被修改的 `DiffBlock`，其对应的 `PendingEditInfo` 的状态应更新为 `modified_pending`。`manualEditDiff` 字段需要记录用户具体做了哪些修改（相对于 AI 的建议）。
        *   UI 上，这个区域的 Accept/Reject 按钮现在将作用于这个“AI建议 + 用户修改”的混合结果。Accept 会应用最终混合内容，Reject 会撤销整个（AI+手动）修改，回到此区域的原始状态。
        *   发送给 AI 的上下文需要包含这些 `PendingEditInfo` 的状态（哪个被接受，哪个被拒绝，哪个被用户修改了），AI 才能更好地进行下一步。

7.  **需要 Checkpoint 记录的内容总结:**
    *   **必须记录在 `FileCheckpoint` 中的:**
        *   完整的 `fileContent`。
        *   所有 *当时* 尚未最终 Accept/Reject 的编辑区域的状态列表 (`pendingEditStates`)，包含：
            *   是 AI 建议还是手动编辑 (`sourceType`)。
            *   当前状态 (`pending`, `modified_pending`, `accepted`, `rejected`)。
            *   如果是 `modified_pending`，用户的具体修改。
            *   该区域在文件中的位置。
    *   **何时创建 Checkpoint (`FileCheckpoint`)：**
        *   **用户点击发送信息时：是。** 这是最主要和必要的时机。
        *   AI 发来建议应用完时：**否。** 这只是中间状态。
        *   用户每点击一次 Accept/Reject 时：**否。** 这只更新当前活动状态，不创建新快照。

8.  **其他所需实体与文件操作:**
    *   `FileSession`, `FileCheckpoint`, `Message` (适配后), `PendingEditInfo` (嵌套结构), `ContentBlock` (扩展后), `DiffBlock` (嵌套结构) 是核心领域实体。
    *   `ManagedFile` 是辅助实体，管理文件元信息。
    *   **LLM 可用的 Tool Use (文件操作):** 这些**不应**设计为独立的顶层领域实体。它们是：
        *   **概念上：** AI 可以请求执行的操作（能力）。
        *   **实现上：**
            *   在 `Message.parsedFileOperations` 中以结构化数据 (`FileOperation` enum/struct) 表示 AI 的请求。
            *   由 **Use Case 层** 负责解释这些请求。
            *   由 **Data Layer** (文件系统适配器) 负责实际执行这些操作（读文件、写文件、搜索等）。
            *   领域层（Entities）只关心操作的 *请求* (作为消息内容的一部分) 和操作导致的 *状态变化* (反映在 `FileCheckpoint` 中)。

9.  **手机与 IDE 的差异考量:**
    *   **领域层 (Entities):** 设计应保持平台无关性。`FileCheckpoint`, `Message`, `DiffBlock` 的概念在两端都适用。
    *   **数据层 (Data Layer):** 文件存储和访问机制不同 (iOS Sandbox vs. Desktop FS)，需要平台特定的实现。
    *   **表现层 (Presentation Layer):** UI/UX 差异巨大。手机上是悬浮对话+有限编辑区，交互依赖触摸和手势。IDE 中是集成编辑面板+侧边栏对话，交互依赖鼠标键盘。ViewModel 需要适配不同平台的视图需求。
    *   **应用层 (Use Cases):** 核心逻辑（如创建 Checkpoint、处理 Accept/Reject、准备给 AI 的上下文）应该是共享的。但可能需要小的适配，比如错误处理、文件路径表示方式等。Clean Architecture 有助于隔离这些差异。

**4. 总结**

此设计方案通过引入 `FileSession` 和 `FileCheckpoint` 实体，并扩展 `Message` 和 `ContentBlock`，为“熔言 / LavaChat”的文件交互功能提供了领域模型基础。

*   **核心:** `FileCheckpoint` 捕获文件内容和待处理编辑状态，实现 Checkpoint 和回溯。`PendingEditInfo` 精确追踪每个编辑区域的状态。
*   **交互:** 清晰分离 `chat` 和 `edit` 模式，通过结构化的 `DiffBlock` 处理 AI 编辑建议，并设计了处理用户手动修改与 AI 建议交互的关键逻辑。
*   **架构:** 保持领域层平台无关，将文件操作视为 Use Case 和 Data Layer 的职责，为跨平台（尤其是未来桌面端）实现奠定了基础。

接下来的工作重点将是 Use Case 层如何编排这些实体以实现完整的交互流程，以及 Presentation Layer 如何根据这些实体状态有效地渲染 UI 并处理用户输入。