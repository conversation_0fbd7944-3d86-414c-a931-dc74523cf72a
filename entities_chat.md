**熔言 / LavaChat - 核心对话模块领域设计文档**

**1. 核心设计理念**

*   **不可变性（Immutability）优先：** 尽量不直接修改已存在的 `Message`，而是创建新的 `Message` 节点来表示变化（如编辑、刷新），这样可以保留完整的历史记录和分支。
*   **清晰的父子关系：** 使用 `parentId` 明确树状结构中的层级关系。
*   **元数据（Metadata）驱动：** 利用一个灵活的元数据字段来存储特定关系（如编辑、刷新、合并来源），而不是为每种特殊关系都添加固定字段，增加未来扩展性。

**2. 领域层实体设计 (Domain Layer Entities)**

以下是核心对话模块相关的领域实体定义，这些定义是平台无关的。

**2.1. `ChatSession` (对话会话)**

`ChatSession` 代表一个完整的对话窗口或主题。

*   **`id` (唯一标识符):** 用于唯一识别一个会话，推荐使用UUID，便于跨平台和同步。
*   **`title` (标题):** 用户可定义的会话标题，或者可以根据首条消息自动生成。
*   **`createdAt` (创建时间):** 会话创建的时间戳。
*   **`lastModifiedAt` (最后修改时间):** 会话中最后一条消息产生或有活动的时间戳，便于排序。
*   **`activeMessageId` (当前活跃消息ID):** 指向当前活跃的消息节点，用于跟踪用户当前的会话状态和上下文路径。
*   **`activeLLMInstanceIds` (活跃模型实例ID列表):**
    *   **类型:** `Data?` (存储序列化后的 `[UUID]`)
    *   **说明:** 当前会话主要使用的 `LLMInstance` 的ID列表。这有助于快速了解会话上下文涉及哪些模型。
*   **`activeContextServerIds` (活跃上下文服务ID列表 - 未来MCP):** `[UUID]` (占位符) 当前会话激活使用的、提供额外上下文或工具的外部服务（MCP Server）的ID列表。V1 中此列表为空。
*   **`settingsId` (会话设置ID):** 指向关联的 `ChatSessionSetting` 实体的ID，包含该会话特定的配置（如系统提示词、温度等）。
*   **`userId` (用户ID):** 指向拥有此会话的 `User` 实体的ID。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 一个键值对集合，用于存储额外信息，如标签、是否置顶、归档状态等，提供未来扩展性。对应的 Swift 类型需遵循 `Codable`。

**2.2. `Message` (消息)**

`Message` 是对话的核心载体，需要设计为树状结构中的节点。

*   **`id` (唯一标识符):** UUID，唯一识别一条消息。
*   **`sessionId` (会话ID):** 指向其所属的 `ChatSession` 或者 `FileSession`的ID。
*   **`parentId` (父消息ID):** 指向其在树状结构中的父节点 `Message` 的ID。对于会话的根消息，此字段可以为 `null` 或指向一个虚拟的会话根节点。**这是实现树状结构的关键。**
*   **`timestamp` (时间戳):** 消息创建或接收的时间。
*   **`role` (角色):** 标识消息发送者类型。
    *   `user`: 用户发送的消息。
    *   `assistant`: AI模型生成的消息。
    *   `system`: 系统消息（例如，初始设定、错误提示、功能性提示）。
    *   `tool`: (保留，未来可能用于非MCP的Tool Use) 如果模型支持工具调用，可能需要此角色。
    *   `merged_assistant`: (建议) 由多个AI回复融合而成的新消息。
*   **`rawResponseText` (原始LLM响应):** (可选, String?) 存储未经解析的 LLM 完整响应文本。方便调试和追溯。
*   **`parsedFileOperations` (解析出的文件操作):** (可选, `[FileOperation]`) 存储从 `rawResponseText` 或结构化输出中解析出的、需要应用或执行的文件相关操作列表（如读、写/应用Diff、搜索）。这部分由 Use Case 层处理。
    ```swift
    enum FileOperation: Codable {
        case readFile(path: String, range: Range?)
        case applyDiff(diff: DiffBlock)
        case searchFile(query: String, path: String?)
        // ...
    }
    ```
*   **`content` (内容):**
    *   **类型:** `Data?` (存储序列化后的 `[ContentBlock]`)
    *   **说明:** 消息的具体内容块列表。`ContentBlock` 枚举需要遵循 `Codable`。
    ```swift
    enum ContentBlock: Codable {
        case text(String) // 普通文本、用户指令、AI解释
        case image(ImageInfo) // 如果支持在讨论中插入图片
        // ... 其他通用类型

        // --- FileSession 特有 (或在 ChatSession 中通过 @文件 功能引入) ---
        case attachedFileContent(fileName: String, content: String) // **(新增)** 用于 @文件 功能引入的内容

        // --- FileSession 特有 (V1 范围) ---
        case thinking(String) // AI 思考过程，UI可选择性展示
        case fileReadRequest(path: String, range: Range?) // AI 请求读取文件内容
        case diffBlock(DiffBlock) // AI 提出的文件修改建议 (核心)
        case fileWriteResult(success: Bool, message: String?) // (可选) AI确认文件写入操作的结果
        case fileSearchRequest(query: String, path: String?) // AI 请求在文件中搜索
        case fileSearchResult(results: [SearchResultInfo]) // AI 返回搜索结果

        // --- 未来 MCP 集成占位 (非V1) ---
        case mcpToolCall(/* 暂不定义详细结构 */) // LLM 请求调用 MCP 工具
        case mcpToolResult(/* 暂不定义详细结构 */) // MCP 工具的执行结果

        // ... 未来可能需要的其他文件操作相关块 ...
    }

    struct DiffBlock: Codable, Identifiable { // 使其 Identifiable 便于 SwiftUI 列表处理
        let id: UUID // 唯一标识一个 diff 建议，用于 PendingEditInfo 关联
        let originalContent: String // 被替换的原文
        let replacementContent: String // 建议替换成的内容
        let filePath: String // 目标文件路径 (初期固定)
        let rangeInOriginal: RangeInfo // 在原始文件中的位置信息 (行/列)
        let explanation: String? // (可选) AI 对此修改的解释
    }

    struct RangeInfo: Codable { /* ... 定义 ... */ } // 定义范围的数据结构
    struct ImageInfo: Codable { /* ... 定义 ... */ } // 定义图片信息结构
    struct SearchResultInfo: Codable { /* ... 定义 ... */ } // 定义搜索结果的数据结构
    ```
*   **`llmInstanceId` (模型实例ID):** 如果 `role` 是 `assistant` 或 `merged_assistant`，此字段指向生成该消息的 `LLMInstance` 的ID。用户或其他角色的消息此字段为 `null`。
*   **`promptTokens` (上行Token数):**
    *   **类型:** 整数 (Integer)，可为空 (Nullable)。
    *   **适用角色:** 主要用于 `assistant` 和 `merged_assistant`。
    *   **说明:** 记录为了生成这条 AI 回复，发送给 LLM API 的提示（包括上下文、用户输入等）所消耗的 Token 数量。对于非 AI 生成的消息，此值为 `null`。
*   **`completionTokens` (下行Token数):**
    *   **类型:** 整数 (Integer)，可为空 (Nullable)。
    *   **适用角色:** 主要用于 `assistant` 和 `merged_assistant`。
    *   **说明:** 记录这条 AI 回复本身所包含的 Token 数量。对于非 AI 生成的消息，此值为 `null`。
*   **`userId` (用户ID):** 如果 `role` 是 `user`，此字段指向发送该消息的 `User` 的ID。
*   **`status` (状态):** 消息的当前状态。
    *   `sending`: 发送中。
    *   `received`: 成功接收/生成。
    *   `error`: 发送/生成失败。
    *   `generating`: AI正在生成中。
    *   `cancelled`: 用户取消生成。
    *   `merged`: 表示此消息是融合结果（配合 `merged_assistant` 角色）。
*   **`userFeedback` (用户反馈状态):** 用于记录用户对 AI 生成消息（`role` 为 `assistant` 或 `merged_assistant`）的显式评价。
    *   类型：枚举 (Enum)
    *   可能的值：
        *   `none` (无反馈 - 默认值)
        *   `liked` (喜欢)
        *   `disliked` (不喜欢)
    *   注意：此字段主要对 `assistant` 和 `merged_assistant` 角色的消息有意义。其他角色的消息此字段应为 `none`。Like 和 Dislike 通常是互斥的。
*   **`isReplied` (是否被回复):** 用于记录用户是否回复某条消息。
    *   类型：布尔值 (Boolean)
    *   `true`: 已回复
    *   `false`: 未回复 (默认值)
    *   注意：虽然理论上用户可以收藏任何类型的消息，但主要场景是收藏 AI 的回复。
*   **`isFavorited` (是否收藏):** 用于记录用户是否收藏了某条消息（通常是 AI 生成的有价值的回复）。
    *   类型：布尔值 (Boolean)
    *   `true`: 已收藏
    *   `false`: 未收藏 (默认值)
    *   注意：虽然理论上用户可以收藏任何类型的消息，但主要场景是收藏 AI 的回复。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 一个灵活的键值对集合，用于存储与此消息相关的特殊信息和关系。**这是实现复杂功能的关键，比固定字段更灵活：**
    *   `is_edit_of: <UUID>`: 如果此消息是用户编辑了之前的某条消息 (`originalMessageId`) 后产生的新消息，这里存储 `originalMessageId`。
    *   `is_regeneration_of: <UUID>`: 如果此消息是AI对之前的某条AI消息 (`originalMessageId`) 进行刷新/重新生成的结果，这里存储 `originalMessageId`。
    *   `merged_from: [<UUID>, <UUID>, ...]`: 如果此消息 (`role` 为 `merged_assistant`) 是由多个AI消息融合而成，这里存储所有原始AI消息的ID列表。
    *   `referenced_message: <UUID>`: 如果此消息的操作（如用户指令或AI生成）是基于之前的某条特定消息，这里存储被引用的消息ID。例如，用户说"请把<ID>这条回复改得更简洁"。
    *   `ui_hidden: <Bool>`: (可选) 用于标记某些内部处理节点或旧版本节点是否应在主界面默认隐藏。
    *   `requested_mode`: `chat` | `edit` (用户消息上，表明意图，只在`FileSession`适用)
    *   `input_file_checkpoint_id`: UUID (触发此消息时基于的 `FileCheckpoint` ID)
    *   `output_file_checkpoint_id`: UUID (如果此消息导致文件状态变更，记录变更后的 `FileCheckpoint` ID，例如AI应用了修改)
    *   `references_diff_block_id`: UUID (如果用户消息是针对某个具体 diff 的评论)
    *   其他未来可能需要的标记...

**2.3. `ChatSessionSetting` (对话会话设置)**

`ChatSessionSetting` 定义了一个特定对话会话的行为和外观配置。它可以是系统默认的，也可以是用户自定义的，并被一个或多个 `ChatSession` 引用。

*   **`id` (唯一标识符):** UUID，用于唯一识别一个设置配置。
*   **`name` (设置名称):** 字符串。用户可识别的名称，例如"默认设置"、"创意写作模式"、"代码助手配置"。
*   **`isSystemDefault` (是否为系统默认):** 布尔值。标记这是否是全局唯一的、不可删除的系统默认设置。有且仅有一个 `ChatSessionSetting` 的此值为 `true`。
*   **`createdAt` (创建时间):** 时间戳。
*   **`lastModifiedAt` (最后修改时间):** 时间戳。
*   **`llmParameterOverrides` (LLM参数覆盖):** **(核心)**
    *   **类型:** `Data?` (存储序列化后的 `[UUID: [String: Codable]]` 结构，例如 JSON)
    *   **说明:** 一个字典结构。键 (Key) 是 `LLMInstance` 的 `id` (UUID)。值 (Value) 是另一个字典，包含需要为该 `LLMInstance` 在 *此会话设置下* 覆盖的参数。例如：`{"gpt4-turbo-instance-id": {"system_prompt": "你是一个乐于助人的助手，请用简洁的语言回答。", "temperature": 0.7}}`。如果某个 `LLMInstance` 在此字典中没有对应的条目，则使用该 `LLMInstance` 自身的默认参数。对应的 Swift 类型需遵循 `Codable`。
*   **`defaultContextServerIds` (默认上下文服务ID列表 - 未来MCP):** `[UUID]?` (占位符) 应用此设置时默认激活的 `ContextServer` ID 列表。V1 中此字段为 `null`。
*   **`uiThemeSettings` (UI主题设置):**
    *   **类型:** `Data?` (存储序列化后的 `UIThemeSettings` 结构)
    *   **说明:** 一个字典或嵌套结构，包含界面相关的配置。`UIThemeSettings` 结构需要遵循 `Codable`。
    *   `themeIdentifier` (主题标识符): 字符串或枚举，例如 "system" (跟随系统), "light", "dark", "ocean_blue" (自定义主题)。
    *   `customColors` (自定义颜色): (可选) 字典，存储自定义主题的颜色值，例如 `{"primary": "#FF5733", "background": "#1E1E1E", "userBubble": "#3A3A3A"}`。
    *   `backgroundImage` (背景图片): (可选) 字符串，存储背景图片的标识符（如资源名或URL）。
    *   `fontSize` (字体大小): (可选) 枚举或数值，如 "small", "medium", "large"。
*   **`messageActionSettings` (消息操作设置):**
    *   **类型:** `Data?` (存储序列化后的 `MessageActionSettings` 结构)
    *   **说明:** 一个字典或嵌套结构，定义不同角色消息上的可用操作按钮。`MessageActionSettings` 结构需要遵循 `Codable`。
    *   `enabledDefaultActionsForUser` (用户消息启用的默认操作): 列表 (List/Array) 的字符串，包含用户选择保留的系统预设操作标识符。例如：`["copy", "edit", "share"]`。UI 根据此列表决定在用户消息上显示哪些标准按钮。
    *   `enabledDefaultActionsForAssistant` (AI消息启用的默认操作): 列表 (List/Array) 的字符串，包含用户选择保留的系统预设操作标识符。例如：`["copy", "favorite", "regenerate", "share"]`。UI 根据此列表决定在AI消息上显示哪些标准按钮。
    *   `customAIActionsForUser` (用户消息自定义AI操作): 列表 (List/Array)，包含用户定义的适用于用户消息的 AI 操作按钮配置。每个元素是一个 `CustomAIAction` 结构。
    *   `customAIActionsForAssistant` (AI消息自定义AI操作): 列表 (List/Array)，包含用户定义的适用于AI消息的 AI 操作按钮配置。每个元素是一个 `CustomAIAction` 结构。
*   **`metadata` (元数据):**
    *   **类型:** `Data?` (存储序列化后的 `[String: Codable]` 结构，例如 JSON)
    *   **说明:** 字典，用于存储未来可能需要的其他配置项。对应的 Swift 类型需遵循 `Codable`。

**2.3.1. 辅助数据结构：`CustomAIAction` (自定义AI操作)**

这不是一个独立的顶层实体，而是 `ChatSessionSetting` 中 `customAIActions` 列表的元素结构。

*   **`id` (唯一标识符):** UUID，用于识别这个特定的自定义操作定义。
*   **`label` (标签):** 字符串。显示在按钮上的文字，例如"总结"、"翻译成英文"、"润色"、"提取要点"。
*   **`icon` (图标):** (可选) 字符串。按钮图标的标识符。
*   **`promptTemplate` (提示词模板):** 字符串。执行此操作时发送给 LLM 的指令模板。可以使用占位符，例如：
    *   `"请总结以下内容：\n{targetMessageContent}"`
    *   `"将下面的文本翻译成英文：\n{targetMessageContent}"`
    *   `"请基于以下对话历史和最后这条消息，提出三个反问：\n{contextMessages}\n用户最后说：{targetMessageContent}"`
    *   占位符需要预定义好，如 `{targetMessageContent}` 代表被操作的消息内容，`{contextMessages}` 代表相关的上下文消息。
*   **`targetLLMInstanceId` (目标LLM实例ID):** UUID。指定使用哪个 `LLMInstance` 来执行这个 AI 操作。允许用户为不同的快捷操作选择最合适的模型。
*   **`applicableToRole` (适用角色):** 枚举或字符串列表。定义这个按钮应该出现在哪种角色的消息上，例如 `assistant` (仅AI回复), `user` (仅用户消息), `all` (所有消息)。
*   **`outputHandling` (输出处理方式):** (可选，高级功能) 枚举。定义如何处理 AI 返回的结果，例如：
    *   `replace`: 替换原消息内容（危险，慎用，或需确认）。
    *   `appendToNewNode`: 在原消息下创建一个新的 AI 回复节点展示结果。
    *   `copyToClipboard`: 将结果复制到剪贴板。
    *   `insertBelow`: 在原消息下方插入一个新的消息节点（可能是用户或AI角色，根据场景）。


**3. 功能实现详解 (How Entities Support Features)**

1.  **树状结构 & 用户/AI节点:**
    *   每个 `Message` 都是一个节点。`parentId` 字段链接节点形成树。根节点的 `parentId` 为 `null`。
    *   `role` 字段区分用户 (`user`) 和 AI (`assistant`) 节点。

2.  **用户编辑触发新分支:**
    *   用户选择编辑消息 `M1` (ID: `m1_id`, Parent: `p_id`)。
    *   不修改 `M1`。创建一个新的 `Message` 节点 `M1_edited`。
    *   `M1_edited.id` 是新的UUID。
    *   `M1_edited.parentId = p_id` (与 `M1` 相同的父节点)。
    *   `M1_edited.role = 'user'`。
    *   `M1_edited.content` 是编辑后的内容。
    *   `M1_edited.metadata = { 'is_edit_of': m1_id }`。
    *   现在，从 `p_id` 出发，存在两个并行的用户消息分支：一个以 `M1` 开始，一个以 `M1_edited` 开始。后续的AI回复将分别挂在 `M1` 和 `M1_edited` 下方，形成不同的对话路径。UI需要提供方式让用户在这些分支间导航查看。

3.  **同时触发多个AI回复:**
    *   用户发送消息 `U1` (ID: `u1_id`)。
    *   App调用两个不同的LLM实例（`LLM1`, `LLM2`）。
    *   生成两个 `Message` 节点：`A1` 和 `A2`。
    *   `A1.id` 是新UUID, `A1.parentId = u1_id`, `A1.role = 'assistant'`, `A1.llmInstanceId = LLM1.id`。
    *   `A2.id` 是新UUID, `A2.parentId = u1_id`, `A2.role = 'assistant'`, `A2.llmInstanceId = LLM2.id`。
    *   这样 `U1` 节点下就有两个并列的子节点 `A1` 和 `A2`，代表两个AI的同时回复。

4.  **AI消息刷新 & 保留历史:**
    *   用户请求刷新AI消息 `A1` (ID: `a1_id`, Parent: `u1_id`)。
    *   不修改 `A1`。创建一个新的 `Message` 节点 `A1_refreshed`。
    *   `A1_refreshed.id` 是新UUID。
    *   `A1_refreshed.parentId = u1_id` (与 `A1` 相同的父节点)。
    *   `A1_refreshed.role = 'assistant'`, `A1_refreshed.llmInstanceId = A1.llmInstanceId` (或用于刷新的实例ID)。
    *   `A1_refreshed.content` 是新的AI回复内容。
    *   `A1_refreshed.metadata = { 'is_regeneration_of': a1_id }`。
    *   现在 `u1_id` 下，与 `A1` 并列（或在UI上表现为 `A1` 的一个新版本）存在 `A1_refreshed`。UI可以根据 `is_regeneration_of` 关系展示版本切换（例如 "查看 V1/V2"）。

5.  **选中回复/Like/Dislike/收藏功能实现:**
    *   当用户在 UI 上对一条 `role` 为 `assistant` 或 `merged_assistant` 的消息进行选中回复开展下一个对话、点赞、点踩或收藏操作时：
        *   更新对应 `Message` 实体的 `isReplied` 字段（设为 `true` 或 `false`）。
        *   更新对应 `Message` 实体的 `userFeedback` 字段（设为 `.liked` 或 `.disliked`，如果取消则设回 `.none`）。
        *   更新对应 `Message` 实体的 `isFavorited` 字段（设为 `true` 或 `false`）。

6.  **多 AI 回答基于历史行为自动排序:**
    *   **数据基础:** 当用户对来自特定 `LLMInstance`（通过 `Message.llmInstanceId` 关联）的回复进行 `liked` 或 `disliked` 操作时，这提供了关于用户对该模型实例偏好的宝贵数据。
    *   **排序逻辑实现 (通常在 Use Case 或 ViewModel 层):**
        *   当用户发送一条消息 `U1`，并同时触发了多个 AI 实例（如 `LLM1`, `LLM2`）生成了并列的回复 `A1` (来自 `LLM1`) 和 `A2` (来自 `LLM2`) 时。
        *   在展示 `A1` 和 `A2` 之前，需要执行排序逻辑。
        *   该逻辑需要查询用户的历史交互数据：
            *   获取与当前用户 (`userId`) 相关的所有 `Message` 中 `role` 为 `assistant` 或 `merged_assistant` 的消息。
            *   根据 `llmInstanceId` 分组，统计每个 `LLMInstance` 获得的 `liked` 和 `disliked` 的总数（或其他更复杂的评分算法，例如考虑时间衰减、反馈强度等）。
            *   为 `LLM1` 和 `LLM2` 计算出一个"偏好得分"。
        *   根据计算出的偏好得分对 `A1` 和 `A2` 进行排序。得分高的（例如 `liked` 多、`disliked` 少）排在前面。
        *   UI 层根据排序后的结果展示 `A1` 和 `A2`。
    *   **性能考量:** 如果历史消息非常多，实时计算偏好得分可能会有性能问题。可以考虑：
        *   **缓存:** 缓存每个用户对每个 `LLMInstance` 的偏好得分，并在用户给出新的反馈时更新缓存。
        *   **聚合表/字段:** 在数据层维护一个用户-模型偏好表（例如 `UserLLMPreference` 实体/表），包含 `userId`, `llmInstanceId`, `likeCount`, `dislikeCount`, `calculatedScore` 等字段，每次用户反馈时更新这个聚合数据。排序时直接查询这个表。这通常是更优化的方案。
    *   **灵活性:** 排序算法本身可以设计得非常灵活，可以结合点赞/点踩数量、收藏次数、甚至用户与该模型后续交互的深度等多种因素。这个算法应封装在领域层或应用层的服务/用例中，与数据存储和 UI 展示解耦。

7.  **多AI回复融合:**
    *   用户选择融合AI消息 `A1` (ID: `a1_id`) 和 `A2` (ID: `a2_id`)，两者父节点都是 `U1` (ID: `u1_id`)。
    *   App调用一个（可能是特定的）LLM实例（`LLM_Merger`）来执行融合任务。
    *   生成一个新的 `Message` 节点 `A_merged`。
    *   `A_merged.id` 是新UUID。
    *   `A_merged.parentId = u1_id` (与被融合消息相同的父节点)。
    *   `A_merged.role = 'merged_assistant'`。
    *   `A_merged.content` 是融合后的内容。
    *   `A_merged.llmInstanceId = LLM_Merger.id`。
    *   `A_merged.metadata = { 'merged_from': [a1_id, a2_id] }`。
    *   `A_merged` 节点现在也是 `U1` 的子节点，代表一个综合性的回复选项。

8.  **AI作用于历史消息 (修改/细化):**
    *   **场景1: 用户要求AI修改之前的AI回复 `A1` (ID: `a1_id`)。**
        *   用户发出指令 `U2` (ID: `u2_id`)，内容可能是"请将<引用A1>回复改得更正式"。
        *   `U2.parentId = a1_id` (将指令挂在被操作的AI回复下，形成上下文)。
        *   `U2.metadata = { 'referenced_message': a1_id }` (明确指出操作对象)。
        *   AI（可能是 `A1` 的原模型，也可能是其他模型）根据 `U2` 的指令生成新的AI回复 `A1_modified` (ID: `a1m_id`)。
        *   `A1_modified.parentId = u2_id` (新回复挂在用户指令 `U2` 下)。
        *   `A1_modified.role = 'assistant'`。
        *   这样就在 `A1` 节点下开辟了一个新的子分支，专门用于对其进行修改和讨论。
    *   **场景2: 用户要求AI根据 `A1` (ID: `a1_id`) 修改之前的用户提问 `U1` (ID: `u1_id`, Parent: `p_id`)。**
        *   用户发出指令 `U2` (ID: `u2_id`)，内容可能是"根据<引用A1>的建议，把我之前的问题<引用U1>改成询问X"。
        *   `U2.parentId = a1_id` (或 `u1_id`? 挂在触发思考的 `A1` 下可能更符合流程)。设置 `U2.metadata = { 'referenced_message': [u1_id, a1_id] }`。
        *   AI生成一个建议性的修改后提问 `U1_revised_suggestion` (ID: `u1s_id`, role: `assistant`, parentId: `u2_id`)。
        *   如果用户接受这个建议（可能通过点击按钮），则创建一个**新的用户消息** `U1_revised` (ID: `u1r_id`)。
        *   `U1_revised.parentId = p_id` (与原始提问 `U1` 相同的父节点，形成平行分支)。
        *   `U1_revised.role = 'user'`。
        *   `U1_revised.content` 是修改后的提问内容。
        *   `U1_revised.metadata = { 'is_edit_of': u1_id, 'context_message': a1_id }` (表明它是 `U1` 的一个编辑版本，且受到了 `A1` 的影响)。
        *   后续的AI交互从 `U1_revised` 开始。

9.  **LLMInstance 特定设置 (System Prompt, Temperature 等): 放在哪里更合理？**

    *   **结论:** 采用**混合方案**最为合理和灵活。
        *   **`LLMInstance` 实体:** 应该存储该实例的**基础/默认**参数设置（System Prompt, Temperature, Max Tokens 等）。这代表了这个"模型实例"的核心配置，可以在多个地方复用。例如，你可能有一个"通用GPT-4"实例和一个"代码专用GPT-4"实例，它们的基础 System Prompt 就不同。
        *   **`ChatSessionSetting` 实体:** 通过 `llmParameterOverrides` 字段，提供在**特定会话**中**覆盖** `LLMInstance` 默认参数的能力。
    *   **用户体验:**
        *   用户可以在"模型管理"（对应你的"模型"tab）界面创建和配置 `LLMInstance`，设定其通用行为。
        *   在具体的聊天会话中，用户可以通过会话设置（可能入口在聊天界面顶部或菜单中），选择一个 `ChatSessionSetting` 预设（或当场修改），并能进一步微调："对于当前这个会话，我想让'通用GPT-4'的 System Prompt 临时变成'扮演莎士比亚'"。
        *   这样既保证了模型实例的可复用性，也提供了会话级别的灵活性，满足用户在不同场景下的微调需求。

10.  **系统默认设置与定制逻辑**

    *   **实现:**
        *   在 App 首次启动或数据库初始化时，创建一个 `ChatSessionSetting` 记录，并将其 `isSystemDefault` 字段设为 `true`。这个设置包含一套预定义的、适用于大多数情况的参数（基础主题、常用消息操作按钮、无 LLM 参数覆盖等）。
        *   当创建一个新的 `ChatSession` 时，默认将其 `settingsId` 指向这个系统默认的 `ChatSessionSetting` 的 `id`。
        *   用户可以在"设置"或特定会话的设置界面中：
            *   查看和管理所有的 `ChatSessionSetting`（系统默认的通常不可删除，但可能允许修改部分内容或基于它创建新设置）。
            *   为当前的 `ChatSession` 选择一个不同的、用户自定义的 `ChatSessionSetting`。此时，更新 `ChatSession` 表中的 `settingsId` 字段。
            *   直接修改当前 `ChatSession` 所关联的 `ChatSessionSetting`（如果是自定义设置）或者基于当前设置创建一个新的自定义设置再应用。
    *   **逻辑流 (获取会话设置):**
        1.  获取当前 `ChatSession` 对象。
        2.  读取其 `settingsId` 字段。
        3.  根据 `settingsId` 查询 `ChatSessionSetting` 表，获取对应的设置对象。
        4.  **应用设置:**
            *   **LLM 参数:** 当需要调用某个 `LLMInstance` 时，先获取该 `LLMInstance` 的基础参数，然后检查当前 `ChatSessionSetting` 的 `llmParameterOverrides` 中是否有针对该 `LLMInstance.id` 的覆盖项。如果有，则使用覆盖后的参数；否则使用基础参数。
            *   **UI:** Presentation Layer 读取 `uiThemeSettings` 并应用。
            *   **消息操作:** Presentation Layer 读取 `messageActionSettings`，结合消息的 `role`，动态生成消息旁边的操作按钮。

11.  **页面样式定制 (主题、配色、背景)**

    *   **实现:** 由 `ChatSessionSetting` 中的 `uiThemeSettings` 字段驱动。
    *   UI 层（Presentation Layer）读取当前会话关联的 `ChatSessionSetting` 中的 `themeIdentifier`, `customColors`, `backgroundImage` 等信息，并据此渲染界面。需要有对应的 Theme Manager 或样式应用逻辑来解析这些配置并更新 UI 元素。

12.  **消息操作按钮定制 (添加/删除默认按钮)**

    *   **实现:** 由 `ChatSessionSetting` 中的 `messageActionSettings` 结构驱动。
    *   UI 层在渲染消息时:
        *   对于用户消息: 检查 `messageActionSettings.enabledDefaultActionsForUser` 列表
        *   对于AI消息: 检查 `messageActionSettings.enabledDefaultActionsForAssistant` 列表
    *   对于每个系统预设动作(如"copy", "edit"), 如果其标识符存在于对应角色的启用列表中, 则显示该按钮
    *   用户可以在设置界面编辑 `ChatSessionSetting` 时, 分别配置用户消息和AI消息的默认操作按钮

13.  **自定义 AI 操作按钮 (用户设定 AI 作用于历史消息)**

    *   **实现:** 由 `ChatSessionSetting` 中的 `messageActionSettings` 结构驱动
    *   **用户侧:**
        *   提供界面允许用户创建两种自定义AI操作:
            *   `customAIActionsForUser`: 适用于用户消息的自定义操作
            *   `customAIActionsForAssistant`: 适用于AI消息的自定义操作
        *   用户需要为每个操作配置:
            *   `label`: 按钮显示文本
            *   `icon`: 按钮图标(可选)
            *   `promptTemplate`: 提示词模板(支持占位符)
            *   `targetLLMInstanceId`: 执行此操作的LLM实例
            *   `outputHandling`: 结果处理方式(可选)
    *   **运行时:**
        *   UI 层渲染消息时，遍历 `customAIActionsForUser` or `customAIActionsForAssistant` 列表。
        *   对于每个 `CustomAIAction`，检查其 `applicableToRole` 是否匹配当前消息的 `role`。
        *   如果匹配，显示对应的按钮（使用 `label` 和 `icon`）。
        *   当用户点击按钮时，触发一个 Use Case，传入 `sessionId`, `targetMessageId`, `customActionId`。
        *   Use Case 负责：查找 `CustomAIAction` 定义 -> 查找目标 `LLMInstance` -> 准备上下文和 Prompt -> 调用 LLM API -> 处理结果（例如，根据 `outputHandling` 创建新消息节点并插入到 `Message` 树中）。

14.  **基于历史行为的多 AI 回答自动排序:**
    *   **数据基础:** `Message` 实体现在存储了 `llmInstanceId`（哪个模型实例生成的）和 `userFeedback`（用户对此类回复的偏好）。
    *   **排序逻辑 (位于 Use Case / Presentation Layer):**
        *   当用户发出一个请求，并同时触发了多个 AI 实例（例如 `LLM_A`, `LLM_B`）生成了并列的回复消息 `Msg_A` 和 `Msg_B` 时。
        *   负责展示这些消息的逻辑（可能在 ViewModel 或 Use Case 中）需要进行排序。
        *   排序算法可以：
            *   查询历史 `Message` 数据。
            *   统计用户对来自 `LLM_A` (`Msg_A.llmInstanceId`) 和 `LLM_B` (`Msg_B.llmInstanceId`) 的消息的 `liked` 和 `disliked` 次数（可能需要加权，比如近期的反馈权重更高）。
            *   计算每个 LLM 实例的"好感度得分"。
            *   根据得分对 `Msg_A` 和 `Msg_B` 进行排序，得分高的排在前面展示。
        *   这个排序是在展示层或准备展示数据时动态计算的，而不是直接存储在 `Message` 结构中。`Message` 结构提供了排序所需的基础数据。

15.  **下载 (Export / Backup) 功能实现：**
    *   **触发：** 用户在会话列表或会话内部选择"下载"或"导出"。
    *   **数据收集：**
        *   根据用户选择的 `ChatSession` 的 `id`，从 Core Data 中获取 `ChatSession` 实体。
        *   递归地（或通过查询 `sessionId`）获取该会话下的**所有** `Message` 实体。确保包含所有分支和历史版本（通过 `parentId` 和 `metadata` 中的 `is_edit_of`, `is_regeneration_of` 等关系连接）。
        *   根据 `ChatSession.settingsId` 获取关联的 `ChatSessionSetting` 实体。
        *   (可选) 根据 `ChatSession.activeLLMInstanceIds` 和 `Message.llmInstanceId` 中涉及到的 ID，获取相关的 `LLMInstance` 的**非敏感**信息（如名称、模型标识、Provider 标识），放入 `llmInstancesInfo`。
    *   **序列化：** 将收集到的实体数据转换为上面定义的 JSON 格式。注意处理 `Date` 类型为 ISO 8601 字符串，`UUID` 转为字符串，`Data` 类型（如果 `metadata` 或 `content` 中有）需要 Base64 编码或类似处理。
    *   **文件生成：**
        *   将 JSON 数据写入一个文件，例如 `会话标题.lavachat` 或 `会话标题.json`。推荐使用自定义扩展名 `.lavachat`，便于系统识别并"用 LavaChat 打开"。
        *   **处理多模态内容：** 如果 `Message.content` 包含本地图片引用，需要：
            *   方案一（推荐）：将图片文件一起打包。创建一个 ZIP 压缩包，里面包含 `chat_data.json` 和一个 `media` 文件夹，存放所有图片文件。JSON 中的图片引用相应地改为相对路径，如 `media/image1.png`。下载的文件就是这个 `.zip` 包（可以仍命名为 `.lavachat`，App 需要能识别并解压）。
            *   方案二：如果图片是网络 URL，直接导出 URL。导入时需要能访问该 URL。
            *   方案三：将图片数据 Base64 编码后嵌入 JSON。简单但会极大增加 JSON 文件大小，不适合大图。
    *   **保存/分享：** 使用 iOS 的 `UIActivityViewController` 提供保存到文件（Files App, iCloud Drive）或通过其他 App 分享该文件的选项。

16.  **分享 (Share) 功能实现：**
    *   **轻量分享 (文本/图片)：**
        *   **文本：** 可以提供一个选项，将对话的主要分支（忽略复杂的编辑/刷新历史）转换为格式化的纯文本或 Markdown，然后通过分享菜单分享文本。这需要一个算法来遍历消息树并生成易读的线性文本。
        *   **图片：** 截屏当前聊天界面的一部分或全部，生成图片后通过分享菜单分享。
    *   **完整分享 (文件)：** 实现方式与"下载"完全一致，最终都是通过 `UIActivityViewController` 分享生成的 `.lavachat` 文件（或 `.zip` 包）。接收方需要安装 LavaChat App 才能理解和导入这个文件。

17.  **导入 (Import) 功能实现：**
    *   **触发：**
        *   通过 iOS 的"Open In..."或从 Files App 中选择 `.lavachat` 文件。App 需要在 `Info.plist` 中注册对该文件类型的支持 (`CFBundleDocumentTypes`, `UTExportedTypeDeclarations`)。
        *   App 内提供"导入会话"的按钮，弹出文件选择器。
    *   **文件处理：**
        *   接收到文件 URL。
        *   检查文件扩展名。如果是 `.zip` 或伪装成 `.lavachat` 的 zip 包，先解压到临时目录。
        *   读取 `chat_data.json` (或直接读取 `.json` 文件)。
    *   **反序列化与验证：**
        *   使用 `JSONDecoder` 将 JSON 数据解析为临时的 Swift 结构体/对象。
        *   检查 `formatVersion` 是否兼容当前 App 版本。
        *   验证数据完整性（例如，`parentId` 是否都能在 `messages` 列表中找到对应的 `id`，除非是根节点）。
    *   **数据处理与冲突解决（关键步骤）：**
        *   **UUID 冲突：**
            *   **策略（推荐）：** 为导入的 `ChatSession`, 所有 `Message`, 以及 `ChatSessionSetting` **生成全新的 UUID**。同时，在内存中维护一个旧 ID 到新 ID 的映射表。在创建 Core Data 实体时，将所有涉及 ID 的字段（`id`, `sessionId`, `parentId`, `settingsId`, `metadata` 中的 ID 引用如 `is_edit_of`, `merged_from` 等）都替换为新的 UUID。这样可以确保导入的数据与现有数据完全隔离，不会产生主键冲突。
            *   **缺点：** 如果用户重复导入同一个文件，会产生多个副本。可以考虑在导入前检查是否存在标题非常相似且内容（例如根消息）也相同的会话，提示用户是否覆盖或跳过。
        *   **用户 ID：** 将导入的 `ChatSession.userId` 和 `Message.userId` (对于 user role) 更新为当前登录用户的 ID。
        *   **设置 (`ChatSessionSetting`)：** 通常直接导入为一个新的自定义设置。如果 `settings.isSystemDefault` 为 `true`，则忽略或将其设为 `false`，因为系统中只能有一个默认设置。可以检查是否存在名称完全相同的设置，提示用户是否复用或重命名。
        *   **LLM 实例 (`llmInstancesInfo` & 引用)：** 这是比较复杂的部分。
            *   遍历导入的 `llmInstancesInfo`。对于每个实例信息，尝试在当前用户的 `LLMInstance` 列表中查找匹配项（基于 Provider+Model 标识，或者名称）。
            *   **映射策略：**
                *   **找到精确匹配：** 将导入数据中引用旧 `llmInstanceId` 的地方（如 `Message.llmInstanceId`, `settings.llmParameterOverrides` 的 key）替换为当前用户匹配到的 `LLMInstance` 的 ID。
                *   **找不到匹配：**
                    *   选项 A：提示用户手动映射。列出导入的未知实例，让用户选择一个现有的本地实例来替代，或跳过（该模型相关的消息/设置可能无法正常工作）。
                    *   选项 B：创建一个"已导入 - 未知模型"的占位 `LLMInstance`，并将引用指向它。用户之后可以手动编辑这个占位实例或修改会话设置。
                    *   选项 C：直接移除对未知实例的引用（例如，将 `Message.llmInstanceId` 设为 null）。
            *   更新 `ChatSession.activeLLMInstanceIds` 和 `ChatSessionSetting.llmParameterOverrides` 中的 ID。
        *   **多模态内容：** 如果是 zip 包导入，将 `media` 文件夹中的文件复制到 App 的安全存储区域，并更新 `Message.content` 中对应的路径/引用为新的存储路径。
    *   **持久化：**
        *   使用处理过的数据（新 UUID、更新的用户 ID、映射后的 LLM 实例 ID、新的媒体路径等）创建新的 `ChatSession`, `Message` (确保 `parentId` 关系正确), 和 `ChatSessionSetting` Core Data 托管对象。
        *   保存 Core Data 上下文。
    *   **UI 更新：** 通知会话列表刷新，显示新导入的会话。可以短暂高亮新导入的会话。


**4. 数据层实现考量 (Data Layer Considerations)**

*   **Core Data:**
    *   `ChatSession`, `Message`, `ChatSessionSetting` 等为实体。
    *   `Message` 对自身有一对多关系 (`children` <-> `parent`)。
    *   `metadata`, `llmParameterOverrides`, `content` 等包含复杂结构或列表的字段，其类型应设置为 `Data`，用于存储序列化后的数据（例如 JSON 或 Plist）。
*   **SQLite:**
    *   `messages` 表有 `parent_id` 外键关联自身 `id`。
    *   `metadata` 等存为 `TEXT` (JSON)。
    *   需为 `session_id`, `parent_id`, `timestamp` 等建立索引。
*   查询特定对话分支（从根到叶）或查找一个节点的所有版本/编辑历史，可能需要递归查询或特定的数据库查询技巧（如SQL中的Common Table Expressions, CTEs）。在应用层处理树的遍历和构建也是常见做法。

**5. 总结**

该设计方案通过 `ChatSession`, `Message`, `ChatSessionSetting` 三个核心实体及其字段（特别是 `parentId`, `metadata`, `llmParameterOverrides`, `messageActionSettings`），构建了一个强大且灵活的对话系统领域模型。

*   **消息层面:** 支持树状结构、编辑/刷新历史、多 AI 并发/融合、用户反馈和收藏。
*   **会话层面:** 支持会话特定设置，包括 LLM 参数微调、UI 主题定制、以及高度可定制的消息操作按钮（含用户自定义 AI 功能）。
*   **架构层面:** 遵循核心设计理念，保持了领域模型的平台无关性和良好的扩展性，为后续功能迭代和跨平台开发奠定了坚实基础。

UI 层（Presentation Layer）需要紧密配合这套领域模型，设计能够清晰展示和操作这些复杂对话结构、分支、版本和设置的交互界面。